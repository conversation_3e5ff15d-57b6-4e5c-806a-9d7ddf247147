# The setup for the project (for Windows)

## Install Node and nvm ([Source](https://docs.antora.org/antora/2.3/install/windows-requirements/))

### Install Chocolatey

The best way to install the Node Version Manager (nvm) and Node is with <PERSON><PERSON>, the package manager for Windows.

1. Open a PowerShell terminal and run it as an Administrator by right clicking on the PowerShell icon and selecting Run as Administrator.

2. Type the following command in the terminal:

		Set-ExecutionPolicy Bypass -Scope Process -Force; iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))

***For getting "Could not create SSL/TLS secure channel." exception in the second step, try running this line first:***

		[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

### Install Node and nvm

1. Open a PowerShell terminal, right click on the PowerShell icon, and select Run as Administrator.

2. To install the Node Version Manager (nvm) for Windows, enter the following command in the terminal:

		choco install -y nvm

3. Close the terminal.

4. Open an new, regular PowerShell terminal, and install Node using nvm:

		nvm install 12.17.0

## Install Yarn ([Source](https://classic.yarnpkg.com/en/docs/install#windows-stable))

### Install via Chocolatey

Once you have Chocolatey installed, you may install yarn by running the following code in your console:

	choco install yarn

This will also ensure that you have Node.js installed.

## Install TortoiseSVN & SVN Checkout

### Download and install TortoiseSVN

- **[TortoiseSVN download site](http://tortoisesvn.net/downloads.html)**

### The steps of SVN Checkout ([Source](http://users.csc.calpoly.edu/~djanzen/setopics/scm/svnTutorial/svnTutorial.html))

1. Open windows explorer.

2. Create a folder where you will store project files.

3. Right-click on the folder you created and select "SVN Checkout".

4. Enter the URL of your repository. Click on "OK".

5. When prompted, enter your username and password.

6. If everything worked, you now have a copy of the repository in your directory.

# Launching the project

1. Under the directory of your folder, run:

		yarn

2. To lunch the project, run:

		yarn mep_dev

3. Then access the project through this address: https://localhost:8080/
