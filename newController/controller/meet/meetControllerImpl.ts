import { CMeetObject } from "@controller/defines/meet/CMeetObject";
import { IMeetController, IMeetList, IMeetRecordingDetail, IMeetTranscriptionItem, MeetUpdateCallback } from "../defines/meetController";
import { MeetUtils } from "@controller/utils/meet";
import { MxAjax, MxISDK } from "isdk";
import moment from "moment";
import { getCacheOrInstantBoard } from "@newController/utils";
import { MxBaseObject, MxSubscription } from "isdk/src/api/defines";
import { MxControllerSubscription } from "@newController/board/subscriptionImpl";
import { ISubscription } from "@newController/defines/common";
import { boardUserToViewModel } from "@model/board/transformers/boardUserTransformer";
import { UserFormatter } from "@controller/utils/user";
import { UserObjectType } from "@model/board/boardUserViewModel";
import { BoardFormatter } from '@controller/utils/board'


let meetController: MeetControllerImpl = null

export class MeetControllerImpl implements IMeetController {

    static getInstance(): MeetControllerImpl {
        if (!meetController) {
            meetController = new MeetControllerImpl()
        }
        return meetController
    }

    getMeetBaseObjectByBoardId(boardId: string, meetBoardId: string): MxBaseObject {
        const mxBoard = getCacheOrInstantBoard(boardId)
        let sessionSeq = mxBoard.sessions?.find(s => s?.session?.board_id === meetBoardId)?.sequence
        if (sessionSeq) {
            return mxBoard.getMeetBaseObject(sessionSeq)
        }
        return null
    }

    queryMeetsOfMonth(fromTs: number, toTs: number, boardId?: string): Promise<IMeetList> {
        const mxUser = MxISDK.getCurrentUser()
        return mxUser.queryMeets(fromTs, toTs, null, boardId).then(user => {
            const meetList: IMeetList = {meets: [], reloaded: true}
            const meetBoards = user?.boards || []
            for (const meetBoard of meetBoards) {
                if (meetBoard.is_deleted) {
                    continue
                }
                const meetObj: CMeetObject = { ...MeetUtils.transformMeetObject(meetBoard.board), ubSequence: meetBoard.sequence }
                meetList.meets.push({ ...meetObj, sequence: null })
            }
            return meetList
        })
    }

    readMonthDateMeetMap(dateTs: number, boardId?: string): Promise<Record<number, boolean>> {
        const mxUser = MxISDK.getCurrentUser()
        const timezone = mxUser.basicInfo?.timezone
        const selectedM = timezone ? moment(dateTs).tz(timezone) : moment(dateTs)
        const monthStartTs = selectedM.startOf('month').valueOf()
        const monthEndTs = selectedM.endOf('month').valueOf()
        return mxUser.queryMeets(monthStartTs, monthEndTs, null, boardId).then(user => {
            const mapping = {}
            const userBoards = user?.boards || []
            for (const ub of userBoards) {
                if (!ub.is_deleted) {
                    const session = ub.board?.sessions?.[0].session || {}
                    const startTime = session.start_time || session.scheduled_start_time
                    mapping[moment(startTime).date()] = true
                }
            }
            return mapping
        })
    }

    readAndSubscribeMeetsOfMonth(monthTs: number, updatedCallback: MeetUpdateCallback, boardId?: string): Promise<[IMeetList, ISubscription]> {
        return new Promise(async (resolve, reject) => {
            try{
                const mxUser = MxISDK.getCurrentUser()
                const timezone = mxUser?.basicInfo?.timezone
                const selectedM = timezone ? moment(monthTs).tz(timezone) : moment(monthTs)
                const monthStartTs = selectedM.startOf('month').valueOf()
                const monthEndTs = selectedM.endOf('month').valueOf()
                const meetList = await this.queryMeetsOfMonth(monthStartTs, monthEndTs, boardId)
    
                let cs: MxControllerSubscription = null
                if (updatedCallback) {
                    cs = MxControllerSubscription.create()
                    let subscription = this.subscribeMeets(monthStartTs, monthEndTs, boardId, updatedCallback)
                    if (subscription) {
                        cs.addSubscription(subscription)
                    }
                }
                resolve([meetList, cs])    
            }catch(e) {
                reject(e)
            }
        })
    }

    async getMeetRecordingMaterials (meetBoardId: string) {
        const mxUser = MxISDK.getCurrentUser()
        const cacheObj = await mxUser.readMeetBasicInfo('', meetBoardId)
        const board = cacheObj?.board
        return MeetUtils.getMeetRecordings(board) ?? []
    }

    async readMeetRecordingDetail (meetBoardId: string): Promise<IMeetRecordingDetail> {
       
        const mxUser = MxISDK.getCurrentUser()
        const loginUser = mxUser && mxUser.user
		
        const cacheObj = await mxUser.readMeetBasicInfo('', meetBoardId)
        const board = cacheObj?.board
        const session = cacheObj?.board?.sessions?.[0]?.session

        const users = (board.users || []).map(buser => BoardFormatter.transformBoardUser(buser, meetBoardId))

        const recordingDetail: IMeetRecordingDetail = {
            title: session?.topic || board?.name,
            videoSeq: session.recording,
            transcriptionSeq: session.transcription,
            vttTranscriptionsSeq: session.transcription_vtt,
            summarySeq: session.meet_summary,
            isSummaryEdited: session.meet_summary_edited,
            videoUrl: session.recording ? `/board/${meetBoardId}/${session?.recording}` : '',
            transcriptionUrl: session.transcription ? `/board/${meetBoardId}/${session?.transcription}` : '',
            vttTranscriptionsUrl: session.transcription_vtt ? `/board/${meetBoardId}/${session?.transcription_vtt}` : '',
            summaryUrl: session.meet_summary ? `/board/${meetBoardId}/${session?.meet_summary}` : '',
            transcriptions: [],
            recordingsForDownloadAndCopyTo: MeetUtils.getMeetRecordings(board) ?? [],
            isOwner: MeetUtils.getHost(users)?.id === loginUser?.id,
        }

        const getUser = (uid: string, name?: string) => {
            let bu = board.users?.find(b => b.user?.id === uid)
            if (!bu) {
                const roster = session?.user_roster?.find(r => r?.user?.id === uid)
                if (roster) {
                    bu = {user: roster.user}
                }
            }

            if (bu) {
                return boardUserToViewModel(bu, board)
            } else if (name) {
                return {
                    objectType: UserObjectType.User,
                    name,
                    avatar: UserFormatter.getInitialAvatar(name)
                }
            }

            return null
        }

        if (recordingDetail.transcriptionUrl) {
            const obj = await MxAjax.downloadJsonResource(recordingDetail.transcriptionUrl)
            for(const transcription of obj?.['transcriptions'] || []) {
                const {id, start, end, text, edited, name, userid} = transcription
                let item: IMeetTranscriptionItem = {
                    id,
                    start,
                    end,
                    text,
                    edited,
                    user: getUser(userid, name)
                }
                recordingDetail.transcriptions.push(item)
            }
        }

        return recordingDetail
    }

    async updateMeetTranscription(meetBoardId: string, transcriptions: IMeetTranscriptionItem[]): Promise<IMeetRecordingDetail> {
        const data =  await MxISDK.getCurrentUser().updateMeetTranscription(meetBoardId, JSON.stringify({transcriptions}))
        const session = data?.sessions?.[0]?.session
        return {
            vttTranscriptionsUrl: session.transcription_vtt ? `/board/${meetBoardId}/${session?.transcription_vtt}` : '',
        }
    }

    async downloadMeetSummary(meetBoardId: string, summarySeq: number): Promise<string> {
        const url = `/board/${meetBoardId}/${summarySeq}`
        const summary = await MxAjax.downloadTextResource(url)
        return summary
    }

    async updateMeetSummary(meetBoardId: string, summary: string): Promise<void> {
        await MxISDK.getCurrentUser().updateMeetSummary(meetBoardId, summary)
    }

    private subscribeMeets(fromTs: number, toTs: number, boardId: string, updatedCallback: MeetUpdateCallback): MxSubscription {
        let subscription = MxISDK.getCurrentUser()?.subscribeUserBoards(userBoards => {
            let reloadMeets = false
            const meetObjects = []
            for (const ub of userBoards) {
                if (ub.is_deleted) {
                    meetObjects.push({ ubSequence: ub.sequence, is_deleted: true })
                }

                const { original_board_id, rrule, start_time, scheduled_start_time } = ub.board?.sessions?.[0]?.session || {}
                const isRecurrent = !!rrule
                const meetTime = start_time || scheduled_start_time
                const isInMonth = meetTime > fromTs && meetTime < toTs
                let isMatchedMeet = false
                if (boardId && boardId === original_board_id && (isRecurrent || isInMonth)) {
                    isMatchedMeet = true
                }else if (!boardId && (isRecurrent || isInMonth)) {
                    isMatchedMeet = true
                }

                if (isMatchedMeet) {
                    if (isRecurrent) {
                        reloadMeets = true
                        break
                    } else {
                        const meetObj = MeetUtils.transformMeetObject(ub.board)
                        if (meetObj.isRestartable) {
                            // update meet list to relocate "Restartable" flag of meet
                            reloadMeets = true
                            break
                        } else {
                            meetObjects.push({ ...meetObj, ubSequence: ub.sequence })
                        }
                    }
                }
            }
            if (reloadMeets) {
                this.queryMeetsOfMonth(fromTs, toTs, boardId).then(meets => updatedCallback(meets))
            } else if (meetObjects.length) {
                updatedCallback({ meets: meetObjects, reloaded: false })
            }
        })

        return subscription
    }
}


export const MeetController = MeetControllerImpl.getInstance();
window['MeetController'] = MeetController
