import { EActionObjectType } from "@model/baseObjects/defines/baseObjectViewModel"
import { IActionController, IResendActionReminderParam, IActionObjToWorkflowStepParam, IAddActionParam, IReadActionParam, IUpdateActionParam } from "@newController/defines/actionController"
import { IWorkflowBoardViewModel, IWorkflowRole, IWorkflowStepViewModel, IWorkflowViewModel } from "@model/workflow"
import { BoardSignatureStatus, TransactionStatus, ITransactionUpdateOption, MxBaseObjectType, MxBoardOption, MxSPath, TransactionStepStatus, WorkflowStep, TransactionType, BoardSignature, BoardTransaction } from "isdk/src/api/defines"
import { BoardViewModelMgr } from "@newController/../viewModelMgr/boardViewModelMgr"
import { IActionObjectViewModel } from "@model/baseObjects/defines/actionObjectViewModel"
import { getCacheBoard, getCacheOrInstantBoard, readBoardBasicInfo } from "@newController/utils"
import { WorkflowEntity } from "@model/workflow/entity/workflowEntity"
import { WorkflowStepEntity } from "@model/workflow/entity/workflowStepEntity"
import { TransactionController } from "./transactionControllerImpl"
import { actionTypeToWorkflowStepType, getActionStepBySequence, getAllActionSteps, spath2BaseObject } from "@model/baseObjects/transformers/actionObjectUtil"
import { isTransactionBasedActionObject } from "@model/baseObjects/transformers/transactionUtil"
import { Defines, MxISDK } from "isdk"
import { cloneDeep, merge } from "lodash"
import { WorkflowBoardController } from "@newController/workflow/workflowBoardControllerImpl"
import { createWorkflowStepEntity } from "@model/workflow/entity/utils"
import { createTransactionEntity } from "@model/baseObjects/entity/transactionEntity"
import { MxLogger } from "@controller/utils"
import { ObjectUtils } from '@commonUtils/object'
import { IBoardAssigneeViewModel } from "@model/board/boardViewModel"
import { BoardController } from "./boardControllerImpl"
import { autoFillBoardAssignee, getRoleId, getTeamId, getUserId, isEmptyAssignee, wrapUserObjectToBoardAssignee } from "@model/board/transformers/boardUserTransformer"
import { BoardEntity } from "@model/board/boardEntity"
import { WorkflowBoardEntity } from "@model/workflow/entity/workflowBoardEntity"
import { ICreateBoardParam } from "@newController/defines/boardController"
import { MxControllerSubscription } from "./subscriptionImpl"
import { ISubscription } from "@newController/defines/common"
import { IUserBase } from "@model/board/boardUserViewModel"
import { ITransactionViewModel } from "@model/baseObjects/defines/transactionViewModel"
import FormFactoryManager from "@model/form/factory";
import { FormViewModel } from "@model/form/defines/formViewModel"
import { createWorkflowBoardEntity } from '@model/workflow/entity/utils'
import { DDRUtil } from '@model/workflow/entity/ddrUtil';
import { createWorkflowEntity } from '@model/workflow/entity/utils'
import { DDRController } from '@newController/index'
import {RequestController} from '@controller/request/RequestController';

const hasAutomationStep = (flowModel: IWorkflowViewModel) => {
  return flowModel?.steps?.some(step => !step.isDeleted && step.type === Defines.WorkflowStepType.WORKFLOW_STEP_TYPE_AUTOMATION)
}

let actionControllerInstance = null

export class ActionControllerImpl implements IActionController {
  static getInstance(): ActionControllerImpl {
    if (!actionControllerInstance) {
      actionControllerInstance = new ActionControllerImpl()
    }
    return actionControllerInstance
  }

  async readAMWorkflow (boardId: string, amWorkflowSeq: number, opt?: IReadActionParam): Promise<IWorkflowViewModel> {
    const mxBoard = getCacheOrInstantBoard(boardId, opt as MxBoardOption)
    const workflow = await mxBoard.readWorkflow(amWorkflowSeq)
    const board = {
      id: boardId,
      workflows: [workflow]
    }
    return WorkflowEntity.toViewModel(board)
  }

  async readActionDetail(boardId: string, spath: MxSPath, opt?: IReadActionParam): Promise<IActionObjectViewModel> {
    let baseObj = spath2BaseObject(spath)

    let model: IActionObjectViewModel
    if (baseObj?.type === MxBaseObjectType.WORKFLOW_STEP) {
      model = await this.readWorkflowStepDetail(boardId, baseObj.parentSequence, baseObj.sequence)
    } else if (baseObj?.type === MxBaseObjectType.TRANSACTION) {
      model = await TransactionController.readTransactionDetail(boardId, baseObj.sequence, opt as MxBoardOption)
    } else if (baseObj?.type === MxBaseObjectType.SIGNATURE) {
      throw new Error("Method not implemented.")
    } else if (baseObj?.type === MxBaseObjectType.TODO) {
      throw new Error("Method not implemented.")
    }
    if(opt?.isSaveAsTemplate){
      // clear data for save as template case
      if(model.type === EActionObjectType.PdfForm) {
        const attachment = model.attachments?.[0]
        if (attachment) {
          delete attachment.sequence
          delete attachment.referenceSequence
          model.attachments = [attachment]
        }
        const formViewModel: FormViewModel = FormFactoryManager.toViewModel(JSON.parse(model.actionData.formJSONString), {
          boardId: boardId,
          transactionSequence:baseObj.sequence,
          viewToken:opt?.viewToken
        })
        FormFactoryManager.resetValue(formViewModel)
        model.actionData.formJSONString = JSON.stringify(FormFactoryManager.toServerData(formViewModel))
        model.actionData.formViewModel = formViewModel
        model.templateName = model.title
        model.dueDate = 0
      }
      delete model.amWorkflowSequence
    }

    if (opt?.isFromActionLibrary) {
      // clear steps
      model?.stepGroups?.forEach(sg => {
        sg.steps = []
      })
    }

    if (opt?.updatedCallback) {
      model.subscription = await this.subscribeActionDetail(boardId, spath, opt)
    }

    return model
  }

  async subscribeActionDetail(boardId: string, spath: MxSPath, opt?: IReadActionParam): Promise<ISubscription> {
    let baseObj = spath2BaseObject(spath)
    let cs: MxControllerSubscription = MxControllerSubscription.create()
    if (!opt?.updatedCallback && !opt?.boardEventHandler) return cs

    let opt2 = cloneDeep(opt)
    delete opt2.updatedCallback
    delete opt2.boardEventHandler

    let mxBoard = await MxISDK.getCurrentUser()?.loadBoard(boardId)
    let sub = mxBoard?.subscribeFullBoard(async (board) => {
      if (opt?.boardEventHandler) {
        BoardController.handleBoardEvent(board, opt?.boardEventHandler)
      }

      if (opt?.updatedCallback && !cs.hasBeenUnsubscribed) {
        let updatedObjSeqs = new Set()
        board.workflows?.[0]?.steps?.forEach(s => updatedObjSeqs.add(s.sequence))
        let objects = [...(board.transactions || []), ...(board.signatures || []), ...(board.todos || [])]
        objects.forEach(obj => {
          updatedObjSeqs.add(obj.sequence)
          if (obj.step) {
            updatedObjSeqs.add(obj.step)
          }
        })

        const isActionUpdated = updatedObjSeqs.has(baseObj.sequence)
        if (isActionUpdated) {
          const newModel = await this.readActionDetail(boardId, spath, opt2)
          if (!cs.hasBeenUnsubscribed) {
            opt.updatedCallback(newModel)
          }
        }
      }
    })

    cs.addLoadedBoard(boardId)
    cs.addSubscription(sub)

    return cs
  }

  async addAction (boardId: string, actionObj: IActionObjectViewModel, amWorkflow?: IWorkflowViewModel, opt?: IAddActionParam): Promise<IActionObjectViewModel> {
    let createdActionObject: IActionObjectViewModel
    if (actionObj.isWorkflowStep) {
      const flowSeq = await this.getWorkflowSeq(boardId)
      const stepModel = await this.actionObjToWorkflowStep(actionObj, { isAddNewStep: true })
      if(!amWorkflow) {
        await WorkflowBoardController.addStep(boardId, flowSeq, stepModel, opt?.insertAfterStepId)
      } else {
        //add amWorkflow step to flow(template or instance)
        let step = amWorkflow?.steps?.length ? amWorkflow.steps[0] : null
        if(step) {
          step = { ...stepModel, clientUuid: step.clientUuid }
          amWorkflow.steps[0] = step
          await WorkflowBoardController.addStepsFromWorkflow(boardId, flowSeq, amWorkflow, opt?.insertAfterStepId)
        }
      }
    } else if (isTransactionBasedActionObject(actionObj.type)) {
      createdActionObject = await TransactionController.createTransaction(boardId, actionObj, opt)
      if(amWorkflow && hasAutomationStep(amWorkflow)) {
        const flowSeq = await this.createAMWorkflow(boardId, createdActionObject.sequence, amWorkflow)
        if(!createdActionObject.isTemplate) {
          //Normal binder adds action with automation needs to kickoff the workflow
          await this.updateWorkflowStatus(boardId, flowSeq, Defines.WorkflowStatus.WORKFLOW_STATUS_RUNNING)
        }
      }
    } else if (actionObj.type === EActionObjectType.ESign) {
      // TODO
    } else if (actionObj.type === EActionObjectType.Todo) {
      // TODO
    }

    await this.inviteActionAssigneesIntoWorkspace(boardId, actionObj)

    if (opt?.actionLibraryBoardId && opt?.actionLibraryObjectSpath) {
      getCacheOrInstantBoard(opt?.actionLibraryBoardId).increaseUsedCount([opt?.actionLibraryObjectSpath])
    }

    return createdActionObject
  }

  async addActionToNewWorkspace (model: IActionObjectViewModel, boardInfo: ICreateBoardParam, amWorkflow?: IWorkflowViewModel): Promise<string> {
    const boardParam: ICreateBoardParam = {
      ...boardInfo,
      isWorkflow: model.isWorkflowStep
    }

    const assignees = this.getAllActionAssignees(model, true, true)

    boardParam.users = assignees.filter(m => m.user?.userId).map(m => m.user)
    boardParam.teams = assignees.filter(m => m.team?.teamId).map(m => m.team)

    const boardId = await BoardController.createBoard(boardParam)
    await this.addAction(boardId, model, amWorkflow)
    return boardId
  }

  async addActionToMockWorkspace(model: IActionObjectViewModel, mockBoardId: string, amWorkflow?: IWorkflowViewModel): Promise<string> {
    const mockBoardInstance = getCacheBoard(mockBoardId)
    const realBoard = await mockBoardInstance.sync()
    await this.addAction(realBoard.id, model, amWorkflow)
    return realBoard.id
  }

  async updateAction (boardId: string, spath: MxSPath, model: IActionObjectViewModel, amWorkflowEntity?: WorkflowEntity, opt?: IUpdateActionParam): Promise<void> {
    const baseObj = spath2BaseObject(spath)
    if (baseObj?.type === MxBaseObjectType.WORKFLOW_STEP) {
      await this.updateWorkflowStep(boardId, spath, model, opt)
    } else {
      await this.updateActionObject(boardId, spath, model, opt)
    }
    const amWorkflow = amWorkflowEntity?.model
    if (model.amWorkflowSequence) {
      const actionAMWorkflow = await this.readAMWorkflow(boardId, model.amWorkflowSequence)
      if (actionAMWorkflow && !actionAMWorkflow.isDeleted) {
        if (amWorkflow) {
          //content library case(only this case has amWorkflow)
          await this._updateAMWorkflow(amWorkflowEntity)
        } else {
          //update title and des only for instance case
          if (actionAMWorkflow?.steps.length) {
            const firstStep = actionAMWorkflow.steps[0]
            await this.updateWorkflowStepInfo(actionAMWorkflow.boardId, actionAMWorkflow.sequence, {
              sequence: firstStep?.sequence,
              name: model.title,
              description: model.description
            })
          }
        }
      }
    } else {
      if (amWorkflow) {
        if (amWorkflow) {
          //content libray case(only this case has amWorkflow)
          const { attrVal } = ObjectUtils.parseSPath(spath)
          await this.createAMWorkflow(boardId, attrVal, amWorkflow)
        }
      }

      await this.inviteActionAssigneesIntoWorkspace(boardId, model)
    }
  }
  async moveAction (boardId: string, spath: MxSPath, dstBoardId: string): Promise<ITransactionViewModel> {
    const { attrVal } = ObjectUtils.parseSPath(spath)
    return TransactionController.moveTransaction(boardId, parseInt(attrVal), dstBoardId)
  }

  async deleteAction(boardId: string, spath: MxSPath): Promise<void> {
    throw new Error("Method not implemented.")
  }

  async resetActionStatus(boardId: string, spath: MxSPath, steps?: Array<{sequence?: number, status?: TransactionStepStatus}>): Promise<void> {
    let baseObj = spath2BaseObject(spath)
    let mxBoard = getCacheOrInstantBoard(boardId)
    if (baseObj.type === MxBaseObjectType.TRANSACTION) {
      await mxBoard.resetTransactionStatus(baseObj.sequence, steps)
    } else if (baseObj.type === MxBaseObjectType.SIGNATURE) {
      await mxBoard.resetSignatureStatus(baseObj.sequence)
    } else if (baseObj.type === MxBaseObjectType.TODO) {
      // TODO
    }
  }

  async reopenAction(boardId: string, spath: MxSPath, generateReopenEvent?: boolean, status?: TransactionStatus|BoardSignatureStatus): Promise<void> {
    let baseObj = spath2BaseObject(spath)
    let mxBoard = getCacheOrInstantBoard(boardId)
    if (baseObj.type === MxBaseObjectType.TRANSACTION) {
      await mxBoard.reopenTransaction(baseObj.sequence, generateReopenEvent, status as TransactionStatus)
    } else if (baseObj.type === MxBaseObjectType.SIGNATURE) {
      await mxBoard.reopenSignature(baseObj.sequence, generateReopenEvent)
    } else if (baseObj.type === MxBaseObjectType.TODO) {
      // TODO
    }
  }

  async resendActionReminder(boardId: string, param: IResendActionReminderParam): Promise<void> {
    let mxBoard = getCacheOrInstantBoard(boardId)

    if (param.isEsignAction) {
      const obj: BoardSignature = {sequence: param.baseObjSeq}
      if (param.isPreparer) {
        obj.editor = {}
      } else {
        obj.signees = [{
            sequence: param.stepSeq
        }]
      }

      await mxBoard.resendSignatureReminder(obj)
    } else {
      const obj: BoardTransaction = {sequence: param.baseObjSeq}
      if (param.isPreparer) {
        obj.editor = {}
      } else {
        obj.steps = [{
            sequence: param.stepSeq
        }]
      }

      await mxBoard.resendTransactionReminder(obj)
    }
  }

  async workflowStepToActionObject(stepModel: IWorkflowStepViewModel, flowModel?: IWorkflowViewModel): Promise<IActionObjectViewModel> {
    let actionModel: IActionObjectViewModel
    const baseObject = WorkflowStepEntity.getStepBaseObject(stepModel)
    if (stepModel.baseObjectSequence) {
      actionModel = await ActionController.readActionDetail(stepModel.inputBoardId, baseObject.spath, { viewToken: stepModel.inputBoardViewToken })
      this.mergeActionFromWorkflowStep(actionModel, stepModel, flowModel)
    }

    return actionModel
  }
  createTodoTransactionObject ({
    boardName,
    noDestBoard,
    invitedTeams,
    invitedIndividuals,
    selectedBoardId,
    transactionTitle,
    transactionDueDate,
    dueInTimeframe,
    excludeWeekends,
    transactionDescription,
    reminderTime,
    step_groups,
    step_group,
    transactionAssigneesList,
    amWorkflow,
    attachmentStore
  }) {
    const tempBoardId = attachmentStore.currentBinder.id
    const existingFiles = attachmentStore.existingFiles
    const uploadedFiles = attachmentStore.boardFiles
    const destBoard = {
      id: selectedBoardId,
      name: boardName,
      teams: invitedTeams,
      users: invitedIndividuals
    }

    const attachments = []

    for (const file of uploadedFiles) {
      if (file.isDDRFile) {
        continue
      }
      if (file.isContentLibrary) {
        const sourceFile = file.file
        attachments.push({
          SPath: sourceFile.SPath,
          boardId: sourceFile.folderId,
          isContentLibrary: true
        })
      }

      if (!file.error) {
        const sequence = file?.response?.object?.board?.page_groups[0]?.sequence

        if (sequence) {
          attachments.push({
            boardId: tempBoardId,
            SPath: `page_groups[sequence=${sequence}]`
          })
        }
      }
    }

    for (const file of existingFiles) {
      if (file.isDDRFile) {
        continue
      }
      const { boardId, SPath, spath, binderId, viewToken } = file
      const allSPath = SPath || spath  // SPath is in old file object, spath is in new IFileBriefViewModel.
      attachments.push({ boardId: boardId || binderId, SPath: allSPath, viewToken })
    }


    const transactionObj: any = {
      title: transactionTitle,
      description: transactionDescription,
      dueDate: transactionDueDate,
      dueInTimeframe,
      excludeWeekends,
      type: 'TRANSACTION_TYPE_TODO',
      attachments: attachments,
      assignees: transactionAssigneesList,
      step_groups,
      step_group,
      reminderTime
    }
    const instance = new RequestController()
    if (!noDestBoard) {
      return instance.createCommonTransactionObject(tempBoardId, destBoard, transactionObj, TransactionType.TRANSACTION_TYPE_TODO, amWorkflow)
    } else {
      return instance.createObjectTemplate(tempBoardId, transactionObj)
    }
  }
  private isPreparerChanged (actionModel: IActionObjectViewModel, stepModel: IWorkflowStepViewModel) {
    const assignee1 = actionModel?.editor
    const assignee2 = stepModel?.editor
    if (getUserId(assignee1) !== getUserId(assignee2)
      || getTeamId(assignee1) !== getTeamId(assignee2)
      || getRoleId(assignee1) !== getRoleId(assignee2)) {
        return true
    }

    return false
  }

  private isActionStepChanged(actionModel: IActionObjectViewModel, stepModel: IWorkflowStepViewModel) {
    if (!actionModel || !stepModel) return false
    const s1 = getAllActionSteps(actionModel) || []
    const s2 = stepModel.subSteps || []

    if (s1.length != s2.length) return true

    // todo: for multiple step group case
    if (stepModel.isSubStepsParallel != actionModel?.stepGroups?.[0]?.isParallel) return true

    for (let i = 0; i < s1.length; i++) {
      let assignee1 = s1[i].assignee
      let assignee2 = s2[i].assignee || s2[i].assigneeUser || s2[i].assigneeRole

      if (getUserId(assignee1) !== getUserId(assignee2)
        || getTeamId(assignee1) !== getTeamId(assignee2)
        || getRoleId(assignee1) !== getRoleId(assignee2)) {
          return true
      }
    }

    return false
  }

  private transformAssignee(assignee: IBoardAssigneeViewModel, flowModel: IWorkflowViewModel, isStepAssigneeChanged?: boolean) {
    let assigneeRole: IWorkflowRole = null
    let assigneeUser: IUserBase = null

    if (assignee?.isRole && assignee?.role?.roleId) {
      if (!assignee?.role?.name) {
        assignee.role.name = assignee?.role?.roleId
      }

      const role = flowModel?.roles?.find(r => (r.roleId || r.name) === assignee?.role?.roleId)
      if (role) {
        assignee.role = role
      }
      assigneeRole = assignee.role
    }

    if (flowModel?.isTemplate) {
      // for use template case, if sub steps changed, assignee should be detached from role, otherwise attached with original role
      if (!isStepAssigneeChanged) {
        let uid = getUserId(assignee) || getTeamId(assignee)
        if (uid) {
          const role = flowModel?.roles?.find(r => getUserId(r.assigneeUser) === uid || getTeamId(r.assigneeUser) === uid)
          if (role) {
            assigneeRole = role
            assignee = wrapUserObjectToBoardAssignee(role.assigneeUser || role)
          }
        }
      }
    }

    assigneeUser = assignee?.user || assignee?.team
    return {assignee, assigneeRole, assigneeUser}
  }

  async actionObjToWorkflowStep(actionObj: IActionObjectViewModel, param?: IActionObjToWorkflowStepParam): Promise<IWorkflowStepViewModel> {
    let shouldCreateTmpBoard = false
    let affectTmpBoardProperties = ['attachments', 'stepGroups', 'enableDecline', 'card']
    if (param?.isAddNewStep || param.createActionInTmpBoard) {
      shouldCreateTmpBoard = true
    } else if (param?.updatedProperties?.filter(p => affectTmpBoardProperties.includes(p))?.length) {
      shouldCreateTmpBoard = true
    }

    const originalStepModel = param.flowModel?.steps?.find(s => s.clientUuid === actionObj.clientUuid)
    if (!shouldCreateTmpBoard && originalStepModel) {
      // basic info changed, do not need to create new tmp board
      const newStepModel = cloneDeep(originalStepModel)
      newStepModel.subSteps = originalStepModel.subSteps
      const { title, description, dueDate, dueInTimeframe, excludeWeekends, enablePreparation } = actionObj
      const { editor, ddrAttachments, option } = actionObj

      merge(newStepModel, { title, description, dueDate, dueInTimeframe, excludeWeekends, enablePreparation })
      if (ddrAttachments !== undefined) {
        newStepModel.ddrAttachments = ddrAttachments
      }

      if (editor !== undefined) {
        let isPreparerAssigneeChanged = this.isPreparerChanged(actionObj, originalStepModel)
        newStepModel.editor = this.transformAssignee(actionObj.editor, param.flowModel, isPreparerAssigneeChanged).assignee
      }

      if (option?.skipSequential !== undefined) {
        newStepModel.skipSequentialOrder = option.skipSequential
      }

      if (param?.isPrepare) {
        newStepModel.status = Defines.WorkflowStepStatus.WORKFLOW_STEP_STATUS_READY
      }

      return newStepModel
    }

    // create action in new tmp board
    const board = await MxISDK.getCurrentUser().createTempBoard({})
    let tmpBoardId = board.id
    let mxTmpBoard = getCacheOrInstantBoard(tmpBoardId)
    await mxTmpBoard.createViewToken(Defines.BoardAccessType.BOARD_OWNER)

    let actionObj2 = cloneDeep(actionObj)
    actionObj2.isWorkflowStep = false
    actionObj2.isTemplate = true

    // clear sequence when add
    actionObj2.stepGroups?.forEach(groups => {
      groups.steps?.forEach(step => {
        delete step.sequence
      })
    })
    delete actionObj2.sequence
    
    try {
      // MV-19398: keep old actions
      if ( (actionObj.type === EActionObjectType.Approval || actionObj.type === EActionObjectType.PdfForm)
        && (originalStepModel?.inputBoardId && originalStepModel?.baseObjectSequence)) {
          const oldModel = await TransactionController.readTransactionDetail(originalStepModel?.inputBoardId, originalStepModel?.baseObjectSequence, {viewToken: originalStepModel?.inputBoardViewToken})
          const reviewerStepAction = getAllActionSteps(oldModel)?.find(s => s.isReviewerStep)?.actions
          const commonStepAction = getAllActionSteps(oldModel)?.find(s => !s.isReviewerStep)?.actions

          const reviewerSteps = getAllActionSteps(actionObj2)?.filter(s => s.isReviewerStep) || []
          const commonSteps = getAllActionSteps(actionObj2)?.filter(s => !s.isReviewerStep) || []

          if (Array.isArray(reviewerStepAction)) {
            for (const s of reviewerSteps) {
              s.actions = reviewerStepAction
            }         
          }

          if (Array.isArray(commonStepAction)) {
            for (const s of commonSteps) {
              s.actions = commonStepAction
            }         
          }
      }
    } catch(e) {}
    
    const createdActionObject = await TransactionController.createTransaction(tmpBoardId, actionObj2, { outputCreatedActionObject: true, isInFlowStepTmpBoard: true })
    const tmpBoard = await MxISDK.getCurrentUser().readFullBoard(tmpBoardId)

    let isStepAssigneeChanged = this.isActionStepChanged(actionObj, originalStepModel)
    let isPreparerAssigneeChanged = this.isPreparerChanged(actionObj, originalStepModel)
    if (param?.isAddNewStep) {
      isStepAssigneeChanged = true
      isPreparerAssigneeChanged = true
    }

    let rawStep: WorkflowStep = {
      client_uuid: actionObj2.clientUuid,
      type: actionTypeToWorkflowStepType(actionObj2.type),
      board_id: tmpBoard.id,
      board_view_token: tmpBoard.view_tokens?.[0]?.token,
      input: { board: tmpBoard }
    }

    if (param?.isPrepare) {
      rawStep.status = Defines.WorkflowStepStatus.WORKFLOW_STEP_STATUS_READY
    }

    let stepModel = WorkflowStepEntity.toViewModel(rawStep)
    stepModel = {
      ...stepModel,
      ddrAttachments: actionObj2?.ddrAttachments,
      enableDecline: actionObj2.enableDecline,
      skipSequentialOrder: actionObj2?.option?.skipSequential,
      enablePreparation: actionObj2.enablePreparation,
      editor: this.transformAssignee(actionObj2.editor, param.flowModel, isPreparerAssigneeChanged).assignee,
    }
    if(originalStepModel?.displayOrder) {
      stepModel.displayOrder = originalStepModel.displayOrder
    }
    const allActionStepsInCreatedObject = getAllActionSteps(createdActionObject)
    const allActionSteps = getAllActionSteps(actionObj2)
    if (actionObj2?.enableDecline && !allActionSteps?.find(s => s.isReviewerStep)) {
      allActionSteps.unshift({isReviewerStep: true})
    }

    if (allActionSteps?.length === stepModel.subSteps?.length) {
      for (let i = 0; i < stepModel.subSteps.length; i++) {
        let ss = stepModel.subSteps[i]
        let {assignee, assigneeRole, assigneeUser} = this.transformAssignee(allActionSteps[i].assignee, param.flowModel, isStepAssigneeChanged)
        ss.sequence = allActionStepsInCreatedObject?.[i]?.sequence
        ss.assignee = assignee
        ss.assigneeRole = assigneeRole
        ss.assigneeUser = assigneeUser
      }
    } else {
      MxLogger.warn('allActionSteps.length != stepModel.subSteps.length')
    }

    // TODO: handle options

    // remove unused fields
    delete stepModel.orderNumber

    return stepModel
  }

  async readAvailableAssignees(boardId: string): Promise<IBoardAssigneeViewModel[]> {
    let boardModel = BoardViewModelMgr.getBasicInfoViewModel(boardId) as IWorkflowBoardViewModel
    if (!boardModel) {
      let board = await readBoardBasicInfo(boardId)
      if (board.type === Defines.BoardType.BOARD_TYPE_WORKFLOW || board.type === Defines.BoardType.BOARD_TYPE_WORKFLOW_TEMPLATE) {
        boardModel = WorkflowBoardEntity.toViewModel(board)
      } else {
        boardModel = BoardEntity.toBriefViewModel(board)
      }
    }

    const assignees: IBoardAssigneeViewModel[] = []
    boardModel?.users?.forEach(user => {
      if(user && !user.isBot && !user.isDeleted && !user.isDisabled) {
          assignees.push({ user })
        }
    })

    boardModel?.teams?.forEach(team => {
      if(team && !team.isDeleted) {
        assignees.push({ isTeam: true, team })
      }
    })

    boardModel?.workflow?.roles?.forEach(role => {
      if (!boardModel?.isWorkflowTemplate && role?.assigneeUser) {
        // in runtime workspace, hide assigned role
      } else {
        if(role && !role.isDeleted) {
          assignees.push({ isRole: true, role })
        }
      }
    })

    assignees.forEach(assignee => {
      autoFillBoardAssignee(assignee)
    })
    return assignees.sort((a, b) => {
      return (a.name || '').toLowerCase().localeCompare((b.name || '').toLowerCase())
    })
  }

  async updateWorkflowStepInfo (boardId: string,flowSeq: number, step: WorkflowStep) {
    const mxBoard = getCacheOrInstantBoard(boardId)
        await mxBoard.updateWorkflow({
          sequence: flowSeq,
          steps: [{
            ...step
          }]
        }, true)
  }
  async updateWorkflowStatus (boardId: string, flowSeq: number, status: Defines.WorkflowStatus) {
    return await WorkflowBoardController.updateWorkflowStatus(boardId, flowSeq, status)
  }
  async createAMWorkflow (boardId: string, transactionSequence: number, amWorkflow: IWorkflowViewModel) {
    const workflowEntity = createWorkflowEntity(amWorkflow)
    const actionStepEntity = workflowEntity.getStepEntityByClientUuid(workflowEntity.steps[0].clientUuid)
    actionStepEntity.update({
      inputBoard: {
        transactions: [{
          sequence: transactionSequence,
          is_workflow_source: true,
          steps: []
        }]
      }
    })
    const boardEntity = createWorkflowBoardEntity();
    boardEntity.load({ workflow: workflowEntity.model });
    const workflowSequence = await WorkflowBoardController.createWorkflow(boardId, boardEntity)

    const ddrFields = await DDRUtil.parseDDRStringsInWorkflow(boardEntity.model?.workflow)
    if (ddrFields?.length) {
      await DDRController.createDDRVariables({ boardId: boardId, clientUuid: boardEntity.workflowEntity?.model?.clientUuid, workflowSequence }, ddrFields)
    }
    return workflowSequence
  }
  private async _updateAMWorkflow (workflowEntity?: WorkflowEntity) {
    if(!workflowEntity) return
    await WorkflowBoardController.updateWorkflow(workflowEntity)
  }
  private async inviteActionAssigneesIntoWorkspace (boardId: string, model: IActionObjectViewModel) {
    try {
      let assignees = this.getAllActionAssignees(model, true, true)
      if (!boardId || !assignees?.length) return

      const board = await readBoardBasicInfo(boardId)

      const currentBoardUserIds = (board.users || []).filter(bu => !bu.is_deleted && !bu.is_from_team && bu.user?.id).map(bu => bu.user.id)
      const currentBoardTeamIds = (board?.teams || []).filter(t => !t.is_deleted && t.group?.id).map(t => t.group.id)
      const currentBoardUserIdSet = new Set(currentBoardUserIds)
      const currentBoardTeamIdSet = new Set(currentBoardTeamIds)
      const toBeInviteUsers: Defines.UserIdentity[] = []
      const toBeInviteTeams: string[] = []
      for (let assignee of assignees || []) {
        if (!assignee.id) continue

        if (!currentBoardUserIdSet.has(assignee.id) && !currentBoardTeamIdSet.has(assignee.id)) {
          if (assignee.isTeam) {
            toBeInviteTeams.push(assignee.id)
            currentBoardUserIdSet.add(assignee.id)
          } else {
            toBeInviteUsers.push({ id: assignee.id })
            currentBoardTeamIdSet.add(assignee.id)
          }
        }
      }

      if (toBeInviteUsers.length > 0 || toBeInviteTeams.length > 0) {
        await BoardController.inviteMember(boardId, toBeInviteUsers, toBeInviteTeams)
      }

    } catch (e) {
      MxLogger.warn('inviteActionAssigneesIntoWorkspace failed: ', e)
    }

  }

  private removeInvalidActionSteps(model: IActionObjectViewModel) {
    model?.stepGroups?.forEach((stepGroup) => {
      stepGroup.steps = stepGroup.steps?.filter(s => {
        // role is deleted ?
        if (s?.assignee?.isRole  && s?.assignee?.role?.isDeleted) {
          return false
        }
        return true
      }) || []
    })
  }

  private getAllActionAssignees(model: IActionObjectViewModel, excludeMySelf?: boolean, excludeRole?: boolean) {
    let assignees: IBoardAssigneeViewModel[] = []
    const myUserId = MxISDK.getCurrentUser()?.id
    const uniqAssigneeIds = new Set()

    if (excludeMySelf && myUserId) {
      uniqAssigneeIds.add(myUserId)
    }

    model?.stepGroups?.forEach((stepGroup) => {
      stepGroup.steps?.forEach((step) => {
        let assigneeId = step.assignee?.id || step.assignee?.user?.userId || step.assignee?.team?.teamId || step.assignee?.role?.roleId
        if (excludeRole && step.assignee?.isRole) {
          // ignore
        } else if (assigneeId && !uniqAssigneeIds.has(assigneeId)) {
          uniqAssigneeIds.add(assigneeId)
          assignees.push(step.assignee)
        }
      })
    })

    return assignees
  }

  private mergeActionObjectModel(oldModel: IActionObjectViewModel, updatedModel: IActionObjectViewModel) {
    let newModel = cloneDeep(oldModel)
    Object.keys(updatedModel)?.forEach(key => {
      newModel[key] = updatedModel[key]
    })

    return newModel
  }

  private async updateWorkflowStep(boardId: string, spath: MxSPath, model: IActionObjectViewModel, opt?: IUpdateActionParam): Promise<void> {
    const oldModel = await this.readActionDetail(boardId, spath)
    if (!oldModel) throw Error(`action object not found: ${spath}`)

    let baseObj = spath2BaseObject(spath)
    let flowSeq = baseObj.parentSequence, stepSeq = baseObj.sequence
    if (oldModel.isNotStarted) {
      //MV-18697 this logic will keep the callback url in the action object
      let fullActionModel = this.mergeActionObjectModel(oldModel, model)
      let flowBoardModel = BoardViewModelMgr.getWorkflowBoardBasicInfoViewModel(boardId)
      let newStepModel = await this.actionObjToWorkflowStep(fullActionModel, {isPrepare: opt?.isPrepare, createActionInTmpBoard: true, flowModel: flowBoardModel?.workflow})
      newStepModel.sequence = stepSeq
      let stepEntity = createWorkflowStepEntity(newStepModel)
      await WorkflowBoardController.updateStep(boardId, flowSeq, stepEntity, opt?.isPrepare)
    } else {
      const [stepModel] = await this.readWorkflowStep(boardId, baseObj.parentSequence, baseObj.sequence)
      let outputObj = WorkflowStepEntity.getStepBaseObject(stepModel)
      const actionModel = cloneDeep(model)
      actionModel.isWorkflowStep = false
      actionModel.spath = outputObj.spath
      actionModel.sequence = stepModel.outputBaseObjectSequence
      delete actionModel.clientUuid

      if (!opt?.isPrepare && stepModel.isPreparing && !stepModel.isPrepareCompleted) {
        if (!opt) opt = {}
        opt.noNeedReopen = true
      }

      await this.updateActionObject(boardId, outputObj.spath, actionModel, opt)

      // update title/description for workflow step
      if (actionModel.title != stepModel.title || actionModel.description != stepModel.description) {
        await this.updateWorkflowStepInfo(boardId, flowSeq, {
          sequence: stepSeq,
          name: actionModel.title,
          description: actionModel.description
        })
      }
    }
  }

  private async updateActionObject(boardId: string, spath: MxSPath, actionObj: IActionObjectViewModel, opt?: IUpdateActionParam): Promise<void> {
    const oldActionObj = await this.readActionDetail(boardId, spath)
    if (!oldActionObj || oldActionObj?.isDeleted) {
      MxLogger.warn(`updateActionObject failed: action object not found: ${spath}`)
      return
    }

    // do not take skipped reviewer step as completed
    const isPartialDone = getAllActionSteps(oldActionObj).filter(s => !(s.isReviewerStep && s.isSkipped)).some(s => s.isCompleted || s.isCanceled)
    if (opt?.isReopen || isPartialDone) {
      // reviewer step need to reset to skipped status
      const reviewerSteps = getAllActionSteps(oldActionObj).filter(s => s.isReviewerStep).map(s => {
        return {sequence: s.sequence, status: TransactionStepStatus.STEP_STATUS_SKIPPED } as Defines.TransactionStep
      })

      // clear reviewer step assignee
      getAllActionSteps(actionObj).filter(s => s.isReviewerStep)?.forEach(s => {
        s.assignee = null
      })

      await this.resetActionStatus(boardId, spath, reviewerSteps)
    }

    if (isTransactionBasedActionObject(actionObj.type)) {
      const entity = createTransactionEntity(oldActionObj)
      entity.fullUpdate(actionObj)
      let updateOption: ITransactionUpdateOption = {
        suppressFeed: opt?.suppressFeed,
        forceReadyFeed: opt?.isPrepare,
        forceReopenFeed: opt?.isReopen,
        forceUpdateFeed: !opt?.suppressFeed && !opt?.isPrepare && !opt?.isReopen,
        isPrepare: opt?.isPrepare,
      }
      await TransactionController.updateTransaction(boardId, entity, updateOption)

    } else if (actionObj.type === EActionObjectType.ESign) {
      // TODO
    } else if (actionObj.type === EActionObjectType.Todo) {

    }

    if (this.needReopenAction(oldActionObj, actionObj, opt)) {
      try {
        this.reopenAction(boardId, spath, opt?.isReopen)
      }catch(e) {
        MxLogger.warn('reopenAction failed:', e)
      }
    }
  }

  private needReopenAction(oldModel: IActionObjectViewModel, newModel: IActionObjectViewModel, opt?: IUpdateActionParam) {
    let needReopen = false
    let isSequentialOld = !oldModel?.stepGroups?.[0]?.isParallel
    let isSequentialNew = !newModel?.stepGroups?.[0]?.isParallel
    let oldSteps = getAllActionSteps(oldModel) || []
    let newSteps = getAllActionSteps(newModel) || []

    if (opt?.isReopen) {
      return true
    }

    if (opt?.noNeedReopen) {
      return false
    }

    if (newModel?.isTemplate) {
      return false
    }

    if (newModel?.enablePreparation === false && oldModel?.enablePreparation === true) {
      // turn off preparation case
      return false
    }

    if (oldModel?.enablePreparation && oldModel?.isPreparing && !oldModel?.isPrepareCompleted) {
      return false
    }

    const isPartialDone = oldSteps?.filter(s => !(s?.isReviewerStep && s?.isSkipped))?.some(s => s.isCompleted || s.isDeclined)
    if (isPartialDone) {
      needReopen = true
    } else if (isSequentialNew) {
      const oldActiveAssignee = oldSteps?.find(s => s.isInProgress)?.assignee
      const newActiveAssignee = newSteps?.at(0)?.assignee
      if (isSequentialOld) {
        // for sequential to sequential case, if first assignee changed, need reopen
        needReopen = oldActiveAssignee?.id !== newActiveAssignee?.id
      } else {
        // for parallel to sequential case, if first assignee is not started before, need reopen
        needReopen = !oldSteps.filter(s => s.isInProgress && s.assignee?.id === newActiveAssignee?.id).length
      }
    } else {
      if (isSequentialNew !== isSequentialOld) {
        // for sequential to parallel case, need reopen
        needReopen = true
      } else {
        // for parallel to parallel case, if add new assignee, need reopen
        let oldAssigneeIds = oldSteps.map(s => s.assignee?.id) || []
        let newAssigneeIds = newSteps.map(s => s.assignee?.id) || []
        let noNewAssignee = newAssigneeIds.every(id => oldAssigneeIds.includes(id))  
        needReopen = !noNewAssignee
      }
    }

    return needReopen
  }

  private async readWorkflowStepDetail(boardId: string, workflowSeq: number, stepSeq: number): Promise<IActionObjectViewModel> {
    const [stepModel, flowModel] = await this.readWorkflowStep(boardId, workflowSeq, stepSeq)
    if (!stepModel) {
      MxLogger.warn(`readWorkflowStepDetail failed, step not found: ${stepSeq}`)
      return {
        spath: getCacheOrInstantBoard(boardId).getWorkflowStepBaseObject(workflowSeq, stepSeq).spath,
        isDeleted: true
      } as IActionObjectViewModel
    }

    let actionModel: IActionObjectViewModel
    const baseObject = WorkflowStepEntity.getStepBaseObject(stepModel)
    if (stepModel.outputBaseObjectSequence) {
      actionModel = await ActionController.readActionDetail(boardId, baseObject.spath)
    } else if (stepModel.baseObjectSequence) {
      actionModel = await ActionController.readActionDetail(stepModel.inputBoardId, baseObject.spath, { viewToken: stepModel.inputBoardViewToken })
      actionModel.attachments?.forEach(atta => atta.viewToken = stepModel.inputBoardViewToken)
    } else {
      MxLogger.warn('step base object not found')
      return actionModel
    }

    this.mergeActionFromWorkflowStep(actionModel, stepModel, flowModel)

    return actionModel
  }

  private mergeActionFromWorkflowStep (actionModel: IActionObjectViewModel, stepModel: IWorkflowStepViewModel, flowModel?: IWorkflowViewModel) {
    const { sequence, clientUuid, spath, skipSequentialOrder, displayOrder, orderNumber } = stepModel
    const { isNotStarted, isInProgress, isCompleted, isDeclined, isPreparing, isPrepareCompleted, isMyTurn, isMyTeamTurn } = stepModel

    Object.assign(actionModel, { sequence, clientUuid, spath, displayOrder, orderNumber })
    if(flowModel?.isSequential === false) {
      actionModel.displayOrder = ''
    }
    actionModel.option = actionModel.option || {}
    if(skipSequentialOrder) {
      actionModel.option.skipSequential = true
    }
    Object.assign(actionModel, { isNotStarted, isInProgress, isCompleted, isDeclined, isPreparing, isPrepareCompleted, isMyTurn, isMyTeamTurn })

    if (stepModel.isNotStarted) {
      const { title, description, dueDate, dueInTimeframe, excludeWeekends, enablePreparation, editor, ddrAttachments } = stepModel

      Object.assign(actionModel, { title, description, dueDate, dueInTimeframe, excludeWeekends, enablePreparation, editor, ddrAttachments })

      actionModel.attachments?.forEach(atta => atta.viewToken = stepModel.inputBoardViewToken)

      if (editor?.isRole && editor?.role?.assigneeUser) {
        actionModel.editor = wrapUserObjectToBoardAssignee(editor?.role?.assigneeUser) 
      }
      autoFillBoardAssignee(actionModel.editor)

      for (let ss of stepModel.subSteps || []) {
        const acctionStep = getActionStepBySequence(actionModel, ss.sequence)
        if (acctionStep) {
          acctionStep.assignee = ss.assignee
          if (acctionStep.assignee?.isRole && ss.assigneeRole) {
            // TODO: for update role name case, role name in stepModel.assigee is not updated
            acctionStep.assignee.role = ss.assigneeRole
          }
          // if assignee is deleted, set it to null
          if (ss.assignee?.role?.isDeleted) {
            acctionStep.assignee = null
          }
          autoFillBoardAssignee(acctionStep.assignee)

          if (isEmptyAssignee(ss.assignee)) {
            if (ss.assigneeUser) {
              acctionStep.assignee = wrapUserObjectToBoardAssignee(ss.assigneeUser)
            } else if (ss.assigneeRole && !ss.assigneeRole.isDeleted) {
              if (ss.assigneeRole?.assigneeUser) {
                acctionStep.assignee = wrapUserObjectToBoardAssignee(ss.assigneeRole?.assigneeUser)
              } else {
                acctionStep.assignee = wrapUserObjectToBoardAssignee(ss.assigneeRole)
              }  
            }
          }

          if (ss.assignee?.isRole && ss.assignee?.role?.assigneeUser) {
            acctionStep.assignee = wrapUserObjectToBoardAssignee(ss.assignee?.role?.assigneeUser) 
          }

          if (ss.orderNumber) {
            acctionStep.orderNumber = ss.orderNumber
          }
        }
      }
    } else {
      // for started step, assignee maybe also role
      const roleAssignees = this.getAllActionAssignees(actionModel, true)?.filter(a => a.isRole && a.role?.roleId) || []
      if (actionModel.enablePreparation && actionModel.editor) {
        roleAssignees.push(actionModel.editor)
      }

      for (const r1 of roleAssignees) {
        const role = flowModel?.roles?.find(r2 => r2.roleId === r1?.role?.roleId)
        if (role) {
          r1.role = cloneDeep(role)
          r1.name = role.displayName
          r1.avatar = role.avatar
        }
      }
    }

    this.removeInvalidActionSteps(actionModel)
  }

  private async readWorkflowStep(boardId: string, workflowSeq: number, stepSeq: number): Promise<[IWorkflowStepViewModel, IWorkflowViewModel]> {
    const mxBoard = getCacheOrInstantBoard(boardId)
    const workflowDetail = await mxBoard.readWorkflowDetail()
    const workflowModel = WorkflowEntity.toViewModel(workflowDetail)
    const stepModel = workflowModel?.steps?.find(s => s.sequence === stepSeq)
    if (workflowModel?.isSequential === false && stepModel) {
      stepModel.displayOrder = ''
    }

    return [stepModel, workflowModel]
  }

  private async getWorkflowSeq(boardId: string): Promise<number> {
    const flowBoard = BoardViewModelMgr.getWorkflowBoardBasicInfoViewModel(boardId)
    if (flowBoard?.workflow?.sequence) {
      return flowBoard.workflow?.sequence
    }

    let board = await readBoardBasicInfo(boardId)
    const workflows = board?.workflows?.filter(f => !f.is_deleted)
    if (workflows?.length) {
      return workflows?.at(-1)?.sequence
    }

    return 0
  }

}

export const ActionController = ActionControllerImpl.getInstance()
window['ActionController'] = ActionController
