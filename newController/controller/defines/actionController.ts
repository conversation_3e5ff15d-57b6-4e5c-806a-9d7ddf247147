
import { IActionObjectViewModel } from '@model/baseObjects/defines/actionObjectViewModel';
import { IBoardAssigneeViewModel } from '@model/board/boardViewModel';
import { IWorkflowStepViewModel, IWorkflowViewModel } from '@model/workflow';
import { BoardSignatureStatus, MxCallback, MxSPath, TransactionStatus, TransactionStepStatus } from 'isdk/src/api/defines';
import { BoardEventHandler, ICreateBoardParam } from './boardController';
import { WorkflowEntity } from '@model/workflow/entity/workflowEntity';

export type ActionUpdatedCallback = MxCallback<IActionObjectViewModel>;
  
export interface IReadActionParam {
  viewToken?: string;
  isFromActionLibrary?: boolean;
  isSaveAsTemplate?: boolean;
  updatedCallback?: ActionUpdatedCallback;
  boardEventHandler?: BoardEventHandler;
}

export interface IAddActionParam {
  workflowSeq?: number;
  insertAfterStepId?: string;
  outputCreatedActionObject?: boolean;

  isInFlowStepTmpBoard?: boolean;

  // add from action library
  actionLibraryBoardId?: string; 
  actionLibraryObjectSpath?: string;
}


export interface IUpdateActionParam {
  isPrepare?: boolean;
  isReopen?: boolean;
  suppressFeed?: boolean;
  noNeedReopen?: boolean;
}

export interface IActionObjToWorkflowStepParam {
  createActionInTmpBoard?: boolean;
  flowModel?: IWorkflowViewModel;
  isPrepare?: boolean;

  isAddNewStep?: boolean;
  updatedProperties?: string[];
}

export interface IResendActionReminderParam {
  isPreparer?: boolean;
  isEsignAction?: boolean;
  baseObjSeq?: number;
  stepSeq?: number;
}

// spath format
// workflows[sequence=10].steps[sequence=20]
// transactions[sequence=10]
// signatures[sequence=10]
// todos[sequence=10]

export interface IActionController {
  readActionDetail(boardId: string, spath: MxSPath, opt?: IReadActionParam): Promise<IActionObjectViewModel>;

  addAction(boardId: string, model: IActionObjectViewModel, amWorkflow?: IWorkflowViewModel, opt?: IAddActionParam): Promise<IActionObjectViewModel>;

  addActionToNewWorkspace(model: IActionObjectViewModel, boardInfo: ICreateBoardParam, amWorkflow?: IWorkflowViewModel): Promise<string>;

  addActionToMockWorkspace(model: IActionObjectViewModel, mockBoardId: string, amWorkflow?: IWorkflowViewModel): Promise<string>;

  updateAction(boardId: string, spath: MxSPath, model: IActionObjectViewModel, amWorkflowEntity?: WorkflowEntity, opt?: IUpdateActionParam): Promise<void>;

  deleteAction(boardId: string, spath: MxSPath): Promise<void>;

  reopenAction(boardId: string, spath: MxSPath, generateReopenEvent?: boolean, status?: TransactionStatus|BoardSignatureStatus): Promise<void>;

  resetActionStatus(boardId: string, spath: MxSPath, steps?: Array<{sequence?: number, status?: TransactionStepStatus}>): Promise<void>;

  resendActionReminder(boardId: string, param: IResendActionReminderParam): Promise<void>;

  // for flow template case, create action object in tmp board
  actionObjToWorkflowStep(actionModel: IActionObjectViewModel, param?: IActionObjToWorkflowStepParam): Promise<IWorkflowStepViewModel>;

  workflowStepToActionObject(stepModel: IWorkflowStepViewModel, flowModel?: IWorkflowViewModel): Promise<IActionObjectViewModel>;

  readAvailableAssignees(boardId: string): Promise<IBoardAssigneeViewModel[]>;

  createAMWorkflow (boardId: string, transactionSequence: number, amWorkflow: IWorkflowViewModel);
}