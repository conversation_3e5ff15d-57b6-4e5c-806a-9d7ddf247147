import { FormElementAllType, FormElementId } from '@model/form/defines/shared'
import { ElementConditionViewModel } from '@model/form/defines/condition'

interface FormElementBaseModel {
  id: FormElementId;
  type: FormElementAllType;
  /**
   * for integration or other Expansion case
   */
  subType?: string;
  name: string;
  condition?: ElementConditionViewModel;
  label: string;
  hideLabel: boolean;
  defaultLabel: string;
}

const enum FormErrorType {
  Required = 'required',
  InvalidTime = 'invalidTime',
  Limit = 'limit',
  Precision = 'precision',
  AnswerLimit = 'answerLimit',
  PhoneNumber = 'phoneNumber',
  MaxLimit = 'maxLimit',
  MinLimit = 'minLimit',
}

interface FormElementError {
  field: string;
  errorType: FormErrorType;
  params?: Record<string, any>;
}

/**
 * for UI logic
 * if you want to use language resource, can set '$t('currency')' as value
 **/

interface FormElementBaseUIProps {
  isVisible: boolean;
  errors: FormElementError[];
}

/**
 * The recorded attributes are ultimately stored in the fieldSpecific property
 */
const FormElementBaseProps = ['id', 'type', 'value', 'defaultValue']

export interface FormElementCustomModel {
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  fontSize?: string;
  letterSpacing?: string;
  numberOfCells?: string;
}
export type OptionLabel = string
export type OptionValue = string
export interface FormElementSelectionOption extends Partial<FormElementCustomModel> {
  label?: OptionLabel;
  value: OptionValue;
  pdfFormFieldId?: string;
  exportValue?: string;
}

interface FormElementInputBaseModel<T> extends FormElementBaseModel {
  value: T;
  defaultValue: T;
  pdfFormFieldId?: string;
  label: string;
  hideLabel: boolean;
  supporting: string;
  required: boolean;
  placeholder: string;
  isProtected: boolean;
  readonly: boolean;
  defaultLabel: string;
  defaultPlaceholder?: string;
  errors?: FormElementError[];
  customData?: FormElementCustomModel;
  uniqueName: string;
}


const FormElementSpecialKeys = [
  'lockCountry',
  'showAddressLineTwo',
  'showCity',
  'condition',
  'showState',
  'showZipcode',
  'showCountry',
  'hideLabel',
  'required',
  'size',
  'maxLength',
  'minLength',
  'precision',
  'placeholder',
  'supporting',
  'readonly',
  'currentDate',
  'format',
  'isProtected',
  'formats',
  'currentTime',
  'timeFormat',
  'dateFormat',
  'withTime',
  'withDate',
  'options',
  'isMoxoStep',
  'defaultFromProfile',
  'disableAutoFill',
  'maxFileSize',
  'maxFileCount',
  'fileAccept',
  'labelAlignment',
  'imageAlignment',
  'imageName',
  'imageUUID',
  'imageWidth',
  'enableImage',
  'imageAlt',
  'rowHeight',
  'layout',
  'amountField',
  'amountTable',
  'currencyCode',
  'defaultCountry',
  'showMiddleName',
  'enableDefaultValue',
  'showPrefix',
  'prefixOptions',
  'showSuffix'
]

export type { FormElementBaseModel, FormElementInputBaseModel, FormElementBaseUIProps, FormElementError }
export {
  FormElementBaseProps, FormElementSpecialKeys, FormErrorType
}