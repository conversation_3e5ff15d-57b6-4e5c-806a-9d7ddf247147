
import { Defines } from 'isdk';
import { BoardTransaction, ObjectFeedType, TransactionStatus, WorkflowStepType } from 'isdk/src/api/defines';
import { IWorkflowStepViewModel } from '../../defines/workflowStepViewModel';
import { WorkflowStepTransaction } from './workflowStepTransaction';


export class WorkflowStepApproval extends WorkflowStepTransaction {

    static create(model_?: IWorkflowStepViewModel): WorkflowStepApproval {
        return new WorkflowStepApproval(model_)
    }

    private constructor(data: IWorkflowStepViewModel = {}) {
        data.type = WorkflowStepType.WORKFLOW_STEP_TYPE_APPROVAL
        super(data);
    }

    prepareBaseObject(): BoardTransaction {
        const actions = [
            {
                id: 'Button1',
                text: 'Decline',
                payload: 'this is payload field',
                type: 'ACTION_TYPE_DECLINE',
                feed_msg: 'Declined'
            },
            {
                id: 'Button2',
                text: 'Approve',
                payload: 'this is payload field',
                type: 'ACTION_TYPE_APPROVE',
                feed_msg: 'Approved',
                style: 'branding'
            }
        ]
        const transaction: BoardTransaction = {
            is_template: true,
            type: Defines.TransactionType.TRANSACTION_TYPE_APPROVAL,
            title: this._model.title,
            is_active: true,
            expiration_date: this.model.dueDate,
            status: Defines.TransactionStatus.TRANSACTION_STATUS_ACTIVE,
            steps: this.subSteps.map(ss => {
                return {
                    action_style: Defines.TransactionActionStyle.ACTION_STYLE_BUTTON,
                    actions: JSON.stringify(actions),
                    status: Defines.TransactionStepStatus.STEP_STATUS_INITIAL,
                    assignee: this.computeSubStepAssignee(ss),
                    order_number: String(ss.orderNumber || '0'),
                    step_group: ''
                }
            })
        }

        return transaction
    }

    stepCompletedFeedType (): ObjectFeedType{
        return ObjectFeedType.FEED_TRANSACTION_UPDATE;
    }

    stepCompletedStatus (): string{
        return TransactionStatus.TRANSACTION_STATUS_COMPLETED;
    }

    stepCompletedStatusObject (): Record<string, any>{
        return {
            transactions:[
                {
                    status:Defines.TransactionStatus.TRANSACTION_STATUS_COMPLETED
                }
            ]
        }
    }
}

export function createWorkflowStepApproval(model_?: IWorkflowStepViewModel): WorkflowStepApproval {
    return WorkflowStepApproval.create(model_)
}