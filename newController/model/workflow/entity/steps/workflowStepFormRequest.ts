
import { Defines } from 'isdk';
import { BoardTransaction, ObjectFeedType, WorkflowStepType } from 'isdk/src/api/defines';
import { IWorkflowStepViewModel } from '../../defines/workflowStepViewModel';
import { WorkflowStepTransaction } from './workflowStepTransaction';


export class WorkflowStepFormRequest extends WorkflowStepTransaction {

    static create(model_?: IWorkflowStepViewModel): WorkflowStepFormRequest {
        return new WorkflowStepFormRequest(model_)
    }

    private constructor(data: IWorkflowStepViewModel = {}) {
        data.type = WorkflowStepType.WORKFLOW_STEP_TYPE_FORM_REQUEST
        super(data);
    }

    prepareBaseObject(): BoardTransaction {
        throw new Error('Method not implemented.');
    }

    stepCompletedFeedType (): ObjectFeedType{
        return ObjectFeedType.FEED_TRANSACTION_FORM_CONVERTED;
    }

    stepCompletedStatus (): string{
        return null;
    }

    stepCompletedStatusObject (): Record<string, any>{
        return null;
    }
}

export function createWorkflowStepFormRequest(model_?: IWorkflowStepViewModel): WorkflowStepFormRequest {
    return WorkflowStepFormRequest.create(model_)
}