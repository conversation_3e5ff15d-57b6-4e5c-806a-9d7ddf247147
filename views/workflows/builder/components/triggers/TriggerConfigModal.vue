<template>
  <BaseModal
    :visible="true"
    class="mobile-fullscreen-dialog trigger-selector"
    @close="$emit('close')"
  >
    <template slot="title">
      <div class="flex items-center">
        <i
          v-if="showBack"
          tabindex="0"
          class="micon-accordion-left-centered back-icon"
          @click="handleBack"
        />
        <i class="micon-trigger top-icon" />
        <div class="mx-ellipsis">
          {{ modalTitle }}
        </div>
      </div>
    </template>
    <template slot="content">
      <TriggerTypeSelectPane
        v-show="currentPane === viewPanes.TYPE_SELECT"
        v-model="triggerType"
        @next="handleSelectNext"
      />
      <TriggerAppSetupPane
        v-if="currentPane === viewPanes.APP_SETUP || hasAppSetupPaneLoaded"
        v-show="currentPane === viewPanes.APP_SETUP"
        :isEditMode="isEditMode"
        @next="handleSetupNext"
      />
      <IntegrationFormView
        v-if="showFormView"
        v-show="currentPane === viewPanes.APP_FORM"
        ref="formRef"
        class="integration-form-view"
        :integration-options="integrationOptions"
        :hideBackButton="true"
        @success="handleFormNext"
      />
    </template>
  </BaseModal>
</template>

<script>
import TriggerTypeSelectPane from './TriggerTypeSelectPane.vue'
import TriggerAppSetupPane from './TriggerAppSetupPane.vue'
import { useAppTriggerBuilderStore } from '@views/stores/appTriggerBuilder'
import { mapActions, mapState } from 'pinia'
import { popupFactory } from '@views/common/useComponent'
import TriggerMappingDialog from './TriggerMappingDialog.vue'
import FlowWebhookConfigDialog from '../FlowWebhookConfigDialog.vue'
import IntegrationFormView from '@views/integrations/integrationCenter/views/IntegrationFormView.vue'

export default {
  name: 'TriggerConfigModal',
  components: {
    IntegrationFormView,
    TriggerTypeSelectPane,
    TriggerAppSetupPane,
  },
  props: {
    isEditMode: {
      type: Boolean,
      default: false
    },
    triggerConfig: {
      type: Object,
      default: {}
    },
    templateId: {
      type: String,
      default: ''
    },
    roles: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      triggerType: 'webhook', //only meaningful when selecting type
      currentPane: this.isEditMode ? 'appSetup' : 'typeSelect',
      connector: null,

      //MVB-42363: if the form is empty, need to skip the form pane entirely and go directly to the mapping config;
      //  the only way to know whether the form is empty or not is to silently build the form behind the setup pane
      hasAppSetupPaneLoaded: this.isEditMode,
      shouldSkipForm: false,

      viewPanes: Object.freeze({
        TYPE_SELECT: 'typeSelect',
        APP_SETUP: 'appSetup',
        APP_FORM: 'appForm'
      })
    }
  },
  computed: {
    ...mapState(useAppTriggerBuilderStore, ['action', 'trigger']),
    modalTitle() {
      return this.isEditMode ? this.$t('Edit_Trigger') : this.$t('Add_New_Trigger')
    },
    showFormView(){
      return this.currentPane === this.viewPanes.APP_FORM && this.integrationOptions.actionKey
    },
    integrationOptions(){
       const { auth_id, data } = this.action
      const formValue = {}
      data.user_input?.forEach(item =>{
        formValue[item.key] = item.value
      })
      return {
        actionKey: data?.action_key,
        enableDDR: true,
        //todo: for edit case need get form values from action steps.0.actions.0
        formDefaultValues:formValue,
        appId: data?.app_id,
        authId: auth_id,
        // assignees: transformIntegrationAssignees(this.basicBoardInfo?.assignee),
        // title: this.basicBoardInfo?.title,
        // description: this.basicBoardInfo?.description
      }
    },
    showBack() {
      return (
        this.currentPane !== this.viewPanes.TYPE_SELECT &&
        !(this.isEditMode && this.currentPane === this.viewPanes.APP_SETUP)
      )
    }
  },
  watch: {
    action(nVal) {
      const { auth_id, data } = nVal || {}
      const { app_id } = data || {}
      if (auth_id && app_id && data?.action_key) {
        this.connector = {
          app_id,
          auth_id
        }
      }
    },
    connector(nVal, oVal) {
      //needed to prevent a race condition: if the connector changes, the form pane will proceed to rebuild; but
      //  updateFormPages emit may take some time
      if (nVal?.auth_id !== oVal?.auth_id) this.shouldSkipForm = false
    },
    currentPane(nVal) {
      if (nVal === this.viewPanes.APP_SETUP) this.hasAppSetupPaneLoaded = true
    }
  },
  mounted() {
    this.initTriggerStore(this.triggerConfig, this.$t)
  },
  methods: {
    ...mapActions(useAppTriggerBuilderStore, [
      'initTriggerStore',
      'updateTriggerAction',
      'setTriggerVariables',
      'clearTriggerError'
    ]),
    handleBack() {
      switch (this.currentPane) {
        case this.viewPanes.APP_SETUP:
          this.currentPane = this.viewPanes.TYPE_SELECT
          break
        case this.viewPanes.APP_FORM:
          if (!this.$refs.formRef.goBack()) {
            this.currentPane = this.viewPanes.APP_SETUP
          }
          break
        default:
          break
      }
    },
    handleSelectNext() {
      if (this.triggerType === 'webhook') {
        this.openWebhookConfigDialog()
      } else if (this.triggerType === 'app') {
        this.currentPane = this.viewPanes.APP_SETUP
      }
    },
    handleSetupNext() {
      if (this.shouldSkipForm) {
        //no form -> skip directly to mapping
        this.openTriggerMappingDialog()
      } else {
        this.currentPane = this.viewPanes.APP_FORM
      }
    },
    handleFormNext(formResult) {
      const { formArray, invokedData } = formResult
        this.updateTriggerAction({
          data: {
            user_input: formArray
          }
        })
        //no need to pass anything to mapping dialog; all relevant data should be stored inside the builder store
        this.setTriggerVariables(invokedData?.variables)
        this.openTriggerMappingDialog() //delay close until after this finishes, then close together

    },
    handleFormPagesUpdate(hasFormPages) {
      this.shouldSkipForm = !hasFormPages

      //edge case: build empty form takes longer than time spent on setup pane; should basically never happen
      if (this.shouldSkipForm && this.currentPane === this.viewPanes.APP_FORM) {
        this.openTriggerMappingDialog()
        this.currentPane = this.viewPanes.APP_SETUP
      }
    },
    openWebhookConfigDialog() {
      const that = this
      const useConfigDialog = popupFactory(FlowWebhookConfigDialog)
      const [showWebhookConfigDialog] = useConfigDialog(
        {
          change(config) {
            that.$emit('change', config) //pass back to parent view
            that.$emit('close')
          }
        },
        () => {
          return {
            //will only be used for creating a new webhook config
            webhookConfig: {},
            templateId: this.templateId,
            roles: this.roles,
            editable: true //no need for workspaceVariablesViewOnly
          }
        }
      )
      showWebhookConfigDialog()
    },
    openTriggerMappingDialog() {
      this.clearTriggerError() //reset trigger error when enter mapping page from trigger app selector dialog

      const that = this
      const useConfigDialog = popupFactory(TriggerMappingDialog)
      const [showTriggerMappingDialog] = useConfigDialog(
        {
          change: (config) => {
            that.$emit('change', config) //pass back to parent view
            that.$emit('close')
          }
        },
        {
          triggerConfig: this.trigger,
          templateId: this.templateId,
          roles: this.roles,
          editable: true, //no need for workspaceVariablesViewOnly
          doNotInit: true //this modal has already set up the store data, so do not reset it
        }
      )
      showTriggerMappingDialog()
    }
  }
}
</script>

<style scoped lang="scss">
.trigger-selector {
  ::v-deep .el-dialog {
    width: 540px;

    .el-dialog__body {
      padding: 0px;
    }
  }
}

.back-icon {
  color: $mx-color-var-label-secondary;
  font-size: 16px;
  margin-right: 8px;

  cursor: pointer;
}
.integration-fill-form{
  min-height: 400px;
  ::v-deep{
    .integration-form-action{
      .el-button{
        width: 100%;
      }
    }
  }
}
.top-icon {
  color: #8a8a8a;
  border: 1px dashed #8a8a8a;
  padding: 3px;
  border-radius: 3px;
  background-color: #f4f4f4;
  font-size: 16px;
  margin-right: 8px;
}
</style>
