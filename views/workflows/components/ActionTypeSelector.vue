<template>
  <MxDialog
    class="template-selector"
    @close="$emit('close')">
    <template slot="title">
      <div class="custom-dialog-title with-icon">
        <span>{{ $t('add_new_step') }}</span>
      </div>
    </template>
    <div class="type-selector-wrap">
      <div class="selector-header">
        <h3 class="mx-text-b1">
          {{ $t('select_action_type') }}
        </h3>
        <p class="mx-text-c2">
          <span class="temp-text">
            {{$t('please_select_action_type')}}
          </span>
        </p>
      </div>
      <div class="selector-body">
        <FlowActionButton
          v-for="item in funcActions"
          :key="item.type"
          :type="item.type"
          :subType="item.subType"
          :flip="false"
          :icon-size="24"
          :label="item.label"
          @click="onAddAction(item)" />
      </div>

      <div
        v-if="layoutActions.length && !fromGlobal"
        class="selector-body">
        <div class="mx-text-c4 mx-color-secondary mx-margin-left-sm category">
          {{ $t('layout') }}
        </div>

        <FlowActionButton
          v-for="item in layoutActions"
          :key="item.type"
          :flip="false"
          :icon-size="24"
          :type="item.type"
          :label="item.label"
          :disabled="isAddToFlowAB"
          @click="onAddAction(item)">
          <span
            v-if="isAddToFlowAB"
            slot="rightInfoContent">
            {{ $t('milestones_cannot_be_added_in_ab_tip') }}
          </span>
        </FlowActionButton>
      </div>
      <div v-if="supportControls.length" class="selector-body">
        <div class="mx-text-c4 mx-color-secondary mx-margin-left-sm category">
          {{ $t('Control') }}
        </div>
        <FlowActionButton
          v-for="(item, index) in supportControls"
          :key="item.type"
          :item="item"
          :flip="false"
          :icon-size="24"
          :type="item.type"
          :label="item.label"
          @click="onAddAction(item)" />
      </div>

      <div v-if="enableMoreIntegrationEndpoint"
           class="selector-body integration-actions">
        <div class="mx-text-c4 mx-color-secondary mx-margin-left-sm category">
          {{ $t('integrations') }}
        </div>

        <template v-if="supportIntegrationActions.length">
          <FlowActionButton
            v-for="item in supportIntegrationActions"
            :key="item.app_id"
            :flip="false"
            :icon-size="24"
            :type="item.type"
            :label="item.label"
            :subType="item.subType"
            :customIcon="cumpCustomIcon(item)"
            @click="onAddAction(item)" />
        </template>
        <div class="add-more-integrations"
             @click="handleGoToMarketPlace">
          <i class="micon-Integration" />
          <p>{{ $t('more_integrations') }}</p>
          <span class="arrow">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="10"
              viewBox="0 0 12 10"
              fill="none">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.66699 9.49463L5.46387 8.28467L7.9248 5.84424H0.835938V4.15576H7.9248L5.46387 1.7085L6.66699 0.505371L11.165 4.99658L6.66699 9.49463Z"
                fill="#616161" />
            </svg>
          </span>
        </div>
        <IntegrationAppsTips />
      </div>
    </div>
  </MxDialog>
</template>

<script>
import MxDialog from '../../contentLibrary/component/MxDialog';
import {getSupportActionsWithPermission} from '@views/workflows/utils/getSupportActionsWithPermission';
//import FlowActionButton from '@views/binderDetail/src/workflow/FlowActionButton.vue';
import FlowActionButton from '@views/workflows/components/FlowActionButton.vue';
import {mapState, mapGetters} from 'vuex'
//import {BrowserUtils} from "@commonUtils";
import { mapState as piniaMapState } from 'pinia'
import {useIntegrationCenterStore} from '@views/stores/integrationCenter'
import { goToMarketPlace } from '@views/integrations/integrationCenter/common/popup'
import IntegrationAppsTips from '@views/integrations/integrationCenter/components/IntegrationAppsTips'
import {Defines, MxConsts} from '@commonUtils'
const ActionObjectType = MxConsts.ActionObjectType
const FlowStepType = Defines.WorkflowStepType
const FlowStepTypeMap = {
  [ActionObjectType.Approval]: FlowStepType.WORKFLOW_STEP_TYPE_APPROVAL,
  [ActionObjectType.Acknowledgement]: FlowStepType.WORKFLOW_STEP_TYPE_ACKNOWLEDGE,
  [ActionObjectType.ESign]: FlowStepType.WORKFLOW_STEP_TYPE_SIGNATURE,
  [ActionObjectType.FileRequest]: FlowStepType.WORKFLOW_STEP_TYPE_FILE_REQUEST,
  [ActionObjectType.FormRequest]: FlowStepType.WORKFLOW_STEP_TYPE_FORM_REQUEST,
  [ActionObjectType.PDFForm]: FlowStepType.WORKFLOW_STEP_TYPE_PDF_FORM,
  [ActionObjectType.MeetRequest]: FlowStepType.WORKFLOW_STEP_TYPE_MEET_REQUEST,
  [ActionObjectType.LaunchWebApp]: FlowStepType.WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP,
  [ActionObjectType.Integration]: FlowStepType.WORKFLOW_STEP_TYPE_INTEGRATION,
  [ActionObjectType.Jumio]: FlowStepType.WORKFLOW_STEP_TYPE_INTEGRATION,
  [ActionObjectType.DocuSign]: FlowStepType.WORKFLOW_STEP_TYPE_DOCUSIGN,

  [ActionObjectType.TodoTransaction]: FlowStepType.WORKFLOW_STEP_TYPE_TODO_TRANSACTION,
  [ActionObjectType.Todo]: FlowStepType.WORKFLOW_STEP_TYPE_TODO,
}

export default {
  name: 'ActionTypeSelector',
  components: {MxDialog, FlowActionButton, IntegrationAppsTips},
  props: {
    isAddToFlowAB: {
      type: Boolean,
      default: false
    },
    fromGlobal: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      actions: []
    }
  },
  emits: ['create', 'select'],
  computed: {
    ...mapGetters('user', ['currentUser']),
    ...mapGetters('group', ['enabledBasicActions']),
    ...piniaMapState(useIntegrationCenterStore, ['supportActions', 'enableMoreIntegrationEndpoint']),
    layoutActions () {
      return this.actions.filter(item => item.isLayout)
    },
    funcActions () {
      const enabledActions = this.enabledBasicActions
      return this.actions.filter(item => {
        if(item.isLayout || item.isControl) {
          return false
        }
        //workaround: only control the action permission for global new button, otherwise follow the previous logic, show basic action as is
        if(this.fromGlobal) {
          return enabledActions.findIndex(action => item.type === FlowStepTypeMap[action]) >= 0
        }
        return true
      })
    },
    cumpCustomIcon () {
      return (item) =>{
        return item?.app_id && item?.subType?.toLowerCase() === 'integration' ? item.icon : ''
      }
    },
    supportIntegrationActions () {
      return this.supportActions.filter(item => item.subType !== 'marketPlace')
    },
    supportControls (){
      if(this.fromGlobal) {
        //MVB-37645: new frame org not show automation control
        return []
      }
      const controls = ['WORKFLOW_STEP_TYPE_AUTOMATION', 'WORKFLOW_STEP_TYPE_AWAIT']
      return this.actions.filter(item => controls.includes(item.type))
    }
  },
  methods: {
    onAddAction (item = {}) {
      let integrationApp = {}
      if(item?.subType?.toLowerCase() === 'integration'){
        integrationApp = {
          app_id: item.app_id,
          auth_id: item.auth_id,
          app_name: item.label || item.app_name,
          subType: item.subType,
        }
      }

      this.$emit('select', item.type, item.subType, integrationApp)
      this.$emit('close')
    },
    handleGoToMarketPlace () {
      const props = { fromScene: 'newAction' }
      goToMarketPlace(this.onAddAction, props)
    },
  },
  created () {
    const originalActions = getSupportActionsWithPermission()
    // this.actions = originalActions.filter(action => !action.isControl)
    this.actions = originalActions
  }
}
</script>

<style scoped lang="scss">
.type-selector-wrap {
  height: 450px;
  overflow-y: auto;
  .category{
    margin-bottom: 8px;
  }
  .selector-body {
    padding: 0px 28px 10px;
    .add-more-integrations{
      display: flex;
      align-items: center;
      height: 44px;
      margin-top: -3px;
      margin-bottom: 8px;
      padding: 10px 24px 10px 0px;
      cursor: pointer;

      i {
        color: $mx-color-var-text-secondary;
        margin-left: 12px;
        font-size: 24px;
        color: $mx-color-var-text-secondary;
      }
      p {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: $mx-color-var-text-primary;
        margin: 0px 0px 0px 10px;
      }
      .arrow {
        display: none;
        margin-left: auto;
      }
      &:hover {
        background-color: #e0e0e0;
        border-radius: 6px;
        .arrow {
          display: block;
        }
      }
    }
    &.integration-actions {
      padding: 0px 28px 32px;
    }
  }

  .selector-header {
    padding: 23px 28px 17px;

    .mx-text-b1 {
      font-size: 19px;
      line-height: 28px;
      margin-top: 0;
      margin-bottom: 2px;
    }

    .mx-text-c2 {
      margin-bottom: 0;
      font-weight: 400;
      color: $mx-color-var-text-secondary;
    }
  }

  ::v-deep {
    .vertical {
      padding-left: 10px;
    }

    [role=button] img {
      filter: none !important;
    }
  }
}

</style>
