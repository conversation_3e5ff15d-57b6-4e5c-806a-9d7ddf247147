<template>
  <ElForm label-position="top">
    <ElFormItem
      v-for="variable in localVariables"
      :class="{ 'variables-text-input': variable.workspaceVariableOptions.type === EWorkspaceVariableType.TEXT }"
      :key="variable.sequence"
      :label="variable.label">
      <ElSelect
        v-if="variable.workspaceVariableOptions.type === EWorkspaceVariableType.LIST"
        v-model="variable.string_value"
        :placeholder="variable.workspaceVariableOptions.placeholder"
        clearable>
        <ElOption
          v-for="(option, index) in variable.workspaceVariableOptions.options"
          :key="index"
          :value="option"
          :label="option" />
      </ElSelect>

      <ElInput
        v-else
        v-model="variable.string_value"
        type="textarea"
        :maxlength="300"
        :placeholder="variable.workspaceVariableOptions.placeholder"
        :autosize="{ minRows: 1 }" />
    </ElFormItem>
  </ElForm>
</template>

<script>
import { ObjectUtils } from '@commonUtils/object'
import { EWorkspaceVariableType } from '@model/workflow'

export default {
  name: 'WorkspaceVariableEditValuesForm',
  props: {
    variables: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      EWorkspaceVariableType,
      localVariables: []
    }
  },
  watch: {
    localVariables: {
      handler (newValue) {
        this.$emit('updated', this.localVariables)
      },
      deep: true
    }
  },
  created () {
    this.localVariables = ObjectUtils.cloneDeep(this.variables).map(variable => {
      if (variable.string_value === undefined) {
        variable.string_value = ''
      }
      return variable
    })
  }
}
</script>

<style scoped lang="scss">
::v-deep section {
  padding-left: 24px;
  padding-right: 24px;
}

.variables-text-input {
  ::v-deep .el-form-item__content {
    line-height: initial;
  }
}

.basic-info-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0px 8px;

  .el-form-variable {
    margin-top: 0;
    margin-bottom: 0;
  }

  ::v-deep .el-form-item__content {
    .el-form-item__error {
      line-height: 20px;
      font-size: 14px;
      position: static;
      margin: 1px 0px 5px 1px;
      padding: 0px;
    }
  }

  ::v-deep .floating-form-group {
    min-height: 44px;
    max-height: 44px;
  }

  ::v-deep .el-select {
    display: block;
    .el-input__inner {
      height: 44px;
    }
  }
}

.mx-panel-info {
  margin: 32px 0 16px;
  width: 328px;
  height: 20px;
  color: $mx-color-var-label-secondary;
  line-height: 20px;
}

.error-tips {
  display: flex;
  gap: 4px;
  line-height: 20px;
  margin: 5px 8px 0px 4px;

  .icon {
    color: rgba(178, 36, 36, 1);
  }

  .info {
    color: rgba(178, 36, 36, 1);
    font-size: 14px;
    line-height: 20px;
    text-align: left;
  }
}

.property-options-wrapper {
  display: flex;
  flex-direction: column;
  align-items: start;
  margin-top: 14px;

  .options-label-wrapper {
    width: 100%;
    padding: 3px 0px 9px 8px;

    .options-text {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: rgba(0, 0, 0, 1);
      text-align: left;
      margin-right: 8px;
    }

    .options-count {
      font-size: 14px;
      line-height: 20px;
      color: $mx-color-var-label-secondary;
      text-align: left;
    }
  }
}
</style>
