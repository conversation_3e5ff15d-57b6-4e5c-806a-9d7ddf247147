<template>
  <div class="step-addition-opts" v-if="showPanel">
    <div
      role="button"
      :class="{rotate: showOptions}"
      class="toggle mx-clickable mx-branding-text-action mx-text-c1"
      @click="showOptions = !showOptions">
      {{ $t('Additional_options') }}
      <i class="micon-mep-arrow-down"/>
    </div>
    <div v-show="showOptions" class="step-addition-content">
      <AdditionalOptionsItem
        v-if="showSequentialOrder"
        v-model="localValue.skipSequentialOrder"
        :label="$t('Skip_Sequential_Order')"
        :tooltip="$t('Skip_Sequential_Order_info')"
        :disabled="disableSequentialOrder"
        @change="onChange">
      </AdditionalOptionsItem>

      <AdditionalOptionsItem
        v-if="showAllowDecline"
        v-model="localValue.allowDecline"
        :label="$t('Allow_Decline_Option')"
        :tooltip="$t('Allow_Decline_Option_info')"
        :disabled="disableAllowDecline"
        @change="onChange">
      </AdditionalOptionsItem>

      <AdditionalOptionsItem
        v-if="showEnableReview"
        v-model="localValue.reviewRequired"
        :label="$t('Require_File_Review')"
        :tooltip="$t('Require_File_Review_info')"
        @change="onChange">
      </AdditionalOptionsItem>
      <AdditionalOptionsItem
        v-if="showCustomFolder"
        v-model="localValue.hasCustomFolder"
        :label="customFolderLabel || $t('Upload_file_to_specific_folder')"
        :tooltip="customFolderTooltip || $t('Require_Custom_Folder_info')"
        @change="onChange">
        <ElInput
          :placeholder="$t('folder_name_placeholder')"
          :data-ta="'folder_name'"
          v-model="localValue.customFolderName"
          :maxlength="128"
          @input="onChange"/>
      </AdditionalOptionsItem>
      <AdditionalOptionsItem
        v-if="showForceRead"
        :disabled="disableForceRead"
        v-model="localValue.forceRead"
        :label="$t('Require_Attachment_Review')"
        :tooltip="$t('Require_Attachment_Review_info')"
        @change="onChange">
      </AdditionalOptionsItem>
      <AdditionalOptionsItem
        v-if="showPreparation"
        :disabled="disablePreparation"
        v-model="localValue.enablePreparation"
        :label="$t('Require_Runtime_Editing_to_Get_Action_Ready')"
        :tooltip="$t('Require_Runtime_Editing_to_Get_Action_Ready_tooltip')"
        @change="onChange"></AdditionalOptionsItem>
    </div>
  </div>
</template>

<script>
import AdditionalOptionsItem from '@views/requests/components/AdditionalOptionsItem.vue';
import { useDDRSupportStore } from '@views/stores/ddrSupport'
import {ObjectUtils} from '@commonUtils';

const supportedItems = ['hasCustomFolder', 'reviewRequired', 'skipSequentialOrder', 'enablePreparation', 'forceRead', 'allowDecline']
const showItems = ['showSequentialOrder', 'showCustomFolder', 'showEnableReview', 'showPreparation', 'showForceRead', 'showAllowDecline'];
export default {
  name: 'StepAdditionalOptions',
  components: {
    AdditionalOptionsItem,
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    showSequentialOrder: {
      type: Boolean,
      default: true
    },
    showAllowDecline: {
      type: Boolean,
      default: false
    },
    showCustomFolder: {
      type: Boolean,
      default: false
    },
    showEnableReview: {
      type: Boolean,
      default: false
    },
    //disable sequential options
    disableSequentialOrder: {
      type: Boolean,
      default: false
    },
    disableAllowDecline: {
      type: Boolean,
      default: false
    },
    reviewRequired: {
      type: Boolean,
      default: true
    },
    showPreparation: {
      type: Boolean,
      default: false
    },
    disablePreparation: {
      type: Boolean,
      default: false
    },
    showForceRead: {
      type: Boolean,
      default: false
    },
    disableForceRead: {
      type: Boolean,
      default: false
    },
    customFolderLabel: {
      type: String,
      default: ''
    },
    customFolderTooltip: {
      type: String,
      default: ''
    },

  },
  data () {
    return {
      showOptions: false,
      localValue: {...this.value}
    }
  },
  watch: {
    value: {
      handler (v) {
        Object.keys(v).forEach(key => {
          this.localValue[key] = v[key]
        })
      },
      deep: true
    },
    'localValue.skipSequentialOrder' (val) {
      this.onSkipSequentialOrderChange(val)
    }
  },
  mounted () {
    //set default, do we need it?
    supportedItems.forEach(key => {
      if (!ObjectUtils.isDefine(this.localValue[key])) {
        this.localValue[key] = false
      }
    })
    
    // If has option enabled, better to show the panel by default to notify user
    this.initShowOptionsPanel()
  },
  computed: {
    showPanel () {
      return !!showItems.find(key => {
        if (this[key]) {
          return true
        }
        return false
      })
    }
  },
  methods: {
    initShowOptionsPanel () {
      const optionEnableStatus = this.localValue
      this.showOptions = (this.showSequentialOrder && optionEnableStatus.skipSequentialOrder)
        || (this.showEnableReview && optionEnableStatus.reviewRequired)
        || (this.showCustomFolder && optionEnableStatus.hasCustomFolder)
        || (this.showForceRead && optionEnableStatus.forceRead)
        || (this.showPreparation && optionEnableStatus.enablePreparation)
        || (this.showAllowDecline && optionEnableStatus.allowDecline)
    },
    onChange () {
      this.$nextTick(() => {
        this.$emit('input', this.localValue)
        this.$emit('change', this.localValue)
      })
    },
    onSkipSequentialOrderChange (val) {
      useDDRSupportStore().setFilterSkipSequentialOrder(val)
    }
  }
}
</script>

<style scoped lang="scss">
.step-addition-opts {
  margin-top: 22px;

  .step-addition-content {
    padding-top: 17px;
  }

  .toggle {


    i {
      display: inline-block;
      font-size: 14px;
    }

    &.rotate i {
      transform: rotate(180deg) translateX(-7px);
    }
  }

  .option-item-wrap:last-child {
    margin-bottom: 0;
  }
}
</style>
