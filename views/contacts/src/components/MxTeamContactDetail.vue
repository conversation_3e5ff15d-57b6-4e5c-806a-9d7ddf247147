<template>
  <div
    class="mx-team-detail">
    <MxContactDetailEmpty
      v-if="isTeamDeleted"/>
    <template v-else-if="isTeamBasicInfoLoaded">
      <div class="team-member-panel">
        <div class="team-general-info-panel">
          <el-tooltip
            v-if="canEditClientGroup"
            placement="top"
            style="float:right;"
            :content="$t('edit_group_details')"
            popper-class="overflow-control-tooltip">
            <el-button
              icon="micon-edit-xs"
              type="secondary"
              size="small"
              square
              @click="showEditClientGroupDialog"></el-button>
          </el-tooltip>
          <div class="team-avatar">
            <MxUserAvatar
              size="128"
              v-if="isClientTeam"
              :user-avatar="teamBaseObject.avatar"
              :alt="teamBaseObject.name"/>
            <img
              v-else
              :src="teamBaseObject.avatar">
          </div>
          <div class="team-name mx-text-h1" tabindex="0">
            {{ teamBaseObject.name }}
          </div>
          <div class="team-member-counts mx-text-c2" tabindex="0">
            <span v-if="showExtBadge" :style="imageStyle" class="external-flag"/>
            {{ memberCountInfo }}
          </div>
        </div>
        <div
          v-if="teamBaseObject.description"
          class="team-information-panel">
          <div class="information-label mx-text-tiny1">
            {{ $t('Group_information') }}
          </div>
          <div tabindex="0" class="team-description">
            <div class="description-area">
              <div class="description-detail mx-text-c2">
                {{ teamBaseObject.description }}
              </div>
            </div>
          </div>
        </div>
        <div class="team-member-list-panel">
          <div class="information-label mx-text-tiny1">
            <div style="display: flex; align-items: center;">
              <div>{{ membersTitle }}</div>
              <el-tooltip
                v-if="isClientTeam"
                placement="top"
                popper-class="client-group-tooltip"
                :content="$t('Client_groups_are_used_to_group_client_users_together')">
                <i class="micon-mep-info-square font-icon-sm mx-margin-left-xs"/>
              </el-tooltip>
              <el-tooltip
                v-if="isFlexibleTempTeam"
                placement="top"
                popper-class="client-group-tooltip"
                :content="$t('role_groups_desc')">
                <i class="micon-mep-info-square font-icon-sm mx-margin-left-xs"/>
              </el-tooltip>
            </div>
            <el-tooltip
              v-if="canEditClients"
              placement="top"
              :content="$t('Edit_Clients')"
              popper-class="overflow-control-tooltip">
              <i
                class="micon-edit-xs mx-clickable"
                @click="showEditClientsDialog()"/>
            </el-tooltip>
          </div>
          <div
            v-if="isLoading"
            style="padding-left: 50px; margin-top: 20px;">
            <span
              v-mx-spinner="spinnerOption"
              style="display: inline-flex;align-items: center;position: relative;">
              <span
                class="mx-semibold"
                style="color: #8e8e93"> {{ $t('loading') }} </span>
            </span>
          </div>
          <ul
            v-else-if="teamMembers.length > 0"
            v-a11y-list-keyboard="{listLength: teamMembers.length, scrollElement: $el}"
            class="member-list-detail">
            <li
              v-for="(member, index) in teamMembers"
              role="listitem"
              :key="member.id"
              class="member-wrapper"
              @click="member.id!==currentUser.id&&!member.isDisabled?$emit('select', member):null"
              :class="{ 'deactivated': member.isDisabled, 'mx-clickable': !viewOnly && $route.name === 'timeline' && (member.id !== currentUser.id && !member.isDisabled) }">
              <MxUserAvatar
                size="40"
                :lazyload="true"
                :alt="member.name"
                :user-avatar="member.avatar"
                :userId="member.id"
                :displayPresence="true"/>
              <div
                class="member-info"
                :class="{ 'mx-color-secondary': member.isDisabled }">
                <div class="name-title">
                  <div
                    class="member-name mx-text-c2 mx-ellipsis"
                    :title="member.name">
                    <div
                      :style="imageStyle"
                      class="external-flag"
                      v-if="showExtBadge && member.isClient"/>
                    {{ member.name }}
                  </div>
                  <div class="member-title mx-text-c4">
                    <span
                      v-if="member.isMe">{{ $t('You') }}</span>
                    <span
                      v-else-if="member.isDisabled">{{ $t('user_deactivated') }}</span>
                    <span
                      v-else>{{ getMemberTitle(member) }}</span>
                  </div>
                </div>
                <div
                  v-if="member.isManager"
                  class="manager-label">
                  {{ $t('manager') }}
                </div>
                <div class="action"
                     v-if="!viewOnly && (canShowQuickAction(member) || canEditClients) && member.id !== currentUser.id"
                     style="margin-left: 4px" @click.stop>
                  <el-dropdown
                    trigger="click"
                    :popper-append-to-body="false"
                    @command="handleDropdownCommand($event, member, index)">
                    <i class="micon-mep-more mx-clickable" role="button" :aria-label="$t('more_options')" tabindex="0"/>
                    <el-dropdown-menu
                      slot="dropdown">
                      <template
                        v-if="canShowQuickAction(member)">
                        <el-dropdown-item
                          v-if="showChatButton"
                          command="chat">
                          {{ $t('chat') }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="showCallButton"
                          command="call">
                          {{ $t('Call') }}
                        </el-dropdown-item>
                      </template>
                      <el-dropdown-item
                        v-if="canEditClients"
                        command="remove">
                        <span class="danger">{{ $t('remove') }}</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </li>
          </ul>
          <div
            v-else
            :class="{'mx-capitalize': !isClientTeam}"
            style="margin-top: 12px; padding: 12px 24px;">
            {{ isClientTeam ? $t('No_clients') : $t('no_member') }}
          </div>
        </div>
      </div>
      <div
        class="contact-detail-right-container">
        <div
          v-if="isClientTeam && !fromMemberProfile"
          class="group-managers-container">
          <div class="mx-margin-bottom-xs mx-text-tiny1 mx-uppercase managers-label">
            <div class="mx-color-secondary">{{ $t('Group_managers') }}</div>
            <el-tooltip
              v-if="canEditClientGroup"
              placement="top"
              style="float:right;"
              :content="$t('Edit_Managers')"
              popper-class="overflow-control-tooltip">
              <el-button
                icon="micon-edit-xs"
                type="ghost"
                size="mini"
                square
                @click="showEditManager"></el-button>
            </el-tooltip>
          </div>
          <template v-if="teamManagers.length">
            <ul
              v-a11y-list-keyboard="{listLength: displayTeamManagers.length}"
              class="manager-list">
              <li
                v-for="member in displayTeamManagers"
                role="listitem"
                :key="member.id"
                class="manager-wrapper"
                @click="member.id !== currentUser.id?$emit('select', member):null"
                :class="{ 'deactivated': member.isDisabled, 'mx-clickable': !viewOnly && $route.name === 'timeline' && member.id !== currentUser.id }">
                <MxUserAvatar
                  size="28"
                  :lazyload="true"
                  :alt="member.name"
                  :user-avatar="member.avatar"
                  :userId="member.id"
                  :displayPresence="true"/>
                <div
                  class="manager-info mx-padding-left-sm"
                  :class="{ 'mx-color-secondary': member.isDisabled }">
                  <div class="name-title">
                    <div
                      class="manager-name mx-text-c2 mx-ellipsis"
                      :title="member.name">
                      {{ member.name }}
                    </div>
                    <div class="manager-title mx-text-c4 mx-color-secondary">
                    <span
                      v-if="member.isMe">{{ $t('You') }}</span>
                      <span
                        v-else-if="member.isDisabled">{{ $t('user_deactivated') }}</span>
                      <span
                        v-else>{{ getMemberTitle(member) }}</span>
                    </div>
                  </div>
                  <div class="action"
                       v-if="!viewOnly && (showChatButton || showCallButton) && member.id !== currentUser.id && !member.isDisabled"
                       @click.stop>
                    <el-dropdown
                      trigger="click"
                      :popper-append-to-body="false"
                      @command="handleDropdownCommand($event, member)">
                      <i class="micon-mep-more mx-clickable" role="button" :aria-label="$t('more_options')"
                         tabindex="0"/>
                      <el-dropdown-menu
                        slot="dropdown">
                        <el-dropdown-item
                          v-if="showChatButton"
                          command="chat">
                          {{ $t('chat') }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="showCallButton"
                          command="call">
                          {{ $t('Call') }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
              </li>
            </ul>
            <div
              role="button"
              tabindex="0"
              v-if="teamManagers.length > 10"
              @click="toggleManagers"
              @keypress.enter="toggleManagers"
              class="mx-clickable mx-branding-text mx-text-c3">
              {{ showMore ? $t('Show_more') : $t('Show_less') }}
            </div>
          </template>
          <div
            v-else
            class="mx-text-c2 mx-margin-top-sm">
            {{ $t('No_group_managers') }}
          </div>
        </div>
        <SharedBinderPanel
          v-if="!isFlexibleTempTeam"
          :team-id="teamId"
          :viewOnly="viewOnly"
          :hide-action-and-conv-panel="hideActionAndConvPanel"
          class="shared-conversations-panel"/>
      </div>
    </template>
  </div>
</template>

<script>
import MxContactDetailEmpty from './MxContactDetailEmpty'
import {mapGetters, mapActions, mapState} from 'vuex'
import SharedBinderPanel from './SharedBinderPanel'
import utils from '@views/common/utils/utils'
import {getTeamAvatar} from '@views/common/utils/team'
import {Meet} from '@views/providers/eventNames'
import _find from 'lodash/find';
import RemoveClientConfirm from './RemoveClientConfirm'
import {popupFactory} from '@views/common/useComponent'
import extBadge from '@views/theme/src/images/ext_badge_mep.svg'
import EditClients from './EditClients'
import EditClientGroup from '@views/contacts/src/components/EditClientGroup.vue';
import EditClientGroupManager from '@views/contacts/src/components/EditClientGroupManager.vue';

export default {
  name: 'MxTeamContactDetail',
  components: {
    SharedBinderPanel,
    MxContactDetailEmpty
  },
  props: {
    teamId: {
      type: String,
      default: ''
    },
    hideActionAndConvPanel: {
      type: Boolean,
      required: false,
      default: () => false
    },
    viewOnly: {
      type: Boolean,
      default: false
    },
    fromMemberProfile: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      teamMembers: [],
      remoteTeamInfo: {},
      isTeamDeleted: false,
      isTeamBasicInfoLoaded: false,
      isLoading: false,
      spinnerOption: {
        color: '#8e8e93',
        corners: 0.7,
        left: '-20px',
        length: 3,
        lines: 11,
        radius: 5,
        scale: 1,
        top: '10px',
        width: 2
      },
      teamManagers: [],
      displayTeamManagers: [],
      imageStyle: {
        'background-image': 'url(' + extBadge + ')',
        'margin-right': '0',
        'min-width': '16px'
      },
      showMore: true
    }
  },
  computed: {
    ...mapState('user', ['userBoards', 'userRelations']),
    ...mapState('group', ['allTeams']),
    ...mapGetters('group', ['enableClientGroupToInternalUsers']),
    ...mapGetters('contacts', ['suggestedContacts']),
    ...mapGetters('user', ['accessibleTeams', 'currentUser', 'allClientTeams']),
    ...mapGetters('privileges', ['canAccessBusinessDirectory', 'canCreateConversation', 'canStartMeet', 'disableCreateCollaboratorConversation', 'canCreateChatWorkspace']),
    showExtBadge () {
      if (!this.currentUser.isInternalUser) {
        return false
      }
      return this.isClientTeam || (this.isFlexibleTempTeam && this.teamObject.hasClientInFlexibleTeam)
    },
    showCallButton () {
      if (!this.currentUser.isInternalUser) {
        return false
      }
      return this.canStartMeet
    },
    showChatButton () {
      if (!this.currentUser.isInternalUser) {
        return false
      }
      return this.canCreateChatWorkspace && this.canCreateConversation
    },
    teamObject () {
      return this.allTeams.find(team => this.teamId === team.id)
    },
    teamBaseObject () {
      if (Object.keys(this.teamObject).length > 0) {
        return this.teamObject
      } else {
        let targetTeamObj = {}
        for (let team of this.accessibleTeams) {
          if (team.id === this.teamId) {
            targetTeamObj = team
            break
          }
        }
        if (Object.keys(targetTeamObj).length === 0 && Object.keys(this.remoteTeamInfo).length > 0) {
          targetTeamObj = this.remoteTeamInfo
        }
        return targetTeamObj
      }
    },
    isClientTeam () {
      return this.teamObject.type === 'GROUP_TYPE_CLIENT_TEAM'
    },
    isFlexibleTempTeam () {
      return this.teamObject.isTemp && this.teamObject.isFlexibleTeam
    },
    membersTitle () {
      if (this.isClientTeam) {
        return `${this.$t('Clients')} (${this.teamMembers.length})`
      } else {
        const memberCounts = this.teamBaseObject.memberCounts || this.teamBaseObject.totalMembers || 0
        return `${this.$t('Members')} (${memberCounts})`
      }
    },
    memberCountInfo () {
      const memberCounts = this.isClientTeam ? this.teamMembers.length : (this.teamBaseObject.memberCounts || this.teamBaseObject.totalMembers || 0)
      if (memberCounts === 1) {
        return this.isClientTeam ? this.$t('one_Client') : this.$t('members_one')
      } else {
        return this.isClientTeam ? this.$t('number_Clients', {number: memberCounts}) : this.$t('members_other', {count: memberCounts})
      }
    },
    canEditClientGroup () {
      if (!this.enableClientGroupToInternalUsers) {
        return false
      }
      if (this.currentUser.isAdmin) {
        return true
      } else {
        const index = this.teamManagers.findIndex(manager => {
          return (manager.id === this.currentUser.id) && (manager.type === 'GROUP_OWNER_ACCESS')
        })
        return index !== -1
      }
    },
    canEditClients () {
      if (this.viewOnly || !this.isClientTeam) {
        return false
      }
      if (this.currentUser.isAdmin) {
        return true
      } else if (this.allClientTeams.length) {
        return this.allClientTeams.findIndex(team => team.id === this.teamId) > -1
      }
      return false
    }
  },
  watch: {
    teamId: {
      handler () {
        this.showMore = true
        this.teamMembers = []
        this.readTeamMembers()
      },
      immediate: true
    },
    teamBaseObject (newVal, oldVal) {
      if (Object.keys(oldVal).length > 0 && Object.keys(newVal).length === 0) {
        this.$emit('deleted')
      }
    }
  },
  methods: {
    ...mapActions('group', ['readTeamMembersByPagination', 'readTeamInfo', 'getTeamManagers']),
    ...mapActions('contacts', ['filterUsersAndQueryPresence', 'syncBoard']),
    ...mapActions('createConversation', ['createMockConversation']),
    async readTeamMembers () {
      if (this.teamId) {
        this.isLoading = true
        let teams = []
        if (this.isClientTeam) {
          teams = this.allTeams
        } else {
          teams = this.accessibleTeams
        }
        let isTeamDeleted = !teams.find(team => team.id === this.teamId)
        if (this.isClientTeam) {
          this.isTeamBasicInfoLoaded = true
        } else if (this.isFlexibleTempTeam) {
          isTeamDeleted = this.teamObject.isDeleted
          this.isTeamBasicInfoLoaded = true
        } else {
          if (!this.canAccessBusinessDirectory) {
            if (isTeamDeleted) {
              try {
                this.remoteTeamInfo = await this.readTeamInfo(this.teamId)
                this.remoteTeamInfo.avatar = getTeamAvatar(this.teamId)
                this.isTeamBasicInfoLoaded = true
                isTeamDeleted = false
              } catch (e) {
                if (e.isNotFoundError) {
                  isTeamDeleted = true
                }
              }
            } else {
              this.isTeamBasicInfoLoaded = true
            }
          } else {
            this.isTeamBasicInfoLoaded = true
          }
        }
        this.isTeamDeleted = isTeamDeleted
        if (!this.isTeamDeleted) {
          if (this.isClientTeam) {
            this.loadTeamManagers()
          }
          this.loadTeamMembers()
        } else {
          this.isLoading = false
        }
      }
    },
    loadTeamManagers () {
      this.getTeamManagers(this.teamId).then((teamManagers) => {
        this.teamManagers = teamManagers
        this.displayTeamManagers = teamManagers.slice(0, 10)
        this.filterUsersAndQueryPresence(this.displayTeamManagers)
      })
    },
    getMemberTitle (member) {
      if (member.title === member.phoneNumber) {
        return utils.getFormattedPhoneNumber(member.phoneNumber)
      } else {
        return member.title
      }
    },
    _createMockConversation (member) {
      return this.createMockConversation({
        name: member.name,
        member: {
          id: member.id,
          title: member.title,
          avatar: member.avatar,
          isInternalUser: !member.isClient
        }
      })
    },
    startCallWithTeamMember (member) {
      const meetTopic = `${this.currentUser.first_name || ''}+${this.$t('meet_topic', {name: member.first_name || ''})}`
      this._createMockConversation(member).then((binder) => {
        if (binder.isMocked) {
          const params = {
            check_status: true,
            invitees: [member],
            topic: meetTopic,
            showMemberSelector: false,
          }
          this.$bus.$emit(Meet.START_MEET, params)

        } else {
          const params = {
            original_binder_id: binder.id,
            check_status: true,
            invitees: [member],
            topic: meetTopic,
            showMemberSelector: false,
          }
          this.$bus.$emit(Meet.START_MEET, params)
        }
      })
    },
    startChatWithTeamMember (member) {
      this._createMockConversation(member).then((binder) => {
        let name = 'timeline'
        if (binder.isMocked) {
          const existingConversationBinder = this.getConversationBinderFromUserBoard(member.id)
          if (existingConversationBinder) {
            this.$router.push({name: name, params: {id: existingConversationBinder.id}})
          } else {
            this.$router.push({name: name, params: {id: binder.id}})
          }
        } else {
          this.$router.push({name: name, params: {id: binder.id}})
        }
      }).catch(err => {
        const existingConversationBinder = this.getConversationBinderFromUserBoard(member.id)
        if (existingConversationBinder) {
          this.$router.push({name: name, params: {id: existingConversationBinder.id}})
        }
      })
    },
    getConversationBinderFromUserBoard (userId) {
      const currUserId = this.currentUser.id
      return _find(this.userBoards, userBoard => {
        const {is_deleted, is_inactive, isconversation, is_external, boardUsers, ownerInfo} = userBoard
        if (!is_deleted && !is_inactive && isconversation) {
          if (!is_external) {
            // 1-1 conversation between rms
            if (boardUsers.length === 2) {
              let containsCount = 0
              boardUsers.forEach(user => {
                if (user.id === userId || user.id === currUserId) {
                  containsCount++
                }
              })
              if (containsCount === 2) {
                return userBoard
              }
            }
          }
        }
      })
    },
    handleDropdownCommand (event, member, memberIndex) {
      if (event === 'call') {
        this.startCallWithTeamMember(member)
      } else if (event === 'chat') {
        this.startChatWithTeamMember(member)
      } else if (event === 'remove') {
        let [showRemoveClientConfirm] = popupFactory(RemoveClientConfirm)({
          removed: () => {
            this.teamMembers.splice(memberIndex, 1)
          }
        }, {
          member,
          teamId: this.teamId,
          getMemberTitle: this.getMemberTitle,
          title: this.$t('Are_you_sure_you_want_to_remove_this_client_from_team', {teamName: this.teamObject.name})
        })
        showRemoveClientConfirm()
      }
    },
    loadTeamMembers () {
      this.isLoading = true
      let params = {
        teamId: this.teamId
      }
      if (this.isClientTeam) {
        params.filter = {
          isInternal: false,
          isLocal: true
        }
      } else if (this.isFlexibleTempTeam) {
        params.filter = {
          isInternal: true,
          isLocal: true
        }
      }
      this.readTeamMembersByPagination(params).then(teamMembers => {
        this.teamMembers = teamMembers
        this.filterUsersAndQueryPresence(teamMembers)
      }).finally(() => {
        this.isLoading = false
      })
    },
    showEditClientGroupDialog () {
      const [show] = popupFactory(EditClientGroup)({
        updated: () => {

        }
      }, {
        groupInfo: this.teamBaseObject
      })
      show()
    },
    showEditManager () {
      const [showEditManagerDialog] = popupFactory(EditClientGroupManager)({
        updateManagers: () => {
          this.loadTeamManagers()
        }
      }, {
        teamId: this.teamId,
        existedManagers: this.teamManagers
      })
      showEditManagerDialog()
    },
    showEditClientsDialog () {
      let [showEditClientsDialog] = popupFactory(EditClients)({
        updateClients: () => {
          this.loadTeamMembers()
        }
      }, {
        teamId: this.teamId,
        existedClients: this.teamMembers
      })
      showEditClientsDialog()
    },
    toggleManagers () {
      if (this.showMore) {
        this.displayTeamManagers = this.teamManagers
        this.filterUsersAndQueryPresence(this.teamManagers.slice(10))
      } else {
        this.displayTeamManagers = this.teamManagers.slice(0, 10)
      }
      this.showMore = !this.showMore
    },
    showChatAndCall (member) {
      if (member.isClient) {
        return this.userRelations.findIndex(contact => contact.id === member.id) >= 0
      } else {
        return true
      }
    },
    canShowQuickAction (member) {
      if (member.isDisabled) {
        return false
      }
      return this.disableCreateCollaboratorConversation ? this.showChatAndCall(member) : (this.canCreateConversation || this.showCallButton)
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin vertical-mode {
  flex-direction: column;

  .team-member-panel {
    width: 100%;
  }

  .contact-detail-right-container {
    width: 100%;
    max-width: 100%;
  }
}

.mx-team-detail {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .team-member-panel {
    padding: 40px 56px;
    width: 62.5%;

    .team-avatar {
      text-align: center;

      img {
        width: 128px;
        border-radius: 16px;
      }
    }

    .team-name {
      margin-top: 12px;
      text-align: center;
    }

    .team-member-counts {
      text-align: center;
    }
  }

  .team-information-panel {
    margin-top: 31px;

    .information-label {
      border-radius: 6px;
      background-color: $mx-color-var-fill-quaternary;
      height: 40px;
      line-height: 40px;
      padding-left: 24px;
      color: $mx-color-var-label-secondary;
      text-transform: uppercase;
    }

    .team-description {
      display: flex;
      align-items: center;
      margin-top: 12px;
      padding: 12px 24px;

      .icon-label {
        color: $mx-color-var-label-tertiary;
      }

      .description-area {

        .description-detail {
          margin-top: 5px;
          word-break: break-all;
        }
      }
    }
  }

  .team-member-list-panel {
    margin-top: 24px;

    .information-label {
      border-radius: 6px;
      background-color: $mx-color-var-fill-quaternary;
      height: 40px;
      line-height: 40px;
      padding-left: 24px;
      padding-right: 24px;
      color: $mx-color-var-label-secondary;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      justify-content: space-between;

      i.micon-edit-xs {
        font-size: 16px;
        color: $mx-color-var-label-secondary;
      }

      i.micon-mep-info-square {
        color: $mx-color-var-fill-primary;
      }
    }

    .member-list-detail {
      .member-wrapper {
        display: flex;
        align-items: center;
        padding: 12px 20px;

        &.deactivated {
          ::v-deep img {
            filter: grayscale(1)
          }
        }

        .member-info {
          display: flex;
          align-items: center;
          width: 100%;
          margin-left: 16px;
          overflow: hidden;

          .name-title {
            flex: 1;
            overflow: hidden;
            margin-right: 10px;
          }

          .member-title {
            color: $mx-color-var-label-secondary;
            margin-top: 3px;
          }

          .manager-label {
            color: $mx-color-var-label-secondary;
          }
        }
      }
    }
  }

  .contact-detail-right-container {
    width: 37.5%;
    padding-top: 32px;
  }

  .group-managers-container {
    padding-right: 16px;

    .managers-label {
      display: flex;
      justify-content: space-between;
    }

    li {
      display: flex;
      align-items: center;
      padding: 8px 0 8px 8px;

      .manager-info {
        flex: 1;
        display: flex;
        overflow: hidden;
        align-items: center;

        .name-title {
          flex: 1;
          overflow: hidden;
        }
      }
    }

    + .shared-conversations-panel {
      margin-top: 32px;
    }
  }

  &.vertical-mode {
    @include vertical-mode;

    .team-member-panel {
      padding: 20px 24px;
    }

    .shared-conversations-panel {
      max-width: 100%;
    }

    .contact-detail-right-container {
      padding: 0 24px;
      margin-top: 4px;
    }
  }

  @media screen and (max-width: 1228px) {
    @include vertical-mode;
  }
}
</style>
