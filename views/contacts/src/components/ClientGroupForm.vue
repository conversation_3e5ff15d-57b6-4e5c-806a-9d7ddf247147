<template>
  <el-form
    ref="form"
    :model="groupInfo"
    :rules="groupRules"
    label-position="top">
    <el-form-item
      prop="name"
      class="is-no-asterisk"
      :label="`${$t('template_name')}*`">
      <el-input
        v-model="groupInfo.name"
        data-ta="flow_name"
        :placeholder="$t('enter_group_name')"
        :maxlength="35"
        :show-limit="true"
        prop="name" />
    </el-form-item>
    <el-form-item
      prop="description"
      :label="$t('description')">
      <el-input
        v-model="groupInfo.description"
        data-ta="flow_name"
        :placeholder="$t('Describe_your_group')"
        type="textarea"
        :show-limit="true"
        :maxlength="200"
        prop="name" />
    </el-form-item>
    <div class="team-avatar-container">
      <div style="width: 100%; margin-bottom: 0;">
        <div class="mx-secondary" style="line-height: 20px; margin-bottom: 12px; color: #616161;">
          {{ $t('client_group_avatar_file_type') }}
        </div>
        <mx-upload-avatar
          ref="uploader"
          :visible.sync="showUploadImageModal"
          :banner-text="$t('upload_group_image')"
          :min-width="0"
          :min-height="0"
          :cap="currentUser.cap || {}"
          :need-crop="true"
          :upload-url="uploadUrl"
          :need-upload="false"
          :auto-upload="true"
          @submit="getCroppedAvatar"
          @after-crop="uploadCroppedAvatar"
        />
        <div class="mx-upload-block mx-clickable mx-ellipsis"
             @click="showUploadImageModal = true"
             @keydown.enter="showUploadImageModal = true"
        >
          <i class="micon-edit-xs"></i>
          {{ $t('upload_image') }}
        </div>
      </div>
      <group-avatar
        :lazyload="true"
        alt="Icon"
        :team-id="groupInfo.id"
        :team-avatar="groupInfo.avatar"
        size="72" />
    </div>
  </el-form>
</template>

<script>

import {useImageCropDialog} from '@views/common/components/userAvatar/useImageCropDialog';
import {blobToUrl} from '@views/common';
import GroupAvatar from '@views/mgrClientGroupMgr/src/side/GroupAvatar.vue';
import MxUploadAvatar from '@views/common/components/uploader/MxUploadAvatar.vue';
import {mapGetters, mapState} from 'vuex';
import urlUtil from '@views/common/utils/url';

export default {
  name: 'ClientGroupForm',
  components: {MxUploadAvatar, GroupAvatar},
  props: {
    groupBasicInfo: {
      type: Object,
      required: true,
    },
    mode: {
      type: String,
      default: 'create'
    },
    teamObject: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      groupInfo: {
        id: '',
        name: '',
        description: '',
        avatar: ''
      },
      groupRules: {
        name: {
          required: true,
          message: this.$t('Required_field'),
          trigger: 'none'
        }
      },
      teamAvatarFile: null,
      showUploadImageModal: false
    }
  },
  created () {
    if(this.mode !== 'create') {
      Object.assign(this.groupInfo, this.groupBasicInfo);
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    ...mapState('group', ['contextPath']),
    uploadUrl () {
      return urlUtil.makeAccessTokenUrl(`${this.contextPath}/group/${this.groupInfo.id}/upload?type=avatar`)
    },
  },
  watch: {
    groupInfo: {
      deep: true,
      handler (val) {
        this.$emit('update', val);
      }
    }
  },
  methods: {
    selectThumbnail () {
      const vm = this
      const [show] = useImageCropDialog({
        submit (file) {
          blobToUrl(file).then(url => {
            vm.imageUrl = url
          })
        }
      }, {
        uploadUrl: '',
        minWidth: 80,
        minHeight: 80,
        bannerText: this.$t('upload_image'),
        needUpload: false,
        cap: this.currentUser.userCap
      })
      show()
    },
    getCroppedAvatar (image) {
      this.teamAvatarFile = image
      const reader = new FileReader()
      const vm = this
      reader.onload = (e) => {
        if (typeof e.target.result === 'object') {
          vm.groupInfo.avatar = window.URL.createObjectURL(new Blob([e.target.result]))
        } else {
          vm.groupInfo.avatar = e.target.result
        }
        reader.onload = null
      }
      reader.readAsDataURL(image)
    },
    startUploadTeamAvatar() {
      if(this.teamAvatarFile) {
        return this.$refs.uploader.$refs.uploader.submit(this.teamAvatarFile)
      } else {
        return Promise.resolve()
      }
    },
    uploadCroppedAvatar (uploadFunc) {
      uploadFunc()
    }
  }
}
</script>

<style scoped lang="scss">
.team-avatar-container {
  display: flex;
  align-items: center;
  img {
    width: 72px;
    height: 72px;
  }
}
::v-deep {
  .el-form-item__error {
    margin-left: 0px;
    padding-top: 0;
  }
  .el-form-item__label {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    display: inline-block;
    padding-bottom: 0;
    color: #000;
  }

}
</style>