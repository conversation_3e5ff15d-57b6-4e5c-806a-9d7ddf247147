<template>
  <el-dialog
    class="client-group-dialog"
    :visible.sync="visible"
    width="540"
    @before-close="visible = false"
    @closed="$emit('close')">
    <div slot="title" class="dialog-title">
      {{ $t('edit_group_details') }}
    </div>
    <ClientGroupForm
      ref="clientGroupForm"
      :group-basic-info="groupBasicInfo"
      mode="edit"
      @update="updateGroupBasicInfo"/>
    <span
      slot="footer"
      class="dialog-footer">
      <el-button-group :size="1">
        <ElButton
          :loading="isEditing"
          type="primary"
          @click="editGroup">
          {{ $t('edit') }}
        </ElButton>
      </el-button-group>
    </span>
  </el-dialog>
</template>

<script>
import ClientGroupForm from '@views/contacts/src/components/ClientGroupForm.vue';
import {mapActions} from 'vuex'

export default {
  name: 'EditClientGroup',
  components: { ClientGroupForm},
  props: {
    groupInfo: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      visible: true,
      isEditing: false,
      groupBasicInfo: {
        name: '',
        description: '',
        avatar: '',
        id: ''
      }
    }
  },
  created () {
    Object.assign(this.groupBasicInfo, this.groupInfo)
  },
  methods: {
    ...mapActions('user', ['editClientGroup']),
    updateGroupBasicInfo (basicInfo) {
      this.groupBasicInfo = basicInfo
    },
    editGroup () {
      this.isEditing = true
      const newTeamInfo = {
        groupId: this.groupBasicInfo.id,
        name: this.groupBasicInfo.name.trim(),
        description: this.groupBasicInfo.description.trim()
      }
      const onSuccess = () => {
        this.isEditing = false
        this.$mxMessage.success(this.$t('Client_group_successfully_updated'))
        this.$emit('close')
      }
      this.editClientGroup(newTeamInfo).then(isSucceed => {
        if (isSucceed) {
          this.$refs['clientGroupForm'].startUploadTeamAvatar().then(onSuccess).catch(err => {
            this.isEditing = false
          })
          } else {
          this.isEditing = false

          console.log(isSucceed)
        }

      }).catch(err => {
        console.log(err)
        this.isEditing = false

      })

    }
  }
}
</script>

<style scoped lang="scss">

.client-group-dialog::v-deep {
  .el-dialog__header {
    padding-left: 28px;
    display: flex;
    align-items: center;
    width: 100%;
    height: 52px;
    justify-content: flex-start;

    .left {
      width: 100%;
      display: flex;
    }

    &.title-left {
      flex: 1;
      text-align: left;
      line-height: 24px;

      margin-right: 5px;
      margin-left: 5px;
      display: inline-block;
    }

  }

  &.add-clients .el-dialog__body {
    height: 392px;
    padding: 0;
  }

  .el-dialog {
    width: 540px;
  }

  .el-dialog__footer {
    padding: 12px 28px;

    .dialog-footer {
      flex: 1;
    }
  }
}
</style>