<template>
  <BaseModal
    :visible="true"
    :modal-option="{
    width: '540px'
  }"
    :title="$t('Edit_Managers')">
    <template
      slot="content">
      <div
        class="scroll-y"
        ref="clientsScroller"
        v-mx-has-scrollbar>
        <div class="mx-text-c1 add-clients-label">{{ $t('Group_managers') }}</div>
        <mx-user-selector
          ref="userSelector"
          :includeMe="false"
          userType="internal"
          :invitedMembers="existedManagers"
          :includingPendingUser="true"
          :includeRelationUser="false"
          :placeholder="SSOEnabledForCurrentUser ? $t('search_user'): $t('add_members_email_name_phone')"
          @totalCountUpdate="onManagersCountUpdate"
          @select="handleSelectUser">
          <template
            slot="empty"
            slot-scope="slotProps">
            <InviteNewUserInSelector
              :userSelector="slotProps"
              :showInviteClient="false"
              :showInviteInternalUser="false" />
          </template>
        </mx-user-selector>
        <ul
          class="mx-margin-top-sm"
          v-a11y-list-keyboard="{listLength: managers.length, scrollElement: $refs.clientsScroller}">
          <li
            v-for="(manager, index) in managers"
            role="listitem"
            :key="manager.id"
            class="mx-hover-show">
            <MxUserListItem
              :avatar="manager.avatar"
              :title="manager.name"
              avatarSize="28"
              :subTitle="getMemberTitle(manager)"
              :class="{'unabled-user': manager.isDisabled}">
              <template slot="action">
                <i
                  data-action="tabindex"
                  class="remove-action mx-clickable micon-toast-error mx-hover-hide"
                  role="button"
                  :aria-label="$t('remove')"
                  @click="removeMember(index)"
                  @keydown.enter="removeMember(index)" />
              </template>
            </MxUserListItem>
          </li>
        </ul>
      </div>
      <el-button
        type="primary"
        :loading="isLoading"
        class="save-changes-btn"
        @click="saveChanges">
        {{ $t('save_changes') }}
      </el-button>
    </template>
  </BaseModal>
</template>

<script>
import BaseModal from '@views/common/components/modals/BaseModal'
import utils from '@views/common/utils/utils'
import MxUserListItem from '@views/common/components/user/MxUserListItem'
import {mapGetters, mapActions} from 'vuex'
export default {
  name: 'EditClientGroupManager',
  components: {
    BaseModal,
    MxUserListItem,
    MxUserSelector: () => utils.retry(import(/* webpackChunkName: 'MxUserSelector' */ '@views/common/components/userSelector/MxUserSelector')),
    InviteNewUserInSelector: () => utils.retry(import(/* webpackChunkName: 'InviteNewUserInSelector' */ '@views/common/components/userSelector/InviteNewUserInSelector')),
  },
  computed: {
    ...mapGetters('group', ['isSamlEnabled']),
    ...mapGetters('user', ['SSOEnabledForCurrentUser']),
  },
  props: {
    existedManagers: {
      type: Array,
      default: []
    },
    teamId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      managers: [],
      reachedLimit: false,
      addManagers: [],
      removeManagers: [],
      isLoading: false

    }
  },
  created () {
    this.managers = this.managers.concat(this.existedManagers)
  },
  methods: {
    ...mapActions('group', ['addTeamManagers', 'removeTeamManager']),
    getMemberTitle (member) {
      if(member.isDisabled){
        return this.$t('user_deactivated')
      }
      const title = member.subTitle || member.title
      const phoneNumber = member.phone_number || member.phoneNumber || member.display_phone_number
      if (title === phoneNumber) {
        return utils.getFormattedPhoneNumber(title)
      }
      return title
    },
    removeMember (index) {
      const manager = this.managers[index]
      this.managers.splice(index, 1)
      const addManagerIndex = this.addManagers.findIndex(item => item.id === manager.id)
      if (addManagerIndex > -1) {
        this.addManagers.splice(addManagerIndex, 1)
      } else {
        this.removeManagers.push(manager)
      }
      this.$refs['userSelector'].onExternalRemoveUser(manager)
    },
    handleSelectUser (manager) {
      this.managers.push(manager)
      const removeManagerIndex = this.removeManagers.findIndex(item => item.id === manager.id)
      if (removeManagerIndex > -1) {
        this.removeManagers.splice(removeManagerIndex, 1)
      } else {
        this.addManagers.push(manager)
      }
    },
    saveChanges () {
      const requests = []
      if (this.addManagers.length) {
        requests.push(this.addTeamManagers({teamId: this.teamId, users: this.addManagers}))
      }
      if (this.removeManagers.length) {
        for (const manager of this.removeManagers) {
          requests.push(this.removeTeamManager({teamId: this.teamId, user: manager}))
        }
      }
      if (requests.length) {
        this.isLoading = true
        Promise.all(requests).then(() => {
          this.$mxMessage.success(this.$t('Client_group_successfully_updated'))
          this.$emit('updateManagers')
          this.$emit('close')
        }).catch(() => {
          this.$mxMessage.error(this.$t('Unable_to_save_changes_to_contact_admin'))
        }).finally(() => {
          this.isLoading = false
        })
      } else {
        this.$emit('close')
      }
    },
    showReachedLimitError () {
      this.$refs['userSelector'].showExternalError({
        warningTip: this.$t('This_group_reached_maximum_numbers_of_clients'),
        alertMessage: this.$t('Client_limit_reached')
      })
      this.reachedLimit = true
    },
    onManagersCountUpdate (count) {
      if (count >= 40) {
        if (this.$refs['userSelector']) {
          this.showReachedLimitError()
        } else {
          this.$nextTick(() => {
            this.showReachedLimitError()
          })
        }
      } else if (this.reachedLimit) {
        this.$refs['userSelector'].showExternalError({
          warningTip: '',
          alertMessage: ''
        })
        this.reachedLimit = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    height: 392px;
    padding: 0;
    .scroll-y {
      display: flex;
      height: calc(100% - 68px);
      overflow-y: auto;
      flex-direction: column;
      padding: 25px 28px 0;
    }
    .add-clients-label {
      margin: 0 0 7px 4px;
    }
    .el-input__suffix-inner {
      line-height: 32px;
    }
    ul {
      flex: 1;
      li:hover {
        background-color: $mx-color-var-fill-quaternary;
      }
    }
    .mx-user-list-item {
      padding: 4px 20px;
      .action i {
        font-size: 16px;
        color: $mx-color-var-fill-primary;
      }
    }
    .save-changes-btn {
      width: calc(100% - 56px);
      margin: 12px 28px 20px;
    }
    .unabled-user{
      .user-name, .user-title{
        color: $mx-color-var-text-tertiary
      }
      .avatar{
        opacity: .5
      }
    }
  }
}
</style>