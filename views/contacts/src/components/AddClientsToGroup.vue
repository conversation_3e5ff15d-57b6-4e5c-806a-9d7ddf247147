<template>
  <div
    class="scroll-y"
    ref="clientsScroller"
    v-mx-has-scrollbar>
    <div class="mx-text-c1 add-clients-label">{{ $t('Add_clients_to_this_group') }}</div>
    <mx-user-selector
      ref="userSelector"
      :includeMe="false"
      userType="external"
      :invitedMembers="clients"
      :includingPendingUser="true"
      :placeholder="SSOEnabledForCurrentUser ? $t('search_user'): $t('add_members_email_name_phone')"
      @totalCountUpdate="onClientsCountUpdate"
      @select="handleSelectUser">
      <template
        slot="empty"
        slot-scope="slotProps">
        <InviteNewUserInSelector
          :userSelector="slotProps"
          :showInviteClient="false"
          :showInviteInternalUser="false" />
      </template>
    </mx-user-selector>
    <ul
      class="mx-margin-top-sm"
      v-a11y-list-keyboard="{listLength: clients.length, scrollElement: $refs.clientsScroller}">
      <li
        v-for="(client, index) in clients"
        role="listitem"
        :key="client.id"
        class="mx-hover-show">
        <MxUserListItem
          :avatar="client.avatar"
          :title="client.name"
          :indicator="true"
          avatarSize="28"
          :subTitle="getMemberTitle(client)"
          :class="{'unabled-user': client.isDisabled}">
          <template slot="action">
            <i
              data-action="tabindex"
              class="remove-action mx-clickable micon-toast-error mx-hover-hide"
              role="button"
              :aria-label="$t('remove')"
              @click="removeMember(index)"
              @keydown.enter="removeMember(index)" />
          </template>
        </MxUserListItem>
      </li>
    </ul>
  </div>
</template>

<script>
import utils from '@views/common/utils/utils'
import MxUserListItem from '@views/common/components/user/MxUserListItem'
import {mapGetters, mapActions} from 'vuex'

export default {
  name: 'AddClientsToGroup',
  components: {
    MxUserListItem,
    MxUserSelector: () => utils.retry(import(/* webpackChunkName: 'MxUserSelector' */ '@views/common/components/userSelector/MxUserSelector')),
    InviteNewUserInSelector: () => utils.retry(import(/* webpackChunkName: 'InviteNewUserInSelector' */ '@views/common/components/userSelector/InviteNewUserInSelector')),
  },
  data () {
    return {
      reachedLimit:false,
      clients: []
    }
  },
  computed: {
    ...mapGetters('group', ['isSamlEnabled']),
    ...mapGetters('user', ['SSOEnabledForCurrentUser']),
  },
  watch: {
    clients (val) {
      this.$emit('changeSelect', val)
    }
  },
  methods: {
    ...mapActions('group', ['addTeamMembers']),
    getMemberTitle (member)  {
      if(member.isDisabled){
        return this.$t('user_deactivated')
      }
      const title = member.subTitle || member.title
      const phoneNumber = member.phone_number || member.phoneNumber || member.display_phone_number
      if (title === phoneNumber) {
        return utils.getFormattedPhoneNumber(title)
      }
      return title
    },
    removeMember (index) {
      const client = this.clients[index]
      this.clients.splice(index, 1)
      this.$refs['userSelector'].onExternalRemoveUser(client)
    },
    handleSelectUser (client) {
      this.clients.push(client)
    },
    showReachedLimitError () {
      this.$refs['userSelector'].showExternalError({
        warningTip: this.$t('This_group_reached_maximum_numbers_of_clients'),
        alertMessage: this.$t('Client_limit_reached')
      })
      this.reachedLimit = true
    },
    onClientsCountUpdate (count) {
      if (count >= 40) {
        if (this.$refs['userSelector']) {
          this.showReachedLimitError()
        } else {
          this.$nextTick(() => {
            this.showReachedLimitError()
          })
        }
      } else if (this.reachedLimit) {
        this.$refs['userSelector'].showExternalError({
          warningTip: '',
          alertMessage: ''
        })
        this.reachedLimit = false
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.scroll-y {
  display: flex;
  height: calc(100% - 68px);
  overflow-y: auto;
  flex-direction: column;
  padding: 25px 28px 0;
}
.add-clients-label {
  margin: 0 0 7px 4px;
}
.el-input__suffix-inner {
  line-height: 32px;
}
ul {
  flex: 1;
  li:hover {
    background-color: $mx-color-var-fill-quaternary;
  }
}
.mx-user-list-item {
  padding: 4px 20px;
  .action i {
    font-size: 16px;
    color: $mx-color-var-fill-primary;
  }
}
.unabled-user{
  .user-name, .user-title{
    color: $mx-color-var-text-tertiary
  }
  .avatar{
    opacity: .5
  }
}
</style>