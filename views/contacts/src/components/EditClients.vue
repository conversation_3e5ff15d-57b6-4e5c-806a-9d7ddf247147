<template>
<BaseModal
  :visible="true"
  :modal-option="{
    width: '540px'
  }"
  :title="$t('Edit_Clients')">
  <template
    slot="content">
    <div
      class="scroll-y"
      ref="clientsScroller"
      v-mx-has-scrollbar>
      <div class="mx-text-c1 add-clients-label">{{ $t('Add_clients_to_this_group') }}</div>
      <mx-user-selector
        ref="userSelector"
        :includeMe="false"
        userType="external"
        :invitedMembers="existedClients"
        :includingPendingUser="true"
        :placeholder="SSOEnabledForCurrentUser ? $t('search_user'): $t('add_members_email_name_phone')"
        @totalCountUpdate="onClientsCountUpdate"
        @select="handleSelectUser">
        <template
          slot="empty"
          slot-scope="slotProps">
          <InviteNewUserInSelector
            :userSelector="slotProps"
            :showInviteClient="false"
            :showInviteInternalUser="false" />
        </template>
      </mx-user-selector>
      <ul
        class="mx-margin-top-sm"
        v-a11y-list-keyboard="{listLength: clients.length, scrollElement: $refs.clientsScroller}">
        <li
          v-for="(client, index) in clients"
          role="listitem"
          :key="client.id"
          class="mx-hover-show">
          <MxUserListItem
            :avatar="client.avatar"
            :title="client.name"
            :indicator="true"
            avatarSize="28"
            :subTitle="getMemberTitle(client)"
            :class="{'unabled-user': client.isDisabled}">
            <template slot="action">
              <i
                data-action="tabindex"
                class="remove-action mx-clickable micon-toast-error mx-hover-hide"
                role="button"
                :aria-label="$t('remove')"
                @click="removeMember(index)"
                @keydown.enter="removeMember(index)" />
            </template>
          </MxUserListItem>
        </li>
      </ul>
    </div>
    <el-button
      type="primary"
      class="save-changes-btn"
      :loading="isUpdating"
      @click="saveChanges">
      {{ $t('save_changes') }}
    </el-button>
  </template>
</BaseModal>
</template>

<script>
import { defineComponent, ref, nextTick } from '@vue/composition-api'
import BaseModal from '@views/common/components/modals/BaseModal'
import utils from '@views/common/utils/utils'
import { useStore } from '@views/common'
import MxUserListItem from '@views/common/components/user/MxUserListItem'
import {mapGetters} from 'vuex'
export default defineComponent({
  name: 'EditClients',
  components: {
    BaseModal,
    MxUserListItem,
    MxUserSelector: () => utils.retry(import(/* webpackChunkName: 'MxUserSelector' */ '@views/common/components/userSelector/MxUserSelector')),
    InviteNewUserInSelector: () => utils.retry(import(/* webpackChunkName: 'InviteNewUserInSelector' */ '@views/common/components/userSelector/InviteNewUserInSelector')),
  },
  computed: {
    ...mapGetters('group', ['isSamlEnabled']),
    ...mapGetters('user', ['SSOEnabledForCurrentUser']),
  },
  props: {
    existedClients: {
      type: Array,
      default: []
    },
    teamId: {
      type: String,
      default: ''
    }
  },
  setup (props, { root, emit }) {
    let clients = ref([])
    let reachedLimit = ref(false)
    let addClients = ref([])
    let isUpdating = ref(false)
    const userSelector = ref(null)
    let removeClients = ref([])
    const groupStore = useStore('group')
    clients.value = clients.value.concat(props.existedClients)
    
    const getMemberTitle = (member) => {
      if(member.isDisabled){
        return root.$t('user_deactivated')
      }
      const title = member.subTitle || member.title
      const phoneNumber = member.phone_number || member.phoneNumber || member.display_phone_number
      if (title === phoneNumber) {
        return utils.getFormattedPhoneNumber(title)
      }
      return title
    }
    
    const removeMember = (index) => {
      const client = clients.value[index]
      clients.value.splice(index, 1)
      const addClientIndex = addClients.value.findIndex(item => item.id === client.id)
      if (addClientIndex > -1) {
        addClients.value.splice(addClientIndex, 1)
      } else {
        removeClients.value.push(client)
      }
      userSelector.value.onExternalRemoveUser(client)
    }
    
    const handleSelectUser = (client) => {
      clients.value.push(client)
      let removeClientIndex = removeClients.value.findIndex(item => item.id === client.id)
      if (removeClientIndex > -1) {
        removeClients.value.splice(removeClientIndex, 1)
      } else {
        addClients.value.push(client)
      }
    }
    
    const saveChanges = () => {
      let requests = []
      if (addClients.value.length) {
        requests.push(groupStore.dispatch('addTeamMembers', {teamId: props.teamId, users: addClients.value}))
      }
      if (removeClients.value.length) {
        for (let client of removeClients.value) {
          requests.push(groupStore.dispatch('removeTeamMember', {teamId: props.teamId, user: client}))
        }
      }
      if (requests.length) {
        isUpdating.value = true
        Promise.all(requests).then(() => {
          root.$mxMessage.success(root.$t('Client_group_successfully_updated'))
          emit('updateClients')
          emit('close')
        }).catch(() => {
          root.$mxMessage.error(root.$t('Unable_to_save_changes_to_contact_admin'))
        }).finally(() => {
          isUpdating.value = false
        })
      } else {
        emit('close')
      }
    }
    
    const showReachedLimitError = () => {
      userSelector.value.showExternalError({
        warningTip: root.$t('This_group_reached_maximum_numbers_of_clients'),
        alertMessage: root.$t('Client_limit_reached')
      })
      reachedLimit.value = true
    }

    const onClientsCountUpdate = (count) => {
      if (count >= 40) {
        if (userSelector.value) {
          showReachedLimitError()
        } else {
          nextTick(() => {
            showReachedLimitError()
          })
        }
      } else if (reachedLimit.value) {
        userSelector.value.showExternalError({
          warningTip: '',
          alertMessage: ''
        })
        reachedLimit.value = false
      }
    }
    
    return {
      clients,
      userSelector,
      saveChanges,
      removeMember,
      getMemberTitle,
      handleSelectUser,
      isUpdating,
      onClientsCountUpdate
    }
  }
})
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    height: 392px;
    padding: 0;
    .scroll-y {
      display: flex;
      height: calc(100% - 68px);
      overflow-y: auto;
      flex-direction: column;
      padding: 25px 28px 0;
    }
    .add-clients-label {
      margin: 0 0 7px 4px;
    }
    .el-input__suffix-inner {
      line-height: 32px;
    }
    ul {
      flex: 1;
      li:hover {
        background-color: $mx-color-var-fill-quaternary;
      }
    }
    .mx-user-list-item {
      padding: 4px 20px;
      .action i {
        font-size: 16px;
        color: $mx-color-var-fill-primary;
      }
    }
    .save-changes-btn {
      width: calc(100% - 56px);
      margin: 12px 28px 20px;
    }
    .unabled-user{
      .user-name, .user-title{
        color: $mx-color-var-text-tertiary
      }
      .avatar{
        opacity: .5
      }
    }
  }
}
</style>