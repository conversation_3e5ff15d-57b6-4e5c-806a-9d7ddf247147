<template>
  <el-dialog
    class="client-group-dialog"
    :class="{'add-clients': currentStep === CreateStep.addClient}"
    :visible.sync="visible"
    width="540"
    @before-close="visible = false"
    @closed="$emit('close')">
    <div slot="title" class="dialog-title">
      <i
        v-if="currentStep === CreateStep.addClient"
        @click="currentStep = CreateStep.createForm" class="micon-accordion-left-centered"/>{{ dialogTitle }}
    </div>
    <ClientGroupForm
      v-show="currentStep === CreateStep.createForm"
      ref="clientGroupForm"
      :group-basic-info="groupBasicInfo"
      mode="create"
      @update="updateGroupBasicInfo"/>
    <AddClientsToGroup
      v-show="currentStep === CreateStep.addClient"
      @changeSelect="updateSelectedClients"/>
    <span
      slot="footer"
      class="dialog-footer">
      <el-button-group :size="1">
        <ElButton
          v-if="currentStep === CreateStep.createForm"
          type="primary"
          @click="goNext">
          {{ $t('next') }}
        </ElButton>
        <ElButton
          v-else
          :loading="isCreating"
          type="primary"
          @click="doCreate">
          {{ $t('create') }}
        </ElButton>
      </el-button-group>
    </span>
  </el-dialog>
</template>

<script>

import ClientGroupForm from '@views/contacts/src/components/ClientGroupForm.vue';
import AddClientsToGroup from '@views/contacts/src/components/AddClientsToGroup.vue';
import {mapActions} from 'vuex'

const CreateStep = {
  addClient: 'addClient',
  createForm: 'createForm'
}

export default {
  name: 'CreateClientGroup',
  components: {ClientGroupForm, AddClientsToGroup},
  data () {
    return {
      visible: true,
      CreateStep,
      isCreating: false,
      currentStep: CreateStep.createForm,
      selectedClients: [],
      groupBasicInfo: {
        name: '',
        description: '',
        avatar: '',
        id: ''
      }
    }
  },
  computed: {
    dialogTitle () {
      return this.$t('new_client_group')
    }
  },
  methods: {
    ...mapActions('user', ['createClientGroup']),
    updateGroupBasicInfo (basicInfo) {
      this.groupBasicInfo = basicInfo
    },
    updateSelectedClients (clients) {
      this.selectedClients = clients
    },
    goNext () {
      this.$refs['clientGroupForm'].$refs['form'].validate(valid => {
        if (valid) {
          this.currentStep = CreateStep.addClient
        }
      })
    },
    doCreate () {
      const onCreated = () => {
        this.isCreating = false
        this.$mxMessage.success(this.$t('Client_group_successfully_created'))
        this.$emit('close')
      }
      this.isCreating = true
      this.createClientGroup({
        name: this.groupBasicInfo.name.trim(),
        description: this.groupBasicInfo.description.trim(),
        clients: this.selectedClients.map(m => {
          return {id: m.userId}
        })
      }).then(team => {
        this.groupBasicInfo.id = team.id

        if (this.groupBasicInfo.avatar) {
          this.$nextTick(() => {
            this.$refs['clientGroupForm'].startUploadTeamAvatar().then(onCreated).catch(err => {
              console.log(err)
              this.isCreating = false
            })
          })
        } else {
          onCreated()
        }
      }).catch((err) => {
        console.log(err)
        this.isCreating = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.client-group-dialog::v-deep {
  .el-dialog__header {
    padding-left: 28px;
    display: flex;
    align-items: center;
    width: 100%;
    height: 52px;
    justify-content: flex-start;

    .left {
      width: 100%;
      display: flex;
    }

    .dialog-title {
      gap: 8px;
      display: flex;
      i {
        color: $mx-color-var-label-secondary;
        cursor: pointer;
      }
    }
    &.title-left {
      flex: 1;
      text-align: left;
      line-height: 24px;

      margin-right: 5px;
      margin-left: 5px;
      display: inline-block;
    }

  }

  &.add-clients .el-dialog__body {
    height: 392px;
    padding: 0;
  }

  .el-dialog {
    width: 540px;
  }

  .el-dialog__footer {
    padding: 12px 28px;

    .dialog-footer {
      flex: 1;
    }
  }
}
</style>