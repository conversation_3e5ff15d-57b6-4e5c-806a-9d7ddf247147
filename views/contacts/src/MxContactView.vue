<template>
  <div class="main-section mx-contact-wrapper">
    <div class="mock-outline" />
    <el-container>
      <el-main>
        <div
          :class="{'open-detail': isDetailShowing}"
          class="mx-container-wrapper">
          <div
            class="left-wrapper margin-right1"
            role="contentinfo"
            aria-labelledby="contact_label">
            <div class="contact-subheader">
              <h1
                id="contact_label"
                class="mx-contacts-title mx-semibold">
                <span
                  :class="{'hide': !isDetailShowing}"
                  tabindex="0"
                  class="hidden-sm-and-up mx-color-action-effect mx-clickable"
                  @keypress.prevent="navBackToList"
                  @click="navBackToList">
                  <i class="micon-mep-chevron-left"/>
                </span> {{ mainTitle }}
              </h1>
            </div>

            <div class="mx-search-with-icon">
              <el-input
                ref="searchBar"
                v-model="localSearchKey"
                v-mx-ta="'contact_search'"
                :placeholder="$t('filter_placeholder')"
                :aria-label="$t('filter_placeholder')"
                :prefix-icon="isFocusingInput ? 'micon-search-s mx-branding-text' : 'micon-search-s'"
                class="mx-branding-focus-border"
                :class="{'is-focusing': isFocusingInput || trimmedSearchKey}"
                @input="searchUser"
                @focus="isFocusingInput = true"
                @blur="isFocusingInput = false">
                <i
                  v-if="localSearchKey"
                  slot="suffix"
                  role="button"
                  :aria-label="$t('Clear')"
                  class="micon-error mx-clickable"
                  :tabindex="localSearchKey?0:-1"
                  @keypress.enter="handleClearSearch"
                  @click.stop="handleClearSearch"/>
              </el-input>
              <el-select
                ref="category"
                type="button"
                class="mx-branding-focus-border filter-select"
                dropdownWidth="226px"
                v-model="selectedUserType"
                data-ta="contact_select"
                @change="switchTab"
                @keydown.right.native="handleRightKeyEvent"
                @visible-change="selectVisible = $event"
                popperClass="contact-filter-select-dropdown">
                <i
                  slot="prefix"
                  class="micon-filter"/>
                <template
                  v-if="clientsFilterType">
                  <el-option
                    v-mx-ta="`contact_selected_${clientsFilterType}`"
                    :value="clientsFilterType"
                    class="client-filter-type"
                    :label="clientsFilterType === 'available' ? $t('Available_clients') : $t('All_clients')">
                    <div
                      :title="clientsFilterType === 'available' ? $t('Available_clients') : $t('All_clients')"
                      class="mx-ellipsis">
                      {{ clientsFilterType === 'available' ? $t('Available_clients') : $t('All_clients') }}
                    </div>
                  </el-option>
                  <div class="divider"/>
                </template>
                <el-option
                  v-mx-ta="'contact_all'"
                  value="all"
                  :label="$t('all')">
                  <div>{{ $t('all') }}</div>
                </el-option>
                <el-option
                  v-mx-ta="'contact_internal'"
                  value="int"
                  :label="$t('Internal')">
                  <div>{{ $t('Internal') }}</div>
                </el-option>
                <el-option
                  v-mx-ta="'contact_teams'"
                  value="teams"
                  :label="$t('teams')">
                  <div>{{ $t('teams') }}</div>
                </el-option>
                <el-option
                  v-mx-ta="'contact_clients'"
                  value="ext"
                  :has-submenu="showClientSubMenu"
                  :class="{'is-focus': subMenuVisible, 'has-submenu': showClientSubMenu}"
                  :label="$t('Clients')">
                  <el-dropdown
                    ref="clientsFilterDropdown"
                    v-if="showClientSubMenu"
                    placement="right-start"
                    :isAsSubmenu="true"
                    @visible-change="subMenuVisible = $event"
                    @command="handleDropdownCommand"
                    @keydown.left.native="handleLeftKeyEvent"
                    @keydown.esc.native="$refs.category.$el.querySelector('input').focus()"
                    @keydown.space.native="$refs.category.$el.querySelector('input').focus()"
                    @keydown.enter.native="$refs.category.$el.querySelector('input').focus()">
                    <div>
                      <div class="mx-ellipsis" :title="$t('Clients')">{{ $t('Clients') }}</div>
                      <i class="micon-right-arrow-new font-icon-sm"/>
                    </div>
                    <el-dropdown-menu
                      slot="dropdown"
                      :offset="-16"
                      :class="{'hide': !selectVisible}"
                      style="width: 226px;"
                      @keydown.left.native="handleLeftKeyEvent"
                      @keydown.enter.native="handleSubmenuEnterKeyEvent"
                      @keydown.esc.native="$refs.category.$el.querySelector('input').focus()">
                      <el-dropdown-item
                        command="clients"
                        :class="['clients-submenu-item', {'active': clientsFilterType === 'clients'}]">
                        <div class="mx-ellipsis" :title="$t('All_clients')">{{ $t('All_clients') }}</div>
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="available"
                        :class="['clients-submenu-item', {'active': clientsFilterType === 'available'}]">
                        <div class="mx-ellipsis" :title="$t('Available_clients')">{{ $t('Available_clients') }}</div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <div v-else>{{ $t('Clients') }}</div>
                </el-option>
                <el-option
                  v-if="isEnableClientGroup"
                  v-mx-ta="'contact_accounts'"
                  value="accounts"
                  :label="$t('Client_Groups')">
                  <div>{{ $t('Client_Groups') }}</div>
                </el-option>
              </el-select>
              <div class="actions">
                <el-dropdown
                  v-if="showNewBtn"
                  trigger="click"
                  @command="doInvite">
                  <el-button
                    v-if="showNewBtn"
                    class="invite-contact-btn"
                    type="branding"
                    size="small"
                    square
                    icon="micon-plus">
                  </el-button>
                  <el-dropdown-menu
                    slot="dropdown"
                    :class="{'hide': !showNewBtn}">
                    <el-dropdown-item
                      v-if="canInviteClient"
                      command="client">
                      <span>{{ $t('Invite_Client') }}</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="canInviteInternalUser"
                      command="internal">
                      <span>{{ $t('Invite_Internal_User') }}</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="canCreateClientGroup"
                      command="clientGroup">
                      <span>{{ $t('new_client_group') }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <MxContactClientsList
              v-if="selectedUserType ==='ext' || clientsFilterType"
              :query="trimmedSearchKey"
              :clientsFilterType="clientsFilterType"
              :autoJump="autoJump"
              @on-select="onItemClick">
              <MxContactsEmptyView
                :selectedUserType="selectedUserType"
                @resetFilters="resetFilters"/>
            </MxContactClientsList>
            <MxContactInternalList
              v-else-if="selectedUserType === 'int'"
              :query="trimmedSearchKey"
              :autoJump="autoJump"
              @on-select="onItemClick">
              <MxContactsEmptyView
                :selectedUserType="selectedUserType"
                @resetFilters="selectedUserType = 'all'"/>
            </MxContactInternalList>
            <MxContactTeamList
              :selectedUserType="selectedUserType"
              v-else-if="selectedUserType === 'teams' || selectedUserType === 'accounts'"
              :teams="selectedUserType === 'teams' ? accessibleTeams : clientGroupsList"
              :query="trimmedSearchKey"
              @resetFilters="selectedUserType = 'all'"/>
            <template v-else-if="selectedUserType === 'all'">
              <div
                v-show="isLoading"
                v-mx-spinner
                class="mx-loading-wrapper"/>
              <template v-if="firstDataComeback && currentUserList.length > 0">
                <div
                  v-show="!shouldShowEmptyState && !isLoading"
                  ref="contact-list-container"
                  class="contact-list-wrapper my-scroll"
                  :class="[isHover?'scroll':'no-scroll']"
                  @mouseenter="isHover = true"
                  @mouseleave="isHover = false">
                  <ul
                    v-a11y-list-keyboard="{listLength: currentUserList.length, winSize: winSize, scrollElement: $refs['contact-list-container']}">
                    <template
                      v-for="(item, index) in currentUserList">
                      <MxContactListItem
                        v-if="!item.isTeam"
                        :key="`${index}${item.id}`"
                        :class="item.id"
                        :contact="item"
                        :contact-id="item.id"
                        :highlight="item.id === currentHighlightIndex"
                        :search-key="trimmedSearchKey.toLowerCase()"
                        @selectUser="handleSelectUser"/>
                      <MxContactListTeamItem
                        v-else
                        :aria-labelledby="`teamItem${item.id}`"
                        :key="`${index}${item.id}`"
                        :class="[item.id === currentHighlightIndex ? 'selected' : 'mx-clickable']"
                        :team="item"
                        :searchKey="trimmedSearchKey"
                        :showClientBadge="item.type === 'GROUP_TYPE_CLIENT_TEAM'"
                        @click="handleSelectTeam"/>
                    </template>
                  </ul>
                  <div v-if="!allDataLoaded">
                    <PagingLoader @next="loadNextPage"/>
                  </div>
                </div>
                <div
                  v-show="shouldShowEmptyState"
                  class="empty-placeholder"
                  tabindex="0"
                  role="status">
                  {{ $t('No_matches_found') }}
                </div>
              </template>
              <template v-else-if="!isLoading">
                <div
                  v-if="trimmedSearchKey"
                  class="empty-placeholder"
                  tabindex="0"
                  role="status">
                  {{ $t('No_matches_found') }}
                </div>
                <div
                  v-else-if="loadContactsFailed"
                  class="load-contacts-failed text-center"
                  role="status">
                  <img :src="genericErrorImage">
                  <div class="failed-title">
                    {{ $t('Something_Went_Wrong_title') }}
                  </div>
                  <div class="failed-subtitle">
                    {{ $t('We_could_not_load_this_content') }}
                  </div>
                  <el-button
                    type="primary"
                    size="small"
                    @click="loadGroupMembers">
                    {{ $t('retry') }}
                  </el-button>
                </div>
                <MxContactsEmptyView v-else/>
              </template>
            </template>
          </div>
          <mx-contact-detail
            v-show="selectedUser.id"
            ref="contactDetail"
            :hideAction="hideQuickActions"
            :selected-user="selectedUser"
            :suggested="suggested"
            class="right-wrapper"
            @viewClientTeam="viewClientTeam"
            @showContactDetail="showContactDetailById"/>
          <MxTeamContactDetail
            v-if="selectedTeam"
            ref="teamDetail"
            :teamId="selectedTeam"
            class="right-wrapper"/>
          <div
            v-show="!selectedUser.id && !selectedTeam"
            class="right-wrapper icon-center">
            <img
              v-if="!isLoading"
              :src="emptyContactsIcon"
              alt>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import {mapActions, mapGetters, mapMutations, mapState} from 'vuex'
import MxContactListItem from './components/MxContactListItem'
import MxContactTeamList from './components/MxContactTeamList'
import MxContactDetail from './components/MxContactDetail'
import MxContactListTeamItem from './components/MxContactListTeamItem'

import _debounce from 'lodash/debounce'
import utils from '@views/common/utils/utils'
import emptyContactsIcon from '@views/theme/src/images/empty/empty-contacts.svg'
import genericErrorImage from '@views/theme/src/images/mep/Generic-Error-Desktop.png'
import {focusTo} from '@views/common/accessibility'
import MxContactClientsList from './components/MxContactClientsList'
import PagingLoader from './components/PagingLoader'
import MxContactInternalList from './components/MxContactInternalList'
import MxContactsEmptyView from './components/MxContactsEmptyView'
import MxTeamContactDetail from './components/MxTeamContactDetail'
import {mergeList} from '@views/common/utils/sort'
import {useInviteClientDialog, useInviteInternalDialog} from '../../invite'
import CreateClientGroup from '@views/contacts/src/components/CreateClientGroup.vue';
import {popupFactory} from '@views/common/useComponent';

const useCreateClientGroup = popupFactory(CreateClientGroup)

export default {
  name: 'MxContactView',
  components: {
    MxContactInternalList,
    PagingLoader,
    MxContactClientsList,
    MxContactDetail,
    MxContactListItem,
    MxContactTeamList,
    MxTeamContactDetail,
    MxContactListTeamItem,
    MxContactsEmptyView
  },
  props: {
    defaultFilterType: {
      type: String,
      default: 'all'  // all, ext, int, team
    },
    autoJump: {
      type: Boolean,
      default: true
    },
    customizedTitle: {
      type: String,
      default: ''
    },
    hideQuickActions: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isHover: false,
      selectedUserType: 'all',
      selectedUser: {},
      selectedTeam: '',
      suggested: false,
      contactId: '',
      relationList: [],
      showBackBtn: false,
      localSearchKey: '',
      currentHighlightIndex: -1,
      //clonedAllTeams: [],
      searchedResult: [],
      isLoading: true,
      checkedItem: [],
      isFocusingInput: false,
      emptyContactsIcon: emptyContactsIcon,
      genericErrorImage: genericErrorImage,
      firstDataComeback: false,
      suggestHasNextPage: false,
      suggestClientList: [],
      clientsFilterType: '',
      subMenuVisible: false,
      selectVisible: false
    }
  },
  computed: {
    ...mapGetters('user', ['currentUserBasicInfo', 'ssoOptions', 'accessibleTeams']),
    ...mapGetters('privileges', ['hideNavBar', 'canAddCustomer', 'canCreateConversation', 'disableUserPresence', 'showOOOOnly', 'canViewAllClients']),
    ...mapGetters('contacts', [
      'contactsList',
      'allDataLoaded',
      'getSearchedContacts',
      'isGettingData',
      'isAllSearchedContactsLoaded',
      'loadContactsFailed',
      'searchKey',
      'clientGroupsList']),
    ...mapGetters('group', ['groupCaps', 'isEnableClientGroup', 'enableClientGroupToInternalUsers']),
    ...mapGetters('application', ['winSize']),
    ...mapState('contacts', ['newAddedUsers']),
    ...mapState('group', ['allTeams']),
    isInternalUser () {
      return this.currentUserBasicInfo.isInternalUser
    },
    showNewBtn () {
      return this.canInviteClient || this.canInviteInternalUser || this.canCreateClientGroup
    },
    trimmedSearchKey () {
      return this.localSearchKey.trim()
    },
    canInviteClient () {
      return this.canAddCustomer && !this.ssoOptions.clientSSOLoginUrl && !this.groupCaps.disable_user_creation && this.canCreateConversation
    },
    canInviteInternalUser () {
      return this.currentUserBasicInfo.isAdmin && !this.groupCaps.disable_user_creation && !this.currentUserBasicInfo.unique_id && !this.ssoOptions.internalSSOLoginUrl
    },
    canCreateClientGroup () {
      return this.enableClientGroupToInternalUsers
    },
    isDetailShowing () {
      return !!this.$route.params.id
    },
    shouldShowEmptyState () {
      return this.currentUserList.length === 0 && !this.isLoading
    },
    currentUserList () {
      const selectedUserType = this.selectedUserType
      if (this.trimmedSearchKey) {
        if (this.selectedUserType === 'all') {
          const filteredTeamList = this.clonedAllTeams.filter(t => utils.hasSubstr(t.name, this.trimmedSearchKey))
          return mergeList(this.searchedResult, filteredTeamList, null, false)
        }
      }
      if (selectedUserType === 'all') {
        if (this.newAddedUsers.length) {
          const mergedList = mergeList(this.contactsList.slice(this.newAddedUsers.length - 1), this.clonedAllTeams, null, !this.allDataLoaded)
          return this.newAddedUsers.concat(mergedList)
        } else {
          return mergeList(this.contactsList, this.clonedAllTeams, null, !this.allDataLoaded)
        }
      } else if (selectedUserType === 'teams') {
        return this.clonedAllTeams.filter(item => item.type === 'GROUP_TYPE_TEAM')
      } else if (selectedUserType === 'accounts') {
        return this.clonedAllTeams.filter(item => item.type === 'GROUP_TYPE_CLIENT_TEAM')
      }
      return []
    },
    showClientSubMenu () {
      return !this.disableUserPresence && !this.showOOOOnly && !this.canViewAllClients
    },
    clonedAllTeams () {
      return mergeList(this.accessibleTeams, this.clientGroupsList, null, false)
    },
    mainTitle () {
      return this.customizedTitle || this.$t('contacts')
    }
  },
  watch: {
    $route (to) {
      const id = to.params.id
      this.applyHighlight(id)
      if (id) {
        if (!to.query.isTeam) {
          this.showContactDetailById(id)
          this.selectedTeam = ''
        } else {
          this.showTeamDetailById(id)
          this.selectedUser = {}
        }
      } else {
        this.selectedUser = {}
        this.selectedTeam = ''
      }
    },
    selectedUserType () {
      this.resetPaginationStatus()
    },
    getSearchedContacts (newValue) {
      // this is because that we defined the searchKey as {key}_INTERNAL_RELATIONUSER in contacts/actions
      if (`${this.trimmedSearchKey}_INTERNAL_RELATIONUSER` === this.searchKey) {
        this.searchedResult = newValue
      }
    },
    isGettingData (newValue) {
      if (this.isLoading && !newValue) {
        this.isLoading = newValue
      }
    }
  },
  created () {
    // this.clonedAllTeams = [...this.accessibleTeams]
    this.selectedUserType = this.defaultFilterType
    this.loadContacts().then(() => {
      this.firstDataComeback = true
      this.contactId = this.$route.params.id || ''
      this.applyHighlight(this.contactId)
      this.isLoading = false
    })
  },
  mounted () {
    const id = this.$route.params.id
    if (!this.$route.query.isTeam) {
      this.showContactDetailById(id)
    } else {
      this.showTeamDetailById(id)
    }
    const filterType = this.$route.query.filter
    if (filterType) {
      this.handleDropdownCommand(filterType)
      // this.clientsFilterType=filterType
    }
    this.$nextTick(() => {
      this.handleClearSearch()
    })
    // MVB-20805
    this.debounceSearchUser = _debounce(function () {
      // this is because that we defined the searchKey as {key}_INTERNAL_RELATIONUSER in contacts/actions
      if (`${this.trimmedSearchKey}_INTERNAL_RELATIONUSER` !== this.searchKey) {
        this.isLoading = true
      }
      this.searchContacts({
        searchKey: this.trimmedSearchKey,
        isInternal: true,
        includeRelation: true,
        includeSuggested: true
      })
    }, 300)
  },
  beforeDestroy () {
    this.resetSearchKey()
    this.resetNewAdded()
  },
  methods: {
    ...mapActions('contacts', [
      'loadContacts',
      'loadNextPageContacts',
      'searchContacts',
      'initContacts',
      'resetSearchKey',
      'resetPaginationStatus',
      'loadSearchPagingContacts',
      'resetSearchContacts']),
    ...mapMutations('contacts', ['setIsSearching', 'resetNewAdded']),
    ...mapActions('group', ['getGroupMember']),
    onItemClick (item) {
      if (!this.autoJump) {
        this.$emit('on-select', item)
      }
    },
    handleClearSearch () {
      this.localSearchKey = ''
      const el = this.$refs.searchBar.$el.querySelector('input')
      focusTo(el)
    },
    applyHighlight (id) {
      this.currentHighlightIndex = id
    },
    loadNextPage () {
      if (this.trimmedSearchKey) {
        if (this.isAllSearchedContactsLoaded) {
          return
        }
        const n = this.searchedResult.length
        const startSequence = n ? this.searchedResult[n - 1].sequence : 0
        this.loadSearchPagingContacts({
          startSequence,
          isInternal: true,
          includeRelation: true,
          includeSuggested: true,
          query: this.trimmedSearchKey,
        })
      } else {
        if (this.allDataLoaded) {
          return
        }
        const includeClients = this.selectedUserType !== 'int'
        this.loadNextPageContacts({
          includeRelation: includeClients,
          includeSuggested: includeClients
        })
      }
    },
    handleSelectUser () {
      focusTo(this.$refs.contactDetail, {delay: 500})
    },
    handleSelectTeam () {
      focusTo(this.$refs.teamDetail, {
        delay: 500
      })
    },
    searchUser () {
      if (this.trimmedSearchKey && this.selectedUserType === 'all') {
        this.debounceSearchUser()
      }
    },
    doInvite (command) {
      switch (command) {
        case 'client':
          const [showInviteClientDialog] = useInviteClientDialog()
          showInviteClientDialog()
          break;
        case 'internal':
          const [showInviteInternalDialog] = useInviteInternalDialog()
          showInviteInternalDialog()
          break
        case 'clientGroup':
          const [show] = useCreateClientGroup()
          show()
          break
      }
    },
    switchTab (val) {
      this.clientsFilterType = ''
      this.$router.push({name: 'contact', params: {id: null}})
      //this.clonedAllTeams = [...this.accessibleTeams]
      if (this.trimmedSearchKey) {
        if (val === 'all') {
          this.setIsSearching(true)
        } else {
          this.resetSearchContacts()
        }
        this.searchUser()
      }
      if (this.currentUserList.length > 0) {
        this.$refs['contact-list-container'] && (this.$refs['contact-list-container'].scrollTop = 0)
      }
    },
    showContactDetailById (id) {
      if (id) {
        const currentUserList = this.currentUserList
        const selectedContactIndex = currentUserList.findIndex((u) => {
          return u.id === id
        })
        if (selectedContactIndex !== -1) {
          this.showContactDetail(currentUserList[selectedContactIndex], false)
        } else {
          this.getGroupMember({
            id: id
          }).then(member => {
            if (member) {
              this.showContactDetail(member, false)
            } else {
              this.navBackToList()
            }
          }).catch((err) => {
            if (err.isNotFoundError) {
              this.showContactDetail({id: 'null'})
            } else {
              this.showContactDetail({id: 'error'})
            }
          })
        }
      } else {
        this.showContactDetail({})
      }
    },
    showTeamDetailById (id) {
      this.selectedTeam = id
    },
    showContactDetail (user, suggested = false) {
      if (user && (user.title === user.phone_number || user.title === user.display_phone_number)) {
        user.title = utils.getFormattedPhoneNumber(user.title)
      }
      this.selectedUser = user || {}
      this.suggested = suggested
    },
    navBackToList () {
      this.$router.push({params: {id: ''}})
    },

    // get checked filted item
    search (val) {
      //console.log(val)
    },
    loadGroupMembers () {
      this.loadContacts().then(() => {
        this.applyHighlight(this.contactId)
      })
    },
    handleDropdownCommand (command) {
      this.clientsFilterType = command
      this.selectedUserType = command
    },
    handleRightKeyEvent () {
      if (this.showClientSubMenu) {
        const clientsOpt = document.querySelector('li[data-ta=contact_clients]')
        if (clientsOpt.className.indexOf('is-focus') > 0) {
          this.$refs.clientsFilterDropdown.show()
        }
      }
    },
    handleLeftKeyEvent () {
      if (this.showClientSubMenu) {
        const clientsOpt = document.querySelector('li[data-ta=contact_clients]')
        if (clientsOpt.className.indexOf('is-focus') > 0) {
          this.$refs.clientsFilterDropdown.hide()
          this.$refs.category.$el.querySelector('input').focus()
        }
      }
    },
    handleSubmenuEnterKeyEvent () {
      this.$refs.category.handleClose()
      this.$refs.category.$el.querySelector('input').focus()
    },
    viewClientTeam (team) {
      this.$router.push({
        name: 'contact',
        params: {
          id: team.id
        },
        query: {
          isTeam: true
        }
      })
    },
    resetFilters () {
      this.selectedUserType = 'all'
      this.clientsFilterType = ''
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.contact-subheader {
  box-shadow: 0px 1px 0px 0px $mx-color-var-fill-tertiary;
  height: 52px;
  display: flex;

  > div {
    flex-grow: 1;
  }

  .actions {
    justify-content: flex-end;
    align-items: center;
    display: flex;
    padding-right: 16px;
  }
}

.my-scroll {
  &.no-scroll {
    overflow-y: hidden;
  }

  &.scroll {
    overflow-y: auto;
  }

}

.main-section {
  height: 100%;
  padding: 0;
  overflow-x: auto;

  .el-container {
    min-width: calc(1024px - 205px);
    height: 100%;
  }
}

.margin-right1 {
  margin-right: 1px;
}

.el-container {
  .mx-contacts-title {
    line-height: 20px;
    font-size: 14px;
    font-weight: 600;
    height: 52px;

    padding: 17px 20px;
  }

  .el-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 23px 6px 13px;
    height: auto !important;


    .el-menu-item.is-active {
      border: 1px solid $mx-color-blue;
      border-radius: 20px;
    }

    .actions {
      display: flex;

      .requests {
        padding: 6px 6px;
        border: 2px solid $mx-color-gray-08;
        margin-right: 20px;
        border-radius: 6px;
        position: relative;
        color: $mx-color-gray-40;
        background-color: $mx-color-gray-08;

        span:last-child {
          padding-left: 10px;
        }

        .red-indicator {
          width: 10px;
          height: 10px;
          left: 20px;
          top: 10px;
          position: absolute;
          border-radius: 50%;
          background-color: $mx-color-red;
          border: 2px solid $mx-color-gray-08;
        }
      }
    }
  }

  .el-main {
    padding: 0;
    height: 100%;
    overflow: hidden;

    .mx-container-wrapper {
      height: 100%;
      position: relative;

      .contacts-category {
        text-align: center;
        height: 38px;

        .el-button {
          text-transform: uppercase;

          + .el-button {
            margin-left: 8px;
          }
        }

        .el-button--mini {
          padding: 3px 11px;
          border-radius: 16px;
        }

        /*.el-button--default {
          color: $mx-color-gray-40;
          background-color: rgb(240, 242, 245);
          border-color: rgb(240, 242, 245);

          &:hover {
            color: mix(#ffffff, $mx-color-gray-40, 15%);
            background-color: mix(#ffffff, rgb(240, 242, 245), 15%);
            border-color: mix(#ffffff, rgb(240, 242, 245), 15%);
          }

          &:active {
            color: mix(#000000, $mx-color-gray-40, 15%);
            background-color: mix(#000000, rgb(240, 242, 245), 15%);
            border-color: mix(#000000, rgb(240, 242, 245), 15%);
          }
        }*/
      }
    }

    .contact-list-wrapper {
      overflow-x: hidden;
      flex: 1;

      .mx-contact-list {
        margin: 0;
        padding: 0;

        ul {
          height: 100%;
        }
      }
    }


  }

  .mx-container-wrapper {
    display: flex;
    padding-top: 1px;

    a.btn {
      padding: 10px 16px;

      i {
        font-size: 14px;
      }
    }
  }

  .mx-search-with-icon {
    margin: 12px 16px;
    display: flex;
    flex: 0 0 auto;
    gap: 12px;

    ::v-deep .el-input {
      input {
        height: 28px;
        border-radius: 28px;
        background-color: $mx-color-var-fill-quaternary;
      }

      &.is-focusing {
        /* width: 374px; */
      }

      .el-input__inner {
        border-width: 2px;
        border-color: transparent;
        padding: 4px 30px 4px 32px;
      }

      .el-input__suffix {
        font-size: 16px;
        color: $mx-color-var-label-secondary;
      }

      .el-input__icon {
        line-height: 25px !important;
      }

      input.is-keyboard-focus {
        outline: none !important;
        border-style: solid;
        border-width: 2px;
      }
    }

    .el-select {
      width: 114px;
      flex: 0 0 auto;

      ::v-deep .el-input__inner {
        text-overflow: ellipsis;
        padding-left: 32px;
        height: 28px;
        overflow: hidden;
        padding-top: 4px;
        padding-bottom: 4px;
        text-align: left;
      }

      ::v-deep .el-select__caret {
        display: block;
        padding-bottom: 0;
        color: $mx-color-var-label-secondary;
      }
    }

    ::v-deep .el-input__prefix {
      left: 8px;
      font-size: 16px;
      line-height: 24px;
      color: $mx-color-var-label-secondary;
    }

    ::v-deep .el-input__suffix i {
      padding-bottom: 4px;
    }
  }

  .mx-loading-wrapper {
    position: absolute;
    left: 50%;
    top: 50%;
  }

  .mx-next-wrapper {
    height: 25px;
    width: 100%;
    position: relative;
    bottom: -15px;
  }

  .left-wrapper {
    flex: 1 1 33%;
    position: relative;
    background-color: $mx-color-white;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .right-wrapper {
    flex: 1 1 67%;
    background-color: $mx-color-white;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.06);

    ::v-deep {
      .managers-label {
        div + div {
          margin-right: 16px;
        }
      }
    }
  }


  .icon-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.empty-placeholder {
  text-align: center;
  padding-top: 41px;
  color: $mx-color-var-label-secondary;
}

.load-contacts-failed {
  overflow-y: auto;
  height: calc(100% - 44px);

  img {
    width: 300px;
    margin-top: 116px;
  }

  .failed-title {
    font-size: 16px;
    line-height: 24px;
    margin: 6px 73.5px 7px;
  }

  .failed-subtitle {
    color: $mx-color-var-label-secondary;
    margin: 0 73.5px 27px;
  }

  .el-button {
    min-width: 102px;
    max-width: 90%;
    margin: auto;
  }
}

::v-deep .list-group-item.is-keyboard-focus {
  outline-offset: -1px;
}

::v-deep .clients-submenu-item {
  line-height: 40px;
  padding-right: 16px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.active i.micon-mep-check-mark {
    display: block;
    margin-top: 0;
    font-size: 24px;
  }
}

@media screen and (max-width: 1228px) {
  .right-wrapper {
    ::v-deep .contact-detail-right-container {
      padding: 0 66px;
    }
  }
}
</style>
