<template>
  <BaseModal
    :visible.sync="visibleProxy"
    :append-to-body="true"
    :showClose="false"
    :modal-option="{
      width: '384px'
    }"
    class="audit-thread-view"
    ref="auditThread">
    <section
      slot="content"
      class="flow-panel">
      <template v-if="showMainThreadPage">
        <div
          class="flow-detail"
          @keydown.tab="focusableElementLoop($event)">
          <header
            ref="header"
            :class="['mx-flex-container', showCreateInfo ? '' : 'action-wrapper']">
            <div
              v-show="showCreateInfo"
              class="left-area">
              <div class="title mx-text-c1 mx-ellipsis">
                {{ config.headerTitle ? config.headerTitle : creatorName }}
              </div>
              <div
                v-if="!config.hideHeaderSubtitle"
                class="time mx-text-c4">
                {{ createdTime }}
              </div>
            </div>
            <div v-show="backTitle">
              <span class="micon-left-arrow-new" role="button" style="cursor: pointer;"
                @click="$emit('goBack')"></span>
              <span class="mx-text-c1">{{ backTitle }}</span>
            </div>
            <div class="right-area">
              <template
                v-if="!config.hideMoreOption">
                <el-dropdown
                  v-if="baseObject.type !== 'TODO'"
                  trigger="click"
                  class="mx-hover-hide"
                  @visible-change="visibleChangeEvent"
                  @command="moreOptionDropdownSelect">
                  <span
                    v-show="moreOpts.length"
                    v-mx-ta="{ page: 'thread', id: `dropdown_option_toggle`}"
                    :title="$t('more_options')"
                    :data-original-title="$t('more_options')"
                    class="mx-clickable el-dropdown-link">
                    <i class="micon-more"/>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-for="(item,index) in moreOpts"
                      :key="index"
                      :data-ta="item.dataTa"
                      :class="item.dropdownClassName"
                      :command="item.commandEvent">
                      <span :class="item.className">{{ item.text }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
              <span
                v-mx-ta="{ page: 'thread', id: `close`}"
                class="mx-clickable"
                role="button"
                :aria-label="$t('close')"
                :class="[dataObjectInclude ? 'right-close-button':'right-close-button-non-scope']"
                tabindex="0"
                @keydown.enter="closeThread"
                @click="closeThread">
                <i :class="closeIcon" />
              </span>
            </div>
          </header>
          <div
            class="flow-detail-container">
            <div class="flow-body" ref="body">
              <div
                v-show="isLoading"
                v-mx-loading-spinner="{width: '30px', height: '30px', margin: '19px auto'}"
                class="loading-wrapper"/>
              <div
                v-if="loadingFailed"
                class="loading-failed-wrap mx-text-b2 text-center">
                <span role="status">{{ $t('load_failed') }}</span>
                <span
                  tabindex="0"
                  class="mx-branding-text-action mx-clickable mx-padding-left-xxs"
                  @click="readThread()"
                  @keypress.enter="readThread()">
                  <span
                    class="mx-semibold"
                    style="padding-right: 2px;">{{ $t('retry') }}</span>
                  <i class="micon-mep-rotate"/>
                </span>
              </div>
              <template
                v-else-if="!isLoading">
                <div
                  role="alert"
                  v-if="config.isNotStarted"
                  class="alert-info-container">
                  <i class="micon-mep-warning"></i>
                  <div class="mx-text-c3 mx-margin-left-xs">{{ existRoleInCurrentStep? $t('Role_need_to_be_set_to_proceed') : $t('Action_not_started') }}</div>
                </div>
                <component
                  :is="baseObjectComponent"
                  v-if="baseObjectComponent"
                  :key="baseObject.sequence"
                  :stepModel="currentStep"
                  :base-object="baseObject"
                  :binder-obj="binderObj"
                  :boardBriefViewModel="boardBriefViewModel"
                  :config="baseObjectConfig"
                  :flowInfos="flowInfos"
                  :selectUserDirect="true"
                  :hideAttachmentDownload="shouldHideAttachmentDownload"

                  @close="$emit('close')"
                  @viewPage="viewPageDetails"
                  @toMeetDetail="payload=> $emit('toMeetDetail',payload)"
                  @viewDecisionBranch="viewDecisionBranch" />
              </template>
            </div>
            <div class="flow-message">
              <div
                  class="activity-container"
                  @keydown="handleKeyDownForFeed($event,getNextFocusId(),getPrevFocusId())">
                <ul
                    v-if="activities"
                    role="feed"
                    class="activities">
                  <component
                    v-for="item in activities"
                    :is="getActivityType(item.Action, item.BaseType)"
                    :key="item.sequence"
                    :activity="item"
                    :ctrl-key="ctrlKey"
                    :binderObj="binderObj"
                    :config="baseObjectConfig"

                    @viewPage="$emit('viewPage', $event)"/>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </template>
      <AuditFlowDBBranchView
        v-else-if="isDecisionBranchVisible"
        :expirationDate="decisionBranchParams.expirationDate"
        :decisionOption="decisionBranchParams.decisionOption"
        :boardBriefViewModel="decisionBranchParams.boardBriefViewModel"
        :title="decisionBranchParams.title"
        @closeBranchDetail="closeBranchDetail"
        @viewPage="viewPageDetails"/>
    </section>
  </BaseModal>
</template>

<script>
import { useStore, useI18n } from '@views/common'
import utils from '@views/common/utils/utils'
import {
  focusableElementLoop,
  getLastFocusableElement
} from '@views/common/accessibility'
import { handleKeyDownForFeed } from '@views/common/accessibility/feed'
import moreActionInfo from '@views/thread/src/mixins/moreActionInfo'
import { computed, defineComponent, onMounted, ref,watch } from '@vue/composition-api'
import commentActivity from '@views/thread/src/activities/CommentActivity'
import commonActivity from '@views/thread/src/activities/CommonActivity'
import { visibleMixin } from '@views/common/components/modals/mixins'
import { ObjectUtils, MxConsts } from '@commonUtils'
import { transactionActionComponent, transactionActionViewAttachment } from '@views/common/utils/transactionActionUtils'
import {toBoardBriefViewModel} from '@views/common/utils/workflow'
import { useDDRSupportStore } from '@views/stores/ddrSupport'
import { UserObjectType } from '@model/board/boardUserViewModel'
import { useBreadCrumbsStore } from '@views/stores/breadCrumbs';
import { storeToRefs } from 'pinia'

export default defineComponent({
  name: 'AuditThreadView',
  components: {
    commentActivity,
    commonActivity,
    commentObject: () => utils.retry(import(/* webpackChunkName: "thread.comment" */ '@views/thread/src/baseObjects/CommentObject')),
    fileObject: () => utils.retry(import(/* webpackChunkName: "thread.file" */ '@views/thread/src/baseObjects/FileObject')),
    meetObject: () => utils.retry(import(/* webpackChunkName: "thread.meet" */ '@views/thread/src/baseObjects/MeetObject')),
    pageObject: () => utils.retry(import(/* webpackChunkName: "thread.page" */ '@views/thread/src/baseObjects/PageObject')),
    signObject: () => utils.retry(import(/* webpackChunkName: "thread.sign" */ '@views/thread/src/baseObjects/SignObject')),
    todoObject: () => utils.retry(import(/* webpackChunkName: "thread.todo" */ '@views/thread/src/baseObjects/TodoObject')),

    DocuSignObject: () => utils.retry(import(/* webpackChunkName: "thread.transaction.docusign" */ '@views/thread/src/baseObjects/DocuSignObject')),
    ApprovalAction: () => utils.retry(import(/* webpackChunkName: "thread.approval" */ '@views/thread/src/component/baseObjects/ApprovalAction')),
    AcknowledgeAction: () => utils.retry(import(/* webpackChunkName: "thread.acknowledge" */ '@views/thread/src/component/baseObjects/AcknowledgeAction')),
    FileRequestAction: () => utils.retry(import(/* webpackChunkName: "thread.fileRequest" */ '@views/thread/src/component/baseObjects/FileRequestAction')),
    TodoTransactionAction: () => utils.retry(import(/* webpackChunkName: "thread.todoTransaction" */ '@views/thread/src/component/baseObjects/TodoTransactionAction')),
    TimeBookingAction: () => utils.retry(import(/* webpackChunkName: "thread.timeBooking" */ '@views/thread/src/component/baseObjects/TimeBookingAction')),
    FormRequestAction: () => utils.retry(import(/* webpackChunkName: "thread.form" */ '@views/thread/src/component/baseObjects/FormRequestAction')),
    LaunchWebAppAction: () => utils.retry(import(/* webpackChunkName: "thread.launchWebApp" */ '@views/thread/src/component/baseObjects/LaunchWebAppAction')),
    JumioAction: () => utils.retry(import(/* webpackChunkName: "thread.jumio" */ '@views/thread/src/component/baseObjects/JumioAction')),
    IntegrationAction: () => utils.retry(import(/* webpackChunkName: "thread.integration" */ '@views/thread/src/component/baseObjects/IntegrationAction')),
    GenericAction: () => utils.retry(import(/* webpackChunkName: "thread.generic" */ '@views/thread/src/component/baseObjects/GenericAction')),
    DecisionAction: () => utils.retry(import(/* webpackChunkName: "thread.Decision" */ '@views/thread/src/component/baseObjects/DecisionAction')),
    WaitAction: () => utils.retry(import(/* webpackChunkName: "thread.waitAction" */ '@views/thread/src/component/baseObjects/WaitAction')),
    AuditFlowDBBranchView: () => utils.retry(import(/* webpackChunkName: "audit.AuditFlowDBBranchView" */ './AuditFlowDBBranchView')),

  },
  mixins: [moreActionInfo, visibleMixin],
  props: {
    threadObject: {
      type: Object,
      required: true
    },
    config: {
      type: Object,
      default: () => ({})
    },
    binderObj: {
      type: Object,
      default: () => ({})
    },
    backTitle:{
      type: String,
      default: ''
    },
    boardBriefViewModel:{
      type: Object,
      default: () => ({})
    }
    
  },
  setup (props, {root,emit}) {
    let body = ref(null)
    let auditThread = ref(null)
    let baseObject = ref({})
    let activities = ref([])
    const ctrlKey = Date.now().toString()
    let objectConfig = ref({})
    let isLoading = ref(true)
    let loadingFailed = ref(false)
    let isDecisionBranchVisible = ref(false)
    let decisionBranchParams = ref({})
    const chatAuditStore = useStore('chatAudit')
    const { clearBreadCrumbsData } = useBreadCrumbsStore()
    let baseObjectComponent = computed(() => {
      let componentName = ''
      const { type, transactionType, sub_type } = baseObject.value || {}
      switch (type) {
        case 'TODO':
          componentName = 'todoObject'
          break
        case 'COMMENT':
          componentName = 'commentObject'
          break
        case 'MEET':
          componentName = 'meetObject'
          break
        case 'PAGE':
          componentName = 'pageObject'
          break
        case 'FILE':
          componentName = 'fileObject'
          break
        case 'SIGNATURE':
          componentName = 'signObject'
          break
        case 'TRANSACTION':
          if(transactionType === MxConsts.TransactionType.TRANSACTION_TYPE_DOCUSIGN) {
            componentName = 'DocuSignObject'
          } else {
            componentName = transactionActionComponent(transactionType, sub_type)
          }
          // componentName = 'transactionObject'
          break
        case 'EMAIL':
          componentName = 'pageObject'
          break
      }
      return componentName
    })
    let moreOpts = computed(() => {
      let moreOpts = []
      if (baseObjectComponent.value === 'fileObject') {
        const { computed } = moreActionInfo
        if (computed.canDownloadFile) {
          moreOpts.push({
            dropdownClassName: 't-bdf_file_download',
            text: root.$t('download'),
            commandEvent: 'downloadFile',
            dataTa: 'fileObject_download',
            className:''
          })
        }
      }
      return moreOpts
    })
    const closeIcon = computed(() => {
      if (props.config.closeIcon) {
        return props.config.closeIcon
      } else {
        const { computed } = moreActionInfo
        return computed.dataObjectInclude ? 'micon-close close-button' : 'micon-close'
      }
    })
    const baseObjectConfig = computed(() => {
      return Object.assign({}, {isAudit: true}, props.config)
    })
    const flowInfos = computed(() => {
      return {
        stepSequence: props.config?.stepSequence,
        flowSequence: props.binderObj.workflows?.[0].sequence
      }
    })
    const flowStepModel = computed(() => {
      const stepSequence = flowInfos.value.stepSequence
      const workflowModel = props.boardBriefViewModel.workflow || {}
      const stepModel = workflowModel.steps?.find(step => step.sequence === stepSequence)
      return stepModel
    })

    const alertMsg = computed(() => {
      if (existRoleInCurrentStep.value) {
        return root.$t('Role_need_to_be_set_to_proceed')
      } else {
        if (flowStepModel.value?.hasInvalidDDRVariable) {
          return root.$t('not_started_step_referenced_data_no_longer_valid')
        } else {
          return root.$t('Action_not_started')
        }
      }
    })

    onMounted(() => {
      setDialogStyle()
    })
    const closeThread = () => {
      clearBreadCrumbsData()
      emit('close')
    }
    const getNextFocusId = () => {
      return auditThread.value.$el.querySelector('span[data-ta=thread_close]')
    }
    const getPrevFocusId = () => {
      return getLastFocusableElement(body.value.$el)
    }
    const setDialogStyle = () => {
      let binderView = document.querySelector('#binderChatView .el-dialog')
      if (binderView) {
        let clientRect = binderView.getBoundingClientRect()
        // 384 is thread dialog width
        let destLeft = clientRect.right - 384
        let left = destLeft - (document.body.clientWidth/2 - 384/2)
        auditThread.value.$el.querySelector('.el-dialog').style.left = `${left}px`
      }
    }

    const setMeetObject = (baseObject) => {
      let session = props.binderObj.sessions.find(item => item.sequence === baseObject.sequence)
      if (session.is_deleted) {
        baseObject.is_deleted = true
        chatAuditStore.dispatch('readAuditBoard', baseObject.session.board_id).then((meetObject) => {
          const meetStore = useStore('meet')
          meetStore.commit('setMeetObject', meetObject)
        })
      }
    }

    const setDDRSupportFilterInfo = () => {
      const ddrSupportStore = useDDRSupportStore()
      ddrSupportStore.setDDRSourceFilter({
        contextId: {boardId: props.binderObj.id},
        filterInfo: {curStepClientUuid: flowStepModel.value.clientUuid }
      })
    }

    function init() {
      isLoading.value = true
      loadingFailed.value = false
      let formatedMeetParams = null
      // when open meet from timeBooking, if meet belong to the binder, should open meet object and activity, else just show meet
      if(props.threadObject?.session){
        let chatAuditStore = useStore('chatAudit')
        baseObject.value = {session: props.threadObject.session, type: 'MEET'}
        activities.value = []
        let binderSessions = chatAuditStore.currentRawBinderInfo.value.sessions
        let aimedSession = binderSessions && binderSessions.find(bSession=>{
          if(bSession.session){
            let {board_id, session_key} = props.threadObject.session
            return bSession.session.board_id === board_id
          }else{
            return null
          }

        })
        if(!aimedSession){
            chatAuditStore.dispatch('readAuditBoard', props.threadObject.session.board_id).then((meetObject) => {
              const meetStore = useStore('meet')
              meetStore.commit('setMeetObject', meetObject)
              baseObject.value = {session: props.threadObject.session, type: 'MEET'}
              activities.value = []
            }).finally(()=>{
              isLoading.value = false
              loadingFailed.value = false
            return
            })
            return

        }else{
          formatedMeetParams = {
            baseObject:{
              sequence:aimedSession.sequence,
              parentSequence: 0,
              spath: `sessions[sequence=${aimedSession.sequence}]`,
              type: 'MEET'
            }
          }
        }

      }
      chatAuditStore.dispatch('getThreadInfo', formatedMeetParams||props.threadObject).then((thread) => {
        if (thread.baseObject.type === 'MEET') {
          setMeetObject(thread.baseObject)
        }
        if (props.binderObj.isWorkflow) {
          thread.baseObject = setBaseObject(thread.baseObject)
          if(thread.baseObject.transactionType === MxConsts.TransactionType.TRANSACTION_TYPE_AWAIT) {
            thread.baseObject.awaitOption = currentStep.value.awaitOption
          } 
          setDDRSupportFilterInfo()
        }
        baseObject.value = thread.baseObject
        if (thread.activities) {
          activities.value = thread.activities.filter(activity => {
            if (activity.Action === 'REPLY' && activity.BaseType === 'TODO') {
              if (activity.relatedObject && activity.relatedObject.is_deleted) {
                return false
              }
            }
            if (activity.Action === 'DELETE_FILE_REPLY' && ObjectUtils.getByPath(activity, 'baseObject.attachments.0.is_original_deleted')) {
              return false
            }
            if (activity.actor && activity.actor.displayDeletedUser) {
              activity.actor.name = `[${useI18n.t('Deleted_User')}]`
            }
            if (activity.BaseType === 'TODO' && activity.Action === 'ASSIGN' && activity.created_time === activity.baseObject.created_time){
              return false
            }
            return true
          }).sort((a, b) => a.created_time - b.created_time)
        }
        isLoading.value = false
        loadingFailed.value = false
      }).catch(() => {
        isLoading.value = false
        loadingFailed.value = true
      })
    }

    const setBaseObject = (baseObject) => {
      let result = baseObject
      const assignees = props.config.assignees
      if (assignees) {
        result = {
          ...baseObject,
          ...props.config.baseObject
        }
        if (baseObject.type === 'TODO') {
          result.assignee = assignees[0]
          objectConfig.value.disableDueDate = true
          if (props.config.dueDateText === root.$t('no_due_date')) {
            result.dueDate = 0
          }
        } else if (baseObject.type === 'SIGNATURE') {
          for (let index in baseObject.signees) {
            let signee = baseObject.signees[index]
            let assignee = assignees[index]
            if (assignee) {
              result.signees[index] = {
                ...signee,
                name: assignee.name,
                avatar: assignee.avatar,
                isCancel: props.config.isCanceled,
                displayDeletedUser: assignee.displayDeletedUser,
                id: assignee.id,
                isInternalUser: assignee.isInternalUser
              }
            }
          }
          if (props.config.isNotStarted) {
            if (baseObject.sign_by_order === false) {
              objectConfig.value.disableActionBtn = false
            } else {
              objectConfig.value.disableActionBtn = false
            }
            result.isMyTurn = objectConfig.value.disableActionBtn
          }
        } else {
          let steps = result.steps
          if (steps) {
            for (let index in steps) {
              let step = steps[index]
              let assignee = assignees[index]
              if (assignee) {
                step.assignee = assignee
              }
            }
            if (props.config.isNotStarted) {
              result.isMock = true
              result.currentStep = {
                actions: steps[0].actions
              }
              result.isMyTurn = false
            }
          }
        }
      } else {
        if (baseObject.type === 'TODO') {
          objectConfig.value.disableDueDate = true
        }
      }
      if(props.config?.custom_data){
        result.custom_data = props.config.custom_data
      }

      let attachments = result.attachments || []
      const ddrAttachments = flowStepModel.value?.ddrAttachments
      if (ddrAttachments?.length) {
        attachments = ddrAttachments.concat(attachments)
        if (attachments.length) {
          result.attachments = attachments
        }
      }

      return result
    }

    function getActivityType (action, baseType) {
      let componentName = ''
      if (baseType === 'WORKFLOW') {
        return 'commentActivity'
      }
      switch (action) {
        case 'REPLY':
        case 'DELETE':
        case 'REASSIGN':
        case 'RESEND_REMINDER':
        case 'ATTACHMENT':
        case 'STEP_SUBMIT':
        case 'STEP_REOPEN':
        case 'FILE_REPLY':
        case 'EXPIRATION_UPDATE':
        case 'EXPIRATION_DATE_ARRIVE':
        case 'UPDATE_REOPEN':
        case 'UPDATE_EDITING':
        case 'UPDATE_READY':
        case 'STEP_READY':
        case 'MARK_AS_COMPLETED':
          componentName = 'commentActivity'
          break
        default:
          componentName = 'commonActivity'
          break
      }
      if ((action === 'UPDATE' && baseType === 'TRANSACTION') || (baseType === 'SIGNATURE' && action !== 'DELETE_FILE_REPLY')) {
        componentName = 'commentActivity'
      }
      if(action === 'REOPEN' && (baseType === 'TRANSACTION' || baseType === 'SIGNATURE')){
        componentName = 'commentActivity'
      }
      let TodoActions = ['COMPLETE','REOPEN','ASSIGN','DUE_DATE','UPDATE','DUE_DATE_ARRIVE'] //REPLY ATTACHMENT ,'UPDATE'
      if(TodoActions.includes(action) && baseType === 'TODO'){
        componentName = 'commentActivity'
      }
      if (action === 'DELETE' && baseType === 'FILE') {
        componentName = 'commonActivity'
      }
      if (action === 'CREATE' && baseType === 'TRANSACTION') {
        componentName = 'commentActivity'
      }
      return componentName
    }
    init()

    watch(() => props.threadObject, (newThread,oldThread) => {
      if (newThread.binderId !== oldThread.binderId || newThread.baseObject.spath !== oldThread.baseObject.spath || newThread.baseObject.sequence !== oldThread.baseObject.sequence) {
        init()
      }
    })

    const currentStep =computed(() => {
      const boardBriefViewModel = toBoardBriefViewModel(props.binderObj)
      const stepSequence = flowInfos.value.stepSequence
      const stepInfo = boardBriefViewModel.workflow?.steps?.find(step => step.sequence === stepSequence)
      if (baseObject.value.editor) {
        return {
          ...stepInfo,
          editor: baseObject.value.editor
        }
      }
      return stepInfo || {}
    })

    const existRoleInCurrentStep = computed(()=>{
      if(currentStep.enablePreparation && currentStep.editor?.isRole) {
        return true
      }

      if(props.config?.assignees){
        return props.config.assignees.findIndex(s=>{
          return s?.isAssigneeRole
        }) >= 0
      }else{
        // MV-17305 For started action
        let hasRoleAssignee = false
        let hasRoleMeetingHost = false 
        if(props.currentStep.subSteps){
          hasRoleAssignee = (props.currentStep.subSteps || []).findIndex(ss => {
            return ss && ss.assignee && ss.assignee.isRole && !ss.assignee.assigneeUser
          }) >= 0
        }
        if(props.currentStep.meetRequestOption){
          const meetingHost = props.currentStep.meetRequestOption.host
          hasRoleMeetingHost = meetingHost && meetingHost.objectType === UserObjectType.Role && !meetingHost.assigneeUser
        }
        return hasRoleAssignee || hasRoleMeetingHost
      }
    })

    const viewPageDetails = (att, optionsMenu) => {
      if(baseObject.value?.type === 'TRANSACTION') {
        const { baseInfo, menus } = transactionActionViewAttachment({
          attachment: att, 
          baseObject: baseObject.value, 
          config: baseObjectConfig.value,
          flowInfos: flowInfos.value
        }, optionsMenu)
        
        emit('viewPage', baseInfo, menus)
      } else {
        emit('viewPage', att)
      }
    }

    const showMainThreadPage = computed(()=>{
      return !isDecisionBranchVisible.value
    })

    const viewDecisionBranch = (payload)=>{
      decisionBranchParams.value = payload
      isDecisionBranchVisible.value = true
    }
    const closeBranchDetail = ()=>{
      isDecisionBranchVisible.value = false
    }

    const shouldHideAttachmentDownload = computed(() => {
      return false
    })

    return {
      body,
      auditThread,
      isLoading,
      loadingFailed,
      baseObject,
      closeIcon,
      moreOpts,
      flowInfos,
      baseObjectConfig,
      baseObjectComponent,
      focusableElementLoop,
      currentStep,
      closeThread,
      ctrlKey,
      activities,
      getActivityType,
      handleKeyDownForFeed,
      getPrevFocusId,
      getNextFocusId,
      readThread: init,
      existRoleInCurrentStep,
      alertMsg,
      viewPageDetails,
      showMainThreadPage,
      isDecisionBranchVisible,
      decisionBranchParams,
      viewDecisionBranch,
      closeBranchDetail,
      shouldHideAttachmentDownload
    }
  }
})
</script>

<style lang="scss" scoped>
.el-dialog__wrapper {
  ::v-deep {
    .el-dialog {
      overflow: hidden;
      left: 20%;
      min-height: 350px;
      height: calc(100% - 144px);
      border-radius: 0 6px 6px 0;
    }
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      padding: 0;
      height: 100%;
    }
    .flow-panel {
      .mx-audio-control {
        cursor: pointer;
        text-align: center;

        i {
          border-radius: 50%;

          &.micon-play {
            color: $mx-color-var-bg-primary;
          }

          &.micon-stop {
            background: $mx-color-var-bg-primary;
          }
        }

        .paused {
          display: inline;
        }

        .playing {
          display: none;
        }

        &.playing {
          .paused {
            display: none;
          }

          .playing {
            display: inline;
          }
        }
      }

      .mx-audio-progress {
        $barColor: $mx-color-var-fill-primary;
        padding-top: 10px;

        .audio-progress-bar {
          width: 100%;
          height: 10px;
          border-radius: $border-radius-large;
          border: 1px solid $barColor;

          > div {
            height: 100%;
            background-color: $mx-color-var-label-secondary;
            border-radius: $border-radius-large;

            .transition-default {
              -webkit-transition: all .3s ease-in-out;
              transition: all .3s ease-in-out;
            }
          }
        }
      }

      .mx-audio-time {
        padding-top: 5px;
      }
    }
    .flow-detail {
      overflow: hidden;
      height: 100%;
      display: flex;
      flex-direction: column;

      > header {
        flex: 0 0 auto;
        border-bottom: 1px solid $mx-color-var-fill-tertiary;
        display: flex;
        align-items: center;
        height: 52px;
        padding-left: 20px;
        padding-right: 16px;
        > :first-child {
          flex: 1 1 auto !important;
        }
        .left-area {
          > span {
            margin-right: $mx-spacing-sm;
          }

          flex: 1 1 auto;
          display: flex;
          flex-direction: column;
          max-width: calc(100% - 74px);

          .time {
            color: $mx-color-var-label-secondary;
            margin-top: -2px;
          }
        }

        .right-area {
          flex: 1 1 auto;
          display: flex;
          justify-content: flex-end;
          // margin-left: auto;

          .right-close-button, .el-dropdown-link {
            color: $mx-color-var-label-secondary;
            display: inline-block;
            width: 28px;
            height: 28px;
            text-align: center;
            border-radius: 6px;

            i {
              line-height: 28px;
            }
          }

          .right-close-button {
            background-color: $mx-color-var-fill-quaternary;
            margin-left: 12px;

            .close-button {
              font-size: 16px;
              color: $mx-color-var-label-secondary;
            }
          }

          .right-close-button-non-scope {
            padding: 12px;
            color: $mx-color-var-label-secondary;
            display: inline-block;
          }

          .el-dropdown-link {
            &:hover {
              background-color: $mx-color-var-fill-quaternary;
            }

            &:active {
              background: $mx-color-var-fill-secondary;
            }

            &[aria-expanded=true] {
              background-color: $mx-hover-selected;
            }
          }

          .dropdown {
            padding-left: 0;
          }

          .close {
            background: $mx-color-bg-selected;
            color: $mx-icon-color-secondary;
          }
        }
      }

      .flow-baseobject-container {
        &.todo-completed {
          .flow-todo-name {
            text-decoration: line-through;
            word-break: break-word;
          }

          > div > span {
            background-color: $mx-color-gray-40;
            border-color: $mx-color-gray-40;
            opacity: .5;
          }

          .flow-todo-actions {
            .mx-hover-hide, .micon-close-d3 {
              display: none !important;
            }
          }

          .flow-todo-assignee-avatar {
            display: inline-block;
          }

          .flow-todo-actions [data-action] {
            cursor: initial;
          }
        }
      }

      .flow-detail-container {
        overflow-y: auto;
        overflow-x: hidden;
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;

        &.manage-pop-view {
          border-radius: 6px;
          background-color: #ffffff;
          border: 1px solid rgb(228, 228, 230);

          header {
            display: block;
          }
        }

        .micon-close-d3 {
          color: $mx-color-var-label-secondary;
          background-color: none;
          border-radius: 50%;
        }
      }

      .flow-body {
        flex: 0 0 auto;

        .flow-baseobject-header {
          .mx-hover-hide {
            flex: 1 1 auto;
            text-align: right;

            * {
              text-align: left;
            }
          }
        }

        .alert-info-container {
          margin: 16px 16px 0;
          display: flex;
          padding: 7px;
          align-items: center;
          border-radius: 6px;
          border: 1px solid $mx-color-var-caution;

          i {
            font-size: 16px;
            color: $mx-color-var-caution;
          }
        }

        .reply-binder-info {
          padding-top: 8px;
          display: inline-block;
          color: $mx-color-gray-40;

          a {
            font-weight: $mx-font-weight-bold;
            color: $mx-color-gray-60;
            font-size: 11px;
          }

          .external-flag {
            margin-left: 4px;
            margin-bottom: -3px;
          }
        }

        .loading-failed-wrap {
          padding: 22px 0;
        }
      }

      .todo-toggle-panel {
        &.active {
          color: $mx-color-gray-90;
        }

        display: flex;
        padding: 12px 24px;
        border-bottom: 1px solid $mx-color-border-primary;
        flex: 0 0 auto;
        color: $mx-color-gray-40;

        > span {
          flex: 1 1 auto;
        }

        > div {
          flex: 0 0 auto;
        }
      }

      .todo-body {
        > div {
          padding: $mx-spacing-xs 20px $mx-spacing-xs 24px;
          display: flex;
          flex: 0 0 auto;

          > i {
            margin-right: 16px;
            color: $mx-color-gray-20;
          }

          .flow-todo-assign-box {
            background-color: white;
          }

          &:hover {
            background-color: rgba($mx-color-gray-04, .8);

            .flow-todo-assign-box {
              background-color: rgba($mx-color-gray-04, 0) !important;
            }
          }

          &.selecting {
            background: none;

            > i:first-child {
              color: $mx-color-blue;
            }


            > .flow-todo-assignee-avatar > i {
              color: $mx-color-blue;
            }
          }
        }

        .flow-todo-creator {
          padding: 0;
          margin-left: 60px;
          font-size: 12px;
          color: $mx-color-font-primary;

          .mx-user-name {
            font-weight: 600;
            padding-right: $mx-spacing-xs;
          }

          .mx-created-time {
            color: $mx-color-font-secondary;
          }
        }

        .flow-todo-desc {
          overflow: hidden;

          > span {
            flex: 1 1 auto;
            word-break: break-word;
          }

          span:after {
            content: ' ';
            display: inline-block;
          }

          + .inline-editor {
            flex: 1 1 auto;

            form {
              textarea {
                font-size: 12px;
                border: none;
                outline: none;
                width: 100%;
                resize: none;
                border-radius: 0;
                line-height: 20px;
                box-shadow: none;
                overflow: auto;
                border-bottom: 1px solid $mx-color-blue;
              }

              width: 100%;
            }
          }

        }

        .flow-todo-priority, .flow-todo-reminder, .flow-todo-name, .flow-todo-desc {
          &.active {
            color: $mx-color-font-primary;

            > i:first-child {
              color: $mx-icon-color-secondary;
            }
          }
        }

        .flow-duedate {
          span {
            flex: 1 1 auto;
            color: $mx-color-gray-40;
            overflow: hidden;

          }

          .mx-hover-hide {
            visibility: hidden !important;
          }

          &.active {
            > i:first-child {
              color: $mx-icon-color-secondary;
            }

            > span {
              color: $mx-color-font-primary;
              font-weight: 500;
            }

            &:hover {
              .mx-hover-hide {
                visibility: visible !important;
              }
            }
          }

          &.past-time {
            span {
              color: $mx-color-var-negative !important;
            }
          }

          .dropdown-menu {
            left: 56px;
          }
        }

        .flow-todo-name {
          > i {
            flex: 0 0 auto;
          }

          .todo-square {
            font-size: 20px;
            border: 2px solid $mx-color-var-branding;
            border-radius: 3px;
            width: 20px;
            height: 20px;
          }

          .micon-tick {
            display: none;
          }

          .flow-title, .inline-editor {
            flex: 1 1 auto;
            overflow: hidden;
          }

          textarea {
            font-size: 12px;
            border: none;
            outline: none;
            width: 100%;
            resize: none;
            border-radius: 0;
            line-height: 20px;
            box-shadow: none;
            overflow: auto;
            border-bottom: 1px solid $mx-color-var-branding;

            &:hover {
              background: $mx-color-var-fill-quaternary;
            }
          }

          .error-message {
            display: none;
          }

          .has-error textarea {
            border-color: $mx-color-var-negative;
          }

          .has-error + .error-message {
            display: inline-block;
            color: $mx-color-var-negative;
          }
        }

        .flow-process {
          .panel-heading {
            padding: 0;
          }

          .flow-process-title {
            color: $mx-color-var-label-secondary;
            display: flex;

            .icon-transform {
              transform: rotate(180deg);
            }

            .panel-heading.collapsed .icon-transform {
              transform: rotate(0);
            }

            .inline-editor {
              flex: 1 1 100%;
            }

            textarea {
              outline: none;
              border: none;
              border-bottom: 1px solid $mx-color-var-branding;
              padding-bottom: $mx-spacing-xs;
              background: none;
              box-shadow: none;
              border-radius: 0;
            }

            .checklist-title {
              flex: 1 1 auto;
              margin-left: 16px;
              overflow: hidden;
              display: flex;

              span.checklist-name {
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: calc(100% - 25px);
                margin-right: $mx-spacing-sm;
                line-height: 20px;
                height: 20px;
                white-space: nowrap;
                flex: 0 1 auto;
              }

              span.task-stat {
                line-height: 20px;
                height: 20px;
                font-size: 12px;
                flex: 1 0 auto;
                color: $mx-color-var-label-secondary;
              }

              .micon-close-d3 {
                flex: 0 0 auto;
                //transform:scale(0.7);
                font-size: $mx-font-size-base;
                margin: auto 0;
              }
            }
          }

          .panel-collapse {
            color: $mx-color-var-text-primary;

            a.btn-link {
              i.micon-plus {
                margin-right: 3px;
                font-size: $mx-font-size-sm;
              }
            }
          }

          > div {
            position: relative;
            width: 100%;
          }

        }

        .flow-attachments {
          &:hover {
            background-color: inherit;
          }

          color: $mx-color-gray-40;
          border-top: solid 1px $mx-color-gray-08;

          > div {
            width: 100%;
          }

          padding: 8px 20px 0 24px;
          overflow: hidden;

          .flow-attachment-header {
            display: flex;
            overflow: hidden;
            margin-top: 8px;

            > i {
              margin-right: 16px;
              flex: 0 0 auto;
            }

            .flow-attachment-list {
              flex: 1 1 auto;
              overflow: hidden;
            }
          }

          ul {
            flex: 1 1 auto;
            overflow: hidden;

            li {
              display: flex;
              align-items: center;
              line-height: 22px;
              height: 22px;
              overflow: hidden;
              margin-bottom: 4px;

              .file-name {
                flex: 1 1 auto;
                color: $mx-color-blue;
                overflow: hidden;
              }

              > span {
                flex: 0 0 auto;
              }
            }
          }

          &.no-attachment {
            display: none;
          }
        }

        .flow-todo-priority {
          .priority-inactive {
            display: inherit;
            color: $mx-color-gray-40;
          }

          .priority-active {
            display: none;
          }

          &.active {
            i {
              color: #FE9D67 !important;
              background-color: inherit;
            }

            .priority-inactive {
              display: none;
            }

            .priority-active {
              display: inherit;
            }
          }
        }

        .flow-todo-assignee {
          &:hover {
            .flow-todo-assign-box:not(:focus) ~ .micon-close-d3 {
              display: inline-block;
            }
          }

          &:not(.active) {
            .micon-close-d3 {
              display: none !important;
            }
          }

          .micon-close-d3 {
            display: none;
          }

          > span {
            position: relative;
            flex: 1 1 auto;
            display: flex;
          }

          input {
            border: none;
            outline: none;
            flex: 1 1 85%;
          }

          .dropdown-menu {
            .list-group-item {
              border: 0;
              padding: 0;
            }
          }
        }

        .flow-todo-reminder {
          .micon-close-d3 {
            display: none;
          }

          &.active {
            .micon-close-d3 {
              display: inline-block;
              border: none;
            }

            .reminder-info {
              flex: 1 1 auto;
            }
          }

          > span {
            display: flex;
            align-items: center;
            flex: 1 0 auto;

            input {
              display: block;
              float: left;
              border: none;

              &.reminders-date {
                min-width: 70px;
              }

              &.reminders-time {
                min-width: 50px;
              }
            }
          }
        }

        .flow-todo-assignee-avatar {
          i {
            color: $mx-color-gray-20;
          }

          .mx-thumbnail-container {
            display: block;
          }

          margin-right: 16px;
        }

        .addChecklistContainer {
          height: 100%;
          background-color: rgba(255, 255, 255, 0.7);
          width: 100%;
          position: absolute;
          left: 0;
          z-index: 1;
          color: initial;

          .checklist-template.has-value {
            display: none;

            & + .checklist-template-container {
              display: none;
            }
          }

          .checklist-template {
            display: inline-block;
            position: absolute;
            right: 16px;
            font-weight: 600;
            top: 40px;
          }

          .checklist-template-container {
            box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12), 0 8px 8px 0 rgba(0, 0, 0, 0.24);
            border-radius: 3px;
            position: absolute;
            right: 16px;
            left: 16px;
            background: white;

            > ul {
              li {
                div:first-child {
                  &:hover {
                    background-color: $mx-color-gray-04;
                  }

                  display: flex;

                  > span {
                    flex: 1 1 auto;
                    align-self: center;
                    padding-left: 24px;
                  }

                  > div {
                    flex: 0 0 auto;
                    padding: $mx-spacing-xs $mx-spacing-sm;

                    .icon-transform {
                      transform: rotate(180deg);
                    }

                    &.panel-heading.collapsed .icon-transform {
                      transform: rotate(0);
                    }
                  }
                }

                .panel-collapse {
                  padding-bottom: 16px;

                  li {
                    display: flex;
                    padding: $mx-spacing-xs 36px 0;
                    align-items: flex-start;

                    > span:first-child {
                      font-size: $mx-font-icon-sm;
                      width: 16px;
                      border: 2px solid $mx-color-blue;
                      border-radius: 3px;
                      height: 16px;
                      color: $mx-color-border-secondary;
                    }

                    .task-name {
                      padding-left: $mx-spacing-xs;
                    }
                  }
                }
              }
            }

          }

          > div {
            background-color: white;
            border-bottom: 1px;
            box-shadow: 0 0 2px rgba(0, 0, 0, .6);

            > * {
              padding: $mx-spacing-sm 16px;
            }

            header {
              display: flex;
              align-items: center;
              line-height: 28px;

              .micon-subtask {
                font-size: 20px;
                color: white;
                padding: 4px;
                background: #669CE4;
                margin-right: 16px;
              }

              > span:first-child {
                flex: 1 1 auto;
              }

              height: 52px;
              border-bottom: 1px solid $mx-color-border-primary;
            }

            textarea {
              border-right: 0;
              border: none;
              resize: none;
              width: 100%;
              overflow: auto;
              line-height: 20px;
              max-height: 60px;
              outline: none;
              border-bottom: 1px solid $mx-color-border-secondary;
            }

            .create-tips {
              display: none !important;
            }

            footer {
              height: 48px;
              text-align: right;

              & > span[data-action="cancelCreateChecklist"] {
                margin-right: $mx-spacing-sm;
              }
            }
          }

          .btn.btn-link {
            padding: 7px 0;

            i.micon-plus {
              font-size: $mx-font-size-sm;
              margin-right: 3px;
            }

            &[data-action="cancelCreateChecklist"] {
              color: $mx-color-gray-60;
            }
          }

          .add-subtask-list li {
            input:focus {
              border-color: $mx-color-branding;
            }
          }
        }

        border-bottom: 1px solid $mx-color-gray-08;
        color: $mx-color-gray-40;
        padding: 12px 0;

        &.todo-completed {
          [data-action] {
            cursor: inherit;
          }

          .flow-title {
            text-decoration: line-through;
          }

          .flow-todo-name {
            .todo-square {
              display: none;
            }

            .micon-tick {
              display: inherit;
              color: white;
              background: $mx-color-blue;
              border-radius: 3px;
              font-size: 14px;
              padding: 3px;
              position: relative;
              text-decoration: none;
              cursor: pointer;
              height: 20px;
            }
          }

          .mx-hover-hide, .micon-close-d3 {
            display: none !important;
          }
        }
      }

      div:first-child .flow-baseobject {
        padding: 0;
      }

      header + .flow-detail-container {
        .flow-baseobject {
          padding: 16px 20px;
          padding-left: 24px;
        }
      }

      .flow-baseobject {
        .baseobject-info {
          display: flex;
          font-size: 12px;
          align-items: baseline;
          height: 18px;
          overflow: hidden;

          .mx-create-user {
            font-weight: $mx-font-weight-bold;
            padding-right: $mx-spacing-xs;
            flex: 0 0 auto;
            max-width: 200px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .mx-created-time {
            flex: 1 1 auto;
            line-height: 18px;
            height: 18px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }

      .flow-message {
        background: linear-gradient(-180deg, $mx-color-var-fill-quaternary 0%, rgb(255, 255, 255) 100%);
        padding: 20px 20px 0;
        flex: 1 0 auto;
        display: flex;
        margin-left: 1px;
        flex-direction: column;

        .activity-container {
          flex: 1 1 auto;
        }
        .activity-container {
          padding: $mx-spacing-xxs 0 $mx-spacing-xs;

          ul.activities > li {
            padding-bottom: $mx-spacing-xs;
          }
        }
      }

      .flow-bottom {
        padding-bottom: $mx-spacing-sm;
        display: flex;
        background: inherit;
        margin: 0 20px;
        flex: 0 0 auto;

        // a.add-page {
        //   padding: 6px;
        //   flex: 0 0 auto;
        //   color: $mx-color-gray-40;
        // }

        .el-dropdown.mx-thread-upload-dropdown {
          flex: 0 0 auto;
          padding: 6px;
          color: $mx-color-var-label-secondary;
        }

        textarea {
          padding: 0 40px 0 $mx-spacing-sm;

          &:-ms-input-placeholder, &::-webkit-input-placeholder, &::-moz-placeholder {
            overflow: hidden;
            text-overflow: ellipsis;
            height: 18px;
          }

          border: none;
          resize: none;
          overflow: auto;
          line-height: 20px;
          width: 100%;
          vertical-align: top;
          border-radius: 0;
          @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
            min-height: 36px;
          }

          &:focus {
            outline: none;
          }
        }

        .mx-bar-right {
          position: absolute;
          right: 10px;
          top: 1px;
          color: $mx-color-var-label-secondary;
        }

        > div {
          border: 2px solid $mx-color-var-fill-tertiary;
          border-radius: 3px;
          position: relative;
        }

        > div:first-child {
          border-right: 0;
          border-radius: 3px 0 0 3px;
          background: white;
        }

        > div:last-child {
          flex: 1 1 auto;
          border-radius: 0 3px 3px 0;
          border-left: 2px solid $mx-color-var-fill-tertiary;
          border-right: 2px solid $mx-color-var-fill-tertiary; //override rule when only one item display. such as no add attachment case.
          padding: 8px 0;
        }
      }

      .notify-all {
        margin-top: $mx-spacing-sm;

        > span:last-child {
          font-size: 12px;
          color: $mx-color-gray-40;
        }
      }

      &.todo-completed {

        .flow-process {
          .mx-hover-hide {
            display: none !important;
          }
        }

        .right-area {
          .dropdown {
            display: none !important;
          }
        }
      }

    }
  }
}
</style>
