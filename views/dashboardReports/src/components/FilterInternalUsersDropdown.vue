<template>
<div :class="!usersList.length?'disabled-class':''">
    <div class="search-user-filter mx-clickable" ref="searchUsersElement" @click="toggleDropdown" :class="isDropdownOpen ?'search-user-filter-active':''">
        <div class="avatar-user-container">
            <img class="avatar-user-img" :src="displayAvatar" :class="!usersList.length?'disabled-class':''" />
            <div class="user-text mx-ellipsis" :class="!usersList.length?'disabled-class':''">
                {{ displayName }}
            </div>
        </div>
        <div class="user-arrow-icon-container">
            <span class="micon-arrow-dropdown-centered mx-clickable"></span>
        </div>
    </div>

    <div class="search-filter-dropdown-container" ref="dropdownContent" v-if="isDropdownOpen">
        <div v-if="displaySpinner && filteredUserList.length" id="spinner_icon_class">
            <span v-mx-loading-spinner="displaySpinner" />
        </div>
        <div class="search-filter-search-bar">
            <el-input size="small" prefix-icon="micon-timeline-search" :placeholder="$t('search_for_user')" v-model="searchedUserName" @input="searchUsers" @clear="searchUsers" />
        </div>
        <div class="search-filter-list-container" :class="displaySpinner?'opaque':''">
            <div class="search-filter-list">
                <div class="search-filter-list-item mx-clickable" v-if="allUsersDisplay" @click="selectUser('')">
                    <div class="search-filter-list-item-inner-container">
                        <img :src="defaultProfile" @click="selectUser('')" class="search-image" />
                        <span class="search-user-user-name">{{ $t('all_users') }}</span>
                    </div>
                </div>
                <div class="search-filter-list-item mx-clickable" @click="selectUser(user)" v-for="user in filteredUserList" :key="user.user.id">
                    <div class="search-filter-list-item-inner-container">
                        <img :src="user.avatar || defaultProfile" class="search-image" />
                        <span class="search-user-user-name mx-ellipsis">{{ user.user.first_name }} {{ user.user.last_name }}</span>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
</template>

<script>
import defaultProfile from '@views/theme/src/images/default/default_profile_2x.png';
import _debounce from 'lodash/debounce';
import {
    mapActions,
    mapMutations
} from 'vuex'
export default {
    name: 'FilterInternalUsersDropdown',
    data() {
        return {
            displayName: this.$t('all_users'),
            displayAvatar: '',
            displaySpinner: false,
            defaultProfile,
            allUsersDisplay: false,
            searchedUserID: '',
            searchedUserName: '',
            isDropdownOpen: false,
            filteredUserList: [],
            usersList: [],
        }
    },
    computed: {},
    created() {
        this.fetchUsers();
    },
    mounted() {
        this.setFilterDropdownUserID('');
        this.displayAvatar = this.defaultProfile,
        document.addEventListener('click', this.handleClickOutside);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside);
    },
    methods: {
        ...mapActions('userSelector', ['filterGroupMembers']),
        ...mapMutations('dashboardReports', ['setFilterDropdownUserID']),
        toggleDropdown() {
            if (this.usersList.length) {
                this.isDropdownOpen = !this.isDropdownOpen;
                this.searchedUserName = ''
                this.processUserList()
            }
        },
        handleClickOutside(event) {
            const dropdownContent = this.$refs.dropdownContent;
            const targetElement = event.target;

            if (
                this.isDropdownOpen &&
                this.$refs.searchUsersElement &&
                !this.$refs.searchUsersElement.contains(targetElement) &&
                dropdownContent &&
                !dropdownContent.contains(targetElement)
            ) {
                this.searchedUserName = ''
                this.processUserList()
                this.isDropdownOpen = false;
            }
        },
        selectUser(user) {
            this.searchedUserID = ''
            this.searchedUserName = ''
            if (!user) {
                this.displayName = this.$t('all_users')
                this.displayAvatar = this.defaultProfile
                this.allUsersDisplay = false
                this.filteredUserList = [...this.usersList]
            } else {
                this.displayName = user.name 
                this.displayAvatar = user.avatar || this.defaultProfile
                this.allUsersDisplay = true
                this.searchedUserID = user.id
            }

            this.processUserList()
            this.setFilterDropdownUserID(this.searchedUserID);
            this.$emit('onSelectUser', this.searchedUserID);
        },
        processUserList() {
            const list = [...this.usersList]
            if (this.searchedUserID) // Selected User Is there
            {
                this.filteredUserList = [...list.filter(x => x.user.id != this.searchedUserID)];
            } else {
                this.filteredUserList = [...list]
            }
        },
        searchUsers() {
            if (this.searchedUserName == '') 
            {
                this.filteredUserList = [...this.usersList]
            }
            this.displaySpinner = true
            this.fetchUsers();
        },
        fetchUsers: _debounce(function () {
            this.filterGroupMembers({
                searchKey: this.searchedUserName,
                includeRelationUser: true,
                includeMe: true,
                userType: 'internal'
            }).then(res => {
                this.displaySpinner = false
                if (this.searchedUserID) {
                    this.filteredUserList = [...res]
                } else {
                    if (this.searchedUserName) {
                        this.filteredUserList = [...res]
                    } else {
                        this.usersList = [...res]
                        this.filteredUserList = [...res]
                    }

                }
            }).catch(() => {
                this.$mxMessage.error(this.$t('system_unknown_error'))
            })
        }, 300)
    }
}
</script>

<style lang="scss" scoped>
.search-user-filter {
    display: flex;
    padding: 4px 6px;
    align-items: center;
    gap: 4px;
    border-radius: 6px;

}

.search-user-filter-active {
    background: rgba(26, 105, 209, 0.10);
}

.avatar-user-container {
    display: flex;
    align-items: center;
    gap: 6px;
}

.avatar-user-img {
    width: 24px;
    height: 24px;
    border-radius: 111px;
    user-select: none;
}

.user-text {
    color: $mx-color-var-black;
    max-width: 220px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    user-select: none;
}

.user-arrow-icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.user-arrow-icon {
    width: 16px;
    height: 16px;
}

.search-filter-dropdown-container {
    display: flex;
    position: absolute;
    top: 50px;
    z-index: 999;
    width: 320px;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 6px;
    background: $mx-color-var-white;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.12), 0px -2px 4px 0px rgba(0, 0, 0, 0.04);
}

.search-filter-search-bar {
    display: flex;
    padding: 20px 20px 8px 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    align-self: stretch;
}

.search-filter-list-container {
    display: flex;
    padding: 8px 0px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    max-height: 300px;
    overflow-y: scroll;
    overflow-x: hidden !important;
}

.search-filter-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.search-filter-list-item {
    display: flex;
    width: 320px;
    height: 44px;
    padding-right: 0px;
    justify-content: center;
    align-items: center;
    gap: 12px;
}

.search-filter-list-item:hover {
    background: $mx-color-var-fill-quaternary;
}

.search-filter-list-item-inner-container {
    display: flex;
    padding: 8px 0px 8px 20px;
    align-items: center;
    gap: 12px;
    flex: 1 0 0;
}

.search-image {
    width: 28px;
    height: 28px;
    user-select: none;
    border-radius: 100px;
}

.search-user-user-name {
    display: block;
    width: 210px;
    max-width: 230px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    align-items: center;
    gap: 8px;
    user-select: none;
    flex: 1 0 0;
    color: $mx-color-var-black;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
}

.disabled-class {
    color: $mx-color-var-fill-primary;
}

.micon-arrow-dropdown-centered:before {
    color: $mx-color-var-text-secondary;
}

#spinner_icon_class {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 30vh;
    color: $mx-color-var-branding;
    z-index: 2030;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
}

.opaque {
    opacity: 0.5;
}
</style>
