import {useFlowStepCreation} from '@views/workflows/plugins/useFlowStepCreation';
import {useWorkspaceSelector} from '@views/createConversation/export'
import {useFullBoard} from '../../../controller/workflow/useBoard';
import { getRootVM, popupFactory, useRouter} from '../useComponent';
import {TemplateType} from '@controller/contentLibrary'
import {createWorkspace, deleteWorkspace} from '@controller/workflow'
import {useTemplateSelector} from '../../contentLibrary/component';
import {createWfAction} from '@controller/flowLibrary/src/actions/factory';
import appStore from '@appStore'
import {ObjectUtils, MxConsts, Defines, FunctionUtil} from '@commonUtils';
import userUtil from '@views/common/utils/user'
import SelectInsertPositionDialog from '@views/workflows/components/addStep/SelectInsertPositionDialog.vue'
import { ROUTER_MAP } from '@views/common/appConst';
import utils from '../utils/utils'
import {useFlowInstantStore} from '@views/stores/flowInstant';
import { getAvailableFlowStepAssignees } from '@views/common/utils/workflow'
import {useFlowStepStore} from '@views/stores/flowStep'
import {useActionObjectCreation} from '@views/contentLibrary/plugins/request/useActionObjectCreation.js'
import {getDefaultFlowAvatarFile} from '@views/workflows/utils/getFlowDefaultAvatar'
import BinderUtil from '@views/common/utils/binder.js'
import {getImageFromUrl} from '@views/common/utils/image'
import {  WorkflowBoardController } from '@newController/index'
import { WorkflowBoardEntity } from '@model/workflow/entity/workflowBoardEntity'
import appConfig from '@views/common/appConfig'
import { UserObjectType } from '@model/board/boardUserViewModel'
import { useContentLibraryStore }  from '@views/stores/contentLibrary'
import { useDDRSupportStore } from '@views/stores/ddrSupport'
import {useActionTypeSelector} from '@views/workflows/plugins/useActionTypeSelector';
import uniqBy from 'lodash/uniqBy'
import { ActionScenes } from '@views/workflows/builder/common/types'

import { directRenderActionCreator } from '@views/newActions/renderActionCreator'
import { ActionMode, ActionScene } from '@views/newActions/common/types'
import { EActionObjectType } from '@model/baseObjects/defines/baseObjectViewModel'

import isEmpty from 'lodash/isEmpty'

const ActionObjectType = MxConsts.ActionObjectType
const TransactionType = Defines.TransactionType

const ActionObjectTypeToNewActionTypeMap = {
  [ActionObjectType.Approval]: EActionObjectType.Approval,
  [ActionObjectType.Acknowledgement]: EActionObjectType.Acknowledgement,
  [ActionObjectType.PDFForm]: EActionObjectType.PdfForm,
}

const TemplateTypeMap = {
  [ActionObjectType.Approval]: TemplateType.Approval,
  [ActionObjectType.Acknowledgement]: TemplateType.Acknowledgement,
  [ActionObjectType.ESign]: TemplateType.ESign,
  [ActionObjectType.FileRequest]: TemplateType.FileRequest,
  [ActionObjectType.FormRequest]: TemplateType.Form,
  [ActionObjectType.PDFForm]: TemplateType.PdfForm,
  [ActionObjectType.MeetRequest]: TemplateType.MeetRequest,
  [ActionObjectType.Jumio]: TemplateType.Jumio,
  [ActionObjectType.Integration]: TemplateType.Integration,
  
  [ActionObjectType.Todo]: TemplateType.Todo,
  [ActionObjectType.TodoTransaction]: TemplateType.TodoTransaction,
  [ActionObjectType.Milestone]: TemplateType.Milestone,
}

const FlowStepType = Defines.WorkflowStepType
const FlowStepTypeMap = {
  [ActionObjectType.Approval]: FlowStepType.WORKFLOW_STEP_TYPE_APPROVAL,
  [ActionObjectType.Acknowledgement]: FlowStepType.WORKFLOW_STEP_TYPE_ACKNOWLEDGE,
  [ActionObjectType.ESign]: FlowStepType.WORKFLOW_STEP_TYPE_SIGNATURE,
  [ActionObjectType.FileRequest]: FlowStepType.WORKFLOW_STEP_TYPE_FILE_REQUEST,
  [ActionObjectType.FormRequest]: FlowStepType.WORKFLOW_STEP_TYPE_FORM_REQUEST,
  [ActionObjectType.PDFForm]: FlowStepType.WORKFLOW_STEP_TYPE_PDF_FORM,
  [ActionObjectType.MeetRequest]: FlowStepType.WORKFLOW_STEP_TYPE_MEET_REQUEST,
  [ActionObjectType.LaunchWebApp]: FlowStepType.WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP,
  [ActionObjectType.Integration]: FlowStepType.WORKFLOW_STEP_TYPE_INTEGRATION,
  [ActionObjectType.Jumio]: FlowStepType.WORKFLOW_STEP_TYPE_INTEGRATION,
  [ActionObjectType.DocuSign]: FlowStepType.WORKFLOW_STEP_TYPE_DOCUSIGN,

  [ActionObjectType.TodoTransaction]: FlowStepType.WORKFLOW_STEP_TYPE_TODO_TRANSACTION,
  [ActionObjectType.Todo]: FlowStepType.WORKFLOW_STEP_TYPE_TODO,
  [ActionObjectType.Milestone]: FlowStepType.WORKFLOW_STEP_TYPE_MILESTONE,
}

const StepUI = {
  SelectActionType: 1,
  SelectWorkspace: 2,
  SelectPosition: 3,
  SelectActionTemplate: 4,
  CreateAction: 5
}

export function useNewInstantAction (initPayload, events = {}) {

  const router = useRouter()
  const isEnableActionLibrary = appStore.getters['group/isEnableActionLibrary']

  let currentStep = -1
  let selectedActionType = null
  let selectedOrginTemplate = null
  let selectedActionTemplate = null
  let selectedBoard = null
  let boardInstant = null
  let isAddIntoExistBoard = false
  let stepPosition = {}
  let prevStepOrderNumber
  let prevStepClientUuid
  let newFlowWsClientUuid
  let isUseExistTemplate = false
  let selectedIntegrationType = null
  let selectIntegrationApp = null
  let initWithSelectedTemplate = false
  let goCreateFromTemplateSelector = false

  const popupHandle = {}

  let flowBoardModel = null
  let flowStep = null
  const steps = {}
  const stepExtraProp = {}

  const onActionCreated = () => {
    if (selectedOrginTemplate) {
      const filterTypes = [TemplateType.Milestone]
      const isFilterOut = filterTypes.findIndex((type)=>{return type === selectedOrginTemplate.type}) > -1
      if(isFilterOut){
      }else{
        appStore.dispatch('request/increaseContentLibraryUsedCount', {
          boardId: selectedOrginTemplate.folderId, 
          SPath: selectedOrginTemplate.SPath
        })
      }

    }
    if (events.created) {
      events.created()
    }
  }

  const setSelectedActionType = (payload) => {
    const {type, subType, integrationApp} = payload || initPayload
    if (type?.startsWith('WORKFLOW_STEP_TYPE')) {
      // TODO: This is a temp solution to handle the select Action type from ActionTypeSelector component.
      selectedActionType = Object.keys(FlowStepTypeMap).find(k => FlowStepTypeMap[k] === type)
      if (selectedActionType === ActionObjectType.Integration && subType?.toUpperCase() === ActionObjectType.Jumio.toUpperCase()) {
        // TODO: Jumio is a special case, it was passed with subType by ActionTypeSelector, but passed with type in traditional "+ New" button. Should be fixed in future
        selectedActionType = ActionObjectType.Jumio
      }
    } else {
      selectedActionType = type
    }
    // Note: integration type is defined as FlowStepType.WORKFLOW_STEP_TYPE_INTEGRATION
    if (selectedActionType === FlowStepType.WORKFLOW_STEP_TYPE_INTEGRATION) {
      selectedActionType = ActionObjectType.Integration
    }
    if ([ActionObjectType.Integration, ActionObjectType.Jumio].includes(selectedActionType) || integrationApp) {
      if (integrationApp) {
        selectIntegrationApp = integrationApp
        selectedIntegrationType = integrationApp?.subType
      } else {
        selectIntegrationApp = null
      }
      if (selectedActionType === ActionObjectType.Jumio) {
        // TODO 'Jumio' should be replaced with a constant action-object-type
        selectedIntegrationType = 'Jumio'
      }
    } else {
      selectIntegrationApp = null
      selectedIntegrationType = null
    }
  }


  const getActionTypeFromTemplate = ({transactionType, type}) => {
    let actionType
    if (transactionType) {
      switch (transactionType) {
        case TransactionType.TRANSACTION_TYPE_APPROVAL:
        actionType = ActionObjectType.Approval
        break
      case TransactionType.TRANSACTION_TYPE_ACKNOWLEDGE:
        actionType = ActionObjectType.Acknowledgement
        break
      case TransactionType.TRANSACTION_TYPE_FILE_REQUEST:
        actionType = ActionObjectType.FileRequest
        break
      case TransactionType.TRANSACTION_TYPE_MEET_REQUEST:
        actionType = ActionObjectType.MeetRequest
        break
      case TransactionType.TRANSACTION_TYPE_FORM_REQUEST:
        actionType = ActionObjectType.FormRequest
        break
      case TransactionType.TRANSACTION_TYPE_PDF_FORM:
        actionType = ActionObjectType.PDFForm
        break
        case TransactionType.TRANSACTION_TYPE_DOCUSIGN:
        actionType = ActionObjectType.DocuSign
        break
      case TransactionType.TRANSACTION_TYPE_LAUNCH_WEB_APP:
        actionType = ActionObjectType.LaunchWebApp
        break
      case TransactionType.TRANSACTION_TYPE_INTEGRATION:
        actionType = ActionObjectType.Jumio
        break
      case TransactionType.TRANSACTION_TYPE_TODO:
        actionType = ActionObjectType.TodoTransaction
        break
      }
    } else {
      actionType = Object.keys(TemplateTypeMap).find(key => TemplateTypeMap[key] === type)
    }
    return actionType
  }

  const setSelectedTemplate = async (template) => {
    selectedOrginTemplate = template || initPayload.template
    const {folderId: boardId, SPath, name, transactionType, type, id} = selectedOrginTemplate
    const currActionType = getActionTypeFromTemplate(selectedOrginTemplate)
    isUseExistTemplate = true
    if (!selectedActionType || selectedActionType !== currActionType) {
      // use action template use case, the "type" here is TemplateType defined in "controller/contentLibrary/src/types.ts"
      selectedActionType = currActionType
    }
    if (transactionType === TransactionType.TRANSACTION_TYPE_TODO) {
      // the Todo-action type in "+New" is "Todo", but selected a todo-transaction template
      selectedActionType = ActionObjectType.TodoTransaction
    }
    if (selectedActionType === ActionObjectType.Jumio) {
      // TODO 'Jumio' should be replaced with a constant action-object-type
      selectedIntegrationType = 'Jumio'
    }
    
    switch (selectedActionType) {
      case ActionObjectType.Approval:
      case ActionObjectType.Acknowledgement:
      case ActionObjectType.FileRequest:
      case ActionObjectType.FormRequest:
      case ActionObjectType.MeetRequest:
      case ActionObjectType.TodoTransaction:
      case ActionObjectType.Jumio:
      case ActionObjectType.PDFForm:
        const transaction = await appStore.dispatch('request/getTransactionDetail', {boardId, SPath})
        selectedActionTemplate = {...transaction, name: name, boardId}
        break
      case ActionObjectType.ESign:
        selectedActionTemplate = {...selectedOrginTemplate}
        break
      case ActionObjectType.Todo:
        const todo = await appStore.dispatch('request/getTodoDetail', {boardId, SPath})
        selectedActionTemplate = {
          title: todo.name,
          description: todo.note,
          dueDate: todo.dueDate,
          dueInTimeframe: todo.dueInTimeframe,
          excludeWeekends: todo.excludeWeekends,
          attachments: todo.attachments,
          reminder: todo.reminder,
          name: name,
          SPath
        }
        break
      case ActionObjectType.Milestone:{
        const contentLibStore = useContentLibraryStore()
        const [model] = await contentLibStore.loadMilestoneTemplate(boardId,id, null, true)
        selectedActionTemplate = {
          ...model
        }
        break
      }
    }
  }

  const runStep = (stepUI, newStep) => {
    if (currentStep >= 0) {
      const temp = popupHandle[currentStep]
      if (Array.isArray(temp)) {
        const [oldshow, hide] = temp;
        hide()
        delete popupHandle[currentStep]
      }
    }
    if (ObjectUtils.isFunction(stepUI)) {
      stepUI = stepUI()
    }
    popupHandle[newStep] = stepUI
    const [show] = stepUI
    show && show()
  }

  const gotoStep = (type) => {
    runStep(steps[type], type)
    currentStep = type
  }

  const createDirect = async () => {
    if (selectedActionType === ActionObjectType.Todo) {
      // Always create todo-transaction type for new todos
      selectedActionType = ActionObjectType.TodoTransaction
    }
    if (selectedBoard.isWorkflow) {
      flowStep = createWfAction(FlowStepTypeMap[selectedActionType])
      if (selectedActionType === ActionObjectType.ESign) {
        const transformedBoard = await flowStep.prepareHostBoard([])
        transformedBoard.boardUsers = boardInstant?.members?.value
        appStore.commit('directService/setDirectBinder', transformedBoard)
        appStore.commit('directService/setServiceType', 'signature')
      } else if (selectedActionType === ActionObjectType.TodoTransaction) {
        stepExtraProp.isTodoTransaction = true
        stepExtraProp.stepType = FlowStepType.WORKFLOW_STEP_TYPE_TODO_TRANSACTION
      }
    } else {
      if (selectedBoard && selectedActionType === ActionObjectType.ESign) {
        appStore.commit('directService/setDirectBinder', selectedBoard)
        appStore.commit('directService/setServiceType', 'signature')
      }
    }
    isUseExistTemplate = false
    selectedActionTemplate = {}
    selectedOrginTemplate = null
    gotoStep(StepUI.CreateAction)
  }

  const createStepWithExistWorkspace = async (step, amWorkflow) => {
    const {boardId} = boardInstant.boardInfo.value
    const board = await boardInstant.getWorkflow()
    const workflowSequence = ObjectUtils.getByPath(board, 'workflows.0.sequence')
    const flowInstantStore = useFlowInstantStore()
    let request
    if(selectedActionType === ActionObjectType.Milestone  && step?.model?.isTemplate){
      request = flowInstantStore.addMilestoneFromTemplate(boardId, workflowSequence,step, {sequence: stepPosition.prevSeq, clientUuid: stepPosition.prevClientUuid})
    }else{
      const stepSeqOrCliUuid = stepPosition.prevSeq ?? stepPosition.prevClientUuid
      if(amWorkflow) {
        //for add action template with automatino(s) case
        amWorkflow.steps.splice(0, 1, {...step, clientUuid: amWorkflow.steps[0]?.clientUuid})
        request = flowInstantStore.addStepsFromWorkflow(boardId, workflowSequence, amWorkflow, stepSeqOrCliUuid, stepPosition.isABSideBranch)
      } else {
        request = flowInstantStore.addStep(boardId, workflowSequence, step, stepSeqOrCliUuid, stepPosition.isABSideBranch)
      }
    }
    return request.then(() =>{
      stepPosition = {}
      router.push({
        name: ROUTER_MAP.project.name,
        params: {
          id: boardId
        }
      })
      boardInstant.destroy()
    })
  }

  sessionStorage.removeItem('newInvitedClientId')
  const saveUsageToStorage = (hasNewInvitedClient) => {
    utils.setUsageToStorage({
      category: 'new_flow_workspace',
      label: 'launched_by_new_action'
    }, 'total')
    if (hasNewInvitedClient) {
      utils.setUsageToStorage({
        category: 'new_flow_workspace',
        label: 'launched_by_new_action_with_newly_invited_client'
      }, 'total')
      sessionStorage.removeItem('newInvitedClientId')
    }
  }

  const handleApprovalDataStatistic = (boardId) => {
    let tags = appStore.getters['group/groupTags']
    if (selectedBoard.isWorkflow && tags.Enable_User_Behavior_Logging) {
      let hasNewInvitedClient = false
      const newInvitedClientIdStr = sessionStorage.getItem('newInvitedClientId')
      if (newInvitedClientIdStr) {
        const flowUserBoards = appStore.getters['user/flowUserBoards']
        let userBoard = flowUserBoards.find(board => board.id === boardId)
        if (userBoard) {
          const newInvitedClientIdArr = JSON.parse(newInvitedClientIdStr)
          hasNewInvitedClient = userBoard.users.some(item => newInvitedClientIdArr.indexOf(item.user.id) >= 0)
        }
      }
      saveUsageToStorage(hasNewInvitedClient)
    }
  }

  const handleOtherActionsDataStatistic = (assignees) => {
    let tags = appStore.getters['group/groupTags']
    if (tags.Enable_User_Behavior_Logging) {
      const newInvitedClientIdStr = sessionStorage.getItem('newInvitedClientId')
      let hasNewAssignee = false
      if (newInvitedClientIdStr) {
        const newInvitedClientIdArr = JSON.parse(newInvitedClientIdStr)
        hasNewAssignee = assignees.some(user => newInvitedClientIdArr.indexOf(user.id) >= 0)
      }
      saveUsageToStorage(hasNewAssignee)
    }
  }
  
  const createStepAndWorkspace = async (step, amWorkflow) => {
    const thumbnailFile = await getDefaultFlowAvatarFile().catch(() => '')
    const board = await createWorkspace({
      clientUuid: newFlowWsClientUuid,
      name: selectedBoard.name, 
      description: selectedBoard.description,
      members: [],
      thumbnail: thumbnailFile,
      archiveAfter: 90 * 24 * 3600 * 1000
    })
    await boardInstant.init(board.id)
    try {
      await createStepWithExistWorkspace(step, amWorkflow)
    } catch (error) {
      return Promise.reject(error);
    }
   
  }

  const popSuccessCreatedMsg = (hasAutomations) => {
    let successMsg
    const rootVm = getRootVM()
    const $t = rootVm.$t
    switch (selectedActionType) {
      case ActionObjectType.ESign:
        successMsg = $t('direct_signature_creation_success')
        break
      case ActionObjectType.Approval:
        successMsg = $t('Approval_successfully_created')
        break
      case ActionObjectType.Acknowledgement:
        successMsg = $t('Acknowledgement_successfully_created')
        break
      case ActionObjectType.FileRequest:
        successMsg = $t('File_request_successfully_created')
        break
      case ActionObjectType.FormRequest:
        successMsg = $t('Form_request_successfully_created')
        break
      case ActionObjectType.MeetRequest:
        successMsg = $t('Meeting_request_successfully_created')
        break
      case ActionObjectType.LaunchWebApp:
        successMsg = $t('Launch_web_app_successfully_created')
        break
      case ActionObjectType.Todo:
      case ActionObjectType.TodoTransaction:
        successMsg = $t('Your_todo_successfully_created')
        break
      case ActionObjectType.DocuSign:
        successMsg = $t('DocuSign_successfully_added')
        break
      case ActionObjectType.Jumio:
        successMsg = $t('Jumio_successfully_added')
        break
      case ActionObjectType.Integration:
        successMsg = $t('action_successfully_created', {appName: selectIntegrationApp.app_name})
        break
      case ActionObjectType.Milestone:
        successMsg = $t('Milestone_successfully_created')
        break
    }
    rootVm.$mxMessage.success(successMsg)

    if(hasAutomations) {
      rootVm.$mxMessage({
        type: 'success',
        message: $t('The_action_with_automations_please_review_for_any_changes'),
        offset: 50
      })
    }
  }

  const isExceedMaxSteps = (flowStepsLen, addStepsNumber)=>{
    const rootVm = getRootVM()
    const $t = rootVm.$t
    const allStepNum = flowStepsLen + addStepsNumber
    const maxStepNum = appConfig.flowLibrary.maxSteps
    const isExceed = allStepNum > maxStepNum ? true : false
    if(isExceed){
      
      rootVm.$mxConfirm($t('Youve_reached_the_maximum_allowable_actions',{existedStepsCount: allStepNum,count: maxStepNum}),$t('Action_limit_reached'),{
        customClass: 'new-style',
        confirmButtonText: $t('dismiss'),
        showCancelButton: false
      })
      return true
    }
    return false
  }

  steps[StepUI.SelectActionType] = useActionTypeSelector({
    select: (type, subType, integrationApp) => {
      setSelectedActionType({type, subType, integrationApp})
      gotoStep(StepUI.SelectWorkspace)
    }
  }, {
    fromGlobal: true
  })

  steps[StepUI.SelectWorkspace] = useWorkspaceSelector({
    created (board) {
      isAddIntoExistBoard = false
      if(boardInstant){
        boardInstant?.destroy()
      }
      if (board.isWorkflow) {
        boardInstant = useFullBoard()
        selectedBoard = board
      } else {
        selectedBoard = {name: board.name}
      }
      if (selectedActionTemplate) {
        // use action template use case
        gotoStep(StepUI.CreateAction)
      } else {
        if ([ActionObjectType.LaunchWebApp, ActionObjectType.DocuSign, ActionObjectType.Integration].includes(selectedActionType)) {
          gotoStep(StepUI.CreateAction)
        } else {
          if (isEnableActionLibrary) {
            gotoStep(StepUI.SelectActionTemplate)
          } else {
            createDirect()
          }
        }
      }
    },
    select (board,workspaceEvents) {
      if(board.isWorkflow) {
        const totalSteps = ObjectUtils.getByPath(board, 'workflows.0.totalSteps', 0)
        const { totalAutomations } = selectedOrginTemplate || {}
        if (totalSteps && totalAutomations) {
          const flowStepsLen = totalSteps
          const isExceed = isExceedMaxSteps(flowStepsLen, totalAutomations + 1)
          if(isExceed){
            return
          }
        }
      }
      if(boardInstant){
        boardInstant?.destroy()
      }

      selectedBoard = board
      isAddIntoExistBoard = true
      boardInstant = useFullBoard()
      boardInstant.init(board.id).then(() => {
        if (board.isWorkflow) {
          if (boardInstant.isEmptyFlow()) {
            // skip to select insert position
            if (selectedActionTemplate) {
              gotoStep(StepUI.CreateAction)
            } else {
              if ([ActionObjectType.LaunchWebApp, ActionObjectType.DocuSign, ActionObjectType.Integration].includes(selectedActionType)) {
                gotoStep(StepUI.CreateAction)
              } else {
                if (isEnableActionLibrary) {
                  gotoStep(StepUI.SelectActionTemplate)
                } else {
                  createDirect()
                }
              }
            }
          } else {
       

            // TODO: in future tune new-instant-action task, this flowBoardModel should be maintained in specific store
            WorkflowBoardController.loadWorkflowBoard(selectedBoard.id).then(([flowBoardViewModel]) => {
              flowBoardModel = flowBoardViewModel

              if(selectedActionType === ActionObjectType.Milestone && selectedActionTemplate?.sequence){
                const flowStepsLen = flowBoardModel.workflow.totalSteps
                const isExceed = isExceedMaxSteps(flowStepsLen, selectedActionTemplate.totalSteps)
                if(isExceed){
                  return
                }else{
                  workspaceEvents?.close()
                }
              }
              gotoStep(StepUI.SelectPosition)
            })
          }
        } else {
          if (selectedActionTemplate) {
            // use action template use case
            gotoStep(StepUI.CreateAction)
          } else {
            if ([ActionObjectType.LaunchWebApp, ActionObjectType.DocuSign, ActionObjectType.Integration].includes(selectedActionType)) {
              gotoStep(StepUI.CreateAction)
            } else {
              if (isEnableActionLibrary) {
                gotoStep(StepUI.SelectActionTemplate)
              } else {
                createDirect()
              }
            }
          }
        }
      })
    },
    back () {
      gotoStep(StepUI.SelectActionType)
    }
  }, () => {
    const isMilestoneTemplate = initPayload?.template?.type === TemplateType.Milestone
    const isActionWithAutomations = !!initPayload?.template?.workflow
    const rootVm = getRootVM()
    return {
      selectedBoard,
      enableSelector: true,
      useRadioAsTab: true,
      disableNonflowBinder: isMilestoneTemplate,
      disabledBinderTips: rootVm.$t('Milestone_template_cannot_be_added_to_a_group_workspace'),
      closeManually: isMilestoneTemplate || isActionWithAutomations,
      showBack: !initWithSelectedTemplate,
      templateType: initPayload?.template?.type,
    }
  })

  steps[StepUI.SelectPosition] = () =>{
    return popupFactory(SelectInsertPositionDialog)({
      addStep (position) {
        stepPosition = position
        prevStepOrderNumber = position.prevOrderNumber
        prevStepClientUuid = position.prevClientUuid

        if (selectedActionTemplate) {
          // use action template use case
          gotoStep(StepUI.CreateAction)
        } else {
          if ([ActionObjectType.LaunchWebApp, ActionObjectType.DocuSign, ActionObjectType.Integration].includes(selectedActionType)) {
            gotoStep(StepUI.CreateAction)
          } else {
            if (isEnableActionLibrary) {
              gotoStep(StepUI.SelectActionTemplate)
            } else {
              createDirect()
            }
          }
        }
      },
      back () {
        gotoStep(StepUI.SelectWorkspace)
      }
    }, () => {
      return {
        flowBoardModel, 
        type: FlowStepTypeMap[selectedActionType], 
        showBack: isAddIntoExistBoard, 
        headerTitle: getRootVM().$t('Select_Action_Position')
      }
    })
  }

  steps[StepUI.SelectActionTemplate] = useTemplateSelector({
    back: () => {
      gotoStep(StepUI.SelectWorkspace)
    },
    create: createDirect,
    select: async (templates, workspaceEvents) => {
      const template = templates[0]

      if(flowBoardModel) {
        const flowStepsLen = flowBoardModel.workflow?.totalSteps
        const { totalAutomations } = template || initPayload.template
        if (totalAutomations) {
          const isExceed = isExceedMaxSteps(flowStepsLen, totalAutomations + 1)
          if(isExceed){
            return
          }
        }
      }
      await setSelectedTemplate(template)
      gotoStep(StepUI.CreateAction)
    }
  }, () => {
    goCreateFromTemplateSelector = true
    let templateType = TemplateTypeMap[selectedActionType]
    if (templateType === TemplateType.TodoTransaction) {
      templateType = TemplateType.Todo
    }
    return {
      multiple: false,
      type: templateType,
      enableCreate: true,
      showBack: true,
      nextButtonStyle: true
    }
  })

  function initDDRStore (newFlowWsClientUuid) {
    // Init DDR support - start
    if (!selectedBoard.isWorkflow) return null

    let flowContextId, flowBoardViewModel
    if (selectedBoard.id) {
      flowContextId = {boardId: selectedBoard.id, clientUuid: selectedBoard.client_uuid}
      flowBoardViewModel = WorkflowBoardEntity.toViewModel(boardInstant.mxBoard.value.board)
    } else {
      flowContextId = {clientUuid: newFlowWsClientUuid}
      flowBoardViewModel = {}
    }
    useDDRSupportStore().initStore(flowContextId, flowBoardViewModel)
    // Init DDR support - end

    // destroy DDR support - start
    const destroyDDRSupportStore = () => {
      useDDRSupportStore().destroy(flowContextId)
    }
    // destroy DDR support - end
    return destroyDDRSupportStore
  }

  steps[StepUI.CreateAction] = () => {
    newFlowWsClientUuid = FunctionUtil.uuid()
    const destroyDDRSupportStore = initDDRStore(newFlowWsClientUuid)
    //todo:PDF
    if ([ActionObjectType.Approval, ActionObjectType.PDFForm].indexOf(selectedActionType) > -1) {
      const initParam = {
        boardId: selectedBoard.id,
        boardModel: {
          name: selectedBoard.name,
          description: selectedBoard.description,
          clientUuid: newFlowWsClientUuid
        }
      }
      const baseProps = {
        actionContext: {
          mode: ActionMode.CREATE
        },
        initParam: initParam,
        uiSchema: {
          showBackIcon: true
        }
      }
      if (!isEmpty(selectedActionTemplate)) {
        //TODO: A temp fix to let the create flow works, here needs to be updated soom
        if (!selectedActionTemplate.SPath) {
          selectedActionTemplate.SPath = `transactions[sequence=${selectedActionTemplate.sequence}]`
        }
        initParam.fromActionTemplate = {
          boardId: selectedActionTemplate.boardId,
          SPath: selectedActionTemplate.SPath //TODO: Check if this is correct
        }
      }
      if (selectedBoard.isWorkflow) {
        if (prevStepClientUuid) {
          initParam.insertAfter = {
            prevStepId: prevStepClientUuid
          }
        }
        initParam.flowModel = initParam.boardId ? selectedBoard.workflow : {
          isSequential: false
        }
        baseProps.actionContext.scene = initParam.boardId ? ActionScene.IN_FLOW_WORKSPACE : ActionScene.IN_DIRECT_CREATE_ACTION_TO_NEW_FLOW_WORKSPACE
      } else {
        baseProps.actionContext.scene = initParam.boardId ? ActionScene.IN_NORMAL_WORKSPACE : ActionScene.IN_DIRECT_CREATE_ACTION_TO_NEW_NORMAL_WORKSPACE
      }

      const newActionType = ActionObjectTypeToNewActionTypeMap[selectedActionType]
      const [showActionCreator, hideActionCreator] = directRenderActionCreator(newActionType, baseProps, {
        back: () => {
          hideActionCreator()
          if (!initWithSelectedTemplate && selectedActionTemplate) {
            selectedActionTemplate = null
            selectedOrginTemplate = null
          }
          if (goCreateFromTemplateSelector) {
            gotoStep(StepUI.SelectActionTemplate)
          } else {
            if(isAddIntoExistBoard){
              if (boardInstant.isEmptyFlow()) {
                gotoStep(StepUI.SelectWorkspace)
              }else{
                gotoStep(StepUI.SelectPosition)
              }
            }else {
              gotoStep(StepUI.SelectWorkspace)
            }
          }
        },
        success: (boardId) => {
          destroyDDRSupportStore?.()
          boardInstant?.destroy()
          router.push({
            name: ROUTER_MAP.project.name,
            params: {
              id: boardId
            }
          })
          handleApprovalDataStatistic(boardId)
        }
      })

      return [showActionCreator, hideActionCreator]
    } else {
      if (selectedBoard.isWorkflow) {
        const stepType = FlowStepTypeMap[selectedActionType]
        if (!flowStep) {
          flowStep = createWfAction(stepType)
        }
        stepExtraProp.stepType = stepType
        stepExtraProp.isTodoTransaction = selectedActionType === ActionObjectType.TodoTransaction ? true : false
        const flowStepStore = useFlowStepStore()
        const integrationProps = {}
        if(stepType === FlowStepType.WORKFLOW_STEP_TYPE_INTEGRATION){
          integrationProps.isWorkflow = true
        }
        flowStepStore.createFlowStep({
          type: stepType
        })

        const [showStepCreator,hideStepCreator] = useFlowStepCreation({
          created: async (stepModel, callbacks, amWorkflow) => {
            try {
              if (isAddIntoExistBoard) {
                await createStepWithExistWorkspace(stepModel, amWorkflow)
              } else {
                let assignees = []
                const allTeamMemberMap = appStore.state.group.teamMembers
                const allClientTeamMemberMap = appStore.state.group.clientTeamMembers
                if(stepType === FlowStepType.WORKFLOW_STEP_TYPE_MILESTONE){
                  stepModel.roles.forEach(role => {
                    if (role.assigneeUser) {
                      const assigneeUser = role.assigneeUser
                      if (assigneeUser?.objectType === UserObjectType.User) {
                        assignees.push({ ...assigneeUser, id: assigneeUser.userId })
                      } else if (assigneeUser?.objectType === UserObjectType.Team) {
                        const teamMembers = allTeamMemberMap[assigneeUser.teamId] || allClientTeamMemberMap[assigneeUser.teamId]
                        if (teamMembers) {
                          assignees = assignees.concat(teamMembers.filter(m => !m.isDisabled))
                        }
                      }
                    }
                  })
                }else{
                  stepModel.subSteps?.filter(subStep => !subStep.isDeleted).forEach(subStep => {
                    if (subStep.assignee?.user) {
                      assignees.push({ ...subStep.assignee.user, id: subStep.assignee.user.userId })
                    } else if (subStep.assignee?.team) {
                      const teamId = subStep.assignee.team.teamId
                      const teamMembers = allTeamMemberMap[teamId] || allClientTeamMemberMap[teamId]
                      if (teamMembers) {
                        assignees = assignees.concat(teamMembers.filter(m => !m.isDisabled))
                      }
                    }
                  })
                }
                if (assignees.length) {
                  const uniqAssignees = uniqBy(assignees, 'id')
                  const checkState = BinderUtil.isMatchBinderUsersCap(uniqAssignees, true)
                  if (!checkState.isMatch) {
                    if(stepType === FlowStepType.WORKFLOW_STEP_TYPE_MILESTONE){

                    }else{
                      getRootVM().$mxMessage.error(checkState.errorMsg)
                    }
                    return callbacks?.events?.onError?.({isExceedBoardUserCap: true})
                  }
                }
                await createStepAndWorkspace(stepModel, amWorkflow)
                handleOtherActionsDataStatistic(assignees)
              }

              const hasAutomations = !!amWorkflow?.steps?.some(step => !step.isDeleted && step.type === FlowStepType.WORKFLOW_STEP_TYPE_AUTOMATION)
              popSuccessCreatedMsg(hasAutomations)
              onActionCreated()
              hideStepCreator()
              callbacks?.onCompleted?.()
            } catch (err) {
              console.log('error : ', err)
              callbacks?.onError?.(err)
              const rootVm = getRootVM()
              rootVm.$mxMessage.error(rootVm.$t('system_exceed_limit_error'))
            }
            destroyDDRSupportStore()
          },
          back: () => {
            hideStepCreator()
            if (!initWithSelectedTemplate && selectedActionTemplate) {
              selectedActionTemplate = null
              selectedOrginTemplate = null
            }
            if (goCreateFromTemplateSelector) {
              gotoStep(StepUI.SelectActionTemplate)
            } else {
              if(isAddIntoExistBoard){
                if (boardInstant.isEmptyFlow()) {
                  gotoStep(StepUI.SelectWorkspace)
                }else{
                  gotoStep(StepUI.SelectPosition)
                }
              }else {
                gotoStep(StepUI.SelectWorkspace)
              }
            }
            destroyDDRSupportStore()
          },
          close: () => {
            hideStepCreator()
            destroyDDRSupportStore()
          }
        }, () => {
          const isSignature = selectedActionType == FlowStepType.WORKFLOW_STEP_TYPE_SIGNATURE
          const flowBoardId = selectedBoard.id
          let isSequential = false, flowStepOrderNumber
          let isReverseMilestoneContext = false, isFirstStepContext = false
          if(selectedBoard.id && selectedBoard.workflows) {
            if (!selectedBoard.workflows?.[0]?.isParallel) {
              flowStepOrderNumber = (prevStepOrderNumber || 0) + 100
              if (prevStepOrderNumber && prevStepOrderNumber % 100000 === 0) {
                const firstStepOrderNumber = parseInt(selectedBoard.workflows?.[0]?.transformedSteps?.find(step => step.stepIndex === 0)?.order_number || 0)
                if (flowStepOrderNumber <= firstStepOrderNumber) {
                  flowStepOrderNumber = 0
                }
              }
              isSequential = true
            }
            const flowBoardViewModel = WorkflowBoardEntity.toViewModel(boardInstant.mxBoard.value.board)
            const workflow = flowBoardViewModel.workflow
            let prevStep = workflow.steps?.find(s => s.clientUuid === prevStepClientUuid)
            if(!prevStep) {
              isFirstStepContext = true
              prevStep = workflow.milestones?.find(m => m.clientUuid === prevStepClientUuid)
              isReverseMilestoneContext = prevStep?.reverseWorkflowOrder
            } else {
              const msContext = workflow.milestones?.find(ms => ms.clientUuid === prevStep.belongToMilestoneId)
              isReverseMilestoneContext = msContext?.reverseWorkflowOrder
            }
          }

          const props =  {
            flowBoardId,
            isSequential,
            flowStepOrderNumber,
            isFirstStepContext,
            prevStepClientUuid,
            isReverseMilestoneContext,
            isDirectFlowMode: true,
            isFlowTemplate: false,
            scenes: ActionScenes.FlowWorkspace,
            supportNoAssignee: false,
            enableEditTemplate: !isUseExistTemplate,
            isEditStepInFlowBinder: true,
            useDefaultUsers: true,
            verifyUserInBinder:isAddIntoExistBoard,
            type: stepType,
            isCreateTemplateMode: true,
            isFromTemplate: !!(selectedActionTemplate?.sequence || selectedActionTemplate?.SPath),
            roles: boardInstant.availableRoles.value,
            // Leon: Here need to be modified, we shouldn't use both getAvailableFlowStepAssignees and mergeTeamAsAssignee here
            //       Suggest to remove mergeTeamAsAssignee, and assemble related logic in getAvailableFlowStepAssignees.
            boardUsers: getAvailableFlowStepAssignees(userUtil.mergeTeamAsAssignee(boardInstant.members.value,boardInstant.teams.value,selectedActionType), boardInstant.availableRoles.value),
            selectUserDirect: true,
            defaultUsers: getAvailableFlowStepAssignees(userUtil.mergeTeamAsAssignee(boardInstant.members.value,boardInstant.teams.value,selectedActionType), boardInstant.availableRoles.value),
            subType: selectedIntegrationType,
            integrationApp: selectIntegrationApp,
            beforeSubmit: isSignature && !isAddIntoExistBoard ? () => {
              return createWorkspace(selectedBoard.name, selectedBoard.description).then(board => {
                isAddIntoExistBoard = false
                boardInstant.init(board.id);
                return board
              })
            } : null,
            template: selectedActionTemplate || {},
            step: flowStep,
            showBack: true,
            enableDDR: true,
            ...stepExtraProp,
            ...integrationProps
          }
          if(stepType === FlowStepType.WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP){
            props.workspaceVariables = ObjectUtils.getByPath(flowBoardModel, 'workflow.variables') || []         
          }
          return props
        })
        return [showStepCreator, hideStepCreator]
      } else {
        const [showActionCreator, hideActionCreator] = useActionObjectCreation({
          created: ({ hasAutomations } = {}) => {
            popSuccessCreatedMsg(hasAutomations)
            onActionCreated()
            hideActionCreator()
          },
          back: () => {
            if (!initWithSelectedTemplate && selectedActionTemplate) {
              selectedActionTemplate = null
              selectedOrginTemplate = null
            }
            hideActionCreator()
            if (goCreateFromTemplateSelector) {
              gotoStep(StepUI.SelectActionTemplate)
            } else {
              gotoStep(StepUI.SelectWorkspace)
            }
          }
        }, () => {
          const props = {}
          if (selectedActionTemplate) {
            if (selectedActionType === ActionObjectType.Todo) {
              props.presetObject = {...selectedActionTemplate}
            } else {
              const {boardId, name} = selectedActionTemplate
              props.presetObject = {...selectedActionTemplate, boardId, templateName: name}
            }
          }
          let boardUsers = []
          if (boardInstant?.members.value) {
            boardUsers = boardInstant?.members?.value?.filter(member => !member.is_deleted && !member.disabled && !member.isBot)
          }
          if(boardInstant?.teams?.value) {
            selectedBoard.teams = boardInstant.teams.value
          }

          // create action by "+ new" button directly, and put it in a new workSpace
          let isDirectCreateActionInNewWorkSpace = false
          if (isEmpty(boardInstant)) {
            isDirectCreateActionInNewWorkSpace = true
          }

          return {
            canBack: true,
            showBack: true,
            // -- esign -- should not provide boardId when using esign-template
            boardId: selectedBoard?.id,   
            selectUserDirect: true,
            // -- esign-end --

            type: selectedActionType,
            isDirectMode: true, 
            isDirectCreateActionInNewWorkSpace,
            enableEditTemplate: !isUseExistTemplate,
            template: selectedActionTemplate,
            hostBoard: {...selectedBoard, boardUsers},
            isFromTemplate: !!(selectedActionTemplate?.sequence || selectedActionTemplate?.SPath),
            integrationApp: selectIntegrationApp,
            boardUsers,
            defaultUsers: userUtil.mergeTeamAsAssignee(boardUsers, selectedBoard.teams, selectedActionType),
            isObjectCreation: true,
            useDefaultUsers: true,
            ...props
          }  
        })
        return [showActionCreator, hideActionCreator]
      }
    }
  }

  const show = async () => {
    currentStep = -1
    selectedOrginTemplate = null
    selectedActionTemplate = null
    boardInstant = null
    selectedBoard = null
    isUseExistTemplate = false
    goCreateFromTemplateSelector = false

    if(initPayload.type || initPayload.template) {
      initWithSelectedTemplate = !!initPayload.template
      gotoStep(StepUI.SelectWorkspace)

      if (initWithSelectedTemplate) {
        await setSelectedTemplate()
      } else {
        setSelectedActionType()
      }
    } else {
      gotoStep(StepUI.SelectActionType)
    }

  }

  return [show]
}
