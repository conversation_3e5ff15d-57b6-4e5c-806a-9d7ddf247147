import { ActionErrorInfo } from '@views/stores/manageAction';
import MoxoErrorType from '@views/stores/moxoError/moxoErrorType';
import {getActionTypeWordingCapitalized } from '@views/workflows/utils/getActionTypeWording';


export function needDismissDialog (actionErrorInfo: ActionErrorInfo) {
    let need = true
    if(actionErrorInfo?.error?.name === MoxoErrorType.UNKNOWN || actionErrorInfo?.error?.name === MoxoErrorType.USER_EXCEED_MAX_LIMIT || actionErrorInfo?.error?.name === MoxoErrorType.CLIENT_NUM_NOT_MATCHED) {
        //user can retry(add/update etc)
        need = false
    }
    return need
}
export async function handleActionError (vm: any, actionErrorInfo: ActionErrorInfo, callback: () => void) {
    const actionType = getActionTypeWordingCapitalized(actionErrorInfo.actionType)
    switch(actionErrorInfo?.error?.name) {
        case MoxoErrorType.ACTION_COMPLETED_WHILE_EDIT: {
            const title = vm.$t('Current_action_was_completed', {actionType})
            const warningTips = vm.$t('This_action_has_been_completed')
            await vm.$mxAlert(warningTips, title, {
                confirmButtonText: vm.$t('dismiss'),
                confirmButtonType: 'gray',
                customClass: 'new-style',
                showCancelButton: false
              })
            break;
        }
        case MoxoErrorType.ACTION_DELETED_WHILE_EDIT: {
            const title = vm.$t('Unable_to_save_changes')
            const warningTips = vm.$t('This_action_was_already_completed_or_deleted', {actionType})
            await vm.$mxAlert(warningTips, title, {
              confirmButtonText: vm.$t('dismiss'),
              confirmButtonType: 'gray',
              customClass: 'new-style',
              showCancelButton: false
            })
            break;
        }
        case MoxoErrorType.ACTION_BOARD_DELETED: {
            break;
        }
        case MoxoErrorType.USER_REMOVED_FROM_ACTION_BOARD: {
            break;
        }
        case MoxoErrorType.PREPARE_COMPLETED_DISABLED_PREPARERCHANGED: {
            await vm.$mxAlert(vm.$t('This_action_cannot_be_prepared'), vm.$t('Unable_to_Prepare'), {
                confirmButtonText: vm.$t('dismiss'),
                confirmButtonType: 'primary',
                customClass: 'new-style',
                showCancelButton: false
              })
            break;
        }
        case MoxoErrorType.USER_EXCEED_MAX_LIMIT: {
            vm.$mxMessage.error(vm.$t('Maximum_participants_reached_Please_try_again'))
            break
        }
        case MoxoErrorType.CLIENT_NUM_NOT_MATCHED: {
            const errorData = actionErrorInfo?.error?.data
            if(errorData) {
                const errorMssage = errorData.requiredClientCounts === 1 ? 
                  vm.$t('Group_workspaces_are_limited_to_1_client_tip') :
                  vm.$t('Group_workspaces_are_limited_to_n_clients_tip', {
                    num: errorData.requiredClientCounts
                })
                vm.$mxMessage.error(errorMssage)
            }
            
            break
        }
        default: {
           vm.$mxMessage.error(vm.$t('something_went_wrong_tip'))
        }
    }
    callback()
}
