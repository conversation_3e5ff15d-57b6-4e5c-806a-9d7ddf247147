<template>
    <el-dialog
      :closeOnPressEscape="false"
      class="language-wrap"
      :visible.sync="confirmLanguageDialog"
      :close-on-click-modal="false"
    >
      <div>
        <div class="title">{{$t('Change_language_to',{language:newLanguage})}}</div>
        <div class="message">{{$t('chang_language_infos') }}</div>
        <div class="foot-btns">
          <el-button
            type="secondary"
            size="small"
            @click="cancel"
            v-mx-ta="{ page: 'languageConfirmDialog', id: `cancel`}">{{$t('use_language',{language:previousLanguage})}}
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="confirm"
          v-mx-ta="{ page: 'languageConfirmDialog', id: `add`}"> {{$t('use_language',{language:newLanguage})}}
        </el-button>
        </div>
      </div> 
   </el-dialog>
</template>

<script>
import { MxConsts} from '@commonUtils'
const languageList = MxConsts.supportedLanguageList
export default {
  name: "languageUpdate",
  props:{
    confirmLanguageDialog:{
      type: Boolean,
      default:false
    },
    newLng:{
      type:String
    },
    currentLanguage:{
      type:String
    }
  },

  computed:{
    previousLanguage(){
      let curLanguage = languageList.find(lng =>{
        return lng.code === this.currentLanguage
      })
      return curLanguage.localName
    },
    newLanguage(){
      if(!this.newLng){
        return ''
      }
      let Language = languageList.find(lng =>{
        if(this.newLng === 'zh-TW'){
          return lng.code === 'zh-tw'
        }else{
          return lng.code === this.newLng
        }
      })
      return Language.localName
    }
  },
  methods:{
    cancel(){
      this.$emit('cancel')
    },
    confirm(){
      this.$emit('confirm')
    }
  }
}
</script>



<style lang="scss" scoped>
 .language-wrap{
    ::v-deep .el-dialog{
      width: 456px;
    }
    ::v-deep .el-dialog__header{
      display: none;
    }
    ::v-deep .el-dialog__body{
      padding: 0;
    }
    .title{
      padding: 22px 28px 11px;
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;
    }
    .message{
      padding: 0px 28px;
      font-size: 14px;
      line-height: 20px;
    }
    .foot-btns{
      padding: 19px 28px 20px;
      text-align: right;
      button{
        max-width: 190px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

  }
</style>