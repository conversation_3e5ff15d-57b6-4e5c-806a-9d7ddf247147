<template>
    <base-modal :before-close="handleClose" :visible.sync="showDialog">
        <div slot="title">{{$t('download')}}</div>
        <div slot="content">
            <div class="" role="group" @click="onSelect" >
                <a
                  tabindex="0"
                  @keypress.enter="onSelect"
                  class="list-group-item mx-clickable" v-if="option.annotate" data-type="annotate">
                    <i class="micon-file-pdf"></i>
                    {{$t('pdf_with_annotation')}}
                </a>
                <a
                  tabindex="0"
                  @keypress.enter="onSelect"
                  class="list-group-item mx-clickable" v-if="option.original" data-type="original">
                    <i class="micon-file"></i>
                    {{$t('share_original_content')}}
                </a>
                <a
                  tabindex="0"
                  @keypress.enter="onSelect"
                  class="list-group-item mx-clickable" v-if="option.pdf" data-type="pdf">
                    <i class="micon-file-pdf"></i>
                    {{$t('pdf_file')}}
                </a>
                <a
                  tabindex="0"
                  @keypress.enter="onSelect"
                  class="list-group-item mx-clickable" v-if="option.zip" data-type="zip">
                    <i class="micon-file"></i>
                    {{$t('zip_file')}}
                </a>
            </div>
        </div>

    </base-modal>


</template>

<script>
  export default {
    name: "DownloadTypeSelector",
    props: {
      option:{
        type:Object,
        default: ()=>{return{}}
      },
      callback:{
        type: Function,
        default:()=>{}
      }
    },
    data: function(){
      return {
        showDialog:true
      }
    },
    methods: {
      onSelect (e) {
        let name = e.target.getAttribute('data-type')

        this.callback({[name] : true});
      },
      handleClose (done) {
        this.callback(null);
        done();
      }
    }
  }
</script>

<style scoped lang="scss">
    .btn-group {
        width:100%;
        .list-group-item{
            cursor: pointer;
        }
    }
    a.is-keyboard-focus {
      outline-offset: -2px;
    }
</style>
