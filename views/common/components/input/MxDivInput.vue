<template>
  <div>
    <Transition
      name="slide"
      appear>
      <div 
        ref="divInput"
        role="textbox"
        tabindex="0"
        class="mx-div-input"
        contenteditable="true"
        @click.stop 
        @input.stop="handleChange"
        @focus.stop="setCursorToEnd" 
        @blur.stop="handleChange" />
    </Transition>
    <Transition
      name="fade-up"
      appear>
      <div
        v-if="footer"
        class="flex justify-end margin-top-16px">
        <el-button
          type="raised"
          size="small"
          :aria-label="$t('cancel')"
          @click.stop="$emit('cancel')">
          {{ $t('cancel') }}
        </el-button>
        <el-button
          type="primary"
          size="small"
          icon="micon-enter-key"
          :disabled="isDisabled"
          :loading="saveLoading"
          :aria-label="$t('save')"
          @click.stop="$emit('save')">
          {{ $t('save') }}
        </el-button>
      </div>
    </Transition>
  </div>
</template>

<script>
export default {
  name: 'MxDivInput',
  props: {
    value: {
        type: String,
        default: ''
    },
    footer: {
        type: Boolean,
        default: true
    },
    disabledFunc: {
        type: Function,
        default: () => false
    },
    saveLoading: {
      type: Boolean,
      default: false
    }

  },
  data () {
    return {
      initValue: this.value,
    }
  },
  computed: {
    isDisabled () {
      return this.value === this.initValue || this.disabledFunc()
    }
  },
  
  
  async mounted () {
    await this.$nextTick()
    this.$refs.divInput.textContent = this.value
    this.focus()
  },
  methods: {
    focus () {
      const el = this.$refs.divInput
      el.focus()
        
    },
    
    handleChange (e) {
      if (e.target.innerHTML === '<br>') {
        e.target.innerHTML = ''
      }
      this.$emit('input', e.target.textContent);
        
    },
    setCursorToEnd (e) {
      const el = e.target;
      const range = document.createRange();
      const selection = window.getSelection();
      range.selectNodeContents(el);
      range.collapse(false); 
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }
}
</script>

<style lang="scss" scoped>
.mx-div-input{
  outline: none;
  position: relative;
  padding-bottom: 4px;
  word-break: break-word; 
  overflow-wrap: break-word;
  overflow: hidden;
  
  &::after{
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--mx-color-var-fill-primary);
    transform: scaleX(1);
    transform-origin: left;
    transition: all 0.2s ease;
  }
 
}
.fade-up-enter-active, .fade-up-leave-active {
  transition: all 0.2s ease;
}
.fade-up-enter, .fade-up-leave-to {
  transform: translateY(-10px); 
  opacity: 0; 
}
.slide-enter, .slide-leave-to {
  &::after{
    transform: scaleX(0);
    opacity: 0; 
  }
}
</style>