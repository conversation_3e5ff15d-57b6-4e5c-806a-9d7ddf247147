<template>
  <div 
    class="resizeable-layout"
    :class="{ 'disabled': disabled }"
    :style="style">
    <div 
      class="resize-bar"  
      :class="isResizeBarLeft ? 'left-pos' : 'right-pos'"  
      @pointerdown="handleDown">
      <div
        v-if="showResizeIndicator">
        <div></div><div></div><div></div><div></div>
      </div>
    </div>
    <slot ref="box"></slot>
  </div>
</template>

<script>
import {
  computed,
  onMounted,
  defineComponent,
  onUnmounted,
  ref,
  watch,
  getCurrentInstance,
  nextTick, inject, provide
} from '@vue/composition-api'
export default defineComponent({
  name: 'ResizeableLayout',
  props: {
    minWidth: {
      type: Number,
      default: 0
    },
    maxWidth: {
      type: Number,
      default: 0
    },
    resizeBarPos: {
      type: String,
      default: 'left'  // value is 'left' or 'right'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showResizeIndicator: {
      type: Boolean,
      default: true
    },
  },
  setup (props,{ emit }) {
    const vm = getCurrentInstance()
    const box = ref(null)
    let startWidth = 0
    let startPoint

    const isResizeBarLeft = computed(() => {
      return props.resizeBarPos === 'left'
    })

    const style = computed(() => {
      const styleObj = {}

      if (props.minWidth) {
        styleObj['min-width'] = `${props.minWidth}px`
      }

      if (props.maxWidth) {
        styleObj['max-width'] = `${props.maxWidth}px`
      }

      return styleObj
    })

    const handleMove = (e) => {
      if (props.disabled) return
      let delta = 0
      if (isResizeBarLeft.value) {
        delta = startPoint.x - e.clientX
      } else {
        delta = e.clientX - startPoint.x
      }
      const width = startWidth + delta
      emit('resize', { width })
    }
    const handleUp = (e) => {
      if (props.disabled) return
      document.body.classList.remove('mx-unselectable')

      document.removeEventListener('pointermove', handleMove)
      document.removeEventListener('pointerup', handleUp)
      emit('resizeEnd')
    }
    const handleDown = (e) => {
      if (props.disabled) return
      emit('resizeStart')
      document.body.classList.add('mx-unselectable')
      startWidth = vm.proxy.$el.offsetWidth
      startPoint = {
        x: e.clientX,
        y: e.clientY
      }
      document.addEventListener('pointermove', handleMove)
      document.addEventListener('pointerup', handleUp)
    }
    return {
      handleDown,
      style,
      isResizeBarLeft
    }
  }
})
</script>

<style scoped lang="scss">
.resizeable-layout{
  position: relative;
  width: 100%;

  &.disabled {
    .resize-bar {
      cursor: auto;

      > div {
        > div {
          box-shadow: none;
          background: transparent;
        }
      }

      &:hover {
        background: transparent;
      }
    }
  }
}
.resize-bar{
  position: absolute;
  height: 100%;
  z-index: 5;
  width: 5px;
  cursor: ew-resize;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  &.left-pos {
    left: -10px;
    transform: translateX(10px);
  }
  &.right-pos {
    right: 10px;
    transform: translateX(10px);
  }
  > div{
    display: flex;
    grid-gap: 3px;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    align-items: center;
    margin-left: 3px;
    > div{
      width: 3px;
      height: 3px;
      border-radius: 3px;
      box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.1);
      background: rgba(0, 0, 0, 0.15);
    }
  }
  &:hover{
    background: #0a84ff50;
  }
}
</style>
