<template>
  <div
    class="inline-editor"
    @keydown.tab="focusableElementLoop">
    <form
      role="form"
      style="width:100%;">
      <div :class="['form-group', {'has-error': !message && !options.canSetEmpty}]">
        <el-input
          ref="inputRef"
          v-model="message"
          v-typeahead="options.mentionSupport ? {} : {noMention: true}"
          v-typeheadMsg="options.canMentionTemplateMsg ? {} : { noMention:true }"
          :name="options.bindingPath"
          :placeholder="options.placeholder"
          :rows="2"
          :max="options.maxLength"
          type="textarea"
          autosize
          autocapitalize="off"
          autocorrect="off"
          @input="$emit('input', $event)"
          @textarea-change="$emit('resize')"
          @focus="message=$event.target.value"
          @blur="message=$event.target.value"
          @keyup.native="$emit('keyup', $event)"
          @keypress.enter.native.exact.prevent="save($event)" />
      </div>
      <div
        v-if="options.needButton"
        class="form-group edit-button mx-double-button">
        <el-button
          ref="primary"
          v-mx-ta="{ page: 'editor', id: `save`}"
          :size="options.buttonSize ? options.buttonSize : 'medium'"
          :disabled="options.saveBtnDisabled"
          :loading="saveBtnLoading"
          type="primary"
          icon="micon-enter-key"
          @click.stop="save($event)">
          {{ $t('save') }}
        </el-button>
        <el-button
          v-mx-ta="{ page: 'editor', id: `cancel`}"
          :size="options.buttonSize ? options.buttonSize : 'medium'"
          type="raised"
          @click="cancel()">
          {{ $t('cancel') }}
        </el-button>
      </div>
    </form>
  </div>
</template>

<script>
import typeheadMsg from '@views/common/components/mentionSelector/mentionTplMsgDirective'
import typeahead from '@views/common/components/mentionSelector/mentionDirective'
import accessibility, { focusTo } from '@views/common/accessibility'
import popupBehavior from '../accessibility/popup'

export default {
  name: 'InlineEditor',
  directives: { 
    typeahead,
    typeheadMsg
  },
  mixins: [accessibility, popupBehavior],
  props: ['options'],
  data () {
    return {
      message: '',
      saveBtnLoading: false
    }
  },
  computed: {
    inputValue () {
      return this.options.value
    }
  },
  mounted () {
    this.a11yCacheTrigger()
    this.$refs.inputRef.focus()
    this.$emit('focus')
    this.$refs.inputRef.scrollTop = this.$refs.inputRef.scrollHeight
    this.message = this.inputValue
    if (this.options.saveBtnLoading) {
      this.unwatch = this.$watch('options.saveBtnLoading', (val) => {
        if (!val) {
          this.saveBtnLoading = false
        }
      })
    }
  },
  beforeDestroy() {
    if (this.unwatch) {
      this.unwatch()
    }
  },
  methods: {
    save ($event) {
      const inputValue = ($event.target.value || this.message).trim()
      if (this.options.canSetEmpty || inputValue) {
        this.$emit('submit', inputValue)
        if (this.options.saveBtnLoading) {
          this.saveBtnLoading = true
        } else {
          this.cancel()
        }
      }
    },
    cancel () {
      this.$emit('exitEdit', this.options.bindingPath, false)
      this.focusToTrigger()
    }
  }
}
</script>

<style lang="scss">


  .inline-editor textarea {
    outline: none;
    border: none;
    border-bottom: 1px solid $mx-color-var-fill-primary;
    background: none;
    box-shadow: none;
    border-radius: 0;

    font-size: 14px;
    font-family: $mx-font-primary;
    color: rgb(31, 33, 38);
    line-height: 20px;
    height: 35px;
    resize: none;
    overflow: auto;
    padding: 6px 12px;
    overflow-y: hidden;
  }
  .mx-double-button .btn, .mx-double-button .el-button{
    max-width: 45%;
  }
</style>
