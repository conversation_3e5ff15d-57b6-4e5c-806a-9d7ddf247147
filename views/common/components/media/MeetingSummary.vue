<template>
  <div class="summary-container">
    <div class="summary-header mx-margin-bottom-xxs">
      <div class="mx-text-b1">{{ $t('Summary') }}</div>
      <div
        v-if="groupTags.enable_copy_to && summaryContent"
        role="button"
        tabindex="0"
        :class="['copy-btn mx-branding-text-action mx-clickable', {'copied': summaryCopied}]"
        @keydown.enter="copy"
        @click="copy">
        <i class="micon-copy font-icon-sm"></i>
        <span class="mx-semibold">
            {{ summaryCopied ? $t('copied') : $t('copy') }}
          </span>
      </div>
    </div>
    <div class="summary-content text-center">
      <div
        v-if="isLoading"
        v-mx-loading-spinner
        class="loading-wrapper"/>
      <div
        v-else-if="summaryContent"
        class="mx-hover-show">
        <div
          v-show="!showInlineEditor"
          class="mx-flex-container">
          <div
            class="mx-text-c2 text-left mx-margin-top-xs mx-margin-bottom-xs">
            <span
              v-safe-html="displayedSummaryContent"
              class="mx-text-c2" />
            <span
              v-if="isSummaryEdited"
              class="edited-label mx-text-c4">
              -{{ $t('Edited') }}
            </span>
          </div>
          <i
            v-if="summaryInfo.isOwner"
            class="micon-edit-xs mx-color-secondary mx-hover-hide mx-margin-top-xs mx-margin-left-xs mx-clickable"
            @click="showInlineEditorPage"/>
        </div>
        <InlineEditor
          v-if="showInlineEditor"
          :options="inlineEditorOpts"
          @exitEdit="showInlineEditor = false"
          @resize="scrollIntoView"
          @input="checkInputValue"
          @submit="saveSummaryContent" />
      </div>
      <div
        v-else-if="isLoadFailed"
        class="load-failed-wrapper mx-text-b2 text-center">
        <div role="status">{{ $t('load_failed') }}</div>
        <div
          tabindex="0"
          @click="loadSummaryContent"
          @keydown.enter="loadSummaryContent"
          class="mx-branding-text-action mx-clickable mx-margin-left-xxs">
          <span class="mx-semibold">{{ $t('retry') }}</span>
          <i class="micon-mep-rotate" />
        </div>
      </div>
      <img
        v-else
        :src="emptySummary"
        alt=""
        aria-hidden="true">
    </div>
  </div>
</template>

<script>
import InlineEditor from '@views/common/components/inlineEditor.vue'
import emptySummary from '@views/theme/src/images/meet/empty_summary.svg'
import templateFormat from '@views/common/utils/formatter/templateFormat'
import { copyText } from '@views/common'
import { mapActions, mapGetters } from 'vuex'

export default {
  name: "MeetingSummary",
  components: {
    InlineEditor
  },
  props: {
    meetBoardId: {
      type: String,
      required: true
    },
    summaryInfo: {
      type: Object,
      default: () => {
        return {
          isOwner: false,
          isSummaryEdited: false,
          summarySeq: 0
        }
      }
    }
  },
  data() {
    return {
      emptySummary,
      isLoading: true,
      isLoadFailed: false,
      summaryContent: '',
      summaryCopied: false,
      showInlineEditor: false,
      inlineEditorOpts: {},
      isSummaryEdited: false
    }
  },
  computed: {
    ...mapGetters('group', ['groupTags']),
    displayedSummaryContent () {
      return templateFormat.chatFormat(this.summaryContent, true)
    }
  },
  created () {
    if (this.summaryInfo.summarySeq) {
      this.loadSummaryContent()
    } else {
      this.isLoading = false
    }
    if (this.summaryInfo.isSummaryEdited) {
      this.isSummaryEdited = true
    }
  },
  methods: {
    ...mapActions('meet', ['getSummaryContent', 'updateMeetSummary']),
    loadSummaryContent () {
      this.isLoading = true
      this.getSummaryContent({meetBoardId: this.meetBoardId, summarySeq: this.summaryInfo.summarySeq}).then((content) => {
        this.isLoadFailed = false
        try {
          JSON.parse(content)
          this.isLoadFailed = true
        } catch (e) {
          this.summaryContent = content
        }
        this.isLoading = false
      })
    },
    copy () {
      copyText(this.summaryContent)
      this.summaryCopied = true
      this.$nextTick(() => {
        setTimeout(() => {
          this.summaryCopied = false
        }, 3000)
      })
    },
    showInlineEditorPage () {
      this.inlineEditorOpts = {
        value: this.summaryContent,
        needButton: true,
        buttonSize: 'small',
        saveBtnDisabled: true,
        saveBtnLoading: true
      }
      this.showInlineEditor = true
    },
    checkInputValue (val) {
      this.inlineEditorOpts.saveBtnDisabled = val === this.summaryContent
    },
    scrollIntoView () {
      this.$nextTick(() => {
        this.$el.scrollIntoView(false)
      })
    },
    saveSummaryContent (val) {
      this.inlineEditorOpts.saveBtnLoading = true
      this.updateMeetSummary({meetBoardId: this.meetBoardId, summaryText: val}).then(() => {
        this.summaryContent = val
        this.isSummaryEdited = true
        this.showInlineEditor = false
      }).catch(() => {
        this.$mxMessage.error(this.$t('system_unknown_error'))
      }).finally(() => {
        this.inlineEditorOpts.saveBtnLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.summary-container {
  padding: 16px 20px;
  border-radius: 12px;
  background-color: #FAFAFA;
  .summary-header {
    display: flex;
    justify-content: space-between;
    .copy-btn {
      &.copied {
        color: $mx-color-var-text-tertiary;
      }
    }
  }
  .summary-content {
    height: calc(100% - 28px);
    .edited-label {
      font-style: italic;
      color: $mx-color-var-label-tertiary;
    }
  }
  .loading-wrapper, .load-failed-wrapper {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.inline-editor {
  width: calc(100% + 40px);
  margin-left: -20px;
  padding: 8px 20px;
  background-color: $mx-color-var-fill-quaternary;
  ::v-deep .form-group {
    margin-bottom: 12px;
    textarea {
      padding: 0;
    }
    &.mx-double-button {
      margin-bottom: 0;
      justify-content: end;
      flex-direction: row-reverse;
      button {
        max-width: fit-content;
        &:first-child {
          margin-left: 12px !important;
        }
      }
    }
  }
}
</style>