<template>
  <base-modal
    :class="['recording-player-modal', {'is-has-transcript': isHasTranscript}]"
    :visible.sync="visibleProxy" 
    :close-on-press-escape="closeOnPressEsc"
    :modal-option="modalOption">
    <template slot="title">
      <div class="flex w-full align-center">
        <div class="flex-1 mx-ellipsis text-align-left">
          {{ title }}
        </div>
        <template v-if="canDownloadAndShareMeetingRecording">
          <el-button
            v-if="recordingDetail.data.recordingsForDownloadAndCopyTo && groupTags.enable_copy_to && hasCopyTo"
            type="secondary"
            size="small"
            icon="micon-copy"
            @click="copyTo">
            {{ $t('copy_to') }}
          </el-button>
          <el-button
            v-if="recordingDetail.data.recordingsForDownloadAndCopyTo"
            type="secondary"
            size="small"
            icon="micon-mep-download"
            @click="download">
            {{ $t('download') }}
          </el-button>
        </template>
      </div>
    </template>
    <template slot="content">
      <div
        v-mx-loading="recordingDetail.loading"
        class="player-container h-full">
        <MxVideo
          v-if="videoUrl" 
          v-show="!recordingDetail.loading"
          ref="video"
          :video-src="videoUrl" 
          :track-src="recordingDetail.data.vttTranscriptionsUrl" 
          open-time-update
          :videoLayoutMode="isHasTranscript ? 'fluid' : 'fill'"
          @timeupdate="handleUpdatetime" />
        <div
          class="vertical-scroll-wrapper">
          <MeetingSummary
            v-if="isHasTranscript"
            v-show="!recordingDetail.loading"
            :meetBoardId="meetId"
            :summaryInfo="summaryInfo"/>
          <div
            v-if="isHasTranscript"
            class="transcript-container">
            <MeetRecordingTranscription
              ref="meetTranscription"
              :transcriptions="transcriptions"
              @update-current-time="handleUpdateCurrentTime"
              @update-transcription="handleUpdateTranscription" />
          </div>
        </div>
      </div>
      <MxCopyFileSelector
        v-if="copyRecording.show"
        :visible.sync="copyRecording.show"
        :binder-id="copyRecording.binderId"
        :resource="copyRecording.resource"
        @after="copyRecording.callback"
        @error="copyRecording.errorCallback" />
    </template>
    <!-- <template
      v-if="model.showDownload"
      slot="footer">
      <el-button
        type="default"
        style="margin-left: 12px"
        @click="visibleProxy = false">
        {{ $t('cancel') }}
      </el-button>
      <el-button
        type="primary"
        style="margin-right: 12px"
        @click="download">
        {{ $t('download') }}
      </el-button>
    </template> -->
  </base-modal>
</template>

<script>
import { visibleMixin } from '@views/common/components/modals/mixins'
import downloadActions from '../../mixins/downloadActions';
import MxVideo from './MxVideo.vue'
import MeetRecordingTranscription from '@views/thread/src/component/MeetRecordingTranscription.vue'
// import debounce from 'lodash/debounce'
import { mapActions, mapGetters } from 'vuex'
import { useState } from '@commonUtils/useAsyncState'
import throttle from 'lodash/throttle';
import MxCopyFileSelector from '@views/common/components/file/MxCopyFileSelector'
import MeetingSummary from '@views/common/components/media/MeetingSummary.vue'
import { ObjectUtils } from '@commonUtils'

export default {
  name: 'MxRecordingPlayer',
  components: {
    MeetingSummary,
    MxVideo,
    MeetRecordingTranscription,
    MxCopyFileSelector
  },
  mixins: [visibleMixin, downloadActions],
  provide: function () {
    return {
      meetId: this.meetId,
      canEdit: () => this.recordingDetail.data?.isOwner,
      getVideo: () => this.$refs.video
    }
  },
  props: {
    model: {
      type: Object,
      default: () => ({})
    },
   
    meetId: {
      type: String,
      default: ''
    },
    hasCopyTo: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      flag: true,
      closeOnPressEsc: false,
      modalOption: {
        top: '0',
        width: 'auto'
      },
      recordingDetail: useState({}, () => this.meetId ? this.readMeetRecordingDetail(this.meetId) :{}),
      copyRecording: {},
    }
  },
  computed: {
     ...mapGetters('group', ['groupTags']),
     ...mapGetters('privileges', ['canDownloadAndShareMeetingRecording']),
    transcriptions () {
      return this.recordingDetail.data?.transcriptions || []
    },
    isHasTranscript () {
      const { transcriptionSeq: transcription , vttTranscriptionsSeq: transcription_vtt } = this.recordingDetail.data 
      return transcription && transcription_vtt
    },
    videoUrl () {
      return this.model.source || this.recordingDetail.data.videoUrl
    },
    title () {
      return this.model?.name?.replace(/\.(mp4|mov|avi|wmv|flv|mkv|webm)$/i, '')
    },
    summaryInfo () {
       return ObjectUtils.pick(this.recordingDetail.data, ['isOwner', 'isSummaryEdited', 'summarySeq'])
    }
  },
 
  methods: {
    ...mapActions('meet', [
      'readMeetRecordingDetail',
    ]),
   
    download () {
      this.downloadUrl(this.recordingDetail.data.recordingsForDownloadAndCopyTo?.[0]?.download_url)
    },
    copyTo () {
      let recording = this.recordingDetail.data.recordingsForDownloadAndCopyTo?.[0]
      if (recording.chat || recording.vtt) {
        recording = [
          {sequence: recording.sequence},
          ...[{sequence: recording?.chat?.sequence}, {sequence: recording?.vtt?.sequence}, {sequence: recording?.summary?.sequence}].filter(item => item.sequence)
        ]
      } 
      this.copyRecording = {
        show: true,
        binderId: this.meetId,
        resource: recording,
        callback:  () => ({}),
        errorCallback:  () => ({})
      } 
    },
    

    // hideContextMenu (e) {
    //   e.preventDefault()
    // },

    binarySearchSubtitle (data, timestamp) {
      let start = 0
      let end = data.length - 1
      let mid = 0
      while (start <= end) {
        mid = Math.floor((start + end) / 2)
        if (data[mid].start <= timestamp && data[mid].end > timestamp) {
          return mid
        } else if (data[mid].start > timestamp) {
          end = mid - 1
        } else {
          start = mid + 1
        }
      } 
      return -1
    },

    // handleSeeked: debounce(function (currentTime) {
    //   const index = this.binarySearchSubtitle(this.transcriptions, currentTime)
    //   ~index && this.$refs.meetTranscription.setCurrentSubtitle(index)
    // }, 500),

    handleUpdatetime: throttle(function (currentTime) {
      const index = this.binarySearchSubtitle(this.transcriptions, currentTime)
      ~index && this.$refs?.meetTranscription?.setCurrentSubtitle(index)

    }, 500),

    handleUpdateCurrentTime (timestamp) {
      this.$refs.video.setVideoTime(timestamp)
    },
    async handleUpdateTranscription (executor) {
      const {vttTranscriptionsUrl, recordingsForDownloadAndCopyTo} = await executor()
      this.recordingDetail.data.vttTranscriptionsUrl = vttTranscriptionsUrl
      this.recordingDetail.data.recordingsForDownloadAndCopyTo = recordingsForDownloadAndCopyTo
    }
  }
}
</script>

<style lang="scss" scoped>
.recording-player-modal{
  ::v-deep .el-dialog{
   
    .el-dialog__header{
      .left {
        flex: 1
      }
    }
  }
  ::v-deep .el-loading-mask{
    margin: -24px;
  }
  &.is-has-transcript {
    .mx-video {
      margin-bottom: 20px;
    }
    .vertical-scroll-wrapper {
      padding-right: 20px;
      overflow-y: auto;
      flex: 1;
    }
  }
  .mx-video{
    flex: 1;
    ::v-deep .video-js {
      border-radius: 12px;
      video {
        border-radius: 12px;
      }
    }
  }
  .vertical-scroll-wrapper {
    padding-bottom: 20px;
  }
}
  
  video:focus {
    outline: none;
  }
 
  .recording-player-modal {
    margin: 40px;
    
    ::v-deep .el-dialog {
      margin: 0 auto;
      min-width: 944px;
      max-width: 1588px;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    ::v-deep .el-dialog__body {
      flex: 1;
      // aspect-ratio: 16/9;
      padding: 0;
      overflow: hidden;
    }
  }
  .player-container{
    display: flex;
    flex-direction: column;
    padding: 20px 20px 0;
  }
  @media screen and (max-width: 1023px) {
    .recording-player-modal {
      ::v-deep .el-dialog {
        min-width: auto;
      }
      &.is-has-transcript {
        .mx-video {
          width: calc(100% - 40px);
        }
      }
    }
  }
  @media screen and (max-width: 1200px) {
    .is-has-transcript {
      .player-container{
        padding-right: 0;
        padding-left: 0;
        .transcript-container{
          flex: 1;
          border-left: none;
          margin: auto;
          flex-shrink: 0;
          width: 100%;
        }
      }
      .mx-video{
        width: 440px;
        margin: 0 auto 20px;
        flex-grow: 0;
      }
      .video-js .vjs-tech {
        height :100%;
      }
      .vertical-scroll-wrapper {
        width: 100%;
        overflow-y: auto;
        padding-right: 20px;
        padding-bottom: 0;
        padding-left: 20px;
      }
    }
  }
  @media screen and (min-width: 1201px) {
    .is-has-transcript {
      .mx-video {
        width: calc(100% - 420px) !important;
        height: auto !important;
        padding-right: 20px;
      }
      .vertical-scroll-wrapper {
        width: calc(100% - 420px);
      }
    }
    .transcript-container {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
    }
  }
  .transcript-container{
    width: 440px;
    min-width: 0;
    flex-shrink: 0;
    border-left: 1px solid var(--mx-color-var-fill-tertiary);
  }
  
</style>


