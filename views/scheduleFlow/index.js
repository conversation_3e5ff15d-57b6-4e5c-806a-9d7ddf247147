import { popupFactory } from '@views/common/useComponent'
import { useFlowTemplateSelector } from '@views/workflows/plugins/useFlowTemplateSelector'
import { usePermissionInterceptor } from '@views/workflows/plugins/usePermissionInterceptor'
import i18nt from '@views/i18n/mepIndex'

import ScheduleFlowWorkspace from './src/ScheduleFlowWorkspace.vue'

export function transformFlowTplToScheduleFlowProp (tpl) {
  const { boardId, name, roles } = tpl
  return {
    baseObject: {
      flowMeta: {
        name,
        usedTemplateBoardId: boardId,
        originalTemplateBoardId: boardId,
        roleMapper: roles?.map(role => ({
          roleId: role.name
        })) ?? []
      }
    }
  }
}

export function useScheduleFlowWorkspace (template, vm, props, events) {
  return new Promise(resolve => {
    const [show] = popupFactory(ScheduleFlowWorkspace)(events, {
      template,
      ...props
    })
    show()
    resolve()
  })
}

export function showScheduleWorkspace (tpl, vm, props, events) {
  usePermissionInterceptor(useScheduleFlowWorkspace, {
    justUse: true,
    vmIns: vm,
    props,
    events: {
      'schedule-success': () => {
        if (events) {
          events.onSuccess && events.onSuccess()
        }
      }
    }
  })(tpl)
}

export function showScheduleFlowWithFlowTplSelector (vm, props) {
  const [showTplSelector, hideTplSelector] = useFlowTemplateSelector({
    select (tpl) {
      showScheduleWorkspace(tpl, vm, props, {
        onSuccess: () => {
          hideTplSelector()
        }
      })
    }
  }, {
    enableInstant: false,
    onlyTriggerEvent: true,
    useBtnText: i18nt.t('schedule')
  })
  showTplSelector()
}

export function useEditScheduledFlowWorkspace (events, props) {
  return popupFactory(ScheduleFlowWorkspace)(events, props)
}