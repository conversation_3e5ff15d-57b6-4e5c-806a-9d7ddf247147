<template>
  <el-dropdown
    :popper-append-to-body="false"
    trigger="click"
    @command="handleDropdownCommand($event)"
  >
    <el-button class="mx-admin-more-button" type="white" shadow square :size="size"
      icon="micon-mep-more" />
    <el-dropdown-menu
      class="mx-admin-list-dropdown"
      slot="dropdown"
      transform-origin="center center"
    >
      <el-dropdown-item icon="micon-mep-edit" command="editTeam" v-show="!fromPanel">
        <span>{{ $t('edit_group_details') }}</span>
      </el-dropdown-item>
      <el-dropdown-item v-show="!isHideDelete" icon="micon-mep-delete" command="delete" class="danger">
        <span>{{ $t('Delete_Group') }}</span>
      </el-dropdown-item>
      <el-dropdown-item v-show="isShowRemoveMembers" icon="micon-mep-delete" command="removeMembers" class="danger">
        <span>{{ $t('Remove_Members') }}</span>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: "GroupListItemDropdown",
  props: {
    team: {
      type: Object,
      default: () => {},
      required: true
    },
    fromPanel: {
      type: Boolean,
      default: false,
      required: false
    },
    size: {
      type: String,
      default: "mini"
    },
    isHideDelete: {
      type: Boolean,
      default: false
    },
    isShowRemoveMembers: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      searchKey: ""
    };
  },
  created() {},
  mounted() {},
  computed: {},
  methods: {
    handleDropdownCommand($event) {
      this.$emit("on-click-item", $event, {
        team: this.team,
        fromViewTeam: false
      });
    }
  }
};
</script>

<style scoped>
</style>