/*
 * Add a new client group OR edit a existing client group
 */

<template>
  <global-right-panel-layout
      :on-back="backStep"
      :show-back="isSecondStep">
    <template slot="header">
      <div>
        <div class="mx-text-h1">{{ pageTitle }}</div>
        <div
            class="mx-text-h2"
            v-if="!showAddMembersPanel && !isEdit">{{ $t('add_detail') }}
        </div>
        <div
            class="mx-text-h2"
            v-if="showAddMembersPanel && !isEdit">{{ $t('Add_Members') }}
        </div>
        <div class="divider mx-branding-color" />
        <div class="mx-section-devider"></div>
      </div>
    </template>
    <template slot="content">
      <div v-show="!showAddMembersPanel">
        <el-form
            :model="team"
            :rules="team_rules"
            class="floating-labels"
            hide-required-asterisk
            ref="team">
          <div class="group-info-title mx-semibold mx-margin-bottom-sm">
            {{ $t('group_info') }}
          </div>
          <mx-floating-input
              :label="$t('group_name')"
              :show-limit="true"
              maxlength="35"
              prop="name"
              v-model="team.name" />
          <mx-floating-input
              :label="$t('description')"
              :show-limit="false"
              maxlength="200"
              prop="description"
              v-model="team.description" />
          <div class="group-info-title mx-semibold mx-margin-top-lg" style="margin-bottom: 3px;">
            {{ $t('group_image') }}
          </div>
          <div class="team-avatar-container">
            <div style="width: 100%; margin-bottom: 0;">
              <div class="mx-secondary" style="line-height: 20px; margin-bottom: 12px; color: #616161;">
                {{ $t('client_group_avatar_file_type') }}
              </div>
              <mx-upload-avatar
                ref="uploader"
                :visible.sync="showUploadImageModal"
                :banner-text="$t('upload_group_image')"
                :min-width="0"
                :min-height="0"
                :cap="currentUser.cap || {}"
                :need-crop="true"
                :upload-url="uploadUrl"
                :need-upload="false"
                :auto-upload="true"
                @submit="getCroppedAvatar"
                @after-crop="uploadCroppedAvatar"
              />
              <div class="mx-upload-block mx-clickable mx-ellipsis"
                @click="showUploadImageModal = true"
                @keydown.enter="showUploadImageModal = true"
              >
                <i class="micon-edit-xs"></i>
                {{ $t('upload_image') }}
              </div>
            </div>
            <group-avatar
              :lazyload="true"
              alt="Icon"
              :team-id="team.id"
              :team-avatar="team.avatar"
              size="72" />
          </div>
        </el-form>
      </div>
      <div v-show="showAddMembersPanel">
        <div class="selector-panel">
          <div class="panel-title">
            <h4>{{ $t('group_managers') }}</h4>
            <img 
              class="tip-icon mx-clickable"
              ref="trigger"
              :src="questionMark"
              tabindex="0"
              @keyup.enter="showHideManagerTip"
              @click="showHideManagerTip">
          </div>
          <div
            ref="tip"
            v-show="showManagerTip" 
            v-mx-click-outside="showHideManagerTip"
            @keyup.esc="()=>{showManagerTip = false;$refs.trigger.focus();}"
            class="tip-content">
            <div>
              <img :src="teamManager">
              <span class="mx-text-c1">
                {{ $t('group_managers_and_admins_will_be_able_to_add_and_remove_clients_from_the_group') }}
              </span>
            </div>
            <el-button
              class="got-it"
              size="small"
              type="default"
              @click.stop.prevent="gotIt"
            >
              {{ $t('client_group_got_it') }}
            </el-button>
          </div>
          <div class="member-selector">
            <mx-user-selector
              ref="mxUserSelectorManager"
              :has-error="showManagerLimitReachedError"
              :invited-members="selectedMembers"
              :include-suggested-user="false"
              :input-options="{ disabled: showManagerLimitReachedError || isOperationInProgress || isOperationFailed }"
              :include-relation-user="false"
              :including-pending-user="true"
              :include-me="true"
              user-type="internal"
              @select="addSelectedMember(true, ...arguments)" />
          </div>
          <div v-if="showManagerLimitReachedError" class="error-tips">
            <div class="icon">
              <span class="micon-close"></span>
            </div>
            <div class="info">{{ managerLimitReachedErrorMessage }}</div>
          </div>
          <div class="relation-list">
            <mx-sortable-list
                :list="sortedSelectedManagers"
                :sortable="false"
                @removed="removeSelectedMember"
                subtitle="subTitle"
                title="displayName"
                :deletable="true"
                :disable-delete-icon="isOperationInProgress"
                :is-transparent="true"
                :is-avatar="true"
                :avatar-size="'s'" />
          </div>
        </div>
        <div class="selector-panel">
          <h4>{{ $t('client_users') }}</h4>
          <div class="member-selector">
            <mx-user-selector
              ref="mxUserSelectorTeammate"
              :has-error="showClientLimitReachedError"
              :invited-members="selectedMembers"
              :input-options="{ disabled: showClientLimitReachedError || isOperationInProgress || isOperationFailed }"
              :include-suggested-user="false"
              :include-relation-user="false"
              :including-pending-user="true"
              :include-me="false"
              user-type="external"
              @select="addSelectedMember(false, ...arguments)" />
          </div>
          <div v-if="showClientLimitReachedError" class="error-tips">
            <div class="icon">
              <span class="micon-close"></span>
            </div>
            <div class="info">{{ clientLimitReachedErrorMessage }}</div>
          </div>
          <div class="relation-list">
            <mx-sortable-list
                :list="sortedSelectedClients"
                :sortable="false"
                @removed="removeSelectedMember"
                subtitle="subTitle"
                title="displayName"
                :deletable="true"
                :disable-delete-icon="isOperationInProgress"
                :is-transparent="true"
                :is-avatar="true"
                :avatar-size="'s'" />
          </div>
        </div>
      </div>
    </template>
    <template slot="footer">
      <div class="mx-edit-footer">
        <el-button
          size="medium"
          :disabled="isOperationInProgress"
          @click="cancelOperation"
        >
          {{ $t('cancel') }}
        </el-button>
        <el-button
            v-if="!isSecondStep && !isEdit"
            :disabled="!isTeamInfoFieldsValid"
            @click="onClickNextButton()"
            size="medium"
            type="primary"
        >
          {{ operationButtonLabel }}
        </el-button>
        <modal-primary-button
            v-else
            :error-message="$t('Failed')"
            :key="buttonKey"
            :loading-message="loadingMessage"
            :call="saveTeam"
            :button-text="operationButtonLabel"
            :button-options="{ disabled: disabledAddOrUpdateButton }"
            :force-emit-error="true"
            @create="CreateNewGroup"
            @success="onOperationSuccess"
            @error="onOperationError" />
      </div>
    </template>
  </global-right-panel-layout>
</template>

<script>
import RightPanelMixins from '@views/common/components/admin/rightPanel'
import MxUserSelector from '@views/common/components/userSelector/MxUserSelector'
import MxSortableList from '@views/common/components/sortable/MxSortableList'
import {mapActions, mapGetters, mapState} from 'vuex'
import mixins from './mixins/mixins'
import util from '@views/common/utils/utils'
import ModalPrimaryButton from '@views/common/components/modals/ModalPrimaryButton'
import questionMark from '@views/theme/src/fonts/icons/source/question-mark.svg'
import teamManager from '@views/theme/src/fonts/icons/source/team-manager.svg'
import {focusTo} from '../../../common/accessibility'
import urlUtil from '@views/common/utils/url'
import MxUploadAvatar from '@views/common/components/uploader/MxUploadAvatar'
import GroupAvatar from './GroupAvatar'

export default {
  name: 'CreateOrUpdateGroup',
  components: {
    MxUserSelector,
    MxSortableList,
    ModalPrimaryButton,
    GroupAvatar,
    MxUploadAvatar,
  },
  mixins: [RightPanelMixins, mixins],
  props: {
    fromPanel: {
      type: Boolean,
      default: false,
      required: false
    },
    value: {
      type: Object
    }
  },
  data () {
    return {
      isEdit: Object.keys(this.value).length > 0,
      selectedMembers: [],
      selectedManagers: [],
      selectedClients: [],
      disabledAddOrUpdateButton: true,
      isSecondStep: false,
      isTeamInfoFieldsValid: false,
      buttonKey: 0,
      team: {},
      originalTeam: {},  // for edit team details
      team_rules: {
        name: {
          required: true,
          message: this.$t('this_field_is_required'),
          trigger: 'none'
        }
      },
      showManagerTip: false,
      questionMark,
      teamManager,
      teamAvatarFile: null,
      managersSetting: {
        include_all_admins: true,
        include_all_internal_users: false
      },
      showUploadImageModal: false,
      managerCap: 40,
      clientCap: 40,
      isOperationInProgress: false,
      isOperationFailed: false,
      createdTeamBeforeUploadAvatar: false,
    }
  },
  computed: {
    ...mapGetters('group', ['isPhoneNumberEnabled', 'groupBasicInfo']),
    ...mapGetters('teamMgr', ['teamMembers']),
    ...mapGetters('user', ['currentUser']),
    ...mapState('group', ['contextPath']),
    operationButtonLabel () {
      if (this.isEdit) {
        return this.$t('save_changes');
      }
      if (!this.isSecondStep) {
        return this.$t('next');
      } else {
        return this.$t('create');
      }
    },
    loadingMessage () {
      return this.isEdit ? this.$t('saving_ellipsis') : this.$t('creating_ellipsis');
    },
    showAddMembersPanel () {
      return this.isSecondStep && !this.isEdit;
    },
    pageTitle () {
      return this.isEdit ? this.$t('edit_group_details') : this.$t('new_client_group');
    },
    sortedSelectedManagers() {
      return this.selectedManagers.sort((memberA, memberB) => {
        return memberA.displayName.toUpperCase() < memberB.displayName.toUpperCase() ? -1 : 1
      })
    },
    sortedSelectedClients() {
      return this.selectedClients.sort((memberA, memberB) => {
        return memberA.displayName.toUpperCase() < memberB.displayName.toUpperCase() ? -1 : 1
      })
    },
    selectedManagersCount () {
      return this.selectedManagers.length;
    },
    selectedClientsCount () {
      return this.selectedClients.length;
    },
    showManagerLimitReachedError() {
      return this.selectedManagersCount >= this.managerCap
    },
    showClientLimitReachedError() {
      return this.selectedClientsCount >= this.clientCap
    },
    managerLimitReachedErrorMessage () {
      return util.format(this.$t('Member_limit_reached'), {
        totalMember: this.managerCap,
        maxMember: this.managerCap
      })
    },
    clientLimitReachedErrorMessage () {
      return util.format(this.$t('client_limit_reached'), {
        totalMember: this.clientCap,
        maxMember: this.clientCap
      })
    },
    uploadUrl () {
      return urlUtil.makeAccessTokenUrl(`${this.contextPath}/group/${this.team.id}/upload?type=avatar`)
    },
  },
  created () {
    if (Object.keys(this.value).length) {
      let avatar = ''
      if (this.value.picture) {
        avatar = `${this.contextPath}/group/${this.value.id}/resource/${this.value.picture}`
      }
      const description = this.value?.description || ''
      this.team = Object.assign({}, this.team, { ...this.value, description, avatar })
      this.originalTeam = Object.assign({}, this.originalTeam, { ...this.value, description, avatar })
    } else {
      this.team = Object.assign({}, this.team, { name: '', description: '', avatar: '', id: '' })
    }
  },
  watch: {
    'team.name'(value) {
      this.isTeamInfoFieldsValid = !!value.trim()
      this.onChangedTeamInfoFields()
    },
    'team.description'() {
      this.onChangedTeamInfoFields()
    },
    'team.avatar'() {
      this.onChangedTeamInfoFields()
    },
  },
  methods: {
    ...mapActions('teamMgr', ['createTeam', 'updateTeam', 'deleteTeam']),
    onClickNextButton() {
      if (!this.isSecondStep) {
        this.isSecondStep = true;
      }
    },
    saveTeam() {
      this.isOperationInProgress = true
      this.isOperationFailed = false

      return new Promise((resolve, reject) => {
        if (this.isEdit) {
          this.isTeamInfoFieldsValid = false

          const newTeamInfo = {
            id: this.team.id,
            name: this.team.name.trim(),
            description: this.team.description.trim()
          }

          if (this.teamAvatarFile) {
            this.startUploadTeamAvatar().then(() => {
              this.updateTeam(newTeamInfo).then(isSucceed => {
                if (isSucceed) {
                  this.originalTeam = Object.assign(
                    this.originalTeam,
                    { ...newTeamInfo },
                    { avatar: this.team.avatar }
                  )
                  resolve()
                } else {
                  reject()
                }
              }).catch(reject)
            }).catch(reject)
          } else {
            this.updateTeam(newTeamInfo).then(isSucceed => {
              if (isSucceed) {
                this.originalTeam = Object.assign(this.originalTeam, { ...newTeamInfo })
                resolve()
              } else {
                reject()
              }
            }).catch(reject)
          }
        } else {
          this.isTeamInfoFieldsValid = false

          if (!this.createdTeamBeforeUploadAvatar) {
            this.createTeam({
              name: this.team.name.trim(),
              description: this.team.description.trim(),
              type: 'GROUP_TYPE_CLIENT_TEAM',
              managersSetting: this.managersSetting,
              managers: this.selectedManagers.map(member => {
                return {id: member.userId}
              }),
              teammates: this.selectedClients.map(m => {
                return {id: m.userId}
              })
            }).then(team => {
              this.createdTeamBeforeUploadAvatar = true
              this.team.id = team.id

              if (this.teamAvatarFile) {
                this.$nextTick(() => {
                  this.startUploadTeamAvatar().then(resolve).catch(reject)
                })
              } else {
                resolve()
              }
            }).catch(reject)
          } else {
            if (this.teamAvatarFile) {
              this.$nextTick(() => {
                this.startUploadTeamAvatar().then(resolve).catch(reject)
              })
            } else {
              resolve()
            }
          }
        }
      })
    },
    CreateNewGroup() {
      if(!this.isEdit){
        util.setUsageToStorage({category: 'oa_new_client_group_panel', label: 'btn_create'})
      }
    },
    onOperationSuccess() {
      const message = this.isEdit
        ? this.$t('client_group_successfully_updated')
        : this.$t('client_group_successfully_created')
      this.$mxMessage.success(message)
      this.closeAction()
    },
    onOperationError() {
      this.isOperationInProgress = false
      this.isOperationFailed = true
    },
    addSelectedMember (isAdmin, member) {
      member.isAdmin = isAdmin;
      this.selectedMembers.push(member);
      if (isAdmin) {
        this.$refs.mxUserSelectorTeammate.onExternalAddUser(member)
        this.selectedManagers.push(member);
      } else {
        this.$refs.mxUserSelectorManager.onExternalAddUser(member)
        this.selectedClients.push(member);
      }
    },
    removeSelectedMember (member) {
      this.selectedMembers = this.selectedMembers.filter(user => {
        return user.sequence != member.sequence;
      });
      this.$refs.mxUserSelectorTeammate.onExternalRemoveUser(member)
      this.$refs.mxUserSelectorManager.onExternalRemoveUser(member)
      if (member.isAdmin) {
        this.selectedManagers = this.selectedManagers.filter(user => {
          return user.sequence != member.sequence;
        });
      } else {
        this.selectedClients = this.selectedClients.filter(user => {
          return user.sequence != member.sequence;
        });
      }
    },
    closeAction () {
      this.isOperationInProgress = false
      this.createdTeamBeforeUploadAvatar = false
      this.isOperationFailed = false
      this.selectedMembers = []
      this.disabledAddOrUpdateButton = true
      this.buttonKey++
      this.closeDialog()
    },
    cancelOperation() {
      if (!this.isEdit && this.team.id && this.isOperationFailed) {
        this.deleteTeam(this.team.id).then(isDeleted => {
          if (!isDeleted) {
            this.$mxMessage.error(this.$t('something_went_wrong_tip'))
          }
          this.closeAction()
        })
      } else if (this.isEdit) {
        this.team = Object.assign(this.team, {...this.originalTeam})
        this.closeAction()
      } else {
        this.closeAction()
      }
    },
    backStep (currentStep) {
      if (currentStep > 0) {
        this.team = Object.assign(this.team, {...this.originalTeam})
        this.gotoView('ViewGroupDetails');
      } else {
        this.isSecondStep = false;
        this.showManagerTip = false;
        this.isTeamInfoFieldsValid = true;
      }
    },
    showHideManagerTipOnEnter (e) {
      this.showHideManagerTip(e)
      this.$nextTick(()=>{
        focusTo(this.$refs.tip)
      })
    },
    showHideManagerTip (e) {
      e.stopPropagation()
      if(e.target.classList.contains('tip-icon')){
        this.showManagerTip = !this.showManagerTip
      } else {
        this.showManagerTip = false
      }
      if (this.showManagerTip) {
        this.$nextTick(()=>{
          focusTo(this.$refs.tip)
        })
      }
    },
    gotIt(e) {
      this.showManagerTip = false
      this.$nextTick(()=>{
        focusTo(this.$refs.trigger, {delay: 300})
      })
    },
    onChangedTeamInfoFields() {
      if (this.isEdit) {
        if (this.team.name.trim() === this.originalTeam.name
            && this.team.description.trim() === this.originalTeam.description
            && this.team.avatar === this.originalTeam.avatar) {
          this.disabledAddOrUpdateButton = true
        } else {
          this.disabledAddOrUpdateButton = !!this.team.name.trim() ? false : true
        }
      } else {
        this.disabledAddOrUpdateButton = false
      }
    },
    startUploadTeamAvatar() {
      return this.$refs.uploader.$refs.uploader.submit(this.teamAvatarFile)
    },
    getCroppedAvatar(image) {
      this.teamAvatarFile = image
      let reader = new FileReader()
      let vm = this
      reader.onload = (e) => {
        if (typeof e.target.result === 'object') {
          vm.team.avatar = window.URL.createObjectURL(new Blob([e.target.result]))
        } else {
          vm.team.avatar = e.target.result
        }
        reader.onload = null
      }
      reader.readAsDataURL(image)
    },
    uploadCroppedAvatar(uploadFunc) {
      uploadFunc()
    }
  }
}
</script>

<style lang="scss" scoped>
.floating-labels {
  margin: 0 8px
}

.selector-panel {
  background: $mx-color-var-fill-quaternary;
  border-radius: 6px;
  width: 100%;
  padding: 10px 16px 16px;
  margin-bottom: 20px;
  .panel-title {
    position: relative;
  }
  .tip-icon {
    position: absolute;
    right: 0;
    top: 5px;
  }

  .tip-content {
    position: absolute;
    background: rgb(32, 32, 32);
    border-radius: 6px;
    min-height: 132px;
    width: 359px;
    z-index: 2;
    right: 74px;
    padding: 14px 16px 16px 16px;
    top: 180px;
    &:before {
      left: 97%;
      border: solid transparent;
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
      transform: rotate(45deg);
      border-radius: 3px;
      border-color: #202020;
      border-width: 7px;
      top: 11px;
    }
    img {
      margin-left: -7px;
    }
    > div {
      display: flex;
      justify-content: center;
      margin-bottom: 14px;
    }
    button {
      width: 100%;
      background: #434344;
      border: 2px solid #434344;
      ::v-deep span {
        color: rgb(90, 98, 245);
        font-weight: bold;
        text-transform: uppercase;
      }
    }
    .mx-text-c1 {
      color:white;
    }
    .got-it {
      background-color: #F4F4F4;
    }
  }

  h4 {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    border: 0;
    margin: auto auto 10px auto;
  }

  .member-selector {
    margin-bottom: 8px;
  }
}

.error-tips {
  display: flex;
  color: #B22424;
  font-weight: normal;
  line-height: 20px;
  align-items: center;

  span {
    font-size: 16px;
  }
}
::v-deep .el-alert--error {
  height: 32px;
}
.micon-warning-triangle {
  color: $mx-color-var-caution;
  font-size: 16px;
  margin-right: 5px;
}
.team-avatar-container {
  display: flex;
  align-items: center;
  img {
    width: 72px;
    height: 72px;
  }
}
.include-users-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  .include-label {
    flex: 1 1 auto;
  }
  .el-switch {
    flex: 0 0 auto;
  }
}

.mx-upload-block {
  width: 180px;
  max-width: 180px;
  height: 36px;
  background: #F4F4F4;
  border-radius: 6px;
  text-align: center;
  padding-top: 8px;
  margin-top: 6px;
  color: #616161;
  font-weight: 600;
  font-size: 12px;
  line-height: 20px;
  i {
    vertical-align: baseline !important;
    font-size: 14px !important;
    margin-right: 4px;
  }
}

.group-info-title {
  height: 24px;
  font-size: 16px;
  line-height: 20px;
}
</style>
