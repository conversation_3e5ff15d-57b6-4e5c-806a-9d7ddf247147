<template>
  <div>
    <ElAlert
      v-show="!!errorMessage"
      :closable="false"
      class="invite-alert"
      v-mx-ta="{page:'invite',id:'commonError'}"
      :title="errorMessage"
      show-icon
      type="error" />
  <el-form
    ref="form"
    :model="form"
    @submit.prevent
    :rules="formRules"
    :validateOnRuleChange="false"
    :class="['floating-labels','invite-form',{disabled: isSending}]"
  >
    <div class="flex-row">
    <mx-floating-input
      :disabled="isSending"
      :label="`${$t('meet_attendee_full_name')}*`"
      v-model="form.name"
      class="mx-invite-client__name"
      :maxlength="nameMaxLength"
      prop="name"
      v-mx-ta="{ page: 'invite', id: 'name'}"
    /></div>
    <mx-floating-input
      v-if="groupTags.Allow_Edit_Client_Display_ID"
      :label="$t('User_ID')"
      v-model.trim="form.display_id"
      prop="display_id"
      :disabled="isSending"
      v-mx-ta="{ page: 'invite', id: 'display_id'}"
    />
    <template v-if="isPhoneNumberEnabled">
      <UserAccountInputPanel
        :class="['mx-invite-client__email_phone', {'phone-error': phoneHasError}]"
        :type.sync="accountType"
        :disable="isSending"
        :labelWithRequired="true"
        :switch-email-label="$t('invite_via_email_address_instead')"
        :switch-phone-label="$t('invite_via_phone_number_instead')"
        :email.sync="form.email"
        :phone.sync="form.phone">
      </UserAccountInputPanel>
    </template>
    <template v-else>
      <mx-floating-input
        :label="`${$t('email_address')}*`"
        v-model.trim="form.email"
        :disabled="isSending"
        class="mx-invite-client__email"
        prop="email"
        v-mx-ta="{ page: 'invite', id: 'email'}"
      />
    </template>
    <el-form-item
      prop="account"
      v-if="isEnableClientGroup && isInviteClient && allClientTeams.length"
      class="additional-section">
      <MxCustomizedMultiSelector
        class="client-groups"
        :options="allClientTeams"
        :filterable="true"
        :placeholder="$t('Select_Group')"
        :disabledOptFn="disabledOptFn"
        :emptyText="$t('No_client_groups')"
        :defaultAvatar="ClientTeamDefaultImg"
        :groupOptions="[{label: $t('Select_Client_Group')}]"
        @selectedValue="getSelectedGroup">
        <div class="client-group-item" slot-scope='{item}'>
          <MxTeamItem
            :team="item"
            :avatarSize="'28'"
            :hideClientBadge="true" />
          <el-tooltip
            v-if="item.disabled"
            placement="top"
            popper-class="overflow-control-tooltip"
            :content="$t('client_reach_to_maximun_for_a_group',{number: 40})">
            <i class="micon-info font-icon-sm tip-icon" />
          </el-tooltip>
        </div>
      </MxCustomizedMultiSelector>
    </el-form-item>
    <el-form-item
      v-if="showSelectDistributionList"
      class="additional-section">
      <MxCustomizedMultiSelector
        class="client-distribution-list"
        :filterable="true"
        :options="groupDistributionList"
        :optionItems="{label: 'name', value: 'sequence'}"
        :placeholder="selectDistributionListPlaceholder"
        :emptyText="$t('No_distribution_lists')"
        :noMatchText="$t('No_matches_found')"
        :groupOptions="[{label: $t('Select_Client_Distribution_Lists')}]"
        @selectedValue="selectedDistributionList = $event"
        @visible-change="handleDistributionListVisibleChange">
        <div class="client-distribution-item" slot-scope='{item}'>
          <div class="client-distribution-name">{{ item.name }}</div>
          <div
            :title="item.description || $t('No_description')"
            class="mx-text-c4 mx-color-secondary mx-ellipsis">
            {{ item.description || $t('No_description') }}
          </div>
        </div>
      </MxCustomizedMultiSelector>
    </el-form-item>
    <div class="invite-help-message" v-if="showHint">
      <span v-if="isInviteClient">{{ hintMessage ? hintMessage : $t('After_inviting_them_youll_show_up_on_their_dashboard_') }}</span>
    </div>
    <el-form-item>
      <el-button type="primary"  id="sendInvitation" :disabled="isSending" :loading="isSending"  @click="invite">{{buttonText || defaultButtonText}}</el-button>
    </el-form-item>
  </el-form>
  </div>
</template>

<script>
  import { mapActions, mapGetters } from 'vuex'
  import UserAccountInputPanel from "@views/identity/components/UserAccountInputPanel"
  import util from '@views/common/utils/utils'
  import {useConfirmUserDialog} from './util'
  import {createRelation, inviteGroupMember} from '@controller/invite'
  import {focusTo} from '../common/accessibility'
  import { parsePhoneNumberFromString as parseMobile } from '@vendor/libphonenumber-js/bundle/libphonenumber-mobile'
  import _pick from 'lodash/pick'
  import MxCustomizedMultiSelector from '@views/common/components/MxCustomizedMultiSelector'
  import MxTeamItem from '@views/common/components/MxTeamItem'

  import ClientTeamDefaultImg from '@views/theme/src/images/team/Client-Group-Default-Avatar.svg'
  import { nameMaxLength } from '@views/common/appConst.js'
  import { UserFormatter } from '@controller/utils/user'
  export default {
    name: 'InviteForm',
    components:{
      UserAccountInputPanel,
      MxCustomizedMultiSelector,
      MxTeamItem
    },
    props: {
      suppressEmailSms: Boolean,
      isInviteClient: Boolean,
      buttonText: String,
      disabled: Boolean,
      user: Object,
      createBinder: Boolean,
      showHint: {
        type: Boolean,
        default: true
      },
      hintMessage: {
        type: String,
        default: ''
      },
      showSuccessTip: {
        type: Boolean,
        default: true
      },
      customizedInviteUser: {
        type: Boolean,
        default: false
      }
    },
    created() {
      if(this.user) {
        let {name, email, phone}  = this.user
        this.form.name = name || ''
        this.form.email = email
        this.form.phone = phone
      }
    },
    data() {
      const defaultButtonText = this.$t('send_invitation')
      return {
        nameMaxLength: `${nameMaxLength}`,
        defaultButtonText,
        accountType: 'email',
        isSending: false,
        phoneHasError: false,
        errorMessage:'',
        isMemberExist: false,
        form: {
          name: '',
          email: '',
          display_id: '',
          phone: ''
        },
        needConfirm: false,
        selectedTeams: [],
        isFocusAfterClose: false,
        ClientTeamDefaultImg,
        selectedDistributionList: [],
        existingDistributionList: [],
        selectDistributionListPlaceholder: this.$t('Select_Distribution_List')
      }
    },
    computed:{
      ...mapGetters('group', ['isPhoneNumberEnabled','groupTags','isEnableClientGroup', 'isHideClientDashboard', 'enableEmailPrivacy', 'enableClientDistributionList', 'groupDistributionList']),
      ...mapGetters('user', ['currentUser', 'allClientTeams']),
      needToSuppressEmailSms () {
        if(this.isHideClientDashboard && this.isInviteClient) {
          return true
        }
        if(this.enableEmailPrivacy && this.isInviteClient) {
          return false
        }
        return this.suppressEmailSms
      },
      formRules (){
        return {
          name: [{
            required: true,
            message : this.$t('Required_field'),
            trigger: 'change'
          }, {
            trigger: 'manual',
            validator: (rule, value, callback) => {
              if (value.trim()) {
                callback()
              } else {
                callback(new Error(this.$t('Required_field')))
              }
            }
          }],
          email: [
            {required: this.accountType === 'email',message : this.$t('Required_field'),trigger: ['manual']},
            {
              trigger: ['manual'],
              validator: (rule, value, callback) => {
                if(value && !util.isEmail(value)){
                  return callback(new Error(this.$t('incorrect_email')))
                }
                this.checkMemberAndHendleCommonError({email: this.form.email}).then(()=>{
                  callback()
                }).catch(err =>{
                    if(err.isMemberExist){
                      callback(new Error(this.$t('email_address_already_in_use')))
                    }
                })
              }
            }
          ],
          phone: [
            {required: this.accountType === 'phone',message : this.$t('Required_field'),trigger: ['manual']},
            {
              trigger: ['manual'],
              validator: (rule, value, callback) => {
                if (value && !util.isPhoneNumber(value)) {
                  return callback(new Error(this.$t('incorrect_phone_number')))
                }
                this.checkMemberAndHendleCommonError({phone_number: this.form.phone}).then(()=>{
                  callback()
                }).catch(err =>{
                  if(err.isMemberExist){
                    callback(new Error(this.$t('phone_number_already_in_use')))
                  }
                })
              }
            }
          ]
        }
      },
      showSelectDistributionList () {
        return this.enableClientDistributionList && this.isInviteClient && this.groupDistributionList.length
      }
    },
    methods: {
      ...mapActions('contacts', ['checkMemberExist']),
      ...mapActions('group', ['inviteMember', 'addTeamMembers', 'updateMemberDistributionList']),
      getInvitationMessage() {
        if (!this.groupTags.Enable_Welcome_Message) {
          return ''
        }
        let invitationMessage = (this.currentUser.invitationMessage && this.currentUser.invitationMessage.value) || ''
        if (!invitationMessage) {
          invitationMessage = this.$t('client_invitation_message_default')
        }
        return invitationMessage
      },
      setSendStatus(flag){
        this.isSending = flag;
        this.$emit('update:disabled', flag)
      },
      _formatPhoneToE164Format(phone) {
        if (phone) {
          return parseMobile(phone).format('E.164')
        }
      },
      checkMemberAndHendleCommonError(userInfo){
        return new Promise((resolve, reject) => {
          this.errorMessage = ''
          let {name, phone, email} = this.form
          const firstAndLastName = UserFormatter.splitNameToFirstAndLastName(name)
          let info = {
            ...userInfo,
            name,
            ...firstAndLastName
          }
          info.phone_number = this._formatPhoneToE164Format(info.phone_number)
          this.needConfirm = false

          this.checkMemberExist(info).then(resolve).catch(err =>{
            let isDisabledUser = err.isDisabledClient || err.isDisabled
            if(this.isInviteClient && isDisabledUser) {
              this.errorMessage = this.$t('This_user_has_been_deactivated_please_contact_your_administrator')
            } else if(isDisabledUser) {
              this.errorMessage = this.$t('You_cannot_invite_users_who_are_deactivated')
            } else if (err.isUnknownError) {
              this.errorMessage = this.$t('Something_Went_Wrong_retry_desc')
            } else if(this.isInviteClient && err.isMyClient) {
              this.errorMessage = this.$t('This_client_is_already_assigned_to_you')
            } else if (err.isMemberExist){
              let {isInternalUser, hasSameName} = err
              if(this.isInviteClient) {
                this.isMemberExist = true
                if(isInternalUser) {
                  // this.errorMessage = this.$t('This_user_already_exists_in_your_contacts')
                }else if (!hasSameName) {
                  let { user } = err
                  let {createBinder} = this
                  let subTitle = phone || email
                  let vm = this;
                  let [showDialog] = useConfirmUserDialog({
                    cancel:()=>{
                      vm.$emit('cancel')
                    },
                    success: ({ user, userExist })=>{
                      vm.$emit('success', { user, isMemberExist:userExist })
                      vm.$emit('close')
                    },

                    continueWithExistUser: ()=>{
                      if (err.groupMemberRoles) {
                        this.existingDistributionList = err.groupMemberRoles
                      }
                      this.$emit('customizedInvite', {user, clientGroups: this.selectedTeams,isMemberExist:true, selectedDistributionList: this.getAllClientDistributionList()})
                    }
                  }, { user, subTitle, createBinder, isPhone: this.accountType==='phone',class: this.accountType==='phone' ? '' : 'web-invite-form-dialog', selectedTeams: this.selectedTeams,customizedInviteUser: this.customizedInviteUser})
                  showDialog();
                  this.needConfirm = true
                  // setTimeout(() => {
                  //   this.$emit('close')
                  // }, 100)
                  resolve()
                }else{
                  if (err.groupMemberRoles) {
                    this.existingDistributionList = err.groupMemberRoles
                  }
                  resolve(err)
                }
              }
            }

            if(this.errorMessage){
              resolve(err)
            } else {
              reject(err)
            }
          })
        })
      },
      doInviteUser() {
        let {phone,name,display_id,email} = this.form
        phone = this._formatPhoneToE164Format(phone)
        const trimmedName = name.trim()
        const firstAndLastName = UserFormatter.splitNameToFirstAndLastName(trimmedName)
        let user =  {phone_number:phone,name: trimmedName, display_id,email,...firstAndLastName}
        if(this.isInviteClient){
          return new Promise((resolve, reject) => {
            createRelation(user, this.needToSuppressEmailSms).then((transformedUser) => {
              resolve(transformedUser)
            }).catch(reject)
          })
        } else {
          return inviteGroupMember({user}, this.needToSuppressEmailSms)
        }
      },
      validateForm (){
        return new Promise((resolve,reject)=>{
          this.$refs.form.validate((valid, err) => {
            let errFields = Object.keys(err)
            if(errFields.length > 0){
              if(err?.phone){
                //adjust phone error message position
                this.phoneHasError = true
              }
              let firstField = errFields[0]
              //todo: focus to first error input
              firstField && this.$nextTick(() => focusTo(`input#${firstField}`, { delay: true }))
              return reject()
            }
            if(this.errorMessage || this.needConfirm){
              //if is common error the field validate fun will pass
              return reject()
            }
            resolve()
          })
        })
      },
      getFormattedUser (){
        let {phone,name,display_id,email} = this.form
        phone = this._formatPhoneToE164Format(phone)
        const trimmedName = name.trim()
        const firstAndLastName = UserFormatter.splitNameToFirstAndLastName(trimmedName)
        const user =  {phone_number:phone,name: trimmedName, display_id,email,...firstAndLastName}
        return user
      },

      async invite () {
        if(this.buttonText) {
          if(this.isInviteClient){
            util.setUsageToStorage({category: 'invite_client_modal', label: 'add_client'})
          } else {
            util.setUsageToStorage({category: 'invite_internal_modal', label: 'add_internal'})
          }
        } else {
          if(this.isInviteClient){
            util.setUsageToStorage({category: 'invite_client_modal', label: 'send_invitation'})
          } else {
            util.setUsageToStorage({category: 'invite_internal_modal', label: 'send_invitation'})
          }
        }
        this.setSendStatus(true)
        this.phoneHasError = false
        this.errorMessage = ''
        let onError = (err)=>{
          if(err) {
            if (err.isExceedLimit) {
              this.errorMessage = this.$t('something_went_wrong_tip')
            } else {
              this.errorMessage = this.$t('Something_went_wrong')
            }
          }
          this.setSendStatus(false)
        }
        try {
          await this.validateForm()
          if(this.customizedInviteUser){
            this.setSendStatus(false)
            this.handleCustomizedInvite()
          }else{
            const member = await this.doInviteUser()
            if (this.selectedTeams.length) {
              let user= _pick(member, 'id', 'email', 'phone_number', 'unique_id')
              for (let i=0; i< this.selectedTeams.length; i++) {
                let groupId = this.selectedTeams[i]
                this.addTeamMembers({teamId: groupId, users: [user]})
              }
            }
            if (this.selectedDistributionList.length) {
              this.updateMemberDistributionList({userId: member.id, roles: this.getAllClientDistributionList()})
            }
            if (this.showSuccessTip) {
              //client will show this message in binder type select dialog
              //won't change the status due to next opreation
              this.setSendStatus(false)
              this.$mxMessage.success(this.$t('Invitation_sent'))
            }
            this.$emit('success', { user: member, isMemberExist:this.isMemberExist })
          }

        }catch(e){
          onError(e)
        }
      },
      disabledOptFn (item){
        return item.memberCounts >= 40
      },
      getSelectedGroup (val){
        this.selectedTeams = val
      },
      handleCustomizedInvite (){
        const user = this.getFormattedUser()
        let userInfo = _pick(user, 'id', 'email', 'phone_number', 'unique_id','name','display_id','first_name','last_name')
        //TODO: tuned as below for now, won't need do it after MVB-36085 fixed
        if(this.accountType === 'email'){
          userInfo.phone_number = ''
        }else if(this.accountType === 'phone'){
          userInfo.email = ''
        }
        this.$emit('customizedInvite', {user: userInfo, clientGroups: this.selectedTeams, selectedDistributionList: this.getAllClientDistributionList()})
      },
      handleDistributionListVisibleChange (visible) {
        if (this.selectedDistributionList.length) {
          this.selectDistributionListPlaceholder = ''
        } else if (visible) {
          this.selectDistributionListPlaceholder = this.$t('Search_for_distribution_list_name')
        } else {
          this.selectDistributionListPlaceholder = this.$t('Select_Distribution_List')
        }
      },
      getAllClientDistributionList () {
        let list = this.selectedDistributionList
        if (this.existingDistributionList.length) {
          list = Array.from(new Set([...this.existingDistributionList, ...list]))
        }
        return list
      }
    }
  }
</script>

<style scoped lang="scss">
  .flex-row{
    display: flex;
    flex-direction: row;
    div{
      flex-grow: 1;
    }
  }
  .el-button{
    width: 100%;
  }
  .invite-form{
    ::v-deep .el-form-item__error{
      position: inherit;
      padding: 0;
      margin: 5px 0 10px;
    }
    ::v-deep .el-form-item__label:before{
      display: none;
    }
    .phone-error{
      ::v-deep .phone-area-code input {
        border: 1px solid #d22a2a;
      }
      ::v-deep .phone-wrap .el-form-item__error{
        margin-left: -130px;
      }
    }

  }
  .invite-help-message{
     color: rgb(138, 138, 142);
     font-size: 12px;
     font-weight: normal;
     line-height: 16px;
    margin-top: 80px;
    margin-bottom: 12px;
   }
  .invite-alert {
    margin-bottom: 16px;
    padding: 8px 16px 8px 12px;
  }
  .mx-invite-client__email_phone + .additional-section {
    margin-top: -7px;
  }
  .mx-invite-client__email_phone + .additional-section, .mx-invite-client__email + .additional-section {
    padding-top: 16px;
    border-top: 1px solid $mx-color-var-fill-tertiary;
    + .el-form-item {
      margin-top: -6px;
    }
  }
</style>
<style lang="scss">
.web-invite-form-dialog {
  .el-dialog__header .left.mx-text-c1 {
    white-space: normal!important;
  }
}
.el-select-dropdown__item{
  .client-group-item{
    display: flex;
    align-items: center;
    .tip-icon{
      margin-left: auto;
      color: rgba(31, 33, 38, .3);
    }
  }
  &.is-disabled{
    .client-group-item {
      .team-avatar{
        opacity: .5;
      }
      .tip-icon, .name-content{
        color: $mx-color-var-text-secondary;
      }
      .team-title{
        color: $mx-color-var-text-tertiary;
      }
    }
  }

}

</style>
