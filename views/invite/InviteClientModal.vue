<template>
  <BaseModal
    :visible.sync="visibleProxy"
    :modal-option="{ width: '540px'}"
    :class="[currentStep === InviteStep.inviteForm ? 'hide-header' : '', 'invite-client-wrap']"
    @close="$emit('close')">
    <div
      v-if="currentStep !== InviteStep.inviteForm"
      slot="title">
      <i
        class="micon-left-arrow-new mx-clickable"
        @click="backFn"/>
      {{ dialogTitle }}
    </div>
    <div slot="content" style="height: 100%;">
      <keep-alive>
        <WorkspaceSelectorForUserInvitation
          v-if="currentStep === InviteStep.selectWorkspace"
          class="workspace-selector-wrap"
          :defaultUser="invitedClientUser"
          :clientGroups="selectedClientGroups"
          @close="closeDialog"/>
        <InviteClientType
          v-else-if="currentStep === InviteStep.selectType"
          @close="closeDialog"
          :defaultUser="invitedClientUser"
          :clientGroups="selectedClientGroups"
          @goWorkspaceSelector="currentStep = InviteStep.selectWorkspace">
        </InviteClientType>
      </keep-alive>
      <InvitationAndInviteLink
        v-show="currentStep === InviteStep.inviteForm"
        :customizedInviteUser="true"
        @customizedInvite="customizedInvite"/>
    </div>
  </BaseModal>
</template>
<script>
import InvitationAndInviteLink from '@views/invite/InvitationAndInviteLink.vue'
import WorkspaceSelectorForUserInvitation from '@views/createConversation/src/WorkspaceSelectorForUserInvitation'
import {visibleMixin} from '@views/common/components/modals/mixins'
import {mapActions as piniaMapActions} from 'pinia'
import {useInviteClientUserStore} from '@views/stores/inviteClientUser';
import InviteClientType from '@views/invite/InviteClientType.vue';

const InviteStep = {
  selectWorkspace: 'selectWorkspace',
  selectType: 'selectType',
  inviteForm: 'inviteForm'
}
export default {
  name: 'InviteClientModal',
  components: {
    InviteClientType,
    InvitationAndInviteLink,
    WorkspaceSelectorForUserInvitation
  },
  mixins: [visibleMixin],

  props: {},
  data () {
    return {
      invitedClientUser: null,
      selectedClientGroups: null,
      InviteStep,
      currentStep: InviteStep.inviteForm
    }
  },
  computed: {
    dialogTitle () {
      return this.$t('Select_channel')
    }
  },
  methods: {
    ...piniaMapActions(useInviteClientUserStore, ['destroyInviteClientUserStore', 'setClientDistributionList']),
    customizedInvite ({user, clientGroups, isMemberExist, selectedDistributionList}) {
      this.invitedClientUser = {
        isMemberExist: isMemberExist,
        ...user
      }
      this.selectedClientGroups = clientGroups
      this.setClientDistributionList(selectedDistributionList)
      this.currentStep = InviteStep.selectType
    },
    backFn () {
      if (this.currentStep === InviteStep.selectType) {
        this.currentStep = InviteStep.inviteForm
      } else {
        this.currentStep = InviteStep.selectType
      }
    },
    closeDialog () {
      this.closeModal()
    }

  },
  beforeDestroy () {
    this.destroyInviteClientUserStore()
  }
}
</script>
<style scoped lang="scss">
.invite-client-wrap {
  &.hide-header {
    ::v-deep .el-dialog__header {
      height: 0;
      padding: 0;
      border: none;
    }

    ::v-deep .el-dialog__body {
      padding: 0;
      height: auto;

    }
  }

  ::v-deep .el-dialog__body {
    padding: 0;
    height: calc(100vh - 280px);
    min-height: 200px;
  }

  .workspace-selector-wrap {
    height: 100%;

    ::v-deep .workspace-area {
      padding: 23px 28px 0;
      height: calc(100% - 68px);
      overflow: auto;
    }

    ::v-deep .action-btn {
      padding: 12px 28px 20px;
    }
  }

}
</style>