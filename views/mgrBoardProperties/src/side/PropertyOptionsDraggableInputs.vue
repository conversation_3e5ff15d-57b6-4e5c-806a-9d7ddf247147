<template>
  <div class="draggable-wrapper">
    <el-form ref="form" :model="formData">
      <Container
        v-if="list.length > 0"
        @drop="onDrop"
        lock-axis="y"
        drag-handle-selector=".drag-handle"
        drag-class="dragging-item"
        drop-class="dragging-item"
      >
        <Draggable
          v-for="(item, index) in list"
          :key="item.id"
        >
          <el-form-item
            :prop="'input' + item.id"
            :rules="formItemRules"
            class="draggable-item"
          >
            <div class="item-wrapper">
              <div class="admin-order-number">{{ index + 1 }}</div>

              <el-input
                :ref="'input' + item.id"
                v-model.trim="item.value"
                :placeholder="inputPlaceholder"
                :maxlength="inputMaxLength"
                @blur="validateForm().then(() => { })" />

              <i class="micon-handle drag-handle" />

              <div
                :class="{ disabled: isDeleteDisabled ? 'disabled' : '' }"
                class="delete-icon"
              >
                <i v-if="!isDeleteDisabled"
                  class="micon-delete-xs mx-clickable"
                  @click="removeAt(index)" />
                <i v-else class="micon-delete-xs" />
              </div>
            </div>
          </el-form-item>
        </Draggable>
      </Container>
    </el-form>

    <el-button
      :disabled="isAddDisabled"
      type="text"
      class="add-button"
      @click="add"
    >
      <i class="micon-mep-plus" />
      {{ $t('add') }}
    </el-button>
  </div>
</template>

<script>
import { Container, Draggable } from 'vue-smooth-dnd'


export default {
  name: 'PropertyOptionsDraggableInputs',
  components: {
    Container,
    Draggable
  },
  props: {
    propertyOptions: {
      type: Array,
      default: () => []
    },
    inputPlaceholder: {
      type: String,
      default: ''
    },
    inputMaxLength: {
      type: Number,
      default: 50
    },
    inputMinCount: {
      type: Number,
      default: 1
    },
    inputMaxCount: {
      type: Number,
      default: 1024
    }
  },
  data () {
    return {
      list: this.propertyOptions.slice(),
      isAddDisabled: false,
      isDeleteDisabled: false,
      formData: {},
      formItemRules: [
        { validator: this.uniqueValueValidator, trigger: 'none' }
      ],
      maxItemId: 0,
    }
  },
  watch: {
    list: {
      handler (newValue) {
        const newFormData = {}
        for (let item of newValue) {
          newFormData[`input${item.id}`] = item.value
        }
        this.formData = Object.assign({}, newFormData)
        this.setMaxItemId()
        this.isDeleteDisabled = newValue.length > this.inputMinCount ? false : true
        this.isAddDisabled = newValue.length < this.inputMaxCount ? false : true

        this.$emit('updatePropertyOptions', this.list)
      },
      deep: true
    }
  },
  created () {
    const newFormData = {}
    for (let item of this.list) {
      newFormData[`input${item.id}`] = item.value
    }
    this.formData = Object.assign({}, newFormData)
    this.setMaxItemId()
    this.isDeleteDisabled = this.list.length > this.inputMinCount ? false : true
    this.isAddDisabled = this.list.length < this.inputMaxCount ? false : true
  },
  methods: {
    removeAt (index) {
      this.list.splice(index, 1)
    },

    add () {
      const emptyItem = this.list.find(item => !item.value)
      if (emptyItem) {
        const firstEmptyInputRef = `input${emptyItem.id}`
        this.$refs[firstEmptyInputRef][0].focus()
      } else {
        this.maxItemId++
        this.list.push({ id: this.maxItemId, value: '' })
      }
    },

    uniqueValueValidator (rule, value, callback) {
      const currentField = rule.fullField
      const repeatedInputFields = Object.keys(this.formData).filter(k => this.formData[k] === value)
      if (value && repeatedInputFields.length > 0 && currentField !== repeatedInputFields[0]) {
        callback(new Error(this.$t('option_name_cannot_be_repeated')))
      } else {
        callback()
      }
    },

    setMaxItemId () {
      let maxId = 0
      if (this.list.length > 0) {
        [maxId] = this.list.map(item => item.id).sort((a, b) => a - b).slice(-1)
      }
      this.maxItemId = maxId
    },

    validateForm () {
      return new Promise(resolve => {
        this.$refs.form.validate((valid, errors) => {
          resolve(valid)
        })
      })
    },

    clearFormValidate () {
      this.$refs.form && this.$refs.form.clearValidate()
    },

    applyDrag (items, dragResult) {
      const { removedIndex, addedIndex, payload } = dragResult
      if (removedIndex === null && addedIndex === null) return items
      const result = [...items]
      let itemToAdd = payload
      if (removedIndex !== null) {
        itemToAdd = result.splice(removedIndex, 1)[0]
      }
      if (addedIndex !== null) {
        result.splice(addedIndex, 0, itemToAdd)
      }
      return result
    },

    onDrop (dropResult) {
      this.list = this.applyDrag(this.list, dropResult)
    },
  }
}
</script>

<style lang="scss" scoped>
.draggable-wrapper {
  width: 100%;
}

.dragging-item {
  background: rgb(255, 255, 255);
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
  border-radius: 3px;
  cursor: grab !important;
}

.draggable-item {
  padding: 8px;
  display: flex;
  align-items: center;
  border-radius: 6px;
}

.drag-handle {
  cursor: grab !important;
  color: #bcc2cc;
  font-size: 18px;
}


.el-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px !important;

  .el-form-item {
    margin-bottom: 0;
  }

  .item-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  ::v-deep .el-form-item__content {
    width: 100%;
    line-height: 36px;

    .el-form-item__error {
      max-width: 70%;
      line-height: 20px;
      font-size: 14px;
      position: static;
      margin: 5px 8px 0px 40px;
      padding: 0px;
      display: flex;
      gap: 4px;

      i {
        padding-top: 3px;
      }
    }
  }
}

.delete-icon {
  color: $mx-color-gray-20;
  display: flex;

  >i {
    font-size: 16px;
    line-height: 36px;
  }

  &:hover {
    color: $mx-color-red;
  }

  &.disabled:hover {
    color: $mx-color-gray-20;
  }
}

.add-button {
  padding: 8px 0px 8px 6px;
}
</style>
