<template>
  <div class="act-tag">
    <div 
      class="tag-wrapper" 
      :class="{'is-type': typeName || !isOverDue, 'is-overdue': isOverDue,'is-time': completedTime || isMyTurn}">
      <i 
        v-show="completedTime"
        class="micon-read-tick" /> 
      <span>{{ labelName }}</span>
    </div>
  </div>
</template>
<script>
export default {
  props:{
    typeName: {
      type: String,
      default: ''
    },
    dueTime: {
      type: String,
      default: ''
    },
    completedTime: {
      type: String,
      default: ''
    },
    isMyTurn: {
      type: Boolean,
      default: false
    },
    isOverDue: {
      type: Boolean,
      default: false
    }
  },
  computed:{
    labelName (){
      if(this.dueTime){
        return this.dueTime
      }else if(this.completedTime){
        return this.completedTime
      }else if(this.isMyTurn){
        return this.$t('Your_Turn')
      }else if(this.typeName){
        return this.typeName
      }else{
        return ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.act-tag {
  border: 1px solid $mx-color-var-white;
  display: inline-block;
  border-radius: 6px;
  margin-right: 4px;
}
.tag-wrapper{
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  text-align: center;
  padding: 4px 9px;
  border-radius: 6px;
  i{
    font-size: 10px;
    margin-right: 10px;
  }
  &.is-type{
    line-height: 14px;
    color: $mx-color-var-label-secondary;
    border: 1px solid $mx-color-var-fill-quaternary;
    background-color: $mx-color-var-white;
  }
  &.is-time{
    color: $mx-color-var-positive;
    background: rgba(47,121,89,0.1);
  }
  &.is-overdue{
    color: $mx-color-var-negative;
    background: rgba(178,36,36,0.1);
  }
}
</style>