<template>
  <div 
    class="create-signature">
    <PrepareSignatureFileAndSignee
      :signeeSelected="signeeSelected"
      :selectedSignees="selectedSignees"
      :binder="binder"
      :isDirectSign="isDirectSign"
      :insideBinder="insideBinder"
      @close="$emit('close')" />
  </div>
</template>

<script>
import PrepareSignatureFileAndSignee from './modals/PrepareSignatureFileAndSignee.vue'

import { defineComponent } from '@vue/composition-api'

export default defineComponent({
  name: 'CreateSignature',
  components: {
    PrepareSignatureFileAndSignee
  },
  props: {
    signeeSelected: {
      type: Boolean,
      default: false
    },
    selectedSignees: {
      type: Array,
      default: () => ([])
    },
    binder: {
      type: Object,
      default: () => ({})
    },
    isDirectSign: {
      type: Boolean,
      default: false,
    },
    insideBinder: {
      type: Boolean,
      default: true
    }
  }
})
</script>
