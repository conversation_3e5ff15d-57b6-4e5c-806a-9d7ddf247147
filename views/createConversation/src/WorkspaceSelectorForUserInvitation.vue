<template>
  <div>
    <div class="workspace-area" @scroll="pageScroll">
      <div>
        <div class="mx-text-h3">
          {{ $t('Select_a_workspace_to_start_with_this_client') }}
        </div>
        <div class="mx-text-c2 mx-margin-top-xxs mx-color-secondary">
          {{ $t('user_will_automatically_join_your_portal_and_the_workspace_you_choose',{user: userInfo }) }}
        </div>
      </div>
      <template v-if="showTabs.length > 1">
        <ElRadioGroup 
          v-model="activeTab" 
          class="workspace-type">
          <ElRadio 
            v-if="supportedWorkspaceTypes.length"
            label="new">
            {{ $t('new_workspace') }}
          </ElRadio>
          <ElRadio label="existing">
            {{ $t('Existing_Workspace') }}
          </ElRadio>
        </ElRadioGroup>
      </template>
      <div class="main-container">
        <WorkspaceSelectorList 
          v-show="activeTab === 'existing'"
          onlyTriggerEvent="true"
          :selectedBinderId="selectedBinder && selectedBinder.id"
          :includeBinderTypeMapper="filteredBinderType"
          @select="handleSelect" />

        <div
          v-show="activeTab === 'new' && supportedWorkspaceTypes.length > 1"
          class="new-workspace-form">
          <div>
            <div class="mx-text-c1 type-selector-title">{{ $t('Select_Workspace_Type') }}</div>
            <CustomStyleSelect
              ref="typeSelector"
              :defaultType.sync="selectedWorkspaceType"
              :options="supportedWorkspaceTypes" />
          </div>
          <div style="margin-top: 25px;">
            <NewProjectBaseForm
              v-if="isGroupWorkspace"
              ref="projectForm"
              :invitedUsers="invitedUsers" />
            <SocialTypeSelector 
              v-else-if="isSocialWorksapce"
              ref="socialForm"
              :user="defaultUser" />
          </div>
        </div>  
      </div>
    </div>
    <div class="action-btn">
      <el-button 
        class="next-btn" 
        type="primary" 
        :disabled="disableButtons"
        :loading="loading"
        @click="handleNext">
        {{ mainButtonText }}
      </el-button>
    </div>
  </div>
</template>

<script>
import WorkspaceSelectorList from '@views/createConversation/src/WorkspaceSelectorList.vue'
import NewProjectBaseForm from '@views/createConversation/src/NewProjectBaseForm'
import CustomStyleSelect from '@views/common/components/CustomStyleSelect.vue'
import SocialTypeSelector from '@views/invite/SocialTypeSelector'
import { isReachtoMaxBoardNum } from '@views/common/biz/showUpgradeReminder'
import canInvite from '@views/createConversation/src/mixin/canInvite.js'
import {useFlowTemplateSelector} from '@views/workflows/plugins/useFlowTemplateSelector'
import {useInviteClientUserStore} from '@views/stores/inviteClientUser';
import { mapActions as piniaMapActions, mapState as piniaMapState } from 'pinia'
import {ROUTER_MAP} from '@views/common/appConst'
import _pick from 'lodash/pick'
import {MxConsts} from '@commonUtils';
import {UserFormatter} from "@controller/utils/user";
import { mapGetters, mapActions,mapMutations } from 'vuex'

const WorkspaceType = {
  GROUP_WORKSPACE: 'GROUP_WORKSPACE',
  DIRECT_WORKSPACE: 'DIRECT_WORKSPACE',
  SOCIAL_WORKSPACE: 'SOCIAL_WORKSPACE',
  FLOW_WORKSPACE: 'FLOW_WORKSPACE'
}
export default {
  name: 'WorkspaceSelectorForUserInvitation',
  components: { 
    WorkspaceSelectorList, 
    CustomStyleSelect,
    NewProjectBaseForm,
    SocialTypeSelector
  },
  mixins: [canInvite],
  props: {
    showBack: {
      type: Boolean,
      default: false
    },
    disabledBinderTips: {
      type: String,
      default: ''
    },

    defaultUser: {
      type: Object,
      default: () => ({})
    },
    clientGroups: {
      type: Array,
      default: ()=>([])
    }
  },
  data () {
    return {
      activeTab: 'new',
      form: {
        name: '',
        desc: '',
        type:  WorkspaceType.FLOW_WORKSPACE
      },
      members: [],
      loading: false,
      newForm: null,
      selectedBinder: null,

      selectedWorkspaceType: '',

      filteredBinderType: {
        flowBinder: true,
        groupBinder: true,
        understaffedBinder: true,
        requiredGroupWorkspaceClientBinder: true
      }
    }
  },
  computed: {
    ...mapGetters('group', ['groupTags', 'isEnableWorkflow', 'isSocialChannelEnabled', 'requiredGroupWorkspaceClientCounts','isHideClientDashboard','enableEmailPrivacy']),
    ...mapGetters('privileges', ['canCreateChatWorkspace']),
    ...mapGetters('user', ['currentUser']),
    ...piniaMapState(useInviteClientUserStore, ['clientDistributionList']),
    showTabs () {
      const result = []
      if (this.supportedWorkspaceTypes.length) {
        result.push({ key: 'new', label: this.$t('Blank') })
      }
      result.push({ key: 'existing', label: this.$t('Existing Workspace') })
      return result
    },
    supportedWorkspaceTypes (){
      const oneoneAndGroupOption = [
        {
          value: WorkspaceType.DIRECT_WORKSPACE,
          title: this.$t('one_one_workspace'),
          subtitle: this.$t('A_one_to_one_workspace_between_you_and_this_user'),
        },
        {
          value: WorkspaceType.GROUP_WORKSPACE,
          title: this.$t('project_convesation'),
          subtitle: this.$t('group_workspace_desc'),
        },
      ]
      const flowOption = [{
        value: WorkspaceType.FLOW_WORKSPACE,
        title: this.$t('Flow_conversation'),
        subtitle: this.$t('flow_workspace_desc'),
      }]
      const socialChannelOption = [{
        value: WorkspaceType.SOCIAL_WORKSPACE,
        title: this.$t('Social_Channel_Workspace'),
        subtitle: this.$t('Connect_with_this_user_via_WhatsApp_or_WeChat'),
      }]
      let allTypes =[]
      if(this.canCreateFlowConversation){
        allTypes = allTypes.concat(flowOption)
      }
      if(this.canCreateChatWorkspace){
        allTypes = allTypes.concat(oneoneAndGroupOption)
      }
      if(this.canCreateChatWorkspace && this.isSocialChannelEnabled){
        allTypes = allTypes.concat(socialChannelOption)
      }
      return allTypes
    },
    userInfo (){
      return this.defaultUser.name + '('+ (this.defaultUser.email || this.defaultUser.phone_number) + ')'
    },

    defaultWorkspaceType (){
      if(this.canCreateFlowConversation){
        return WorkspaceType.FLOW_WORKSPACE
      }else if(this.canCreateChatWorkspace){
        return WorkspaceType.DIRECT_WORKSPACE
      }
      return ''
    },
    isGroupWorkspace (){
      return this.selectedWorkspaceType === WorkspaceType.GROUP_WORKSPACE
    },
    isSocialWorksapce (){
      return this.selectedWorkspaceType === WorkspaceType.SOCIAL_WORKSPACE
    },
    isFlowWorkspace (){
      return this.selectedWorkspaceType === WorkspaceType.FLOW_WORKSPACE
    },
    invitedUsers (){
      const {name, phone_number,email} = this.defaultUser
      const userAvatar = this.getPendingInviteUserAvatar(name)
      return [{
        name, phone_number,email,
        displayName: name,
        id: 'MOCK_ID'+ (email || phone_number),
        subTitle: email || phone_number,
        avatar: userAvatar
      }]
    },
    disableButtons (){
      return this.loading
    },
    mainButtonText (){
      if(this.activeTab === 'new' && this.isFlowWorkspace){
        return this.$t('next')
      }
      return this.$t('send')
    }

  },
  
  watch: {
    'supportedWorkspaceTypes.length':{
      handler (value){
        if(value <= 0){
          this.activeTab = 'existing'
        }
      },
      immediate: true
    }

  },
  mounted () {
    this.selectedWorkspaceType = this.defaultWorkspaceType
  },
  methods: {
    ...piniaMapActions(useInviteClientUserStore, ['addTeamMembers','createGroupConversation','createMockOneOnOneConversation','setClientTeamsForUser','inviteClientUserToBinder','createRelationAndUpdateClientInfo','inviteMember','createSocialConnection','loadBoard']),
    ...mapActions('contacts', ['checkMemberExist']),
    ...mapMutations('contacts', ['addNewAdded']),

    async handleCreateNewWorkspace () {
      if(this.selectedWorkspaceType === WorkspaceType.FLOW_WORKSPACE){
        this.createFlowWorkspace()
      }else if(this.selectedWorkspaceType === WorkspaceType.GROUP_WORKSPACE){
        this.createGroupWorksapce() 
      }else if(this.selectedWorkspaceType === WorkspaceType.DIRECT_WORKSPACE){
        this.createDirectWorkspace()
      }else if(this.selectedWorkspaceType === WorkspaceType.SOCIAL_WORKSPACE){
        this.createSocialWorkspace()
      }
    },
    handleSelect (board) {
      this.selectedBinder = board
    },
    async handleNext () {
      let isValid = await this.verifyUser()
      if(isValid === false){
        return
      }
      if (this.activeTab === 'existing') {  
        this.inviteToExistingBinder()
      } else {
        this.handleCreateNewWorkspace()
      }
    },


    isValidBinder (){
      let errorMsg
      let isValid = true
      if(this.selectedBinder){
        if(this.requiredGroupWorkspaceClientCounts > 0 && this.selectedBinder.isGroupWorkspace){
          const boardUsers = this.selectedBinder?.users || []
          const clientUsers = boardUsers.filter(bu=>{
            return !bu.is_deleted && bu.user?.type ===  MxConsts.UserType.USER_TYPE_LOCAL
          }) || []
          if(clientUsers.length >= this.requiredGroupWorkspaceClientCounts){
            isValid = false
            errorMsg = this.requiredGroupWorkspaceClientCounts === 1 ? this.$t('This_workspace_is_limited_to_1_client_tip'):this.$t('This_workspace_is_limited_to_n_clients_tip',{num:this.requiredGroupWorkspaceClientCounts})
          }
        }

      }else{
        if (!(this.canCreateChatWorkspace || this.canCreateFlowConversation)) {
          errorMsg = this.$t('select_a_workspace_to_proceed')
        } else {
          errorMsg = this.$t('direct_signature_creation_error')
        }
        isValid = false
      }
      errorMsg && this.$mxMessage.error(errorMsg)
      return isValid

    },
    async inviteToExistingBinder (){
      const isValid = this.isValidBinder()
      if(!isValid){
        return
      }
      let suppressEmailSms = this.computedSupressOrgInvitationEmialSMS({isWorkspace:true})

      const param = {
        user: this.defaultUser,
        clientTeamIds: this.clientGroups,
        suppressEmailSms: suppressEmailSms
      }
      let userInfo = null
      try {
        this.loading = true
        userInfo = await this.createRelationAndUpdateClientInfo(param)
        await this.inviteMember(this.selectedBinder.id,userInfo)
        this.invitationSuccess(this.selectedBinder.id)

        
      } catch (error) {
        // judge if reach client user number load board
        if(error.detailCode === 'EXCEED_BOARD_USERS_MAX'){
          this.$mxMessage.error(this.$t('Maximum_number_of_participants_exceeded'))
        }else if(error.detailCode === 'ERROR_INVALID_USER_TYPE'){
          let errorInfo = this.defaultUser.email? this.$t('email_address_already_in_use') : this.$t('phone_number_already_in_use')
          this.$mxMessage.error(errorInfo)
        }else{
          this.$mxMessage.error(this.$t('system_exceed_limit_error'))
        }
      } finally{
        this.loading = false
      }
    },

    createGroupWorksapce (){
      function getSelectedMembers (users, mockInvitedUserId, invitedUserId){
        const invitedUsers = users.map(m=>{
            if(m.id ===  mockInvitedUserId){
              return {id: invitedUserId}
            }else{
              return {id: m.id}
            }
          })
        return invitedUsers
      }
      const continueFunc = async ()=>{
        try {
           const { name, members, teams }= await this.$refs.projectForm.getFormData()
           let suppressEmailSms = this.computedSupressOrgInvitationEmialSMS({isWorkspace:true})
            this.loading = true
            const param = {
              user: this.defaultUser,
              suppressEmailSms: suppressEmailSms,
              clientTeamIds: this.clientGroups
            }
            const transformedUser = await this.createRelationAndUpdateClientInfo(param)

            const invitedUsers = getSelectedMembers(members,this.invitedUsers[0].id,transformedUser.id)
            const newBoardId = await this.createGroupConversation({name,members:invitedUsers,teams,syncInvite: true})
            this.invitationSuccess(newBoardId)
        } catch (error) {
          if(error?.requiredClientCountsError){
            this.$mxMessage.error(this.$t('Group_workspaces_require_at_least_client',{number: this.requiredGroupWorkspaceClientCounts}))
          }else if(error?.exceedBoardUserMaxError || error.detailCode === 'EXCEED_BOARD_USERS_MAX'){
            this.$mxMessage.error(this.$t('Maximum_number_of_participants_exceeded'))
          }else if(error.detailCode === 'ERROR_INVALID_USER_TYPE'){
            let errorInfo = this.defaultUser.email? this.$t('email_address_already_in_use') : this.$t('phone_number_already_in_use')
            this.$mxMessage.error(errorInfo)
          }else{
            this.$mxMessage.error(this.$t('system_exceed_limit_error'))
          }
          return 
        } finally{
          this.loading = false
        }
      }
  
      isReachtoMaxBoardNum({continueAct: continueFunc})
    },

    async createDirectWorkspace (){
      let suppressEmailSms = this.computedSupressOrgInvitationEmialSMS({isOneone:true})
      const param = {
        user: this.defaultUser,
        suppressEmailSms: suppressEmailSms,
        clientTeamIds: this.clientGroups
      }

      try {
        this.loading = true
        const newBoard = await this.createMockOneOnOneConversation(param)
        let newBoardId = newBoard.id
        this.invitationSuccess(newBoardId)

      } catch (error) {
         if(error.detailCode === 'ERROR_INVALID_USER_TYPE'){
          let errorInfo = this.defaultUser.email? this.$t('email_address_already_in_use') : this.$t('phone_number_already_in_use')
          this.$mxMessage.error(errorInfo)
        }else{
          this.$mxMessage.error(this.$t('system_exceed_limit_error'))
        }
        return 
      } finally {
        this.loading = false
      }
    },
    async createFlowWorkspace (){
      const vm = this
      const continueFunc = ()=>{
          const [show, hide] = useFlowTemplateSelector({
            back (){
              hide()
            },
            close (){
              vm.$emit('close')
            },
            useSuccess(){
              vm.$mxMessage.success(vm.$t('invitation_successfully_sent'))
            }
          },{
            showBack: true,
            preselectUserFromClientInvite: {...this.defaultUser, clientGroups: this.clientGroups, clientDistributionLists: this.clientDistributionList},
            defaultModalTitle: this.$t('New_Flow_Conversation'),
            hideSearchBarForEmpty: true,
            hideTemplateLibrary: true,
            beforeClose: ()=>{
              this.$emit('close')
              hide()
            },
          })
          show()
        }
        isReachtoMaxBoardNum({
          continueAct: continueFunc,
          closeParent: () => {
            this.$emit('close')
          }
        },{})
    },
    async createSocialWorkspace (){

      const {user,socialType} = await this.$refs.socialForm.getFormData()
      let suppressEmailSms = this.computedSupressOrgInvitationEmialSMS({isWorkspace: true})

      const userInfo = {
        name: this.defaultUser.name,
        email: user?.email,
        phone_number: user.phone
      }
      const param = {
        user: userInfo,
        suppressEmailSms: suppressEmailSms,
        clientTeamIds: this.clientGroups
      }
      try {
        this.loading = true
        const transformedUser = await this.createRelationAndUpdateClientInfo(param)
        const baseUser = _pick(transformedUser, 'id', 'email', 'phone_number', 'unique_id')
        const newBoardId = await this.createSocialConnection(socialType,baseUser)
        this.invitationSuccess(newBoardId)
      } catch (error) {
        if(error.detailCode === 'ERROR_INVALID_USER_TYPE'){
          let errorInfo = this.defaultUser.email? this.$t('email_address_already_in_use') : this.$t('phone_number_already_in_use')
          this.$mxMessage.error(errorInfo)
        }else{
          this.$mxMessage.error(this.$t('system_exceed_limit_error'))
        }
      }finally{
        this.loading = false
      }


    },

    invitationSuccess (boardId){
      this.$mxMessage.success(this.$t('invitation_successfully_sent'))
      this.$emit('close')
      this.$router.push({
        name:  ROUTER_MAP.project.name,
        params: {
          id: boardId
        }
      })
    },

    computedSupressOrgInvitationEmialSMS({isOneone, isWorkspace}){
      if(this.isHideClientDashboard){
        // always not send org invitation
        return true
      }else{
        if(this.enableEmailPrivacy){
          return false
        }else{
          if(isOneone){
            return false
          }else if(isWorkspace){
            return true
          }
        }
      }
    },
    getPendingInviteUserAvatar(name){
      const {first_name, last_name} = UserFormatter.splitNameToFirstAndLastName(name);
      return UserFormatter.getInitialAvatar(first_name, last_name)
    },
    async verifyUser(){
      // Handle corner case: before sending,the user is changed to deactived user or be invited as an internal user
      try {
        await this.checkMemberExist(this.defaultUser)
      } catch (err) {
        if(err.isMemberExist){
          if(err.isInternalUser){
            let errorInfo = this.defaultUser.email? this.$t('email_address_already_in_use') : this.$t('phone_number_already_in_use')
            if(err.isDisabled){
              errorInfo = this.$t('This_user_has_been_deactivated_please_contact_your_administrator')
            }
            this.$mxMessage.error(errorInfo)
            return false
          }else if(err.isDisabledClient){
            this.$mxMessage.error(this.$t('This_user_has_been_deactivated_please_contact_your_administrator'))
            return false
          }          
        } 
      }
    },
    pageScroll(e){
      this.$refs?.projectForm?.updatePopperPosition()
      this.$refs?.typeSelector?.updatePopperPosition()
    }
  }
}
</script>

<style scoped lang="scss">

.main-container {
  overflow-y: auto;
  overflow-x: hidden;
  .new-workspace-form {
    padding: 23px 0 50px;
    .type-selector-title{
      margin-bottom: 7px;
    }
  }
}

.el-radio {
  border: 1px solid $mx-color-border-primary;
  width: calc(50% - 8px);
  border-radius: 6px;
  padding: 12px 24px;

  ::v-deep .el-radio__label {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.el-radio.is-checked {
  padding: 11px 23px;
  border: 2px solid var(--duo-primary-color);
}

.workspace-type {
  padding-top: 20px;
  width: 100%;
}

.action-btn{
  display: flex;
  button {
    flex: 1 1 0%;
  }
}

</style>
