<template>
  <div class="tablist mx-clickable" role="tablist">
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      role="tab"
      class="tab"
      :class="{'active': selectedTab === index}"
      @click="switchTab">
      <div class="mx-text-c2 mx-semibold button-tab-text">
        <template v-if="$slots[tab.key]">
          <slot :name="tab.key" />
        </template>
        <template v-else>
          {{ tab.label }}
        </template>
      </div>
    </div>
    <div aria-hidden="true" class="active-tab-mask mx-semibold text-center button-tab-text"
         :class="{'active-first': selectedTab === 0, 'active-second': selectedTab === 1}">
      <template v-if="$slots[tabs[selectedTab].key]">
        <slot :name="tabs[selectedTab].key" />
      </template>
      <template v-else>
        {{tabs[selectedTab].label}}
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ButtonTab',
  props: {
    tabs: {
      type: Array,
      default: ()=>[]
    },
    initialTab: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      selectedTab: this.initialTab
    }
  },
  created() {
    window.vm = this
  },
  methods: {
    switchTab () {
      if (this.selectedTab === 0) {
        this.selectedTab = 1
      } else {
        this.selectedTab = 0
      }
      this.$emit('change', this.tabs[this.selectedTab].key)
    }
  }
}
</script>

<style scoped lang="scss">
.tablist {
  display: flex;
  padding: 8px 0;
  background-color: #f4f4f4;
  border-radius: 8px;
  position: relative;
  border: 0.5px solid rgba(0, 0, 0, 0.04);
}
.tab {
  flex: 1 1 50%;
  text-align: center;
}
.active-tab-mask {
  position: absolute;
  width: calc(50% - 4px);
  height: 32px;
  background: white;
  top: 2px;
  line-height: 32px;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.32), 0 4px 8px rgba(0, 0, 0, 0.06), 0 8px 48px #EEEEEE;
  border-radius: 6px;
  transition: .3s ease all;
  cursor: default;
  &.active-first {
    left: 2px;
  }
  &.active-second {
    left: calc(50% + 2px);
  }
}

</style>
