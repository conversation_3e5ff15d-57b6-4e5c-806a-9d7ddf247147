<template>
  <div class="searched-binders mx-padding-top-sm mx-padding-bottom-sm">
    <div
      :class="{'active': activeIndex === 0, 'on-mobile': isMobile}"
      class="search-tip mx-clickable list-item"
      @click="enterDetailSearch"
      @mouseenter="$emit('mouse-enter-element', 0)"
      @mouseleave="$emit('mouse-leave-element')">
      <i class="micon-search-s" />
      <div class="mx-padding-left-md mx-ellipsis" v-safe-html="searchTip"></div>
    </div>
    <ul
      role="listbox">
      <li
        v-for="(item, index) in matchedBinders"
        :key="item.id"
        role="option"
        :class="['searched-binder-item list-item','mx-clickable', index === activeIndex - 1 ? 'active' : '']"
        @mouseenter="$emit('mouse-enter-element', index + 1)"
        @mouseleave="$emit('mouse-leave-element')"
        @click="gotoBinder(item)">
        <i
          class="micon-mep-arrow-right"
          style="transform: rotate(-45deg); font-weight: bold;" />
        <MxFlowThumbnail
          v-if="item.isWorkflow"
          class="pull-left"
          :width="24"
          :height="28"
          :avatar="item.boardThumbnail" />
        <MxTimelineThumbnail
          v-else
          size="xxs"
          class="pull-left"
          :board-users="item.boardUsers"
          :inactive-time="item.inactive_time"
          :is-acd="item.is_acd"
          :binder-id="item.id"
          :is-conversation="item.isconversation"
          :is-inactive="item.is_inactive"
          :is-relation="item.is_relation"
          :is-social="item.isSocial"
          :is-inbox="item.is_inbox"
          :thumbnail="item.boardThumbnail" />
        <div class="binder-name mx-padding-left-sm mx-semibold mx-ellipsis">{{ item.name }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
import templateFormat from '@views/common/utils/formatter/templateFormat'
import { mapGetters } from 'vuex'
import _debounce from 'lodash/debounce'
import utils from '@views/common/utils/utils'
import MxTimelineThumbnail from '@views/common/components/thumbnail/MxTimelineThumbnail'
import MxFlowThumbnail from '@views/common/components/thumbnail/MxFlowThumbnail';
import { BrowserUtils } from '@commonUtils'


export default {
  name: 'SearchedBinders',
  components: { MxTimelineThumbnail,MxFlowThumbnail },
  props: {
    searchText: {
      type: String,
      default: ''
    },
    value: {
      type: Number,
      default: 0
    },
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      matchedBinders: [],
      isMobile: BrowserUtils.isMobile
    }
  },
  computed: {
    ...mapGetters('user', ['timelineItems']),
    searchTip () {
      return templateFormat.chatFormatForBBCode(this.$t('Search_key_in_messages_files_and_more',
        {
          key: this.searchText
        }
      ))
    }
  },
  watch: {
    searchText: {
      handler () {
        this.searchForMatchedBinders()
      },
      immediate: true
    },
    matchedBinders () {
      this.$emit('input', this.matchedBinders.length + 1)
    }
  },
  methods: {
    gotoBinder (item, index) {
      if (item.id) {
        this.$emit('gotoBinder', item)
      } else {
        this.$emit('gotoBinder', this.matchedBinders[index])
      }
    },
    enterDetailSearch () {
      this.$emit('enterDetailSearch', this.searchText)
    },
    searchForMatchedBinders: _debounce(function () {
      let matchedBinders = this.timelineItems.binders.filter(binder => !(binder.islive)).filter(binder => binder.title.toLocaleLowerCase().includes(this.searchText.toLowerCase().trim()))
      matchedBinders.sort((binderA, binderB)=>{
        return binderA.timestamp < binderB.timestamp ? 1 : -1
      })
      this.matchedBinders = matchedBinders.splice(0, 6)
    }, 300)
  }
}
</script>

<style lang="scss" scoped>
  .searched-binders {
    border-top: 1px solid $mx-color-var-fill-secondary;
  }
.search-tip {
  display: flex;
  padding: 12px 24px;
  &.active:not(.on-mobile) {
    background-color: $mx-color-var-fill-quaternary;
  }
  i {
    color: $mx-color-var-label-secondary;
  }
  span {
    line-height: 20px;
    color: #000;
  }
}
.searched-binder-item {
  display: flex;
  align-items: center;
  padding: 10px 26px;
  //&:hover,
  &.active {
    background-color: $mx-color-var-fill-quaternary;
  }
  i {
    font-size: 18px;
    margin-right: 16px;
    color: $mx-color-var-label-secondary;
  }
  .binder-name {
    color: #000;
    line-height: 20px;
  }
}
</style>
