import { AdvancedDashboardReportsController } from '@controller/advancedDashboard/src/advancedDashboardReportsController'
import moment from 'moment-timezone'
import { FunctionUtil } from '@commonUtils'
import { defaultWorkspaceConfig, defaultActionConfig } from '@views/common/appConst'
import { ContactController } from '@controller/contacts/src/contactsController'
import { GroupController } from '@controller/group/src/groupController'
import userUtil from '@views/common/utils/user'
import i18n from '@views/i18n/mepIndex'
import { useManageEngagementStore } from '@views/stores/manageEngagement'
import util from '@views/common/utils/utils'
import { UserFormatter } from '@controller/utils/user'
import defaultProfile from '@views/theme/src/images/default/default_profile_2x.png'
import MILESTONE from '@views/theme/src/images/content_lib/<EMAIL>'
import { uniqBy } from 'lodash'

import {
  ACKNOWLEDGEMENT,
  APPROVAL,
  FILEREQUEST,
  TODO,
  TRANSACTION,
  ESIGN,
  FORMREQUEST,
  PDFFORM,
  DOCUSIGNWRAPPER,
  LAUNCHWEBAPP,
  Jumio,
  MEETREQUEST,
  DECISION,
  WAIT,
  DEFAULTINTEGRATIONAPP
} from '@views/theme/src/images/base_action/index'

let controller
let contactCtrl
let groupCtrl
let defaultTeamIdMEPX = ''
export default {
  initDashboardReports({ commit }) {
    controller = AdvancedDashboardReportsController.getInstance()
  },
  destroyDashboardReports({ commit }) {
    if (controller) {
      controller.destroyDashboardReports()
    }
    commit('resetState')
  },
  initializeView({ commit, state, rootGetters, getters, dispatch }) {
    commit('resetFilters')
    dispatch('searchOwners', { searchKey: '', isLoadMore: false })
    dispatch('searchMembers', { searchKey: '', isLoadMore: false })
    dispatch('fetchTemplates', { searchKey: '', isLoadMore: false })
  },
  fetchWorkspaceConfig({ commit, state, rootGetters, getters, dispatch }) {
    commit('setActionInProgress', true)
    commit('setShowSpinner', true)
    commit('setSavedWorkspaceConfigs', [])
    commit('setWorkspaceConfig', [])
    const api_payload = {}
    getDefaultTeamId(rootGetters)

    return controller
      .fetchWorkspaceConfig(api_payload)
      .then((response) => {
        let completeConfig = null
        if (response) {
          if (response?.views) {
            if (response?.views.length) {
              // If views are present, we need to process them
              const hasDefaultView = response.views.some(
                (v) => v.view_seq === 'mepx_dashboard_default_tab'
              )
              completeConfig = JSON.parse(JSON.stringify(response.views))
              if (hasDefaultView) {
                // If default view is present, we need to process it
                completeConfig.forEach((res, index) => {
                  if (
                    res.view_seq === 'mepx_dashboard_default_tab' &&
                    !res.view_type &&
                    !res.tab_type
                  ) {
                    const constWorkspaceConfig = JSON.parse(JSON.stringify(defaultWorkspaceConfig))
                    constWorkspaceConfig.order_no = res.order_no
                    constWorkspaceConfig.view_seq = res.view_seq
                    completeConfig[index] = { ...constWorkspaceConfig }
                  }
                  completeConfig[index].state = 'SAVED'
                })
              } else {
                // If no default view is present, we need to add the default workspace config
                completeConfig.push(JSON.parse(JSON.stringify(defaultWorkspaceConfig)))
              }
            } else {
              // If no views are present, we need to add the default workspace config
              const constWorkspaceConfig = JSON.parse(JSON.stringify(defaultWorkspaceConfig))
              completeConfig = [{ ...constWorkspaceConfig }]
            }
          }

          const workspaceBoardProperties = getters.getWorkspaceTagProperties

          completeConfig.forEach((res) => {
            res.state = 'SAVED'
            processConfigs(state, res)
            if (workspaceBoardProperties && workspaceBoardProperties.length) {
              res.column_config = mergeUniqueBySequence(res.column_config, workspaceBoardProperties)
            }
          })

          completeConfig.sort((a, b) => a.order_no - b.order_no)
          commit('setSavedWorkspaceConfigs', completeConfig)
          commit('setWorkspaceConfig', completeConfig)
        }
      })
      .finally(() => {
        commit('setActionInProgress', false)
      })
  },
  updateWorkspaceConfig({ commit, state, getters, dispatch }, { mode, view_seq }) {
    commit('setActionInProgress', true)
    const api_payload = {}

    let updatingConfig = null
    if (view_seq) {
      updatingConfig = getters.getWorkspaceConfig.find((item) => item.view_seq === view_seq)
    }

    let views = []

    if (mode === 'update') {
      views = [getters.currentTabConfig]
    } else if (mode === 'delete') {
      views = [
        {
          view_seq: view_seq,
          is_deleted: true
        }
      ]
    } else if (mode === 'rename') {
      views = [updatingConfig]
    }

    if (views.length) {
      api_payload.views = views
    }

    return controller
      .updateWorkspaceConfig(api_payload)
      .then((response) => {
        if (response) {
          if (mode === 'update') {
            getters.currentTabConfig.state = 'SAVED'
            const configJSON = getters.getSavedWorkspaceConfigs.find(
              (item, index) => item.view_seq === getters.currentTabConfig.view_seq
            )
            if (configJSON) {
              // If already saved in the saved workspace configs
              const completeSavedConfig = JSON.parse(
                JSON.stringify(getters.getSavedWorkspaceConfigs)
              )
              completeSavedConfig.forEach((item, index) => {
                if (item.view_seq == getters.currentTabConfig.view_seq) {
                  completeSavedConfig[index] = JSON.parse(JSON.stringify(getters.currentTabConfig))
                }
              })
              commit('setSavedWorkspaceConfigs', completeSavedConfig)
            } else {
              // Need to add currentTabConfig to saved workspace configs
              const completeConfig = JSON.parse(JSON.stringify(getters.getSavedWorkspaceConfigs))
              const tempConfigJSON = JSON.parse(JSON.stringify(getters.currentTabConfig))
              tempConfigJSON.state = 'SAVED'
              completeConfig.push(tempConfigJSON)
              commit('setSavedWorkspaceConfigs', completeConfig)
            }
            commit('setShowSpinner', false)
          } else if (mode === 'rename') {
            const configJSON = getters.getSavedWorkspaceConfigs.find(
              (item, index) => item.view_seq === view_seq
            )
            if (configJSON) {
              // If already saved in the saved workspace configs
              const completeSavedConfig = JSON.parse(
                JSON.stringify(getters.getSavedWorkspaceConfigs)
              )
              completeSavedConfig.forEach((item, index) => {
                if (item.view_seq == view_seq) {
                  completeSavedConfig[index] = JSON.parse(JSON.stringify(updatingConfig))
                }
              })
              commit('setSavedWorkspaceConfigs', completeSavedConfig)
            } else {
              // Need to add currentTabConfig to saved workspace configs
              const completeConfig = JSON.parse(JSON.stringify(getters.getSavedWorkspaceConfigs))
              const tempConfigJSON = JSON.parse(JSON.stringify(updatingConfig))
              tempConfigJSON.state = 'SAVED'
              completeConfig.push(tempConfigJSON)
              commit('setSavedWorkspaceConfigs', completeConfig)
            }
            commit('setShowSpinner', false)
          } else if (mode === 'delete') {
            const completeConfig = JSON.parse(JSON.stringify(getters.getSavedWorkspaceConfigs))
            const updatedConfig = completeConfig.filter((item) => item.view_seq !== view_seq)
            commit('setSavedWorkspaceConfigs', updatedConfig)
            commit('setShowSpinner', false)
          }
        }
      })
      .finally(() => {
        commit('setActionInProgress', false)
      })
  },
  updateConfigOrder({ commit, state, getters, dispatch }) {
    commit('setActionInProgress', true)
    let entire_update = []

    if (state.dashboardType === 'advancedWorkspaceReport') {
      entire_update = getters.getWorkspaceConfig.map(({ view_seq, order_no }) => ({
        view_seq,
        order_no
      }))
    } else {
      entire_update = getters.getActionConfig.map(({ view_seq, order_no }) => ({
        view_seq,
        order_no
      }))
    }

    const api_payload = {
      views: entire_update,
      module: state.dashboardType
    }

    return controller
      .updateConfigOrder(api_payload)
      .then((response) => { })
      .finally(() => {
        commit('setActionInProgress', false)
      })
  },
  fetchWorkspaces({ commit, state, getters, rootGetters }, payload = {}) {
    commit('setActionInProgress', true)
    const { is_kanban_fetching = false, page_number = 1 } = payload
    if (!is_kanban_fetching) {
      commit('setShowSpinner', true)
    }

    const currentUser = rootGetters['user/currentUser']

    const currentTabConfig = getters.currentTabConfig
    const api_payload = {
      browser_timezone: moment.tz.guess(),
      view_type: currentTabConfig?.view_type || 'SELF',
      tab_type: currentTabConfig?.tab_type || 'LIST',
      kanban_column: currentTabConfig?.kanban_column || '',
      search_text: currentTabConfig?.search_text || '',
      team_id: currentTabConfig?.team_id || '',
      search_size: state.searchSize,
      current_user_timezone: currentUser.timezone,
      page_number: is_kanban_fetching ? page_number : state.currentPage,
      ...currentTabConfig
    }

    if (currentTabConfig?.date_range) {
      const date_range = processDateRange(currentTabConfig?.date_range)
      api_payload.from_date = date_range.from_date
      api_payload.to_date = date_range.to_date
    }

    commit('setTotalRecords', 0)
    commit('setWorkspaces', [])
    if (!is_kanban_fetching) {
      commit('setKanbanData', {})
    }

    return controller
      .fetchWorkspaces(api_payload)
      .then((response) => {
        if (response.total_records) {
          commit('setTotalRecords', response.total_records)
        }

        if (response.list) {
          if (currentTabConfig?.tab_type === 'KANBAN') {
            if (!is_kanban_fetching) {
              commit('setKanbanData', response)
            } else {
              const kanban_data = {
                ...state.kanbanData,
                list: [...state.kanbanData.list, ...response.list]
              }
              commit('setKanbanData', kanban_data)
            }
          }
          try {
            const list = response.list.map((item) => {
              item.open_steps = item?.open_actions_count
              item.assignees = processUserImage(item?.assignees, rootGetters)
              item.formatted_last_updated_at = formatDateToReadable(item.last_updated)
              item.formatted_duration = formatDuration(item)
              item.processed_workspace_status = processStatus(item?.workspace_status)
              item.processed_action_status = processStatus(item?.action_status)
              item.processed_total_steps = processSteps(item?.total_steps)
              item.processed_open_steps = processSteps(item?.open_steps)
              item.processed_waiting_to_text = processWaitingToTextAction(item)
              item.current_action_icon = processTypeDetails(
                item,
                item.obj_type_id,
                item.obj_sub_type
              ).icon
              item.processed_step_type_name = processTypeDetails(
                item,
                item.obj_type_id,
                item.obj_sub_type
              ).title
              item.open_actions_list = processActions(item)
              return item
            })
            syncFilters(getters.currentTabConfig) 
            commit('setWorkspaces', list)
          } catch (e) {
            console.log(e)
          }
        }
      })
      .finally(() => {
        commit('setActionInProgress', false)
        commit('setShowSpinner', false)
      })
  },
  fetchActionConfig({ commit, state, rootGetters, getters, dispatch }) {
    commit('setActionInProgress', true)
    commit('setShowSpinner', true)
    commit('setActionConfig', [])
    commit('setSavedActionConfigs', [])
    getDefaultTeamId(rootGetters)

    const api_payload = {}

    return controller
      .fetchActionConfig(api_payload)
      .then((response) => {
        if (response) {
          let completeConfig = null
          if (response?.views) {
            if (response?.views.length) {
              // If views are present, we need to process them
              const hasDefaultView = response.views.some(
                (v) => v.view_seq === 'mepx_dashboard_default_tab'
              )
              completeConfig = JSON.parse(JSON.stringify(response.views))
              if (hasDefaultView) {
                // If default view is present, we need to process it
                completeConfig.forEach((res, index) => {
                  if (
                    res.view_seq === 'mepx_dashboard_default_tab' &&
                    !res.view_type &&
                    !res.tab_type
                  ) {
                    const constActionConfig = JSON.parse(JSON.stringify(defaultActionConfig))
                    constActionConfig.order_no = res.order_no
                    constActionConfig.view_seq = res.view_seq
                    completeConfig[index] = { ...constActionConfig }
                  }
                  completeConfig[index].state = 'SAVED'
                })
              } else {
                // If no default view is present, we need to add the default action config
                completeConfig.push(JSON.parse(JSON.stringify(defaultActionConfig)))
              }
            } else {
              // If no views are present, we need to add the default action config
              const constActionConfig = JSON.parse(JSON.stringify(defaultActionConfig))
              completeConfig = [{ ...constActionConfig }]
            }
          }

          completeConfig.forEach((res) => {
            res.state = 'SAVED'
            processConfigs(state, res)
          })

          completeConfig.sort((a, b) => a.order_no - b.order_no)
          commit('setActionConfig', completeConfig)
          commit('setSavedActionConfigs', completeConfig)
        }
      })
      .finally(() => {
        commit('setActionInProgress', false)
      })
  },
  updateActionConfig({ commit, state, getters, dispatch }, { mode, view_seq }) {
    commit('setActionInProgress', true)
    const api_payload = {}

    let updatingConfig = null
    if (view_seq) {
      updatingConfig = getters.getActionConfig.find((item) => item.view_seq === view_seq)
    }

    let views = []
    if (mode === 'update') {
      views = [getters.currentTabConfig]
    } else if (mode === 'delete') {
      views = [
        {
          view_seq: view_seq,
          is_deleted: true
        }
      ]
    } else if (mode === 'rename') {
      views = [updatingConfig]
    }

    if (views.length) {
      api_payload.views = views
    }

    return controller
      .updateActionConfig(api_payload)
      .then((response) => {
        if (response) {
          if (mode === 'update') {
            getters.currentTabConfig.state = 'SAVED'
            const configJSON = getters.getSavedActionConfigs.find(
              (item, index) => item.view_seq === getters.currentTabConfig.view_seq
            )
            if (configJSON) {
              // If already saved in the saved action configs
              const completeSavedConfig = JSON.parse(JSON.stringify(getters.getSavedActionConfigs))
              completeSavedConfig.forEach((item, index) => {
                if (item.view_seq == getters.currentTabConfig.view_seq) {
                  completeSavedConfig[index] = JSON.parse(JSON.stringify(getters.currentTabConfig))
                }
              })
              commit('setSavedActionConfigs', completeSavedConfig)
            } else {
              // Need to add currentTabConfig to saved action configs
              const completeConfig = JSON.parse(JSON.stringify(getters.getSavedActionConfigs))
              const tempConfigJSON = JSON.parse(JSON.stringify(getters.currentTabConfig))
              tempConfigJSON.state = 'SAVED'
              completeConfig.push(tempConfigJSON)
              completeConfig.sort((a, b) => a.order_no - b.order_no)
              commit('setSavedActionConfigs', completeConfig)
            }
            commit('setShowSpinner', false)
          } else if (mode === 'rename') {
            const configJSON = getters.getSavedActionConfigs.find(
              (item, index) => item.view_seq === view_seq
            )
            if (configJSON) {
              // If already saved in the saved action configs
              const completeSavedConfig = JSON.parse(JSON.stringify(getters.getSavedActionConfigs))
              completeSavedConfig.forEach((item, index) => {
                if (item.view_seq == view_seq) {
                  completeSavedConfig[index] = JSON.parse(JSON.stringify(updatingConfig))
                }
              })
              commit('setSavedActionConfigs', completeSavedConfig)
            } else {
              // Need to add currentTabConfig to saved action configs
              const completeConfig = JSON.parse(JSON.stringify(getters.getSavedActionConfigs))
              const tempConfigJSON = JSON.parse(JSON.stringify(updatingConfig))
              tempConfigJSON.state = 'SAVED'
              completeConfig.push(tempConfigJSON)
              commit('setSavedActionConfigs', completeConfig)
            }
            commit('setShowSpinner', false)
          } else if (mode === 'delete') {
            const completeConfig = JSON.parse(JSON.stringify(getters.getSavedActionConfigs))
            const updatedConfig = completeConfig.filter((item) => item.view_seq !== view_seq)
            commit('setSavedActionConfigs', updatedConfig)
            commit('setShowSpinner', false)
          }
        }
      })
      .finally(() => {
        commit('setActionInProgress', false)
      })
  },
  fetchActions({ commit, state, getters, dispatch, rootGetters }, payload = {}) {
    commit('setActionInProgress', true)
    const { is_kanban_fetching = false, page_number = 1 } = payload
    if (!is_kanban_fetching) {
      commit('setShowSpinner', true)
    }

    const currentUser = rootGetters['user/currentUser']

    const currentTabConfig = getters.currentTabConfig
    const api_payload = {
      browser_timezone: moment.tz.guess(),
      view_type: currentTabConfig?.view_type || 'SELF',
      tab_type: currentTabConfig?.tab_type || 'LIST',
      kanban_column: currentTabConfig?.kanban_column || '',
      search_text: currentTabConfig?.search_text || '',
      team_id: currentTabConfig?.team_id || '',
      search_size: state.searchSize,
      page_number: is_kanban_fetching ? page_number : state.currentPage,
      ...currentTabConfig
    }

    if (currentTabConfig?.date_range) {
      const date_range = processDateRange(currentTabConfig?.date_range)
      api_payload.from_date = date_range.from_date
      api_payload.to_date = date_range.to_date
    }

    commit('setTotalRecords', 0)
    commit('setActions', [])
    if (!is_kanban_fetching) {
      commit('setKanbanData', {})
    }

    return controller
      .fetchActions(api_payload)
      .then((response) => {
        if (response.total_records) {
          commit('setTotalRecords', response.total_records)
        }

        if (response.list) {
          if (currentTabConfig?.tab_type === 'KANBAN') {
            if (!is_kanban_fetching) {
              commit('setKanbanData', response)
            } else {
              const kanban_data = {
                ...state.kanbanData,
                list: [...state.kanbanData.list, ...response.list]
              }
              commit('setKanbanData', kanban_data)
            }
          }
          try {
            const list = response.list.map((item) => {
              item.assignees = processUserImage(item?.assignees, rootGetters)
              item.workspace_timeframe = processWorkspaceTimeframe(
                item?.workspace_created_at,
                item?.workspace_due_date,
                currentUser.timezone
              )
              item.workspace_status = processStatus(item?.workspace_status)
              item.formatted_last_updated_at = formatDateToReadable(item.updated_at)
              item.formatted_duration = formatDuration(item)
              item.processed_action_status = processStatus(item?.action_status)
              item.processed_waiting_to_text = processWaitingToTextAction(item)
              item.processed_step_type_name = processTypeDetails(
                item,
                item.obj_type_id,
                item.obj_sub_type
              ).title
              item.current_action_icon = processTypeDetails(
                item,
                item.obj_type_id,
                item.obj_sub_type
              ).icon
              return item
            })
            syncFilters(getters.currentTabConfig) 
            commit('setActions', list)
          } catch (e) {
            console.log(e)
          }
        }
      })
      .finally(() => {
        commit('setActionInProgress', false)
        commit('setShowSpinner', false)
      })
  },
  handleExports({ commit, state, getters, rootGetters, dispatch }, { file_name, column_type }) {
    const currentTabConfig = getters.currentTabConfig
    const current_locale = moment.locale()
    const api_payload = {
      browser_timezone: moment.tz.guess(),
      view_type: currentTabConfig?.view_type || 'SELF',
      tab_type: currentTabConfig?.tab_type || 'LIST',
      kanban_column: currentTabConfig?.kanban_column || '',
      search_text: currentTabConfig?.search_text || '',
      file_name: file_name,
      current_locale: current_locale,
      ...currentTabConfig
    }

    if (currentTabConfig?.date_range) {
      const date_range = processDateRange(currentTabConfig?.date_range)
      api_payload.from_date = date_range.from_date
      api_payload.to_date = date_range.to_date
    }

    const columns = []
    const headers = []

    currentTabConfig.column_config.forEach((el, index) => {
      if (column_type === 'current') {
        if (el.visible) {
          columns.push(el?.name)
          if (el?.name == 'duration_in_seconds') {
            headers.push(i18n.t('duration_in_days'))
          }
          else {
            headers.push(i18n.t(el?.label))
          }
          if (state.dashboardType === 'advancedWorkspaceReport') {
            if (el?.name === 'binder_name') {
              columns.push('binder_type')
              headers.push(i18n.t('Conversation_Type'))
            } else if (el?.name === 'binder_obj_title') {
              columns.push('binder_obj_type')
              headers.push(i18n.t('current_action_type'))
            } else if (el?.name === 'owned_by') {
              columns.push('created_at')
              headers.push(i18n.t('created_at'))
            } else if (el?.name === 'last_updated') {
              columns.push('binder_id')
              headers.push(i18n.t('workspace_id'))
            }
          } else {
            if (el?.name === 'binder_obj_title') {
              columns.push('binder_obj_type')
              headers.push(i18n.t('action_type'))
            } else if (el?.name === 'binder_name') {
              columns.push('workspace_created_at')
              headers.push(i18n.t('workspace_created_at'))
              columns.push('workspace_due_date')
              headers.push(i18n.t('workspace_due_date'))
              columns.push('workspace_status')
              headers.push(i18n.t('workspace_status'))
            } else if (el?.name === 'duration_in_seconds') {
              columns.push('binder_id')
              headers.push(i18n.t('workspace_id'))
              columns.push('binder_obj_seq')
              headers.push(i18n.t('action_id'))
            }
          }
        }
      } else {
        columns.push(el?.name)
        if (el?.name == 'duration_in_seconds') {
          headers.push(i18n.t('duration_in_days'))
        }
        else {
          headers.push(i18n.t(el?.label))
        }

        if (state.dashboardType === 'advancedWorkspaceReport') {
          if (el?.name === 'binder_name') {
            columns.push('binder_type')
            headers.push(i18n.t('Conversation_Type'))
          } else if (el?.name === 'binder_obj_title') {
            columns.push('binder_obj_type')
            headers.push(i18n.t('current_action_type'))
          } else if (el?.name === 'creator') {
            columns.push('created_at')
            headers.push(i18n.t('created_at'))
          } else if (el?.name === 'last_updated') {
            columns.push('binder_id')
            headers.push(i18n.t('workspace_id'))
          }
        } else {
          if (el?.name === 'binder_obj_title') {
            columns.push('binder_obj_type')
            headers.push(i18n.t('action_type'))
          } else if (el?.name === 'binder_name') {
            columns.push('workspace_created_at')
            headers.push(i18n.t('workspace_created_at'))
            columns.push('workspace_due_date')
            headers.push(i18n.t('workspace_due_date'))
            columns.push('workspace_status')
            headers.push(i18n.t('workspace_status'))
          } else if (el?.name === 'duration_in_seconds') {
            columns.push('binder_id')
            headers.push(i18n.t('workspace_id'))
            columns.push('binder_obj_seq')
            headers.push(i18n.t('action_id'))
          }
        }
      }
    })

    if (state.dashboardType === 'advancedWorkspaceReport') {
      api_payload.columns = columns
      api_payload.headers = headers
      return controller.exportWorkspaceData(api_payload)
    } else {
      api_payload.columns = columns
      api_payload.headers = headers
      return controller.exportActionData(api_payload)
    }
  },
  fetchTemplates({ commit, state, getters, dispatch }, { searchKey = '', isLoadMore = false }) {
    let selectedTemplate = null
    const currentTabConfig = getters.currentTabConfig
    const { filter_config } = currentTabConfig
    const templateId = !searchKey && !isLoadMore && getFilterValue(filter_config, 'template_id')
    const milestoneId =
      !searchKey && !isLoadMore && getFilterValue(filter_config, 'template_milestone_seq')
    if (state.templateList.length) {
      selectedTemplate = state.templateList.find(
        (template) => template.flow_tmpl_binder_id === templateId
      )
    }
    const totalRecords = state.totalTemplateListRecords || 0
    const pageNumber = isLoadMore ? Math.floor(totalRecords / state.searchSize) + 1 : 1
    if (isLoadMore && state.templateList.length >= totalRecords) {
      selectedTemplate = true
    }
    const api_payload = {
      browser_timezone: moment.tz.guess(),
      view_type: currentTabConfig?.view_type || 'SELF',
      search_text: searchKey,
      template_id: templateId,
      search_size: state.searchSize,
      page_number: pageNumber
    }
    if (!selectedTemplate) {
      commit('setTemplatesLoading', true)
      return controller
        .fetchTemplates(api_payload)
        .then((response) => {
          if (!response) return

          selectedTemplate = templateId
            ? response.list.find(({ flow_tmpl_binder_id }) => flow_tmpl_binder_id === templateId) ||
            {}
            : {}

          if (templateId && selectedTemplate.total_milestones > 0) {
            dispatch('fetchMilestones', {
              templateId,
              searchKey: '',
              isLoadMore: false,
              fromInit: true
            })
          }

          if (milestoneId || selectedTemplate.total_steps > 0) {
            dispatch('fetchTemplateSteps', {
              templateId,
              milestoneId,
              templateStepId: '',
              searchKey: '',
              pageNumber: 1,
              isLoadMore: false,
              fromInit: true
            })
          }

          const existingList = state.templateList ?? []
          const mergedList = uniqBy(
            [...existingList, ...(response.list ?? [])],
            'flow_tmpl_binder_id'
          ).map((template) => {
            const {
              flow_tmpl_binder_id,
              thumbnail_sequence,
              total_steps,
              created_by,
              flow_tmpl_name
            } = template

            const icon = thumbnail_sequence
              ? `/board/${flow_tmpl_binder_id}/${thumbnail_sequence}`
              : '0'

            const stepsLabel = i18n.t(total_steps > 1 ? 'Steps_upper_case' : 'step')

            return {
              ...template,
              title: flow_tmpl_name,
              value: flow_tmpl_binder_id,
              icon,
              sub_title: `${created_by} | ${total_steps} ${stepsLabel}`
            }
          })

          commit('setTemplateList', mergedList)
          if (!templateId && !searchKey) {
            commit('setTotalTemplateListRecords', response.total_records)
          }
          return response
        })
        .finally(() => {
          commit('setTemplatesLoading', false)
        })
    } else if (!isLoadMore && !searchKey) {
      if (templateId && selectedTemplate.total_milestones > 0) {
        dispatch('fetchMilestones', {
          templateId,
          searchKey: '',
          isLoadMore: false,
          fromInit: true
        })
      }

      if (milestoneId || selectedTemplate.total_steps > 0) {
        dispatch('fetchTemplateSteps', {
          templateId,
          milestoneId,
          templateStepId: '',
          searchKey: '',
          pageNumber: 1,
          isLoadMore: false,
          fromInit: true
        })
      }
    }
  },
  fetchMilestones(
    { commit, state, getters, dispatch },
    { templateId, searchKey = '', isLoadMore = false, fromInit = false }
  ) {
    commit('setMilestonesLoading', true)
    const currentTabConfig = getters.currentTabConfig

    const totalFetched = state.milestoneList?.length || 0
    if (state.totalMilestoneListRecords <= totalFetched && totalFetched > 0 && isLoadMore) {
      commit('setMilestonesLoading', false)
      return
    }

    const pageNumber = isLoadMore ? Math.floor(totalFetched / state.searchSize) + 1 : 1

    const api_payload = {
      view_type: currentTabConfig?.view_type || 'SELF',
      search_size: state.searchSize,
      page_number: pageNumber,
      flow_template_id: templateId,
      search_text: searchKey
    }
    if (!searchKey) {
      commit('setMilestoneList', [])
    }
    return controller
      .fetchMilestones(api_payload)
      .then((response) => {
        commit('setHasMilestone', true)
        if (response) {
          let updatedList = response.list || []
          // Merge with existing list while ensuring uniqueness
          const existingList = state.milestoneList || []
          const existingIds = new Set(existingList.map((t) => t.flow_tmpl_milestone_seq))
          updatedList = [
            ...existingList,
            ...updatedList.filter((t) => !existingIds.has(t.flow_tmpl_milestone_seq))
          ]

          updatedList.forEach((milestone) => {
            milestone.title = milestone.flow_tmpl_milestone_name
            milestone.value = String(milestone.flow_tmpl_milestone_seq)
            milestone.icon = MILESTONE
          })

          commit('setMilestoneList', updatedList)
          commit('setTotalMilestoneListRecords', response?.total_records)
        }
      })
      .finally(() => {
        commit('setMilestonesLoading', false)
      })
  },
  fetchTemplateSteps(
    { commit, state, getters, dispatch },
    { templateId, milestoneId, searchKey = '', isLoadMore = false, fromInit = false }
  ) {
    commit('setCurrentStepsLoading', true)
    const currentTabConfig = getters.currentTabConfig

    const { filter_config } = currentTabConfig
    milestoneId =
      milestoneId || (fromInit && getFilterValue(filter_config, 'template_milestone_seq'))

    const totalFetched = state.templateStepList?.length || 0
    if (state.totalTemplateStepListRecords <= totalFetched && totalFetched > 0 && isLoadMore) {
      commit('setCurrentStepsLoading', false)
      return
    }

    const pageNumber = isLoadMore ? Math.floor(totalFetched / state.searchSize) + 1 : 1

    const api_payload = {
      view_type: currentTabConfig?.view_type || 'SELF',
      search_size: state.searchSize,
      page_number: pageNumber,
      flow_template_id: templateId,
      flow_template_milestone_seq: milestoneId
    }
    if (!searchKey) {
      commit('setTemplateStepList', [])
    }
    return controller
      .fetchTemplateSteps(api_payload)
      .then((response) => {
        commit('setHasStep', true)
        if (response) {
          let updatedList = response.list || []
          // Merge with existing list while ensuring uniqueness
          const existingList = state.templateStepList || []
          const existingIds = new Set(existingList.map((t) => t.flow_tmpl_step_seq))
          updatedList = [
            ...existingList,
            ...updatedList.filter((t) => !existingIds.has(t.flow_tmpl_step_seq))
          ]

          updatedList.sort((a, b) => a?.flow_tmpl_step_name.localeCompare(b?.flow_tmpl_step_name))

          updatedList.forEach((action) => {
            action.title = action.flow_tmpl_step_name
            action.value = String(action.flow_tmpl_step_seq)
            action.icon = ''
            action.flow_tmpl_step_type = action.flow_tmpl_step_type
            action.flow_tmpl_step_sub_type = action?.flow_tmpl_step_sub_type || ''
          })

          commit('setTemplateStepList', updatedList)
          commit('setTotalTemplateStepListRecords', response.total_records)
        }
      })
      .finally(() => {
        commit('setCurrentStepsLoading', false)
      })
  },
  async fetchUsers({ commit, state, getters, dispatch }, { payload }) {
    const currentTabConfig = getters.currentTabConfig

    const api_payload = {
      browser_timezone: moment.tz.guess(),
      view_type: currentTabConfig?.view_type || 'SELF',
      search_size: state.searchSize,
      page_number: payload?.page_number || 1,
      ...payload
    }

    return controller
      .fetchUsers(api_payload)
      .then((response) => {
        if (response) {
          return response
        }
      })
      .finally(() => { })
  },
  searchOwners({ commit, getters, state, rootGetters }, { searchKey, isLoadMore = false, teamId = '' }) {
    const currentTabConfig = getters.currentTabConfig
    const { filter_config } = currentTabConfig
    const id = !searchKey && !isLoadMore && getFilterValue(filter_config, 'workspace_owner_id')
    state.isSearchingOwners = !!searchKey
    let startSeq = 0
    groupCtrl = groupCtrl || GroupController.getInstance()
    contactCtrl = contactCtrl || ContactController.getInstance()
    if (getters.currentTabConfig.view_type === 'SELF') {
      isLoadMore = isLoadMore && getters.myViewOwnersHasNextPage
      if (id) {
        const memberIds = id.split(',')
        const notFound = memberIds
          .filter((memberId) => !getters.myViewOwners.some((user) => user.id === memberId))
          .map((memberId) => ({ id: memberId }))
        if (notFound.length) {
          commit('setWorkspaceOwnerLoading', true)
          groupCtrl
            .queryGroupMembers(notFound)
            .then((member) => {
              let contactsObj = userUtil.formatContacts([member], false, null, getters.myViewOwners)
              const list = processUserData(contactsObj.contacts)
              commit('setMyViewOwners', list)
            })
            .finally(() => {
              commit('setWorkspaceOwnerLoading', false)
            })
        }
      }
      if (searchKey || isLoadMore || !getters.myViewOwners.length) {
        commit('setWorkspaceOwnerLoading', true)
        if (isLoadMore) {
          startSeq = getters.myViewOwners[getters.myViewOwners.length - 1].sequence || 0
        }
        contactCtrl
          .searchContactsWithPaging(startSeq, searchKey, {
            involveMe: true,
            includeRelation: false,
            includeSuggested: false
          })
          .then(({ contacts, hasNextPage }) => {
            if (contacts && contacts.length) {
              const currentUser = rootGetters['user/currentUser']
              contacts.push(currentUser)
              const contactsObj = userUtil.formatContacts(contacts, false, null, getters.myViewOwners)
              const list = processUserData(contactsObj.contacts)
              commit('setMyViewOwners', list)
              commit('setMyViewOwnersHasNextPage', hasNextPage)
            }
          })
          .finally(() => {
            state.isSearchingOwners = false
            commit('setWorkspaceOwnerLoading', false)
          })
      }
    } else if (getters.currentTabConfig.view_type === 'ADMIN') {
      isLoadMore = isLoadMore && getters.adminViewOwnersHasNextPage
      if (id) {
        const memberIds = id.split(',')
        const notFound = memberIds
          .filter((memberId) => !getters.adminViewOwners.some((user) => user.id === memberId))
          .map((memberId) => ({ id: memberId }))
        if (notFound.length) {
          commit('setWorkspaceOwnerLoading', true)
          groupCtrl
            .queryGroupMembers(notFound)
            .then((member) => {
              let contactsObj = userUtil.formatContacts([member], false, null, getters.myViewOwners)
              const list = processUserData(contactsObj.contacts)
              commit('setAdminViewOwners', list)
            })
            .finally(() => {
              commit('setWorkspaceOwnerLoading', false)
            })
        }
      }
      if (searchKey || isLoadMore || !getters.adminViewOwners.length) {
        if (isLoadMore) {
          startSeq = getters.adminViewOwners[getters.adminViewOwners.length - 1].sequence || 0
        }
        commit('setWorkspaceOwnerLoading', true)
        groupCtrl
          .filterGroupMembers(startSeq, 100, {
            involveMe: true,
            includeRelation: true,
            includeSuggested: true,
            searchKey,
            isInternal: true
          })
          .then((members) => {
            if (members && members.length) {
              let contactsObj = userUtil.formatContacts(
                members,
                false,
                null,
                getters.adminViewOwners
              )
              const list = processUserData(contactsObj.contacts)
              commit('setAdminViewOwners', list)
              commit('setAdminViewOwnersHasNextPage', !members.noNextPage)
            }
          })
          .finally(() => {
            commit('setWorkspaceOwnerLoading', false)
            state.isSearchingOwners = false
          })
      }
    } else if (getters.currentTabConfig.view_type == 'MANAGER') {
      if(teamId)
      {
          commit('setManagerViewOwners', [])
      }
      const latestTeamId = teamId || getters.currentTabConfig?.team_id || ''
      const piniaStore = useManageEngagementStore()
      const filter = {
        teamId: latestTeamId,
        searchKey,
        pageNumber: 1,
        fetchAll: true
      }
      piniaStore
        .readTeamMembers(filter)
        .then((res) => {
          let updatedList = (res.members || []).map((user) => {
            user.fullName = user.fullName || user.name
            user.subTitle = user.subTitle || user.email
            user.id = user.id || user.userId
            return user
          })
          const existingList = getters.managerViewOwners || []
          const contactObj = userUtil.formatContacts(updatedList, false, null, existingList)
          commit('setManagerViewOwners', processUserData(contactObj.contacts))
          commit('setWorkspaceOwnerLoading', false)
        })
        .catch(() => {
          this.$mxMessage.error(i18n.t('system_unknown_error'))
          this.handleClear()
        })
        .finally(() => {
          commit('setWorkspaceOwnerLoading', false)
        })
    }
  },
  searchMembers({ commit, getters, state, dispatch }, { searchKey, isLoadMore = false, fromTeam = false }) {
    const currentTabConfig = getters.currentTabConfig
    const { filter_config } = currentTabConfig
    groupCtrl = groupCtrl || GroupController.getInstance()
    contactCtrl = contactCtrl || ContactController.getInstance()
    let startSeq = 0
    state.isSearchingMembers = !!searchKey
    const id = !searchKey && !isLoadMore && getFilterValue(filter_config, 'workspace_member_id')
    if (getters.currentTabConfig.view_type === 'SELF' || fromTeam) {
      isLoadMore = isLoadMore && getters.myViewMembersHasNextPage
      if (id) {
        const memberIds = id.split(',')
        const notFound = memberIds
          .filter((memberId) => !getters.myViewMembers.some((user) => user.id === memberId))
          .map((memberId) => ({ id: memberId }))
        if (notFound.length) {
          commit('setWorkspaceMemberLoading', true)
          groupCtrl
            .queryGroupMembers(notFound)
            .then((member) => {
              let contactsObj = userUtil.formatContacts(
                [member],
                false,
                null,
                getters.myViewMembers
              )
              const list = processUserData(contactsObj.contacts)
              commit('setMyViewMembers', list)
            })
            .finally(() => {
              commit('setWorkspaceOwnerLoading', false)
            })
        }
      }
      if (searchKey || isLoadMore || !getters.myViewMembers.length) {
        if (isLoadMore) {
          startSeq = getters.myViewMembers[getters.myViewMembers.length - 1].sequence || 0
        }
        contactCtrl
          .searchContactsWithPaging(startSeq, searchKey, { involveMe: true })
          .then(({ contacts, hasNextPage }) => {
            if (contacts && contacts.length) {
              let contactsObj = userUtil.formatContacts(
                contacts,
                false,
                null,
                getters.myViewMembers
              )
              const list = processUserData(contactsObj.contacts)
              commit('setMyViewMembers', list)
              commit('setMyViewMembersHasNextPage', hasNextPage)
            }
          })
          .finally(() => {
            state.isSearchingMembers = false
          })
      }
    } else if (getters.currentTabConfig.view_type === 'ADMIN') {
      isLoadMore = isLoadMore && getters.adminViewMembersHasNextPage
      if (id) {
        const memberIds = id.split(',')
        const notFound = memberIds
          .filter((memberId) => !getters.adminViewMembers.some((user) => user.id === memberId))
          .map((memberId) => ({ id: memberId }))
        if (notFound.length) {
          commit('setWorkspaceMemberLoading', true)
          groupCtrl
            .queryGroupMembers(notFound)
            .then((member) => {
              let contactsObj = userUtil.formatContacts([member], false, null, getters.myViewOwners)
              const list = processUserData(contactsObj.contacts)
              commit('setAdminViewMembers', list)
            })
            .finally(() => {
              commit('setWorkspaceOwnerLoading', false)
            })
        }
      }
      if (searchKey || isLoadMore || !getters.adminViewMembers.length) {
        if (isLoadMore) {
          startSeq = getters.adminViewMembers[getters.adminViewMembers.length - 1].sequence || 0
        }
        groupCtrl
          .filterGroupMembers(startSeq, 200, {
            involveMe: true,
            includeRelation: true,
            includeSuggested: true,
            searchKey
          })
          .then((members) => {
            if (members && members.length) {
              let contactsObj = userUtil.formatContacts(
                members,
                false,
                null,
                getters.adminViewMembers
              )
              const list = processUserData(contactsObj.contacts)
              commit('setAdminViewMembers', list)
              commit('setAdminViewMembersHasNextPage', !members.noNextPage)
            }
          })
          .finally(() => {
            state.isSearchingMembers = false
          })
      }
    } else if (getters.currentTabConfig.view_type === 'MANAGER') {
      let ownerId = null
      if (state.inProgressFilterConfig.filterType === 'quick') {
        ownerId = state.quickFilter?.workspace_owner_id
      } else if (state.advancedFilter) {
        ownerId = state.advancedFilter.line_items.find(({ filter }) => filter === 'workspace_owner_id')?.value
      }
      const apis = []
      const params = []
      const piniaStore = useManageEngagementStore()
      if (
        ownerId ||
        (ownerId && searchKey) ||
        (ownerId && isLoadMore && getters.nextTeamClientPage)
      ) {
        params.push({ ownerId, searchKey, pageNumber: getters.nextTeamClientPage })
        apis.push((params) =>
          piniaStore.readInternalUserClients(params.ownerId, {
            searchKey: params.searchKey,
            pageNumber: params.pageNumber
          })
        )
      }
      if (searchKey || (isLoadMore && getters.myViewMembersHasNextPage)) {
        params.push({ searchKey, isLoadMore })
        apis.push((params) =>
          dispatch('searchMembers', {
            searchKey: params.searchKey,
            isLoadMore: params.isLoadMore,
            fromTeam: true
          })
        )
      }

      state.teamClientCount = 0
      commit('setManagerViewMembers', [])

      return util.wrapLoopPromiseCalls(apis, params).then((res) => {
        const found = res.success.find((success) => success.request.ownerId)
        if (found) {
          state.teamClientCount = found.response.totalClients || 0
          let contactsObj = userUtil.formatContacts(
            found.response.clients,
            false,
            null,
            getters.managerViewMembers
          )
          const list = processUserData(contactsObj.contacts)
          commit('setManagerViewMembers', list)
          return found.response
        } else {
          return {}
        }
      })
    }
  },
  handleTabCreation({ commit, state, getters, dispatch }, { type, tab_type, view_seq }) {
    if (state.workspaceConfig.length <= 5 || state.actionConfig.length <= 5) {
      const completeConfig = JSON.parse(
        JSON.stringify(
          state.dashboardType === 'advancedWorkspaceReport'
            ? state.workspaceConfig
            : state.actionConfig
        )
      )

      let tab_title = ''
      const nextOrderNo = completeConfig.reduce(
        (max, { order_no }) => (order_no && order_no >= max ? order_no + 1 : max),
        1
      )
      const constWorkspaceConfig = JSON.parse(JSON.stringify(defaultWorkspaceConfig))
      const constActionConfig = JSON.parse(JSON.stringify(defaultActionConfig))
      const workspaceConfig = JSON.parse(JSON.stringify(state.workspaceConfig))
      const actionConfig = JSON.parse(JSON.stringify(state.actionConfig))
      let currentTabConfigJSON = null

      if (state.dashboardType === 'advancedWorkspaceReport') {
        currentTabConfigJSON = getters.getWorkspaceConfig.find((item) => item.view_seq === view_seq)
      } else {
        currentTabConfigJSON = getters.getActionConfig.find((item) => item.view_seq === view_seq)
      }

      if (state.dashboardType === 'advancedWorkspaceReport') {
        const completeData = [constWorkspaceConfig, ...workspaceConfig]
        let largestNumber = 0

        completeData.forEach(({ title }) => {
          const match = title.match(/^View (\d)$/)
          if (match) {
            const num = parseInt(match[1], 10)
            if (num > largestNumber) largestNumber = num
          }
        })
        const nextNumber = largestNumber + 1
        tab_title = `View ${nextNumber}`

        const workspaceBoardProperties = getters.getWorkspaceTagProperties

        let processedCompleteConfig = []
        if (type === 'duplicate_view') {
          currentTabConfigJSON.title = tab_title
          currentTabConfigJSON.state = 'DRAFT'
          currentTabConfigJSON.view_seq = FunctionUtil.uuid()
          currentTabConfigJSON.order_no = nextOrderNo
          syncFilters(currentTabConfigJSON)
          processedCompleteConfig = [...workspaceConfig, currentTabConfigJSON]
          state.activeTab = currentTabConfigJSON.view_seq
          commit('setWorkspaceConfig', processedCompleteConfig)
        } else if (type === 'create_new_view') {
          constWorkspaceConfig.title = tab_title
          constWorkspaceConfig.tab_type = tab_type
          constWorkspaceConfig.state = 'DRAFT'
          constWorkspaceConfig.view_seq = FunctionUtil.uuid()
          constWorkspaceConfig.order_no = nextOrderNo
          constWorkspaceConfig.column_config.push(...workspaceBoardProperties)
          syncFilters(constWorkspaceConfig)
          processedCompleteConfig = [...workspaceConfig, constWorkspaceConfig]
          state.activeTab = constWorkspaceConfig.view_seq
          commit('setWorkspaceConfig', processedCompleteConfig)
        }
      } else if (state.dashboardType === 'advancedActionReport') {
        const completeData = [constActionConfig, ...actionConfig]
        let largestNumber = 0

        completeData.forEach(({ title }) => {
          const match = title.match(/^View (\d)$/)
          if (match) {
            const num = parseInt(match[1], 10)
            if (num > largestNumber) largestNumber = num
          }
        })

        const nextNumber = largestNumber + 1
        tab_title = `View ${nextNumber}`

        let processedCompleteConfig = []
        if (type === 'duplicate_view') {
          currentTabConfigJSON.title = tab_title
          currentTabConfigJSON.state = 'DRAFT'
          currentTabConfigJSON.view_seq = FunctionUtil.uuid()
          currentTabConfigJSON.order_no = nextOrderNo
          syncFilters(currentTabConfigJSON)
          processedCompleteConfig = [...actionConfig, currentTabConfigJSON]
          state.activeTab = currentTabConfigJSON.view_seq
          commit('setActionConfig', processedCompleteConfig)
        } else if (type === 'create_new_view') {
          constActionConfig.title = tab_title
          constActionConfig.tab_type = tab_type
          constActionConfig.state = 'DRAFT'
          constActionConfig.view_seq = FunctionUtil.uuid()
          constActionConfig.order_no = nextOrderNo
          syncFilters(constActionConfig)
          processedCompleteConfig = [...actionConfig, constActionConfig]
          state.activeTab = constActionConfig.view_seq
          commit('setActionConfig', processedCompleteConfig)
        }
      }
    }
  },
  loadTeamMembers(
    { dispatch },
    { ownerId, searchKey = '', pageNumber = 1, hasTeamNext, hasMemberNext, isLoadMore }
  ) {
    const apis = []
    const params = []
    const piniaStore = useManageEngagementStore()
    if (ownerId || (ownerId && searchKey) || (ownerId && isLoadMore && hasTeamNext)) {
      params.push({ ownerId, searchKey, pageNumber })
      apis.push((params) =>
        piniaStore.readInternalUserClients(params.ownerId, {
          searchKey: params.searchKey,
          pageNumber: params.pageNumber
        })
      )
    }
    if (searchKey || (isLoadMore && hasMemberNext)) {
      params.push({ searchKey, isLoadMore })
      apis.push((params) =>
        dispatch('searchMembers', {
          searchKey: params.searchKey,
          isLoadMore: params.isLoadMore,
          fromTeam: true
        })
      )
    }
    return util.wrapLoopPromiseCalls(apis, params).then((res) => {
      const found = res.success.find((success) => success.request.ownerId)
      if (found) {
        return found.response
      } else {
        return {}
      }
    })
  }
}

function processDateRange(date_range) {
  const [from_date, to_date] = date_range.split('-').map((date) => Number(date.trim()))
  return { from_date, to_date }
}

const mergeUniqueBySequence = (arr1, arr2) => {
  return [...arr1, ...arr2].reduce((acc, curr) => {
    if (!curr.hasOwnProperty('sequence')) {
      acc.push(curr)
    } else {
      const existing = acc.find((item) => item.sequence === curr.sequence)
      if (!existing) {
        acc.push(curr)
      }
    }
    return acc
  }, [])
}

function processUserImage(assignees, rootGetters) {
  if (assignees && assignees.length) {
    assignees.forEach((assignee) => {
      if (assignee.assignee_user_id) {
        if (
          assignee?.assignee_type === 'USER_TYPE_NORMAL' ||
          assignee?.assignee_type === 'USER_TYPE_LOCAL'
        ) {
          if (assignee.group_member_seq && assignee.picture_seq) {
            assignee.image = `/group/${rootGetters['group/groupBasicInfo'].id}/member/${assignee.group_member_seq}/${assignee.picture_seq}`
          } else {
            assignee.id = assignee.assignee_user_id
            assignee.first_name = assignee.assignee_name
            assignee.last_name = ''
            assignee.image = UserFormatter.getGroupUserAvatar(assignee)
          }
        } else if (assignee?.assignee_type === 'GROUP_TYPE_TEAM') {
          assignee.image = rootGetters['user/accessibleTeams'].find(
            (res) => res?.id === assignee?.assignee_user_id
          )?.avatar
        } else if (assignee?.assignee_type === 'GROUP_TYPE_CLIENT_TEAM') {
          assignee.image = rootGetters['user/allClientTeams'].find(
            (res) => res?.id === assignee?.assignee_user_id
          )?.avatar
        } else if (assignee?.assignee_type === 'GROUP_TYPE_TEAM_FLEXIBLE') {
          assignee.image =
            rootGetters['user/allClientTeams'].find((res) => res?.id === assignee?.assignee_user_id)
              ?.avatar ||
            rootGetters['user/accessibleTeams'].find(
              (res) => res?.id === assignee?.assignee_user_id
            )?.avatar
        } else {
          // Worst Case Scenario
          assignee.id = assignee.assignee_user_id
          assignee.first_name = assignee.assignee_name
          assignee.last_name = ''
          assignee.image = UserFormatter.getGroupUserAvatar(assignee)
        }
      }
    })
  }
  return assignees
}

function processWorkspaceTimeframe(completed_at, due_date, current_user_timezone) {
  if (completed_at && due_date) {
    return `${workspaceDateRangeFormatting(completed_at)} - ${workspaceDateRangeFormatting(due_date)}`
  }
  if (completed_at) {
    return `${i18n.t('Since')} ${workspaceDateRangeFormatting(completed_at)}`
  }
  if (due_date) {
    return `${i18n.t('Due_On')} ${workspaceDateRangeFormatting(due_date)}`
  }
  return '-'
}

function processUserData(userList) {
  const seen = new Set();
  return userList
    .map((user) => ({
      ...user,
      title: user?.fullName || user?.displayName || user?.name || '-',
      sub_title: user?.email || user?.subTitle || user?.phoneNum || '-',
      icon: user?.avatar || defaultProfile,
      is_external:
        user?.isInternalUser !== undefined ? !user.isInternalUser :
        user?.isInternal !== undefined ? !user.isInternal :
        user?.isClient !== undefined ? !!user.isClient :
        false,
      value: user?.id || user?.userId
    }))
    .filter((user) => {
      if (seen.has(user.value)) return false;
      seen.add(user.value);
      return true;
    });
}

function getFilterValue(filterConfig, key) {
  if (key) {
    const { applied_filter_type, quick_params, advanced_params } = filterConfig

    if (applied_filter_type === 'quick') {
      return quick_params.find((item) => item.name === key && item.string_value)?.string_value || ''
    } else {
      return (
        advanced_params.line_items.find((item) => item.filter === key && item.value)?.value || ''
      )
    }
  }
}

function formatDateToReadable(utcMilliseconds) {
  if (utcMilliseconds) {
    const currentTime = Date.now()
    const timeDifference = currentTime - utcMilliseconds
    const seconds = Math.floor(timeDifference / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    const months = Math.floor(days / 30)
    let years = Math.floor(timeDifference / (1000 * 60 * 60 * 24 * 365))

    if (months < 12) {
      years = 0
    }

    const formattedTime = {
      years: years,
      months: months,
      days: days,
      hours: hours,
      minutes: minutes,
      seconds: seconds
    }

    let timeAgo = formattedTime.years > 0 ? formattedTime.years + ' ' + i18n.t('years_ago') : null
    timeAgo =
      timeAgo ||
      (formattedTime.months > 0 ? formattedTime.months + ' ' + i18n.t('months_ago') : null)
    timeAgo =
      timeAgo ||
      (formattedTime.days > 0
        ? formattedTime.days === 1
          ? i18n.t('one_day_ago')
          : formattedTime.days + ' ' + i18n.t('days_ago')
        : null)
    timeAgo =
      timeAgo ||
      (formattedTime.hours > 0
        ? formattedTime.hours === 1
          ? i18n.t('one_hour_ago')
          : formattedTime.hours + ' ' + i18n.t('hours_ago')
        : null)
    timeAgo =
      timeAgo ||
      (formattedTime.minutes > 0
        ? formattedTime.minutes === 1
          ? i18n.t('one_minute_ago')
          : formattedTime.minutes + ' ' + i18n.t('minutes_ago')
        : null)
    timeAgo = timeAgo || formattedTime.seconds + ' ' + i18n.t('seconds_ago')

    // Check if the month in between has 31 days
    const monthInBetween = new Date(currentTime).getMonth() - new Date(utcMilliseconds).getMonth()
    if (
      monthInBetween === 1 ||
      monthInBetween === 3 ||
      monthInBetween === 5 ||
      monthInBetween === 7 ||
      monthInBetween === 8 ||
      monthInBetween === 10 ||
      monthInBetween === 12
    ) {
      if (formattedTime.days === 31) {
        timeAgo = i18n.t('thirty_one_days')
      }
    }

    return timeAgo
  } else {
    return '-'
  }
}

function formatDuration(item) {
  const duration_in_seconds = item.duration_in_seconds
  if (!duration_in_seconds) {
    return '-'
  }
  const milliseconds = duration_in_seconds * 1000
  const d = moment.duration(milliseconds)

  const years = d.years()
  const months = d.months()
  const days = d.days()
  const hours = d.hours()
  const minutes = d.minutes()
  const seconds = d.seconds()
  let duration = '-'
  if (years > 0) {
    duration = years === 1 ? '1 ' + i18n.t('year') : years + ' ' + i18n.t('years')
  } else if (months > 0) {
    duration = months === 1 ? '1 ' + i18n.t('month') : months + ' ' + i18n.t('months')
  } else if (days > 0) {
    duration = days === 1 ? '1 ' + i18n.t('day') : days + ' ' + i18n.t('days')
  } else if (hours > 0) {
    duration = hours === 1 ? '1 ' + i18n.t('hour') : hours + ' ' + i18n.t('hours')
  } else if (minutes > 0) {
    duration = minutes === 1 ? '1 ' + i18n.t('minute') : minutes + ' ' + i18n.t('minutes')
  } else if (seconds > 0) {
    duration = seconds === 1 ? '1 ' + i18n.t('second') : seconds + ' ' + i18n.t('seconds')
  }
  return duration.toLowerCase()
}

function processStatus(status) {
  const v_status = status?.toUpperCase() || ''
  let translated_status = ''
  switch (v_status) {
    case 'OPEN':
      translated_status = i18n.t('In_Progress')
      break
    case 'OVERDUE':
      translated_status = i18n.t('Overdue')
      break
    case 'COMPLETED':
      translated_status = i18n.t('Completed')
      break
    case 'CANCELLED':
      translated_status = i18n.t('Canceled')
      break
    case 'DUE_TODAY':
      translated_status = i18n.t('Due_Today_upper')
      break
    case 'DUE_TOMORROW':
      translated_status = i18n.t('Due_Tomorrow_upper')
      break
    case 'DUE_IN_7_DAYS':
      translated_status = i18n.t('due_in_seven_days')
      break
    default:
      translated_status = '-'
  }
  if (translated_status) {
    return translated_status.toLowerCase().replace(/\b\w/g, (s) => s.toUpperCase())
  } else {
    return translated_status
  }
}

function processSteps(steps) {
  if(steps)
  {
  return `${steps} ${i18n.t(steps > 1 ? 'Actions' : 'action')}`
  }
  else
  {
    return '-'
  }
}

function processWaitingToTextAction(item) {
  if (item.obj_type_id) {
    const stepTypeMap = {
      TODO: 'Waiting_',
      TRANSACTION_TYPE_TODO: 'Waiting_',
      ESIGN: 'Waiting_to_Sign',
      'E-SIGN': 'Waiting_to_Sign',
      TRANSACTION_TYPE_DOCUSIGN: 'Waiting_to_Sign',
      TRANSACTION_TYPE_APPROVAL: 'Waiting_',
      TRANSACTION_TYPE_FILE_REQUEST: 'Waiting_to_review',
      TRANSACTION_TYPE_ACKNOWLEDGE: 'Waiting_',
      TRANSACTION_TYPE_FORM_REQUEST: 'Waiting_',
      TRANSACTION_TYPE_PDF_FORM: 'Waiting_'
    }

    const current_step_type = item.obj_type_id.toUpperCase()
    const default_value = i18n.t('Waiting_to_review')
    const return_value = stepTypeMap[current_step_type]
      ? i18n.t(stepTypeMap[current_step_type])
      : default_value

    return return_value
  } else {
    return ''
  }
}

function processTypeDetails(item, type = 'TRANSACTION', sub_type = '') {
  type = type ? type.toUpperCase() : ''
  sub_type = sub_type ? sub_type.toUpperCase() : ''

  const details = {
    title: '',
    icon: TRANSACTION
  }

  if (type === 'TRANSACTION_TYPE_INTEGRATION') {
    if (sub_type && sub_type === 'JUMIO') {
      details.title = i18n.t('Jumio')
      details.icon = Jumio
    } else if (sub_type && sub_type === 'INTEGRATION') {
      details.title = item?.custom_data?.integration?.app_name || i18n.t('Integration')
      details.icon = item?.custom_data?.integration?.app_id
        ? `/integration/framework/v1/apps/${item?.custom_data?.integration?.app_id}/logo`
        : DEFAULTINTEGRATIONAPP
    } else {
      details.title = i18n.t('Integration')
      details.icon = DEFAULTINTEGRATIONAPP
    }
  } else {
    if (type === 'TRANSACTION_TYPE_APPROVAL') {
      details.title = i18n.t('Approval')
      details.icon = APPROVAL
    } else if (type === 'TRANSACTION_TYPE_ACKNOWLEDGE') {
      details.title = i18n.t('Acknowledgement')
      details.icon = ACKNOWLEDGEMENT
    } else if (type === 'TRANSACTION_TYPE_FILE_REQUEST') {
      details.title = i18n.t('File_Request')
      details.icon = FILEREQUEST
    } else if (type === 'TRANSACTION_TYPE_TIME_BOOKING' || type === 'TIME BOOKING') {
      details.title = i18n.t('meeting_request')
      details.icon = MEETREQUEST
    } else if (type === 'TRANSACTION_TYPE_MEET_REQUEST' || type === 'MEET REQUEST') {
      details.title = i18n.t('meeting_request')
      details.icon = MEETREQUEST
    } else if (type === 'TRANSACTION_TYPE_DOCUSIGN' || type === 'DOCUSIGN') {
      details.title = i18n.t('DOCU_SIGN')
      details.icon = DOCUSIGNWRAPPER
    } else if (type === 'TRANSACTION_TYPE_PDF_FORM') {
      details.title = i18n.t('PdfForm_capitalized')
      details.icon = PDFFORM
    } else if (type === 'TRANSACTION_TYPE_FORM_REQUEST' || type === 'FORM REQUEST') {
      details.title = i18n.t('form_request')
      details.icon = FORMREQUEST
    } else if (
      type === 'TRANSACTION_TYPE_LAUNCH_WEB_APP' ||
      type === 'TRANSACTION_TYPE_LAUNCH_WEB_APP'
    ) {
      details.title = i18n.t('Launch_Web_App')
      details.icon = LAUNCHWEBAPP
    } else if (type === 'TRANSACTION_TYPE_TODO' || type === 'TODO') {
      details.title = i18n.t('To_do')
      details.icon = TODO
    } else if (type === 'ESIGN' || type === 'E-SIGN') {
      details.title = i18n.t('E_signature_short')
      details.icon = ESIGN
    } else if (type === 'TRANSACTION_TYPE_DECISION' || type === 'DECISION') {
      details.title = i18n.t('Decision')
      details.icon = DECISION
    } else if (type === 'TRANSACTION_TYPE_AWAIT' || type === 'AWAIT') {
      details.title = i18n.t('Wait')
      details.icon = WAIT
    } else {
      details.title = '-'
      details.icon = TRANSACTION
    }
  }

  return details
}

function processActions(item) {
  const open_actions = item.open_actions.filter((obj) => Object.keys(obj).length > 0)

  if (open_actions.length) {
    open_actions.forEach((action) => {
      action.icon = processTypeDetails(action, action.action_type, action.action_sub_type).icon
    })
    return open_actions
  } else {
    return []
  }
}

function syncFilters(config) {
  const filterConfig = config?.filter_config
  if (!filterConfig) return config

  const { applied_filter_type, quick_params = [], advanced_params = {} } = filterConfig

  if (applied_filter_type === 'advanced') {
    const quickParams = []
    const operator = advanced_params.operator
    const items = advanced_params.line_items || []

    items.forEach((item) => {
      if (item.condition === 'is' && item.value) {
        if (operator === 'OR') {
          quickParams.push({ name: item.filter, string_value: item.value })
        } else if (operator === 'AND') {
          if (!quickParams.some((q) => q.name === item.filter)) {
            quickParams.push({ name: item.filter, string_value: item.value })
          }
        }
      }
    })
    filterConfig.quick_params = quickParams
  } else if (applied_filter_type === 'quick') {
    const filtered = quick_params.filter((p) => !['date_range', 'team_id'].includes(p.name))
    const line_items = filtered
      .filter((p) => p.string_value)
      .map((p) => ({
        condition: 'is',
        filter: p.name,
        type: 'dropdown',
        tag_type: '',
        value: p.string_value
      }))

    const order = ['template_id', 'template_milestone_seq', 'template_step_seq']
    const sorted_items = (line_items.length
      ? line_items
      : [
        {
          condition: '',
          filter: '',
          type: '',
          tag_type: '',
          value: ''
        }
      ]
    ).sort((a, b) => order.indexOf(a.filter) - order.indexOf(b.filter))

    filterConfig.advanced_params = {
      operator: 'AND',
      line_items: sorted_items
    }
  }
}

function upgradeDateRangeField(date_range) {
  const now = moment()
    .endOf('day')
    .valueOf()
  let from = null

  if (!date_range || date_range === 'last_12_months') {
    from = moment()
      .subtract(12, 'months')
      .add(1, 'day')
      .startOf('day')
      .valueOf()
  } else if (date_range === '13_24_months') {
    from = moment()
      .subtract(24, 'months')
      .add(1, 'day')
      .startOf('day')
      .valueOf()
  } else if (date_range === '25_36_months') {
    from = moment()
      .subtract(36, 'months')
      .add(1, 'day')
      .startOf('day')
      .valueOf()
  }

  if (from !== null) {
    date_range = `${from}-${now}`
  }

  return date_range
}

function processConfigs(state, config) {
  syncFilters(config)
  config.date_range = upgradeDateRangeField(config.date_range)
  if (state.dashboardType === 'advancedWorkspaceReport') {
    if (config.kanban_column == 'status') {
      config.kanban_column = 'workspace_status'
    }

    config.column_config = config.column_config.filter((item) => item.name !== 'created_by')
    config.column_config.forEach((column) => {
      if (column.name === 'name') {
        column.label = 'current_assignees'
      }

      if (column.name === 'status') {
        column.name = 'workspace_status'
        column.label = 'workspace_status'
      }
    })

    const columnsToAdd2 = [
      {
        name: 'owned_by',
        label: 'owned_by',
        width: '180',
        visible: true,
        slot: true,
        disabled: false
      },
      {
        name: 'workspace_due_date',
        label: 'Due_date',
        width: '200',
        visible: true,
        slot: true,
        disabled: false
      },
      {
        name: 'completed_at',
        label: 'completed_at_capitalized',
        width: '200',
        visible: true,
        slot: true,
        disabled: false
      }
    ]

    const existingNames2 = config.column_config.map((col) => col.name)

    const index2 = config.column_config.findIndex((col) => col.name === 'flow_tmpl_name')

    if (index2 !== -1) {
      columnsToAdd2.forEach((col, i) => {
        if (!existingNames2.includes(col.name)) {
          config.column_config.splice(index2 + i, 0, col)
        }
      })
    }

    config.filter_config.quick_params.forEach((quick_filter) => {
      if (quick_filter.name === 'status') {
        quick_filter.name = 'workspace_status'
      }
      if (config.view_type === 'MANAGER' && quick_filter.name === 'team_id') {
        config.team_id = quick_filter.string_value
      }
    })

    if (config?.filter_config?.advanced_params.line_items.length) {
      config?.filter_config?.advanced_params.line_items.forEach((advanced_filter) => {
        if (advanced_filter.filter === 'status') {
          advanced_filter.filter = 'workspace_status'
        }

        if (config.view_type === 'MANAGER' && advanced_filter.filter === 'team_id') {
          config.team_id = advanced_filter.value
        }
      })
    }

    config.filter_config.quick_params = config.filter_config.quick_params.filter(
      (item) => item.name !== 'assigned_to_me' || item.name !== 'action_type' || item.name !== 'team_id'
    )

    config.filter_config.advanced_params.line_items = config?.filter_config?.advanced_params.line_items.filter(
      (item) => item.filter !== 'team_id'
    )
  } else if (state.dashboardType === 'advancedActionReport') {
    if (config.kanban_column == 'status') {
      config.kanban_column = 'action_status'
    }

    config.column_config.forEach((column) => {
      if (column.name === 'status') {
        column.name = 'action_status'
        column.label = 'action_status'
      }
    })

    const columnsToAdd = [
      {
        name: 'completed_at',
        label: 'completed_at_capitalized',
        width: '200',
        visible: true,
        slot: true,
        disabled: false
      }
    ]

    const existingNames = config.column_config.map((col) => col.name)

    if (!existingNames.includes('completed_at')) {
      const index = config.column_config.findIndex((col) => col.name === 'duration_in_seconds')
      if (index !== -1) {
        config.column_config.splice(index + 1, 0, ...columnsToAdd)
      }
    }

    config.filter_config.quick_params.forEach((quick_filter) => {
      if (quick_filter.name === 'status') {
        quick_filter.name = 'action_status'
      }

      if (config.view_type === 'MANAGER' && quick_filter.name === 'team_id') {
        config.team_id = quick_filter.string_value
      }
    })

    if (config?.filter_config?.advanced_params.line_items.length) {
      config?.filter_config?.advanced_params.line_items.forEach((advanced_filter) => {
        if (advanced_filter.filter === 'status') {
          advanced_filter.filter = 'action_status'
        }

        if (config.view_type === 'MANAGER' && advanced_filter.filter === 'team_id') {
          config.team_id = advanced_filter.value
        }
      })
    }

    config.filter_config.quick_params = config.filter_config.quick_params.filter(
      (item) => item.name !== 'team_id'
    )

    config.filter_config.advanced_params.line_items = config?.filter_config?.advanced_params.line_items.filter(
      (item) => item.filter !== 'team_id' && item.filter !== 'owned_by_me'
    )

    if (!config.filter_config.quick_params.some((p) => p.name === 'workspace_status')) {
      const idx = config.filter_config.quick_params.findIndex((p) => p.name === 'action_status')
      if (idx !== -1) {
        config.filter_config.quick_params.splice(idx + 1, 0, {
          name: 'workspace_status',
          string_value: 'open,due_today,overdue'
        })
      }
    }

    if (!config.filter_config.quick_params.some((p) => p.name === 'assigned_to')) {
      const idx = config.filter_config.quick_params.findIndex(
        (p) => p.name === 'workspace_owner_id'
      )
      if (idx !== -1) {
        config.filter_config.quick_params.splice(idx, 0, {
          name: 'assigned_to',
          string_value: ''
        })
      }
    }
  }
}

function getDefaultTeamId(rootGetters) {
  const allTeams = rootGetters['user/accessibleTeams']
  if (!Array.isArray(allTeams)) return

  const validTeamMembers = allTeams
    .filter(team => team?.isMember && team?.isManager)
    .map(team => ({
      ...team,
      avatar: team.avatar || '',
      title: team.name || '',
      value: team.id || ''
    }))

  defaultTeamIdMEPX = validTeamMembers[0]?.value || ''
}

function workspaceDateRangeFormatting(datetime) {
  const utc_milliseconds = Number(datetime);
  if (!isNaN(utc_milliseconds) && utc_milliseconds > 0) {
    const date = new Date(utc_milliseconds);
    const now = new Date();
    const options = {
      month: "short",
      day: "numeric",
      ...(date.getFullYear() !== now.getFullYear() && { year: "numeric" })
    };
    return date.toLocaleDateString("en-US", options);
  }
  return "-";
}