import {ThreadController} from '@controller/thread/src/threadController';
import { TodoController } from '@controller/todos/src/todoController'
import { useBinderWorkSpaceDetailStore } from '@views/stores/binderWorkspaceDetail'
import uniqBy from 'lodash/uniqBy'


const threadCtrls = {}

function setCurrentActivities (context, currentActivities = []) {
    const {commit, state} = context
    commit('setcurrentActivities', uniqBy([...state.currentActivities, ...currentActivities], 'sequence'))
}

export default {

    /**
     * generate thread controller instance and read/subscribe selected thread
     * @param {*} ctrlKey - key for match threadCtrl
     * @param {*} param - CThreadParam
     * @param {*} callback - callback for read_thread and subscribe_thread
     */
    subscribeThread (_, {ctrlKey, param, callback}) {

        const baseObject = param.baseObject || {}
        if ((baseObject.type === 'MEET' || baseObject.type === 'SCHEDULE_FLOW') && !baseObject.sequence && param.session) {
            // handle Calendar meetObject, no need to init ThreadController
            const currentThread = {baseObject: {session: param.session}}
            callback(currentThread, ctrlKey)

        } else {
            const threadCtrl = threadCtrls[ctrlKey]
            if (!threadCtrl) {
                ThreadController.getInstance(param).then(threadCtrl => {
                    threadCtrls[ctrlKey] = threadCtrl
                    threadCtrl.getThread().then(thread => {
                        const {type, start_time, end_time} = param.baseObject
                        if (thread.baseObject?.session && type === 'MEET' && start_time && end_time) {
                            // for open recurring meeting detail in freemium binder, bring meet time from UI
                            thread.baseObject.session = {...thread.baseObject.session, start_time, end_time}
                        }
                        callback(thread, ctrlKey)
                        setCurrentActivities(_, thread.activities || [])
                        threadCtrl.subscribeThread(updatedThread => {
                            if (updatedThread.activities) {
                                setCurrentActivities(_, updatedThread.activities)
                            }
                            callback(updatedThread, ctrlKey)
                        })
                    }).catch(err=>{
                        const loadBindError = {
                            isError: true
                        }
                        callback(loadBindError, ctrlKey)
                    })
                })
            }
        }
    },

    clearThread (_, ctrlKey) {
        _.commit('setcurrentActivities', [])
        const threadCtrl = threadCtrls[ctrlKey]
        if (threadCtrls[ctrlKey]) {
            threadCtrl.destroy()
            delete threadCtrls[ctrlKey]
        }
    },

    updateBaseComment ({rootState}, {ctrlKey, payload}) {

        if(payload.url_preview) {
            const preLoad = rootState.chat.preLoadLink
            const linkPreview = JSON.parse(payload.url_preview)
            if(preLoad.data && linkPreview.url === preLoad.url ){
                payload.url_preview = JSON.stringify(preLoad.data)
            }else {
                threadCtrls[ctrlKey].updateCommentLinkPreview(payload.sequence, preLoad.url)
            }
        }
        return threadCtrls[ctrlKey].updateBaseComment(payload)
    },
    deleteBaseComment (_, {ctrlKey, payload}) {
        return threadCtrls[ctrlKey].deleteBaseComment(payload)
    },

    deleteBaseFile (_, {ctrlKey, spath}) {
        return threadCtrls[ctrlKey].deleteBaseFile(spath)
    },

    createThreadComment ({dispatch}, {ctrlKey, payload}) {
        return threadCtrls[ctrlKey].createThreadComment(payload.relatedObject)
    },

    updateThreadComment (_, {ctrlKey, payload, baseObject}) {
        return threadCtrls[ctrlKey].updateThreadComment(payload, baseObject)
    },

    deleteThreadComment (_, {ctrlKey, payload, baseObject}) {
        return threadCtrls[ctrlKey].deleteThreadComment(payload, baseObject)
    },

    updateActionLogForStep ({}, {ctrlKey, payload}) {
        return threadCtrls[ctrlKey].updateActionLogForStep(payload.transactionSequence, payload.stepSequence, payload.clickButtonId, payload.status, payload.card, payload.customAction,payload.customResult, payload.suppressFeed)
    },
    saveDraftForm ({}, {ctrlKey,transactionSequence,  stepSequence, data}) {
        return threadCtrls[ctrlKey].saveDraftForm(transactionSequence, stepSequence, data)
    },

    deleteTodo ({commit}, { sequence, binderId }) {
      const binderWorkSpaceDetailStore = useBinderWorkSpaceDetailStore()
      binderWorkSpaceDetailStore.setActionDeletedByInfo({sequence, isMySelf: true})
      return TodoController.getInstance(binderId).deleteTodo(sequence)
    },
    reopenTransactionStep (_, {ctrlKey, transactionSeq, stepSeq}) {
        return threadCtrls[ctrlKey].reopenTransactionStep(transactionSeq, stepSeq)
    },
    makeUploadToTransReplyFileUrl (_, {ctrlKey, transactionSeq, fileName}) {
        return threadCtrls[ctrlKey].makeUploadToTransReplyFileUrl(transactionSeq, fileName)
    },
    makeUploadToSignReplyFileUrl (_, {ctrlKey, signatureSeq, fileName}) {
        return threadCtrls[ctrlKey].makeUploadToSignReplyFileUrl(signatureSeq, fileName)
    },
    addSignatureAttachment (_, {ctrlKey, signatureSeq, file, isReply, suppressFeed}) {
        return threadCtrls[ctrlKey].addSignatureAttachment(signatureSeq, file,  isReply, suppressFeed)
    },
    createTransactionAttachmentReply (_, {ctrlKey, transactionSeq, file, suppressFeed}) {
        return threadCtrls[ctrlKey].createTransactionAttachmentReply(transactionSeq, file, suppressFeed)
    },
    removeTransactionAttachment (_, {ctrlKey, transactionSeq, referenceSeq}) {
        return threadCtrls[ctrlKey].removeTransactionAttachment(transactionSeq, referenceSeq)
    },
    removeSignatureAttachment (_, {ctrlKey, signatureSeq, referenceSeq}) {
        return threadCtrls[ctrlKey].removeSignatureAttachment(signatureSeq, referenceSeq)
    },
    updateTransactionViewTime (_, {ctrlKey, transactionSeq,stepSeq}){
        return threadCtrls[ctrlKey].updateTransactionViewTime(transactionSeq,stepSeq)
    },
    updateIntegrationlogForStep (_, {ctrlKey, transactionSeq,step,isManually}){
        let stepInfo = {}
        if(isManually){
            stepInfo = {
                sequence: step.sequence,
                action_logs: [{
                  custom_action: JSON.stringify({type:'MANUAL_COMPLETE_ACTION'})
                }],
                status: 'STEP_STATUS_COMPLETED'
              }
        }else{
            stepInfo = {
                sequence:  step.sequence,
                action_logs: [{
                  click_btn_id: step.actionId,
                }]
              }
        }
        return threadCtrls[ctrlKey].updateIntegrationlogForStep(transactionSeq,stepInfo)
    },
    transferActionToMySelf({ state }, payload){

    }
}
