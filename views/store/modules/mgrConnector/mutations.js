import {ObjectUtils} from '@commonUtils';

export default {

    setSFDCReports(state, payload) {
        state.sfdcReports = payload
    },

    setNonMatchUsers(state, payload) {
        if(!state.nonMatchUsers || state.nonMatchUsers.length === 0) {
            state.nonMatchUsers = payload
            state.displayNonMatchUsers = ObjectUtils.cloneDeep(payload)
            state.displaySeq = payload.length;
        }
        else {
            state.nonMatchUsers.push(...payload)
        }
    },

    loadMoreUsers(state, payload) {
        if(state.displaySeq  < state.nonMatchUsers.length ) {
            let end;
            if(state.nonMatchUsers.length - state.displaySeq - 1 >= 200 ){
                end = state.displaySeq + 200
            } else
                end = state.nonMatchUsers.length + 1

            state.displayNonMatchUsers.push(...state.nonMatchUsers.slice(state.displaySeq, end))
            state.displaySeq = end
        }
    },

    resetNonMatchUsers(state) {
        state.nonMatchUsers = []
        state.displaySeq = 0
        state.displayNonMatchUsers = []
    }
}