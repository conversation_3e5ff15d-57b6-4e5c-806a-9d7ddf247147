import { QuickLinksController } from '@controller/quickLinks/src/quickLinksController'
import { processClientQuickLinksRoles } from './utils'
import { ObjectUtils } from "@commonUtils"

let quickLinksControllerInstance = null

export default {

  initQuickLinks({ commit, rootGetters }, ) {
    commit('resetStates')

    quickLinksControllerInstance = QuickLinksController.getInstance()

    let quickLinks = quickLinksControllerInstance.getQuickLinks()
    quickLinks.internalList.forEach(link => {
      delete link.__selected__
    })
    quickLinks.clientList.forEach(link => {
      delete link.__selected__
    })
    commit('setInternalList', quickLinks.internalList)
    commit(
      'setClientList',
      processClientQuickLinksRoles(
        quickLinks.clientList,
        rootGetters['group/groupDistributionList']
      )
    )

    quickLinksControllerInstance.subscribeQuickLinks((links) => {
      commit('setInternalList', links.internalList)
      commit(
        'setClientList',
        processClientQuickLinksRoles(
          links.clientList, rootGetters['group/groupDistributionList']
        )
      )
    })
  },

  destroyQuickLinks({ commit, dispatch }) {
    commit('resetStates')
    if (quickLinksControllerInstance) {
      quickLinksControllerInstance = null
    }
  },

  createOrUpdateTag({ state }, payLoad) {
    let existingLinks = ObjectUtils.cloneDeep(
      state.quickLinkUserType === 'Quick_Links_Internal'
        ? state.internalList
        : state.clientList
    )
    const newOrUpdatedQuickLinks = [ ...payLoad.quick_links ]
    const newOrUpdatedQuickLinksOrderNumbers = newOrUpdatedQuickLinks.map(link => link.order_number)

    if (existingLinks) {
      switch (payLoad.action) {
        case 'ADD':
          existingLinks = existingLinks.concat(newOrUpdatedQuickLinks)
          break
        case 'UPDATE':
          existingLinks = existingLinks.map(link =>
            link.order_number === newOrUpdatedQuickLinks[0].order_number
              ? newOrUpdatedQuickLinks[0] : link
          )
          break
        case 'DELETE':
          existingLinks = existingLinks.filter(link => {
            return newOrUpdatedQuickLinks.indexOf(link.order_number) === -1
          })
          break
        case 'UPDATE_DISPLAY_ORDER':
          existingLinks = newOrUpdatedQuickLinks
          break
      }
    }

    const fullUpdatedLinks = (existingLinks || newOrUpdatedQuickLinks).map(link => {
      if (
        payLoad.action === 'UPDATE_DISPLAY_ORDER'
        || !newOrUpdatedQuickLinksOrderNumbers.includes(link.order_number)
      ) {
        link.roles = link.rawRoles
      } else {
        if (link.roles) {
          link.roles = link.roles.map(role => role.sequence)
        }
      }
      delete link.__selected__
      return link
    })
    return quickLinksControllerInstance.createOrUpdateTag(state.quickLinkUserType, fullUpdatedLinks)
  }
}
