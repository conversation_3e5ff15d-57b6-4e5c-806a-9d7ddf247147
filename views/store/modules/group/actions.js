import {GroupController} from '@controller/group/src/groupController';
import {ObjectUtils} from '@commonUtils';
import userUtil from '@views/common/utils/user'
import { parsePhoneNumberFromString as parseMobile } from '@vendor/libphonenumber-js/bundle/libphonenumber-mobile'

let groupController;

/*
function mergeTwoSortedArray (arrayA, arrayB) {
	let j = 0
	if (!arrayA.length && arrayB.length) {
		return arrayB
	} else if (arrayA.length && !arrayB.length) {
		return arrayA
	}
	for (let i = 0; i < arrayB.length; i ++) {
		let currentB = arrayB[i]
		if (j >= arrayA.length) {
			break
		}
		for (;j < arrayA.length;j++) {
			let currentA = arrayA[j]
			let currentANext = arrayA[j + 1]
			if (currentB.displayName.toLowerCase() > currentA.displayName.toLowerCase()) {
				if (!currentANext || (currentANext && currentB.displayName.toLowerCase() < currentANext.displayName.toLowerCase)) {
					arrayA.splice(j, 0, currentB)
					j++
					break
				}
			}
		}
	}
	return arrayA
}
*/

export default {
	initGroupSubscriber ({commit, dispatch}) {
		groupController = GroupController.getInstance();
		groupController.subscribeGroupTeams(({ updatedTeams, deletedTeams }) => {
		  commit('setAllTeams', {
			updatedTeams,
			deletedTeams
		  })
		  dispatch('checkTeamMemberChange')
		})
		groupController.subscribeGroupBasicInfo((group) => {
			commit('setBasicInfo', group);
			if (group.integrations) {
				commit('setGroupIntegrations', group.integrations);
			}
			commit('setGroupCaps', group.group_caps);
			if (group.board_properties) {
				commit('setProperties', group.board_properties)
			}
			commit('setGroupRoles', groupController.groupRoles);

			if(group.group_caps.enable_service_request)
				dispatch('user/loadSRAgentAndBoard', null, { root: true })
		});
		commit('setGroupCaps', groupController.groupCaps);
		commit('setGroupTags', groupController.tags);
		commit('setContextPath', GroupController.getContextPath())
		//commit('setCaps', groupController)
	},
	getGroupMember ({}, userIdentity) {
		return groupController.getGroupMember(userIdentity);
	},

	readGroupMember ({}, userIdentity) {
		return groupController.readGroupMember(userIdentity);
	},

	getUserBasicInfoByQRToken ({commit}, token) {
		return GroupController.getUserBasicInfoByQRToken(token);
	},
	async getGroupInfo ({commit, dispatch}) {
		try {
			await dispatch('identity/getSSOLoginOptions', null, {root: true});
		} catch (e) {

		}
		return GroupController.getOrgInfo(sessionStorage.getItem('mx:contextPath')).then(group => {
			commit('setBasicInfo', group);
			commit('setGroupCaps', group.group_caps);
			if (group.board_properties) {
				commit('setProperties', group.board_properties)
			}
			commit('setGroupIntegrations', group.integrations);
			commit('setContextPath', GroupController.getContextPath())
			dispatch('privileges/initGuestPrivileges', null, { root: true })
			return group;
		});
	},
	getClientQuickLinksForWeb ({commit}, cb) {
		return groupController.getQuickLinks(true, true, (links)=>{
			commit('user/setQuicklinks', links, {root: true});
			cb && cb(links)
		});
	},
	getInternalQuickLinksForWeb ({commit}, cb) {
		return groupController.getQuickLinks(false, true, (links)=>{
			commit('user/setQuicklinks', links, {root: true});
			cb && cb(links)
		});
	},
	searchAuditableUsers ({commit, state}, payLoad) {
		return new Promise((resolve, reject) => {
			if (!payLoad.isScroll) {
				commit('setDeleterUsers', null)
			}
			groupController.searchAuditableUsers(payLoad.searchKey, payLoad.startSequence, payLoad.pageSize, payLoad.isForAudit).then(members => {
				let {deletedUsers} = state
				let cachedUsers = deletedUsers.cachedUsers || []
				if (!deletedUsers.loadedAll) {
					groupController.searchAuditableUsers(payLoad.searchKey, deletedUsers.startSequence, payLoad.pageSize, payLoad.isForAudit, true).then(deleted=>{
						if (deleted && deleted.length) {
							commit('setDeleterUsers', {
								loadedAll: deleted.length < payLoad.pageSize,
								startSequence: deleted[deleted.length - 1].sequence,
								cachedUsers: [...cachedUsers, ...deleted]
							})
						}
						resolve(members, state.deletedUsers.cachedUsers);
					})
				} else {
					resolve(members, cachedUsers)
				}
			}).catch(e => {
				reject(e);
			});
		});
	},
	destroy ({state}) {
		if (groupController) {
			groupController.destroy();
			groupController = null;
		}
		// integration data shouldn't be fetched if user session out
		state.integrations = []
		state.allTeams = []
		state.teamMembers = {}
		state.clientTeamMembers = {}
	},
	updateAllPlatformBranding ({commit}, tags) {
		return groupController.updateAllPlatformBranding(tags);
	},
	updateOnlineBillingBranding({ }, { tags, logos }) {
		return groupController.updateOnlineBillingBranding(tags, logos)
	},
  updateGroupSettings({ rootGetters }, newGroupSettings) {
    let groupSettings = rootGetters['group/groupBasicInfo'].group_settings || {}
    groupSettings = Object.assign(groupSettings, newGroupSettings)
    return groupController.updateGroup({ group_settings: groupSettings })
  },
  updateGroup({ }, newGroup) {
    return groupController.updateGroup(newGroup)
  },
	getUserType({ commit, state }) {
		return new Promise((resolve, reject) => {
			let domain = location.host;
			let integration = (state.integrations || []).filter((integration) => {
				if (integration.is_deleted) {
					return false;
				}
				if (integration.type !== 'GROUP_INTEGRATION_LOCAL') {
					return false;
				}
				// for local dev env
				if (domain.startsWith('localhost') || domain.startsWith('0.0.0.0') || domain.startsWith('127.0.0.1')) {
					return !!integration.domain
				}
				return domain === integration.domain;

			}).sort((a, b) => {
				if (a.sequence > b.sequence) {
					return -1;
				} else {
					return 1;
				}
			}).shift();
			if (integration) {
				let id = ObjectUtils.getByPath(integration, 'webapp.id');
				if (id) {
					GroupController.readWebApp(id).then((res) => {
						commit('setIsM0App', !!res.is_universal)
						resolve(res.is_universal);
					}).catch(reject);
					return;
				}
			}
			resolve(false);
		});
	},
	updateMemberEmailPhoneNum ({}, {id: id, email: email, phoneNumber: phoneNumber}) {
		if (phoneNumber) {
			phoneNumber = parseMobile(phoneNumber).format('E.164')
		}
		return groupController.updateMemberEmailPhoneNum(id, email, phoneNumber);
	},
	inviteMember ({}, user) {
		return groupController.inviteMember(user);
	},

	readManagementClients ({}, {fromTime: fromTime, toTime: toTime, clientFilter: clientFilter, size: size, start: start}) {
		return groupController.readManagementClients(fromTime, toTime, clientFilter, start, size);
	},

	readManagementInternalUsers ({}, {fromTime: fromTime, toTime: toTime, size: size, start: start}) {
		return groupController.readManagementInternalUsers(fromTime, toTime, start, size);
	},

	readManagementClientDetail ({}, {id: id, email: email, phoneNumber: phoneNumber, fromTime: fromTime, toTime: toTime}) {
		return groupController.readManagementClientDetail({
			email: email,
			id: id,
			phoneNumber: phoneNumber
		}, fromTime, toTime);
	},

	readManagementInternalUserDetail ({}, {id: id, email: email, phoneNumber: phoneNumber, fromTime: fromTime, toTime: toTime}) {
		return groupController.readManagementInternalUserDetail({
			email: email,
			id: id,
			phoneNumber: phoneNumber
		}, fromTime, toTime);
	},

	readManagementTeamDetail ({}, {teamId: teamId, fromTime: fromTime, toTime: toTime}) {
		return groupController.readManagementTeamDetail(teamId, fromTime, toTime);
	},

	readManagementShareUserBoards ({}, {
		clientId: clientId,
		internalId: internalId,
		fromTime: fromTime,
		toTime: toTime
	}) {
		return groupController.readManagementShareUserBoards({id: clientId}, {id: internalId}, fromTime, toTime);
	},

	filterActiveGroupMembers ({}, userIds) {
		return groupController.filterActiveGroupMembers(userIds);
	},
	queryGroupMembers ({}, users) {
		return groupController.queryGroupMembers(users);
	},
    createOrUpdateInvitationToken () {
	  return groupController.createOrUpdateInvitationToken()
    },
	readOfficeHour ({ rootState }) {
		if (rootState.user.viewToken) //GroupController is not initialized yet in anonymous ACD use case
			groupController = GroupController.getInstance()
		return groupController.readOfficeHour()
	},
	checkSmsServiceStatus ({ state }) {
	  return groupController.checkSmsServiceStatus()
	    .then(status => {
			state.hasSmsService = status
			return status
		})
		.catch(err => {
			throw err
		})
	},
	readTeamMembersByPagination ({ commit }, { teamId, filter }) {
		return groupController.readTeamMembersByPagination(teamId, filter)
		.then(members => {
			// has filter for client team
			commit('syncTeamMemberInfo', {
				id: teamId,
				members: members,
				isClientTeam: !!filter  //FixMe: This is a temp solution, not got detail info about why client team members can not be syncTo state
			})
			return members
		})
	},
	getTeamManagers (_, teamId) {
		return groupController.getTeamManagers(teamId)
	},
	getGroupMembers ({ rootState, dispatch, rootGetters }, payload) {
		return groupController.loadGroupMembers(payload.startSeq, {}).then((response) => {
			let result = userUtil.formatContacts(response.members, !rootGetters['privileges/disableUserPresence'], rootState.contacts.presences, payload.existedContacts)
			if (result.nonPresenceUsers.length) {
				dispatch('contacts/queryUserPresence', result.nonPresenceUsers, {root: true})
			}
			return {
				contacts: result.contacts,
				hasNextPage: response.hasNextPage
			}
		})
	},
	searchGroupMembers ({ rootState, dispatch, rootGetters }, payload) {
		return groupController.fetchGroupMembers(payload.startSeq, payload.searchKey, {}).then((members) => {
			let result = userUtil.formatContacts(members, !rootGetters['privileges/disableUserPresence'], rootState.contacts.presences, payload.existedContacts)
			if (result.nonPresenceUsers.length) {
				dispatch('contacts/queryUserPresence', result.nonPresenceUsers, {root: true})
			}
			return result.contacts
		})
	},
	readTeamInfo (_, teamId) {
      return groupController.readTeamInfo(teamId)
	},
	checkTeamMemberChange ({ dispatch, state }) {
	  const changedTeamIds = []
	  for (let team of state.allTeams) {
		const teamMembers = state.teamMembers[team.id]
		if (teamMembers && teamMembers.length !== team.memberCounts) {
		  changedTeamIds.push(team.id)
		}
	  }
	  if (changedTeamIds.length > 0) {
		const reqs = changedTeamIds.map(id => dispatch('readTeamMembersByPagination', {
		  teamId: id
		}))
		return Promise.all(reqs)
	  }
	  return Promise.resolve()
	},

	downloadClientLog () {
		return groupController.downloadClientLog()
	},

	getLocalLog(){
		return groupController.getLocalLog()
	},
	addTeamMembers (_, {teamId, users}) {
		return groupController.addTeamMembers(teamId, users)
	},
	removeTeamMember (_, {teamId, user}) {
		return groupController.removeTeamMember(teamId, user)
	},
	readGroupMembers (_, payload) {
		return groupController.readGroupMembers(payload)
	},
	removeTeamManager (_, {teamId, user}) {
		return groupController.removeTeamManager(teamId, user)
	},
	addTeamManagers (_, {teamId, users}) {
		return groupController.addTeamManagers(teamId, users)
	},
	createOrUpdateIntegration({}, {integrationSeq, integration}) {
    return groupController.createOrUpdateIntegration(integrationSeq, integration)
  },
  deleteIntegration({}, integrationSeq) {
    return groupController.deleteIntegration(integrationSeq)
  },
  verifyIntegration({}, integrationSeq) {
    return groupController.verifyIntegration(integrationSeq)
  },
  unregisterGroup({}, verifycode) {
    return groupController.unregisterGroup(verifycode)
  },
  updateMemberDistributionList (_, {userId, roles}) {
	return groupController.updateMemberDistributionList(userId, roles)
  }
};
