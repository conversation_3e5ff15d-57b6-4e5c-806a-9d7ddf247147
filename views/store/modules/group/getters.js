import {getCRMIntegrationObject} from '../../../mgrConnectors/util/getCRMIntegrationObject';
import {MoxoBillingPlanIds} from '../../../common/appConst';
import i18n from '../../../i18n/mepIndex';
import {GroupUtil} from '@controller/utils/group.ts';
import {BotDefaultAvatar} from '@views/theme/src/images'
import {MxConsts,ObjectUtils} from '@commonUtils';
import { distributionListSortFn } from '@views/contacts/src/utils/contact'

const ActionObjectType = MxConsts.ActionObjectType
function fallbackTagWithName (array, object) {
  let result
  for (const tagName of array) {
    if (object.hasOwnProperty(tagName)) {
      result = object[tagName]
      break
    }
  }
  return result
}

export default {
  groupBasicInfo (state) {
    return state.basic_info;
  },
  groupTags (state) {
    return state.basic_info.tags || {};
  },
  groupCaps (state) {
    return state.group_caps;
  },
  enableUserDataDownload (state) {
    return state.basic_info.group_settings?.enable_user_data_downloading
  },
  isEnableWorkspaceReportAuditing (state) {
    return state.basic_info.group_settings?.enable_workspace_report_auditing
  },
  isHideClientDashboard (state) {
    return state.group_caps.hide_client_dashboard
  },
  enableChatWorkspace (state) {
    return state.group_caps.enable_chat_workspace
  },
  enableEmailPrivacy (state) {
    return state.group_caps.has_email_privacy
  },
  isEnableAudit (state) {
    return state.group_caps['has_audit']
  },
  isEnableReport (state) {
    return state.group_caps['has_org_report'] === null || state.group_caps['has_org_report'] === undefined ? state.group_caps['has_audit'] : state.group_caps['has_org_report']
  },
  isEnableActionLibrary (state, getters) {
    const flag = state.basic_info.group_settings?.enable_action_library
    if (flag === undefined) {
      return getters.isEnableContentLibrary
    }
    return flag
  },
  isEnableContentLibrary (state) {
    const flag = state.basic_info.group_settings?.enable_content_library
    if(flag === undefined){
      return true
    }
    return flag
  },
  isEnableWorkflow (state) {
    const flag = state.basic_info.group_settings?.enable_flow_template_library
    if (flag === undefined) {
      return false
    }
    return flag
  },
  isEnableApproval (_, getters) {
    return getters.groupTags?.Enable_Approval
  },
  isEnableAcknowledgement (_, getters) {
    return getters.groupTags?.Enable_Acknowledgement
  },
  isEnableOOOMandateBackupUser (_, getters){
    return getters.groupTags?.OOO_Mandate_Backup_User
  },
  isEnableFileRequest (_, getters) {
    return getters.groupTags?.Enable_File_Request
  },
  isEnableLaunchWebApp (_, getters) {
    return getters.groupTags?.Enable_Launch_Web_App
  },
  isEnableDashboardReports (_,getters) {
    return getters.groupTags['Enable_DashboardReports'] === undefined ? true : getters.groupTags['Enable_DashboardReports']
  },
  isEnableGetClientCoverageReportFromBiz (_,getters) {
    return getters.groupTags['get_client_coverage_report_from_biz'] === undefined ? false : getters.groupTags['get_client_coverage_report_from_biz']
  },
  isEnableFlowGallery (_, getters) {
    return getters.groupTags?.Enable_Flow_Gallery
  },
  canScheduleFlow (state, getters, rootState, rootGetters) {
    const { isInternalUser } = rootGetters['user/currentUser']
    const canCreateConversation = rootGetters['privileges/canCreateConversation']
    const canShowCalendar = rootGetters['privileges/showCalendar']
    const canCreateFlowWorkspace = getters.isEnableWorkflow && canCreateConversation && isInternalUser
    return canShowCalendar && canCreateFlowWorkspace
  },
  isWeChatIntegrationEnabled (state) {
    const activeIntegration = state.integrations.filter(integration => integration.type === 'GROUP_INTEGRATION_WECHAT' && !integration.is_deleted);
    return activeIntegration && activeIntegration.length > 0;
  },
  isLineIntegrationEnabled (state) {
    const activeIntegration = state.integrations.filter(integration => integration.type === 'GROUP_INTEGRATION_LINE' && !integration.is_deleted);
    return activeIntegration && activeIntegration.length > 0;
  },
  isWhatsappIntegrationEnabled (state) {
    const activeIntegration = state.integrations.filter(integration => integration.type === 'GROUP_INTEGRATION_WHATSAPP' && !integration.is_deleted);
    return activeIntegration && activeIntegration.length > 0;
  },
  isSocialChannelEnabled (_, getters) {
    return getters.isWeChatIntegrationEnabled ||
      getters.isLineIntegrationEnabled ||
      getters.isWhatsappIntegrationEnabled;
  },
  isSamlEnabled (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.has_saml) {
      return true;
    }
    return false;
  },
  isBrandingEnabled (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.has_branding) {
      return true;
    }
    return false;
  },
  isAcdEnabled (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.enable_acd) {
      return true;
    }
    return false;
  },
  isEnableLinkPreview (state, getters) {
    return getters.groupTags.Show_Previews_for_Links
  },
  enabledBasicActions (state, getters, rootState, rootGetters) {
    const arr = []
    //esign
    if(rootGetters['privileges/enableEsign'] && rootGetters['privileges/canCreateSignature'] && (rootGetters['privileges/canUploadFile'] || getters.isEnableContentLibrary)) {
      arr.push(ActionObjectType.ESign)
    }
    if(rootGetters['privileges/canCreateApproval']) {
      arr.push(ActionObjectType.Approval)
    }
    if(rootGetters['privileges/canCreateFileRequest']) {
      arr.push(ActionObjectType.FileRequest)
    }
    if(rootGetters['privileges/canCreateAcknowledge']) {
      arr.push(ActionObjectType.Acknowledgement)
    }
    if(rootGetters['privileges/canCreateTodo']) {
      arr.push(ActionObjectType.TodoTransaction)
    }
    if(rootGetters['privileges/canCreateTimeBooking']) {
      arr.push(ActionObjectType.MeetRequest)
    }
    if(rootGetters['privileges/canCreateForm']) {
      arr.push(ActionObjectType.FormRequest)
    }
    if(rootGetters['privileges/canCreatePDFForm']) {
      arr.push(ActionObjectType.PDFForm)
    }
    if(rootGetters['privileges/canCreateLaunchWebApp']) {
      arr.push(ActionObjectType.LaunchWebApp)
    }
    return arr
  },
  acdAllowedChannels (state, getters) {
    const tags = getters.groupTags || {}
    if (tags['Maximum_Allowed_Acd_Channels']) {
      return parseInt(tags['Maximum_Allowed_Acd_Channels'], 10)
    } else
      return 1
  },
  isServiceRequestEnabled (state, getters) {
    const caps = getters.groupCaps;
    return !!(caps && caps.enable_service_request);
  },

  isSFDCIntegrationEnabled (state, getters) {
    const caps = getters.groupCaps;
    return !!(caps && caps.enable_sfdc_integration);
  },
  sfdcIntegrationConfig (state) {
    return getCRMIntegrationObject(state.integrations, obj => obj.aud_server)
  },
  isGlobalRelayIntegrationEnabled (state, getters) {
    const caps = getters.groupCaps;
    return !!(caps && caps.enable_globalrelay_integration);
  },
  globalRelayIntegrationConfig (state) {
    return getCRMIntegrationObject(state.integrations, obj => obj.service === 'global_relay')
  },
  isHubSpotIntegrationEnabled (state, getters) {
    const caps = getters.groupCaps;
    return !!(caps && caps.enable_hubspot_integration);
  },
  hubSpotIntegrationConfig (state) {
    return getCRMIntegrationObject(state.integrations, obj => obj.service === 'hubspot')
  },
  isXeroIntegrationEnabled (state, getters) {
    const caps = getters.groupCaps;
    return !!(caps && caps.enable_xero_integration);
  },
  xeroIntegrationConfig (state) {
    return getCRMIntegrationObject(state.integrations, obj => obj.service === 'xero')
  },
  isDynamicsIntegrationEnabled (state, getters){
    const caps = getters.groupCaps;
    return !!(caps && caps.enable_dynamics_integration)
  },
  dynamicsIntegrationConfig (state) {
    return getCRMIntegrationObject(state.integrations, obj => obj.service === 'dynamics')
  },
  isFilevineIntegrationEnabled (state) {
    return state.group_caps?.enable_filevine_integration || false
  },
  filevineIntegrationConfig (state) {
    return getCRMIntegrationObject(state.integrations, obj => obj.service === 'filevine')
  },
  isRedtailIntegrationEnabled (state) {
    return state.group_caps?.enable_redtail_integration || false
  },
  isZohoIntegrationEnabled (state) {
    return state.group_caps?.enable_zoho_integration || false
  },
  isSmarshIntegrationEnabled (state) {
    return state.group_caps?.enable_smarsh_integration || false
  },
  smarshIntegrationConfig (state) {
    return getCRMIntegrationObject(state.integrations, obj => obj.service === 'smarsh')
  },
  isWealthBoxIntegrationEnabled (state) {
    //return true
    return state.group_caps?.enable_wealthbox_integration || false
  },
  isAnyCrmConnectorEnabled (state, getters) {
    return getters.isSFDCIntegrationEnabled ||
      getters.isGlobalRelayIntegrationEnabled ||
      getters.isHubSpotIntegrationEnabled ||
      getters.isXeroIntegrationEnabled ||
      getters.isDynamicsIntegrationEnabled ||
      getters.isFilevineIntegrationEnabled ||
      getters.isRedtailIntegrationEnabled ||
      getters.isZohoIntegrationEnabled ||
      getters.isSmarshIntegrationEnabled ||
      getters.isWealthBoxIntegrationEnabled
  },

  isSubscriptionEnabled (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.enable_channel_subscription) {
      return true;
    }
    return false;
  },
  enableVerifyEmail (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.ignore_email_verification === true) {
      return false;
    }
    return true;
  },
  clientCanAccessWeb (state, getters) {
      const tags = getters.groupTags || {}
      return !tags.hasOwnProperty('Client_Can_Access_Web') || tags['Client_Can_Access_Web'] === true
  },
  // clientCanAccessMobileWeb (state, getters) {
  //   const tags = getters.groupTags || {}
  //   return tags.hasOwnProperty('Client_Can_Access_Mobile_Web') && tags['Client_Can_Access_Mobile_Web'] === true
  // },
  canAcdClientAddFile (state, getters) {
    const tags = getters.groupTags || {};
    if(tags['API_Acd_Show_Add_File'] === false){
      return false
    }else{
      return true
    }
  },
  hideTermsPolicy (_, getters) {
    const tags = getters.groupTags || {}
    return tags['Hide_Terms_Policy'] || false
  },
  downloadAppLinks (state, getters) {
    const tags = getters.groupTags || {};
    return {
      androidDownloadLink: tags.OrgConfig_DownloadLink_Android,
      iosDownloadLink: tags.OrgConfig_DownloadLink_iOS
    };
  },
  isAppDownloadLinkConfigured (_, getters) {
    const tags = getters.groupTags || {}
    return tags.OrgConfig_DownloadLink_Android || tags.OrgConfig_DownloadLink_iOS
  },
  actionColor (state, getters, rootGetters) {
    const tags = getters.groupTags || {};
    const websdkConfig = rootGetters['websdk/websdkConfig']
    if (websdkConfig && websdkConfig.actionColor) {
      return websdkConfig.actionColor
    } else if (tags.B_Branding_Color) {
      return tags.B_Branding_Color
    } else {
      return '#1A69D1'
    }
  },
  brandingLogoLeft (state, getters) {
    const tags = getters.groupTags || {};
    return tags.Main_Color_Rectangle_Logo_Left;
  },
  brandingLogoCenter (state, getters) {
    const tags = getters.groupTags || {};
    return tags.Main_Color_Rectangle_Logo;
  },
  brandingName (state, getters) {
    const tags = getters.groupTags || {};
    return tags.OrgConfig_BrandName || getters.groupBasicInfo.name;
  },
  groupName (state, getters) {
    return getters.groupBasicInfo.name || ''
  },
  groupRoles (state) {
    return state.roles;
  },
  isPhoneNumberEnabled (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.enable_phone_number_sign_up === true) {
      return true;
    }
    return false;
  },
  isPaymentExpired (state) {
    return state.basic_info.isPaymentExpired;
  },
  isPhoneNumberPrimary (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.primary_sign_up_phone_number === true) {
      return true;
    }
    return false;
  },
  samlIntegrations (state) {
    const samlIntegrations = state.integrations.filter((integration) => integration.type === 'GROUP_INTEGRATION_SAML' && !integration.is_deleted);
    return samlIntegrations;
  },
  groupIntegrations (state) {
    return state.integrations;
  },
  smtpIntegration (state) {
    return state.integrations.findLast(integration => integration.type === 'GROUP_INTEGRATION_SMTP' && !integration.is_deleted)
  },
  isOnlineBillingOrg (state, getters) {
    return getters.groupCaps.is_online_billing;
  },
  isPrivateMeetEnabled (state) {
    const basic_info = state.basic_info;
    return (basic_info.group_settings && basic_info.group_settings.enable_private_meet);
  },
  isMeetPasswordEnabled (state) {
    const basic_info = state.basic_info;
    return (basic_info.group_settings && basic_info.group_settings.enable_meet_password);
  },
  meetDefaultPassword (state) {
    const basic_info = state.basic_info
    return (basic_info.group_settings && basic_info.group_settings.meeting_default_password);
  },
  enableMobileWebMeetingJoin (state) {
    const basic_info = state.basic_info;
    return (basic_info.group_settings && basic_info.group_settings.enable_mobile_web_meeting_join);
  },
  roleMap (state) {
    const result = {};
    if (state.roles) {
      state.roles.forEach(role => {
        result[role.sequence] = role;
      });
    }
    return result;
  },
  defaultInternalRoleSequence (state) {
    const roles = state.roles || [];
    let defaultSequence = 0;
    roles.forEach(role => {
      if (role.is_default && role.type === 'ROLE_TYPE_NORMAL') {
        defaultSequence = role.sequence;
      }
    });
    return defaultSequence;
  },
  defaultClientRoleSequence (state) {
    const roles = state.roles || [];
    let defaultSequence = 0;
    roles.forEach(role => {
      if (role.is_default && role.type === 'ROLE_TYPE_LOCAL') {
        defaultSequence = role.sequence;
      }
    });
    return defaultSequence;
  },
  allRolesScheduleMeetDisabled (state) {
    let disabled = true
    const roles = state.roles || []
    for (const role of roles) {
      if (role.type === 'ROLE_TYPE_NORMAL' || role.type === 'ROLE_TYPE_LOCAL') {
        if (role.name !== 'MXAuditRole' && role.name !== 'MXReportRole' && role.meet && role.meet.can_scheduled_meet) {
          disabled = false
          break;
        }
      }
    }
    return disabled
  },
  myTeams (state) {
    return state.myTeams;
  },
  myOwnedTeam (state) {
    return state.myManagedTeams;
  },
  myManagedMembers (state) {
    return state.myManagedMembers
  },
  hideMeetingRecording (state) {
    const basic_info = state.basic_info;
    return (basic_info.group_settings && basic_info.group_settings.hide_meet_recording);
  },
  exposeContactInfoToClients (state) {
    const basic_info = state.basic_info
    const shouldExpose = basic_info.group_settings && basic_info.group_settings.expose_contact_info_to_clients
    return shouldExpose === false ? false : true
  },
  exposeContactInfoToInternal (state){
    const basic_info = state.basic_info
    const shouldExpose = basic_info.group_settings && basic_info.group_settings.expose_client_contact_info_to_internals
    return shouldExpose === false ? false : true
  },
  remainingDaysForTrial (state) {
    const time = state.basic_info.trial_end_time
    const now = Date.now()
    const day = 24 * 60 * 60 * 1000
    return Math.ceil((time - now) / day)
  },
  isM0App (state) {
    return state.isM0App
  },
  isReportsEnabled (state, getters) {
    const caps = getters.groupCaps;
    if (caps && caps.has_org_report) {
      return true;
    } else {
      if (caps.has_audit && (caps.has_org_report === undefined || caps.has_org_report === null)) {
        return true;
      } else {
        return false;
      }
    }
  },
  remoteUploadOptions (state, getters) {
    const tags = getters.groupTags || {};
    let json = {}
    if (tags.Cloud_File_Integration_Options) {
      const string = tags.Cloud_File_Integration_Options
      try {
        json = JSON.parse(string)
        Object.keys(json).forEach((k) => {
          //delete if value is false so that Object.keys(json).length will be 0, if all services are turned off by OA
          if (!json[k]) {
            delete json[k]
          }
        })
      } catch (e) {
      }
    }
    return json
  },
  isQuicklinksEnabledForOA (state, getters) {
    const tags = getters.groupTags || {}
    return tags['Enable_Quick_Links_For_OA'] || false;
  },

  enableDigestEmail (state) {
    const basic_info = state.basic_info;
    if (basic_info.group_settings) {
      if (basic_info.group_settings.enable_digest_email) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
	isRoleOptionEnabledForOA (state, getters) {
		const tags = getters.groupTags || {}
		return tags.hasOwnProperty('Allow_OA_Configure_Role') && tags['Allow_OA_Configure_Role'] === true
	},
	showNativeFileSelector (state, getters) {
		return !getters.groupTags.Enable_Share_File_Warning && !Object.keys(getters.remoteUploadOptions).length
	},
	isBroadcastEnabled (state, getters) {
		let enable_broadcast = true
		const basic_info = state.basic_info
		if (basic_info.group_settings && typeof basic_info.group_settings.enable_broadcast === 'boolean') {
			enable_broadcast = basic_info.group_settings.enable_broadcast
		}
		return enable_broadcast
	},
  cachedDeletedUsers (state) {
    return state.deletedUsers || {}
  },
  is2faEnabled (state) {
    return state.group_caps['enable_2fa']
  },
  sessionTimeoutValue (state, getters, rootState, rootGetters) {
    const user = rootGetters['user/currentUser']
    let {isInternalUser} = user, {groupTags} = getters
    const tagNameOrder = ['Web_Session_Timeout_Interval', 'Session_Timeout_Interval']
    if (!isInternalUser) {
      tagNameOrder.unshift('Web_Session_Timeout_Client')
    }
    return fallbackTagWithName(tagNameOrder, groupTags)
  },
  isEnableAI (state) {
    const basic_info = state.basic_info;
    return (basic_info.group_settings && basic_info.group_settings.enable_ai);
  },
  aiBotUser (state) {
    const integrations = state.integrations.filter((integration) => integration.type === 'GROUP_INTEGRATION_APP' && integration.webapp.type === 'WEBAPP_TYPE_AI')
    .map(integration => {
      const {user} = integration
      const avatar = GroupUtil.getBotAvatar(state.basic_info.id, integration, BotDefaultAvatar)
      return {
        name: integration.webapp?.app_name || user.user.name,
        id: user.user.id,
        avatar
      }
    })

    return integrations?.length > 0 ? integrations[0] : null
  },
  botUsers (state){
    const integrations = state.integrations.filter((integration) => integration.type === 'GROUP_INTEGRATION_APP' && integration.webapp.type === 'WEBAPP_TYPE_BOT')
        .map(integration => {
          const {user} = integration
          const avatar = GroupUtil.getBotAvatar(state.basic_info.id ,integration, BotDefaultAvatar)
          return {
            name: integration.webapp?.app_name || user.user.name,
            id: user.user.id,
            avatar
          }
        })
    integrations.sort(function (a, b) {
      const nameA = a.name.toUpperCase();
      const nameB = b.name.toUpperCase();
      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
      return 0;
    });
    return integrations
  },
  inboxBotUsers (state){
    const integrations = state.integrations.filter((integration) => integration.type === 'GROUP_INTEGRATION_APP' && integration.webapp.type === 'WEBAPP_TYPE_INBOX_BOT')
        .map(integration => {
          const {user} = integration
          const avatar = GroupUtil.getBotAvatar(state.basic_info.id ,integration, BotDefaultAvatar)
          return {
            name: integration.webapp?.app_name || user.user.name,
            id: user.user.id,
            avatar
          }
        })
    integrations.sort(function (a, b) {
      const nameA = a.name.toUpperCase();
      const nameB = b.name.toUpperCase();
      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
      return 0;
    });
    return integrations
  },
  hideActionEntries (state, getters, rootState, rootGetters) {
    return rootGetters['privileges/hideActionTab'] || getters.groupTags?.Hide_Action_Entries
  },
  displayDeletedOrgUserNameAsDeletedUser (_, getters) {
    return getters.groupTags.Show_Deleted_User_Name === false
  },
  isFormEnable (_,getters) {
    return getters.groupTags['Enable_Form'] === undefined ? true : getters.groupTags['Enable_Form']
  },
  isPDFFormEnable (_,getters) {
    return getters.groupTags['Enable_PDF_Form'] === undefined ? true : getters.groupTags['Enable_PDF_Form']
  },
  clientPortalTitle (_,getters) {
    return getters.groupTags.Client_Portal_Direct_Conversation_Section_title
  },
  isMagicLinkEnabled (state) {
    const enable_magic_link = state.basic_info?.group_settings?.enable_magic_link
    return enable_magic_link === undefined ? true : !!enable_magic_link
  },
  isMagicLinkEnabledForInternalUsers (state, getters) {
    const enable_magic_link = state.basic_info?.group_settings?.enable_magic_link_for_internal_users
    return enable_magic_link === undefined ? getters.isMagicLinkEnabled : !!enable_magic_link
  },
  internalCanAccessMobileWebViaPad (_,getters) {
    return getters.groupTags?.internal_can_access_mobile_web_via_pad
  },
  enableServiceRequestFlow (state, getters) {
    const {Enable_Flow_Service_Request} = getters.groupTags
    return getters.isEnableWorkflow && Enable_Flow_Service_Request
  },
  showUpgradePlan (state) {
    const isEnterprise = (state.basic_info.plan_code === MoxoBillingPlanIds.Business_Pro
        || state.basic_info.plan_code === MoxoBillingPlanIds.Enterprise)
    return !state.basic_info.customer_id || !isEnterprise
  },
  isFreeOrBizPlan (state) {
    return [MoxoBillingPlanIds.Free, MoxoBillingPlanIds.Business_Basic, MoxoBillingPlanIds.Business_Basic_Yearly].indexOf(state.basic_info.plan_code) > -1
  },
  isBusinessProPlan (state) {
    return state.basic_info.plan_code === MoxoBillingPlanIds.Business_Pro || state.basic_info.plan_code === MoxoBillingPlanIds.Business_Pro_Yearly
  },
  isTraditionalOrg (state) {
    return !state.group_caps.enable_new_frame
  },
  isEnableClientUsageReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['enable_client_usage_report'] ?? false)
  },
  isEnableInternalUsageReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['enable_internal_usage_report'] ?? false)
  },
  isEnableWorkspaceListReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['Enable_WorkSpaceList_Reports'] ?? true)
  },
  isEnableActionListReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['Enable_ActionList_Reports'] ?? true)
  },
  isEnableMeetingDetailsReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['enable_meet_usage_report'] ?? false)
  },
  isEnableMeetingListReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['enable_meeting_list_report'] ?? false)
  },
  isEnableServiceRequestListReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['enable_sr_list_report'] ?? false)
  },
  isEnableDashboardReportAdminManagerView (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['Enable_Reports_Admin_View'] ?? false)
  },
  isEnableDailyOrgActivityReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['enable_daily_org_activity_report'] ?? false)
  },
  isEnableDailyUserActivityReport (_, getters) {
    const groupTags = getters.groupTags || {}
    return Boolean(groupTags['enable_daily_user_activity_report'] ?? false)
  },
  isEnableClientEngagementReport (_, getters) {
    const groupTags = getters.groupTags || {};
    const enableClientEngagementReport = groupTags.enable_client_engagement_report;
    return enableClientEngagementReport === undefined || enableClientEngagementReport === true;
  },
  isEnableInternalEngagementReport (_, getters) {
    const groupTags = getters.groupTags || {};
    const enableInternalEngagementReport = groupTags.enable_internal_engagement_report;
    return enableInternalEngagementReport === undefined || enableInternalEngagementReport === true;
  },
  isEnableClientCoverageReport (_, getters) {
    const groupTags = getters.groupTags || {};
    const enableClientCoverageReport = groupTags.enable_client_coverage_report;
    return enableClientCoverageReport === undefined || enableClientCoverageReport === true;
  },
  userBoardsAutoArchiveThreshold (state) {
    return state.basic_info.group_settings?.user_boards_auto_archive_threshold || 1000
  },
  isEnableClientGroup (state, getters) {
    return state.basic_info?.group_settings?.enable_client_group
    //return isEnableClientGroup === undefined ? true : isEnableClientGroup
  },
  enableClientGroupToInternalUsers(state, getters) {
    return getters.isEnableClientGroup && state.basic_info.group_settings?.enable_client_group_to_internal_users
  },
  clientTeams (state){
    const teams = state.allTeams?.filter(team=>{
      return team.type === 'GROUP_TYPE_CLIENT_TEAM'
    })
    return teams || []
  },
  isEnableJumio (_,getters) {
    return getters.groupTags['Enable_Jumio']
  },
  isEnableDocuSign (_, getters) {
    return getters.groupTags?.Enable_DocuSign
  },
  isEnableOtherIntegrationApps (_, getters) {
    return getters.groupTags?.Enable_Other_Integration_Apps
  },
  /* isEnableMeetRequest(_, getters) {
    return getters.groupTags?.Enable_Meet_Request
  }, */
  isEnableMeetingRequest (state, getters, rootState, rootGetters){
    const isEnable =  getters.groupTags?.Enable_Meet_Request !== false
    const isMeetEnable = rootGetters['privileges/callEnabled']
    return isEnable && isMeetEnable
  },
  currentPlanInfo (state, getters) {
    const current = state.basic_info.plan_code
    return getters.moxoBillingPlans.find(p=>p.id === current)
  },
  moxoBillingPlans (state, getters) {
    return [
      {
        id: MoxoBillingPlanIds.Free,
        name: i18n.t('free'),
        altname: 'plan-free',
        description: i18n.t('plan_subtitle1'),
        price_numeric: 0,
        type: 'both',
        price: '$0',
        hide: true,
        include: i18n.t('plan_remind1'),
        cap: {
          active_workspace_number: 2
        },
        details: [
          i18n.t('plan_description2'),
          i18n.t('plan_description3'),
          i18n.t('plan_description4'),
          i18n.t('plan_description5'),
          i18n.t('plan_description6'),
          i18n.t('plan_description34'),
          i18n.t('plan_description7'),
          i18n.t('plan_description8'),
          i18n.t('plan_description9'),
          i18n.t('plan_description10'),
          i18n.t('plan_description12'),
          i18n.t('gb_per_user', {number: 5}),
        ]
      },
      {
        id: MoxoBillingPlanIds.Business_Basic,
        name: i18n.t('business'),
        fullname: 'Business Plan',
        altname: 'plan-business',
        description: i18n.t('plan_subtitle3'),
        price_numeric: 30,
        price: '$30',
        type: 'monthly',
        hide: true,
        cap: {
          active_workspace_number: 10
        },
        include: i18n.t('plan_remind3'),
        details: [
          i18n.t('plan_description20'),
          i18n.t('plan_description21'),
          i18n.t('plan_description36'),
          i18n.t('plan_description22'),
          i18n.t('plan_description23'),
          i18n.t('plan_description24'),
          i18n.t('gb_per_user', {number: 50}),
        ]
      },
      {
        id: MoxoBillingPlanIds.Business_Basic_Yearly,
        name: i18n.t('business'),
        fullname: 'Business Annual Plan',
        altname: 'plan-business',
        description: i18n.t('plan_subtitle3'),
        price_numeric: 288,
        price: '$288',
        type: 'yearly',
        hide: true,
        cap: {
          active_workspace_number: 10
        },
        include: i18n.t('plan_remind3'),
        details: [
          i18n.t('plan_description20'),
          i18n.t('plan_description21'),
          i18n.t('plan_description36'),
          i18n.t('plan_description22'),
          i18n.t('plan_description23'),
          i18n.t('plan_description24'),
          i18n.t('gb_per_user', {number: 50}),
        ]
      },
      {
        id: MoxoBillingPlanIds.Business_Pro,
        name: i18n.t('Business_Pro'),
        altname: 'plan-business-pro',
        description: i18n.t('plan_subtitle4'),
        price_numeric: 49.99,
        price: i18n.t('custom'),
        type: 'both',
        cap: {
          active_workspace_number: 0
        },
        include: i18n.t('plan_remind4'),
        details: [
          i18n.t('plan_description27'),
          i18n.t('plan_description29'),
          i18n.t('gb_per_user', {number: 100})
        ],
        optional: [
          i18n.t('plan_description32'),
          i18n.t('plan_description33'),
        ]
      },
      {
        id: MoxoBillingPlanIds.Enterprise,
        name: i18n.t('Enterprise'),
        altname: 'plan-enterprise',
        description: i18n.t('plan_subtitle5'),
        price_numeric: Infinity,
        price: i18n.t('custom'),
        type: 'both',
        //hide: true,
        cap: {
          active_workspace_number: 0
        },
        include: i18n.t('plan_remind5'),
        details: [
          i18n.t('plan_description37'),
          i18n.t('plan_description38'),
          i18n.t('plan_description39'),
          i18n.t('tb_per_user', {number: 1})
        ],
        optional: [
          i18n.t('plan_description32'),
          i18n.t('plan_description33'),
          i18n.t('plan_description40'),
          i18n.t('plan_description41'),
          i18n.t('plan_description42'),
          i18n.t('service_requests'),
          i18n.t('plan_description43'),
          i18n.t('quick_links'),
        ]
      }
    ];
  },
  requiredGroupWorkspaceClientCounts (_, getters) {
    return getters.groupTags?.Group_workspace_required_client_users ?? 0
  },
  isEnabledCustomEmailDomain (state) {
    return state.group_caps?.enable_custom_smtp_integration || false
  },
  shareTemplateGroupId (state) {
    let sharedTplGroupId
    const tags = state.basic_info.tags || {}
    const tplIdsArrStr = tags['Shared_Templates_IDs']
    if (tplIdsArrStr) {
      let latest
      const tplIdsArr = JSON.parse(tplIdsArrStr)
      tplIdsArr.forEach(tplInfo => {
        if (!latest) {
          latest = tplInfo
        } else if (latest.version < tplInfo.version) {
          latest = tplInfo
        }
      })
      sharedTplGroupId = latest.orgID.replace(latest.version, '')
    } else {
      sharedTplGroupId = tags['Shared_Templates_ID']
    }
    return sharedTplGroupId
  },
  includeCOCInSignedFile (state) {
    return state.basic_info.group_settings?.include_coc_in_signed_file
  },
  isEnableMagicCodeLogin (_, getters) {
    return ObjectUtils.isUndefined(getters.groupTags['Enable_Magic_Code_Login']) ? true : getters.groupTags['Enable_Magic_Code_Login']
  },
  signDateFormat (_, getters) {
    return getters.groupTags?.Sign_Date_Formator ?? ''
  },
  enableBoardProperty (state){
    return state.orgProperties.length > 0
  },
  enableClientDistributionList (state) {
    return state.basic_info.group_settings?.enable_client_distribution_list
  },
  groupDistributionList (state) {
    return state.roles.filter(role => role.category === MxConsts.GroupRoleCategory.ROLE_CATEGORY_DISTRIBUTION_LIST).sort(distributionListSortFn)
  },
  orgCreatedTimestamp (state) {
    return state.basic_info.created_time
  },
  isEnableWhatsAppReasonToContact (state) {
    const activeIntegration = state.integrations.filter(integration =>
        integration.type === 'GROUP_INTEGRATION_WHATSAPP' && !integration.is_deleted)
    if (!activeIntegration || activeIntegration.length === 0) return true

    try {
      const connectorInfo = JSON.parse(activeIntegration[0].connector)
      return !connectorInfo.disable_reason_to_contact
    } catch (e) {
      return true
    }
  },
  isEnableOAUsageReport (_, getters) {
    return !!getters.groupTags?.Enable_Usage_Report  //default false
  },
  isEnableBroadcastRecipient (state) {
    return state.basic_info.group_settings?.enable_broadcast_recipient || false;
  },
  allMeetServiceTypes (_, getters) {
    if (getters.groupTags?.Session_Service_Types) {
      return JSON.parse(getters.groupTags.Session_Service_Types)
    }
    if (getters.groupTags?.Session_Service_Type) {
      return [getters.groupTags.Session_Service_Type]
    }
    return [
      MxConsts.VendorServiceType.SERVICE_DEFAULT
    ]
  },

  isEnableInbox (state) {
    return state.basic_info.group_settings?.enable_inbox || false
  },

  inboxBotUser (state){
    const inboxBot = state.basic_info.integrations.find(integration=>{
      return integration.type === 'GROUP_INTEGRATION_APP' && integration.webapp.type === 'WEBAPP_TYPE_INBOX_BOT' && !integration.is_deleted && integration.user?.user && !integration.user.user.disabled
    })
    if(inboxBot){
      const  defaultInboxBotUserAvatar =  'assets/inbox/default_inbox_avatar.png'

      const avatar = GroupUtil.getBotAvatar(state.basic_info.id, inboxBot, defaultInboxBotUserAvatar)
      const botUser = inboxBot.user.user
      return {
        name: botUser.name,
        id: botUser.id,
        avatar,
        title: botUser.title
      }
    }
    return null
  }
};
