import { AmServiceType } from '@model/automation/defines';

export default {
  canShowAutomations (state, getters, rootState, rootGetters) {
    const countServiceKeys = Object.keys(AmServiceType)?.length;
    const disabledServicesList = state.disabledServices || []
    const hasServicesEnable = disabledServicesList.length < countServiceKeys
    const isInternalUser = rootGetters['user/currentUser'].isInternalUser
    return isInternalUser && hasServicesEnable;
  }
}
