export default {
  setLastEventTimer (state, payload) {
    state.lastEventTimer = payload
  },
  setWinStatus (state, payload) {
    state.winStatus = payload
  },
  setWinSize (state, size) {
    state.winSize = size
  },
  setRouterHistory (state, payload) {
    state.routerHistory = payload
  },
  setMainLayout (state, layout) {
    state.mainLayout = layout
  },
  setNewBusinessContext (state) {
    state.isNewBusinessContext = true
  }
}
