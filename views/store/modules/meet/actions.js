import { Meet<PERSON><PERSON>roller } from '@controller/meet/src/meetController'
import { getVendorServiceOAuthName } from '@views/common/components/meet/utils'
import { MeetController as MeetControllerNew, IntegrationCenterController } from '@newController/index'

const meetCtrls = {}
export default {

  resetMeetObject({ commit }, meet) {
    return new Promise((resolve,reject)=>{
      
 
      let meetCtrl = meetCtrls[meet.board_id]
      if (meetCtrl) {
        if (meet.forceReload || meetCtrl.session_key !== meet.session_key
          || (meet.board_id && meetCtrl.board_id !== meet.board_id)) {
          // clean current meetCtrl
          meetCtrl.destroy()
          meetCtrl = null
        }
      }

      if (!meetCtrl) {
        meetCtrl = MeetController.getInstance(meet)
      }

      // 1. get meet board
      meetCtrl.getMeetObject().then(meetObject => {
        commit('setMeetObject', meetObject)

        // 2. subscribe meet board
        meetCtrl && meetCtrl.subscribeMeetInfo(meetObject => {
          commit('setMeetObject', meetObject)
        })
        resolve()
      }).catch(error=>{
        console.log(error,"errr")
        reject(error)
      })

      // cache meetCtrl
      meetCtrls[meet.board_id] = meetCtrl

    })
  },

  clearMeetObject({ commit }, meet) {
    let meetCtrlValid = false
    let meetCtrl = meetCtrls[meet.board_id]
    if (meetCtrl && meet && meet.session_key === meetCtrl.session_key) {
      if (meet.board_id || meetCtrl.board_id) {
        // for ended recurrent meet, hold same session_key but different board_id
        if (meet.board_id === meetCtrl.board_id) {
          meetCtrlValid = true
        }
      } else {
        // Note: will depend on serial_no to do meet multiple instance control
        if (meet.serial_no === meetCtrl.serial_no) {
          meetCtrlValid = true
        }
      }

      if (meetCtrlValid) {
        commit('clearMeetObject', meet.board_id)
        meetCtrl && meetCtrl.destroy()
        meetCtrls[meet.board_id] = meetCtrl = null
        delete meetCtrls[meet.board_id]
      }
    }
  },

  resetMeetObjectWithAnonymous({ commit, rootGetters }, meetObject) {
    const board_id = meetObject.board_id
    commit('setMeetObject', meetObject)
    // if user logged in
    const loginUser = rootGetters['user/currentUser']
    if (loginUser && meetObject.members) {
      const idx = meetObject.members.findIndex(member => member.id === loginUser.id)
      if (idx > -1) {
        // login user is meet_member, subscribe current meet
        meetCtrls[board_id] = MeetController.getInstance({ session_key: meetObject.session_key })
        meetCtrls[board_id].subscribeMeetInfo(meetObject => {
          commit('setMeetObject', meetObject)
        })
      }
    }
  },

  acceptMeetByBoardId(_, {board_id}) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id})
    return meetCtrl.acceptMeet(board_id)
  },

  declineMeetByBoardId(_, {board_id}) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id})
    return meetCtrl.declineMeet(board_id)
  },

  deleteMeetByBoardId(_, {board_id}) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id})
    return meetCtrl.deleteMeet(board_id)
  },

  deleteMeets(_, boardIds) {
    return MeetController.deleteMeets(boardIds)
  },

  deleteRecurringVendorMeet (_, { meetParam, isDeleteAll }) {
    return MeetController.deleteRecurringVendorMeet(meetParam, {
      isDeleteAll
    })
  },

  deleteRecurringMeet (_, meetParam) {
    return MeetController.deleteRecurringMeet(meetParam)
  },

  updateVendorMeetReminder (_, meetParam) {
    return MeetController.updateVendorMeetReminder(meetParam)
  },

  updateRecurringMeets (_, {meetParam, meetObjs}) {
    return MeetController.updateRecurringMeets(meetParam, meetObjs)
  },

  updateRecurringVendorMeet (_, { meetParam, isUpdateAll }) {
    return MeetController.updateRecurringVendorMeet(meetParam, {
      isUpdateAll
    })
  },

  scheduleExceptionMeet (_, {meetParam, sourceMeet}) {
    return MeetController.scheduleExceptionMeet(meetParam, sourceMeet)
  },

  deleteMeetMember(_, {board_id, userSequence }) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id})
    return meetCtrl.deleteMeetMember(board_id, userSequence)
  },
  deleteMeetTeams(_, {board_id, teamSequences }) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id})
    return meetCtrl.deleteMeetTeams(board_id, teamSequences)
  },

  removeMemberAndTeam (_, {board_id, userSeqs, teamSeqs }) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id})
    return meetCtrl.removeMemberAndTeam(userSeqs || [], teamSeqs || [])
  },

  inviteMeetMembers(_, {session_key, board_id, users, teams }) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id, session_key})
    return meetCtrl.inviteMeetMembers(board_id, users, teams)
  },
  inviteFlowBinderUserToMeeting (_, {session_key, board_id, users, flowBinderId}) {
    const meetCtrl = meetCtrls[board_id] || MeetController.getInstance({board_id, session_key})
    return meetCtrl.inviteFlowBinderUserToMeeting(board_id, flowBinderId, users)
  },

  updateMeet(_, meetParam) {
    const meetCtrl = meetCtrls[meetParam.board_id] || MeetController.getInstance({board_id: meetParam.board_id})
    return meetCtrl.updateMeet(meetParam)
  },

  scheduleMeet(_, meetParam) {
    return MeetController.scheduleMeet(meetParam)
  },

  startOfflineMeet (_, meetParam) {
    return MeetController.startOfflineMeet(meetParam)
  },

  getMeetInfoWithOutLogin(_, { meetId, password }) {
    return MeetController.getMeetInfoWithOutLogin(meetId, password)
  },

  sendServerLog(_, { meetId, place, isStart }) {
    return MeetController.sendServerLog(meetId, place, isStart)
  },

  flushMeetLog(_, meetId) {
    return MeetController.flushMeetLog(meetId)
  },

  loadRelatedMeets ({commit}, opts) {
    return MeetController.loadRelatedMeets(opts)
  },

  //meetParam needs to include vendor_service_type to tell what kind of meeting it is!!!
  deleteMeetByServiceType(_,meetParam){
    return MeetController.deleteMeetByServiceType(meetParam)
  },
  startVendorInstantMeet(_,meetParam){
    return MeetController.startVendorInstnatMeet(meetParam);
  },
  getCurrentSessionId(_) {
    return MeetController.getCurrentSessionId();
  },
  getVendorMeetAccountInfo (_, { vendorType, authId, userId }) {
    return MeetController.getVendorMeetAccountInfo(vendorType, authId, userId)
  },

  validateVendorAuthId (_, { vendorType, authId, userId }) {
    return MeetController.validateVendorAuthId(vendorType, userId, authId)
  },

  async getVendorAccountByUserId (_, { userId, vendorType }) {
    const authId = await MeetController.getVendorAuthId(vendorType, userId)
    
    if (!authId) return null

    const vendorAccountInfo = await MeetController.getVendorMeetAccountInfo(vendorType, authId, userId)

    return {
      vendorAuthInfo: {
        auth_id: authId,
        user_id: userId,
        login_account: vendorAccountInfo?.loginAccount,
      },
      vendorAccountInfo,
    }
  },

  getMeetObject (_, meet) {
    return MeetController.getInstance(meet).getMeetObject()
  },

  readMeetRecordingDetail (_, meetBoardId) {
    return MeetControllerNew.readMeetRecordingDetail(meetBoardId)
  },

  getMeetRecordingMaterials (_, meetBoardId) {
    return MeetControllerNew.getMeetRecordingMaterials(meetBoardId)
  },

  updateMeetTranscription (_, {meetBoardId, transcriptions}) {
    return MeetControllerNew.updateMeetTranscription(meetBoardId, transcriptions)
  },

  async reconnectVendorAccount (_, { userId, vendorType }) {
    const oauthAcct = await IntegrationCenterController.doOAuth(getVendorServiceOAuthName(vendorType))
    if (oauthAcct) {
      const authId = oauthAcct.auth_id
      const vendorAccountInfo = await MeetController.getVendorMeetAccountInfo(vendorType, authId, userId)
      return vendorAccountInfo
    }
    return null
  },

  resetVendorAuthId (_, authId) {
    return MeetController.resetVendorAuthId(authId)
  },
  getSummaryContent (_, {meetBoardId, summarySeq}) {
    return MeetControllerNew.downloadMeetSummary(meetBoardId, summarySeq)
  },
  updateMeetSummary (_, {meetBoardId, summaryText}) {
    return MeetControllerNew.updateMeetSummary(meetBoardId, summaryText)
  }
}
