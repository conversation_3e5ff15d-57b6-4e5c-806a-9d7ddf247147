import {UserController} from '@controller/user/src/userController'
import {getLocale, loadLanguageAsync} from '@views/i18n/mepIndex'
import {ObjectUtils} from '@commonUtils'
import {AcdController} from '@controller/acd/AcdController'
import {SRController} from '@controller/serviceRequest/src/ServiceRequestController'
import {websdkController} from '@controller/websdk/WebsdkController'
import utils from '@views/common/utils/utils'
import i18n from '@views/i18n/mepIndex'
import EventBus from '@views/common/plugins/eventBus'
import {GroupController} from '@controller/group/src/groupController'
import {createWorkspace} from '@controller/workflow';
import {UserFormatter} from '@controller/utils/user';
import {Defines, MxISDK} from 'isdk';
import cloneDeep from 'lodash/cloneDeep'

let userController
let groupController
let tempUserInfo, tempTermObj

export default {
  initUserSubscriber ({commit, dispatch, rootState, state, getters}) {
    if (!userController) {
      userController = UserController.getInstance()
      const isInternalUser = ObjectUtils.getByPath(userController, 'basicInfo.isInternalUser')
      const isInMeetTab = /\/#\/(start|join)Meet\?/.test(location.href)
      userController.subscribeUserBoards(({ userBoards, pendingMeets, activeMeets, remindMeets, pendingAcdBoard, recentClosedAcd, inboxBinderList, forceReloadMeets }) => {
        const inboxBinders = []
        if (inboxBinderList) {
          inboxBinderList.forEach(binder => {
            let tgtBidner = userBoards[binder.index]
            if (tgtBidner) {
              if (isInternalUser) {
                // append with translated suffix "(Inbox)" for inbox binder
                tgtBidner.name = tgtBidner.originalName + ` (${i18n.t('inbox')})`
              }
              inboxBinders.push(tgtBidner)
            }
          })
        }
        commit('setInboxBinders', inboxBinders)

        //feature testing for subscription channel
        if (ObjectUtils.getByPath(userController, 'groupCtrl.groupCaps.enable_channel_subscription')) {
          const subsBoards = userBoards.filter(board => (board.is_channel_subscription) || board.is_deleted)
          dispatch('subs/setSubsBoards', subsBoards, {root: true})
          if (!isInternalUser) {
            dispatch('subs/init', null, {root: true})
            dispatch('subs/initAllBinders', null, {root: true})
          }
          userBoards = userBoards.filter(board => !board.is_channel_subscription)
        }

        // feature testing for service request
        if (ObjectUtils.getByPath(userController, 'groupCtrl.groupCaps.enable_service_request')) {
          const srBoards = userBoards.filter(board => board.is_service_request || board.is_deleted)
          srBoards.map(sr => {
            if (!sr.routing_channel) {
              if (!groupController) {
                groupController = GroupController.getInstance()
              }
              sr.routing_channel = ObjectUtils.getByPath(groupController, 'getBasicInfo.srChannels.0.sequence')
            }
          })
          // client get all srboards, internal get srboards that the internal is a agent for the channel
          if (isInternalUser) {
            let srController = SRController.getInstance()
            let visibleSrChannels = srController.isAgentForSR()
            commit('sr/setSrChannels', visibleSrChannels, {root: true})
          } else {
            // Internal user will get SR boards from independent requests
            commit('sr/setSrBoards', srBoards, {root: true})
          }
          userBoards = userBoards.filter(board => !board.is_service_request)
        }

        if (websdkController.isHideInactiveRelationChat) {
          userBoards = userBoards.filter(userBoard => {
            let isNoAction = userBoard.isNoAction
            let isRelation = userBoard.is_relation
            if (isNoAction && isRelation) {
              return false
            } else {
              return true
            }
          })
        }

        state.pendingMeets = pendingMeets
        commit('setRemindMeets', remindMeets)
        state.activeMeets = activeMeets
        state.pendingAcdBoard = pendingAcdBoard
        state.recentClosedAcd = recentClosedAcd
        commit('setUserBoards', userBoards)

        if (forceReloadMeets) {
          dispatch('reloadComingMeets', true)
        }
      }, (error) => {
        console.log(`error: ${error}`)
      })
      userController.subscribeUserBasicInfo((userInfo) => {
        if (!userInfo.language) {
          userInfo.language = getLocale(true)
          userInfo.isAutoLanguage = true
        } else if (userInfo.language === 'fi') {
          userInfo.language = 'en'
        }
        if (userInfo.outOfOffice) {
          let {backup} = userInfo.outOfOffice
          if (backup && backup.id) {
            userController.getUserInfo(backup.id).then(user => {
              commit('setBackupUser', user)
            }).catch(e => {
              commit('setBackupUser', null)
            })
          } else {
            commit('setBackupUser', null)
          }
        } else {
          commit('setBackupUser', null)
        }

        if (userInfo.teams) {
          commit('setMyTeams', userInfo.teams)
          commit('group/syncMyTeamMemberInfo', userInfo.teams || [], {root: true})
        }

        if (ObjectUtils.getByPath(userController, 'groupCtrl.groupCaps.enable_service_request')) {
          dispatch('loadSRAgentAndBoard')
          if (userInfo.isInternalUser) {
            Promise.all([
              dispatch('sr/listMyOpenSRBoards', null, {root: true}),
              dispatch('sr/listMyCompletedSRBoards', null, {root: true})
            ]).finally(() => {
              dispatch('sr/subscribeMySRBoards', null, {root: true})
            })
          }
        }

        if (ObjectUtils.getByPath(userController, 'groupCtrl.groupCaps.enable_acd')
          && ObjectUtils.getByPath(userController, 'basicInfo.isInternalUser')) {

          commit('setShowAcdEntry', AcdController.isSelfAcdAgent())

        }

        let term_client = ObjectUtils.getByPath(userController, 'groupCtrl.tags.B_Term_Set_Client')
        let term_internal = ObjectUtils.getByPath(userController, 'groupCtrl.tags.B_Term_Set_Internal')
        let term_workspace = ObjectUtils.getByPath(userController, 'groupCtrl.tags.B_Term_Set_Workspace')
        let termObj = (term_client || term_internal || term_workspace) ? { term_client, term_internal, term_workspace } : null
        tempUserInfo = userInfo
        tempTermObj = termObj
        if (getters.firstTimeLoadLanguage) {
          dispatch('loadLanguage')
          commit('setfirstTimeLoadLanguage', false)
        }

        commit('setUserBasicInfo', userInfo)
      })
      userController.subscribeUserRelation((userRelations) => {
        if (isInternalUser) {
          // For internal user, new comming relation may be a suggested contact
          if (userRelations && state.userRelations && state.userRelations.length) {
            for (let relation of userRelations) {
              if (relation.is_deleted) {
                const existsRelation = state.userRelations.find(rela => rela.sequence === relation.sequence)
                existsRelation && (relation.id = existsRelation.id)
              }
            }
          }
          commit('contacts/mutateSuggestContacts', {relations: userRelations}, {root: true})
        } else {
          // For client user, should merge relations to contacts list
          commit('contacts/mergeToContactsList', userRelations, {root: true})
        }
        commit('setUserRelations', userRelations)
      })
      if (isInternalUser) {
        userController.subscribeUserNotifications(notifications => {
          commit('mutateUserNotifications', notifications)
        })
      } else {
        userController.subscribeActionItems(actionItems => {
          userController.handleDueDateChange(actionItems, state.actionItems)
          commit('setUserActionItems', actionItems)
        })
      }
      userController.subscribeMentionMes(() => {
        commit('setMentionsTimestamp', userController.mentionmes)
      })
      commit('setMentionsTimestamp', userController.mentionmes)
      if (isInternalUser) {
        dispatch('reloadComingMeets')
      }
    }
  },
  initBasicUserSubscriber ({commit, dispatch}, payload) {
    if (!userController) {
      userController = UserController.getInstance()
      userController.subscribeUserBasicInfo((userInfo) => {
        if (!userInfo.language) {
          userInfo.language = payload
          userInfo.isAutoLanguage = true
        } else if (userInfo.language === 'fi') {
          userInfo.language = 'en'
        }
        let term_client = ObjectUtils.getByPath(userController, 'groupCtrl.tags.B_Term_Set_Client')
        let term_internal = ObjectUtils.getByPath(userController, 'groupCtrl.tags.B_Term_Set_Internal')
        let term_workspace = ObjectUtils.getByPath(userController, 'groupCtrl.tags.B_Term_Set_Workspace')
        let termObj = (term_client || term_internal || term_workspace) ? { term_client, term_internal, term_workspace } : null
        loadLanguageAsync(userInfo.language, termObj).then(() => {
          EventBus.$emit('LOAD_TERM_OBJECT_READY')
          localStorage.setItem('mx:CurrentLanguage', userInfo.language)
          sessionStorage.setItem('mx:CurrentLanguage', userInfo.language)
          dispatch('profile/setLang', userInfo.language, {root: true})
        })
        commit('setUserBasicInfo', userInfo)
      })
    }
  },
  createACDBinder ({}, payload) {
    return AcdController.createChannel(payload.name, payload.sequence)
  },
  getAccessToken () {
    return userController.getAccessToken()
  },
  isInternalUser ({commit}, type) {
    // userController = UserController.getInstance()
    return userController.isInternalUser(type)
  },
  updateUserBoardAccessTime ({commit}, boardId) {
    return userController.updateUserBoardAccessTime(boardId)
  },
  updateUserNotificationsAccessTime () {
    return userController.updateUserNotificationsAccessTime()
  },
  startChat ({}, userId) {
    return userController.startChat(userId)
  },
  createRelationWithQRToken ({}, qrToken) {
    return UserController.createRelationWithQRToken(qrToken)
  },
  joinBoard ({commit}, {boardId, password, noFeed}) {
    return userController.acceptBoard(boardId, password, noFeed)
  },
  leaveBoard ({commit}, boardId) {
    return userController.declineBoard(boardId)
  },
  updatePendingInvitationRemind ({commit}, time) {
    return userController.updatePendingInvitationRemind(time)
  },
  confirmRelation ({commit}, payload) {
    return userController.confirmRelation(payload.relationSequence, payload.noFeed, payload.invitationMsg, payload.resendEmail, payload.resendSms)
  },
  resendInvitationByRM ({}, {sequence: sequence, viaEmail: viaEmail, viaPhoneNumber: viaPhoneNumber, noRelationBoard}) {
    if (viaEmail) {
      return userController.resendInvitationByRM(sequence, true, false, noRelationBoard)
    } else if (viaPhoneNumber) {
      return userController.resendInvitationByRM(sequence, false, true, noRelationBoard)
    }
  },
  createRelation ({commit}, {user, noFeed, message}) {
    return userController.createRelation(user, noFeed, message)
  },
  reactivateSocialConnection (_, {binderId, isWeChat, isWhatsapp, isLine, reason}) {
    return userController.reactivateSocialConnection(binderId, {
      isWeChat: isWeChat,
      isWhatsapp: isWhatsapp,
      isLine: isLine
    }, reason)
  },
  createSocialConnection (_, {socialType, user, isResend}) {
    return userController.createSocialConnection(socialType, user, isResend)
  },
  destroyControllers ({commit, dispatch}, fromAdmin) {
    commit('resetUserStates')

    if (!fromAdmin) {
      if (ObjectUtils.getByPath(userController, 'groupCtrl.groupCaps.enable_channel_subscription')) {
        commit('subs/resetSubsBoards', null, {root: true})
      }
      if (ObjectUtils.getByPath(userController, 'groupCtrl.groupCaps.enable_service_request')) {
        dispatch('sr/destroy', null, {root: true})
      }
    }
    if (userController) {
      userController.destroy()
      userController = null
    }
  },
  getQrToken () {
    return userController.getQrToken()
  },
  resendVerificationEmail (_, clientUserId) {
    return userController.resendVerificationEmail(clientUserId)
  },
  postAuditLog ({commit}, payload) {
    if (!userController) {
      userController = UserController.getInstance()
    }
    return userController.postAuditLog(payload.actionGroupId, payload.actionTypeId, payload.payload, payload.actionBoardId, payload.board)
  },
  openAcdRequest ({commit, state, rootState}, binderId) {
    if (state.viewToken)
      return AcdController.openAcdRequest(binderId, state.viewToken)
    else
      return AcdController.openAcdRequest(binderId)
  },
  requestAddBot ({commit, state, rootState}, binderId) {
    if (state.viewToken)
      return AcdController.requestAddBot(binderId, state.viewToken)
    else
      return AcdController.requestAddBot(binderId)
  },
  closeAcdRequest ({commit, state, rootState}, binderId) {
    if (state.viewToken)
      return AcdController.closeAcdRequest(binderId, state.viewToken)
    else
      return AcdController.closeAcdRequest(binderId)
  },
  requestWaitingAcd ({commit}, binderId) {
    return AcdController.requestWaitingAcd(binderId)
  },
  closeAllAcdRequests ({commit, state}) {
    commit('setShowAcdToast', false)
    // Safe to use state.userBoards here since ACD boards will always in the first page user boards
    const boards = state.userBoards.filter(board => board.is_acd && !board.is_deleted && !board.isACDLeaveMessage)
    if (boards && boards.length > 0) {
      return Promise.all(boards.map((board) => AcdController.closeAcdRequest(board.id)))
    }
  },
  acceptAcdRequest ({commit, state}, payload) {
    return AcdController.acceptAcdRequest(payload)
  },
  declineAcdRequest ({commit, state}, payload) {
    return AcdController.declineAcdRequest(payload)
  },
  resetPendingAcdBoard ({commit}) {
    commit('resetPendingAcdBoard')
  },
  createJWT ({commit}, payload) {
    const app_lang = i18n.locale
    return userController.createJWT(payload, app_lang)
  },
  getRelationWithQRToken ({commit}, qrtoken) {
    return userController.getRelationWithQRToken(qrtoken)
  },
  syncRelationIdentity ({commit}, identity) {
    commit('syncRelationIdentity', identity)
  },
  createPrivateConversation ({}, userId) {
    if (!userController) {
      userController = UserController.getInstance()
    }
    userController.createPrivateConversation(userId)
  },
  searchMemberBoards (_, payload) {
    const {searchKey, startIndex, pageSize, needTotalCount, searchType} = payload
    return userController.searchUserBoards({
      searchKey,
      startIndex,
      pageSize,
      needTotalCount,
      searchType: 'MEMBER_NAME',
      searchDataSourceType: searchType
    })
  },
  searchArchivedBoardsByMemberName (_, payload) {
    return userController.searchArchivedBoardsByMemberName(payload.searchKey, payload.startIndex, payload.pageSize, payload.needTotalCount)
  },
  searchNormalBoardsByMemberName (_, payload) {
    return userController.searchNormalBoardsByMemberName(payload.searchKey, payload.startIndex, payload.pageSize)
  },
  searchNormalBoardsByBoardName (_, {searchKey, startIndex, pageSize}) {
    return userController.searchNormalBoardsByBoardName(searchKey, startIndex, pageSize)
  },
  searchArchivedBoardsByBoardName (_, {searchKey, startIndex, pageSize}) {
    return userController.searchArchivedBoardsByBoardName(searchKey, startIndex, pageSize)
  },
  updateActionItemsAccessTime () {
    return userController.updateActionItemsAccessTime()
  },
  isValidActionItem (_, payload) {
    return userController.isValidActionItem(payload)
  },
  recalculateActionItems ({state, commit}) {
    const now = Date.now()
    const syncActItms = []
    if (state.preremindActionItems.length) {
      for (let i = 0; i < state.preremindActionItems.length; i++) {
        const actItm = state.preremindActionItems[i]
        if (now > actItm.pre_remind_time) {
          syncActItms.push({
            sequence: actItm.sequence,
            enabled_time: actItm.pre_remind_time
          })
          commit('removePreremindActionItem', i--)
        }
      }
      if (syncActItms.length) {
        userController.updateActionItemEnableTime(syncActItms)
      }
    }
  },
  checkBackupUser ({commit, state}) {
    let backupUserId = ObjectUtils.getByPath(state, 'userBasicInfo.outOfOffice.backup.id')
    if (!backupUserId) {
      return
    }
    userController.getUserInfo(backupUserId).then(user => {
      commit('setBackupUser', user)
    }).catch(e => {
      commit('setBackupUser', null)
    })
  },
  getMentionList ({commit}) {
    let mention = userController.getMentionList()
    commit('updateMentionList', mention)
  },
  resetMentionList ({commit}) {
    commit('resetMentionList')
    userController.resetMentionPaginationSequence()
  },
  updateUserTag (_, tag) {
    return userController.updateUserTag(tag)
  },

  createOrUpdateTag (_, {name, string_value}) {
    return userController.createOrUpdateTag(name, string_value)
  },

  loadBinderCover (_, id) {
    return userController.loadBinderCover(id)
  },

  async getPrivateChatWithUser ({state}, userId) {
    let privateChat

    const filterUserBoardTypeCondition = (uboard) => uboard?.is_relation || uboard?.isconversation
    const findPrivateChatCondition = (uboard) => {
      const boardUsers = uboard.boardUsers
      const tgtUser = boardUsers.find(user => user.id === userId && !user.is_deleted)
      if (tgtUser && (tgtUser.isClient || tgtUser.isOwner)) {
        return true
      }
      return false
    }

    privateChat = state.userBoards.filter(filterUserBoardTypeCondition).find(findPrivateChatCondition)
    if (!privateChat) {
      const sharedBoards = await userController.getSharedBinder([userId])
      privateChat = sharedBoards.filter(filterUserBoardTypeCondition).find(findPrivateChatCondition)
    }

    return privateChat
  },

  async getTransformedUserBoard ({state, dispatch}, id) {
    let targetUserBoard = state.userBoards.find(ub => ub.id === id)

    if (!targetUserBoard) {
      const relatedUserBoards = await dispatch('readUserBoards', {
        ids: [id]
      })
      targetUserBoard = relatedUserBoards[0] || {}
    }

    return targetUserBoard
  },

  readUserBoards (_, {
    ids,
    keepOriginal
  }) {
    return UserController.getInstance().readUserBoards(ids, keepOriginal)
  },

  loadSRAgentAndBoard ({commit, rootState}) {
    if (ObjectUtils.getByPath(userController, 'basicInfo.isInternalUser') && rootState.sr) {
      let srController = SRController.getInstance()
      const isSRAgent = srController.isSelfSRAgent()

      if (isSRAgent) {
        commit('sr/setShowInbox', {show: true}, {root: true})
        srController.subscribeSRBoards((srBoards) => {
          // filter out srBoards which the current user is not belong to it's agents
          let srChannels = srController.isAgentForSR()
          if (!groupController) {
            groupController = GroupController.getInstance()
          }
          let defaultSrChannelSeq = ObjectUtils.getByPath(groupController, 'getBasicInfo.srChannels.0.sequence')

          let channelArr = []
          srChannels.forEach(channel => { channelArr.push(channel.sequence) })
          let visibleSrBoards = srBoards.filter(sr => {
            if (!sr.routing_channel) {
              sr.routing_channel = defaultSrChannelSeq
            }
            return channelArr.includes(sr.routing_channel)
          })
          commit('sr/setSrChannels', srChannels, {root: true})
          commit('sr/setDispatchBoards', visibleSrBoards, {root: true})
        })
      } else {
        if (rootState.sr.showInbox) {
          commit('sr/setShowInbox', {show: false}, {root: true})
          srController.unSubscribeSRBoards()
        }
      }
    }
  },
  updateClientProfile ({}, {clientId, user}) {
    return userController.updateClientProfile(clientId, user)
  },
  updateTooltipViewTime (_, accessedTime) {
    return userController.updateTooltipViewTime(accessedTime)
  },
  removeMentionmes ({commit, dispatch}, payload) {
    let {mentionSequence, beforeTime} = payload
    return userController.removeMentionmes(mentionSequence, beforeTime).then(() => {
      if (mentionSequence) {
        commit('removeMentionItem', mentionSequence)
      }
    })
  },
  dismissActiveMeet ({}, boardId) {
    return userController.dismissActiveMeet(boardId)
  },
  reloadComingMeets ({state, commit}, force) {
    userController.reloadComingMeets(force).then(({ remindMeets, activeMeets, pendingMeets, declinedMeets, expiredMeets, upcomingMeets }) => {
      commit('setRemindMeets', remindMeets)
      commit('setDeclinedMeets', declinedMeets)
      commit('setUpcomingMeets', upcomingMeets)
      state.activeMeets = activeMeets
      state.pendingMeets = pendingMeets
      state.expiredMeets = expiredMeets
    })
  },
  updateMeetReminder ({}, {boardId, interval}) {
    return userController.updateMeetReminder(boardId, interval)
  },
  syncTimelineMeets ({state, commit, dispatch}) {
    const now = Date.now()
    const startedRemindMeets = []
    let hasRecurringExpired = false
    for (let i = 0; i < state.remindMeets.length; i++) {
      const remindM = state.remindMeets[i]
      const {scheduled_start_time} = ObjectUtils.getByPath(remindM, 'sessions.0.session')
      if (now > scheduled_start_time) {
        // remind meets should transform to ongoing status
        // remove from remind list, add to ongoing list
        startedRemindMeets.push(remindM)
      } else {
        remindM.timeLeft = scheduled_start_time - now
      }
    }
    const expiredActMeets = []
    for (let i = 0; i < state.activeMeets.length; i++) {
      const actMeet = state.activeMeets[i]
      const {session_status, scheduled_end_time, rrule} = ObjectUtils.getByPath(actMeet, 'sessions.0.session')
      if (session_status === 'SESSION_SCHEDULED') {
        if (scheduled_end_time < now) {
          expiredActMeets.push(actMeet)
          if (rrule) {
            hasRecurringExpired = true
            break
          }
        }
      }
    }
    const expiredPenMeets = []
    for (let i = 0; i < state.pendingMeets.length; i++) {
      const penMeet = state.pendingMeets[i]
      const {scheduled_end_time, rrule} = ObjectUtils.getByPath(penMeet, 'sessions.0.session')
      if (scheduled_end_time < now) {
        expiredPenMeets.push(penMeet)
        if (rrule) {
          hasRecurringExpired = true
          break
        }
      }
    }
    if (hasRecurringExpired) {
      dispatch('reloadComingMeets', true)

    } else if (startedRemindMeets.length || expiredActMeets.length || expiredPenMeets.length) {
      const {remindMeets, activeMeets, pendingMeets} = userController.syncTimelineMeets({
        startedRemindMeets: startedRemindMeets,
        expiredActiveMeets: expiredActMeets,
        expiredPendingMeets: expiredPenMeets
      })
      commit('setRemindMeets', remindMeets)
      state.activeMeets = activeMeets
      state.pendingMeets = pendingMeets
    }
  },

  updateBoardRSVPReply (_, {boardId, rsvpReply}) {
    return userController.updateBoardRSVPReply(boardId, rsvpReply)
  },

  updateBoardUserAOSM (_, {boardId, rsvpState}) {
    return userController.updateBoardUserAOSM(boardId, rsvpState)
  },

  readBoardField (_, {boardId, fieldName, sequenceArray}) {
    return userController.readBoardField(boardId, fieldName, sequenceArray)
  },

  loadLanguage ({commit, dispatch, rootState}) {
    let userInfo = tempUserInfo
    let termObj = tempTermObj
    loadLanguageAsync(userInfo.language, termObj).then(() => {
      EventBus.$emit('LOAD_TERM_OBJECT_READY')
      if (rootState.profile.language) {
        commit('group/recalculateTeamTitle', null, {root: true})
      }
      localStorage.setItem('mx:CurrentLanguage', userInfo.language)
      sessionStorage.setItem('mx:CurrentLanguage', userInfo.language)
      dispatch('profile/setLang', userInfo.language, {root: true})
    })
    commit('setUserBasicInfo', userInfo)
  },
  updateAppStatistics (_, payload) {
    return userController.updateAppStatistics(payload)
  },
  dismissUserNotifications (_, payload) {
    return userController.dismissUserNotifications(payload)
  },
  isValidNotification (_, payload) {
    return userController.isValidNotification(payload)
  },
  getRelationOr1on1ChatIdWithUserId (_, userId) {
    return userController.getRelationOr1on1ChatIdWithUserId(userId)
  },
  getUserInfo (_, userId) {
    return userController.getUserInfo(userId)
  },

  updateEmail (_, {email, verifyCode}) {
    return userController.updateEmail(email, verifyCode)
  },
  updatePhoneNumber (_, {phoneNumber, verifyCode}) {
    return userController.updatePhoneNumber(phoneNumber, verifyCode)
  },
  leaveAcdMessage ({rootGetters}, {name, description, userName, email, anonymousUser}) {
    if (!userController) {
      userController = UserController.getInstance()
    }
    let currentUser = rootGetters['user/currentUser'], phoneNumber = ''
    if (anonymousUser) {
      phoneNumber = anonymousUser.phone_number
    } else {
      phoneNumber = currentUser.phone_number || currentUser.display_phone_number
    }
    if (phoneNumber) {
      phoneNumber = utils.getFormattedPhoneNumber(phoneNumber)
    }
    return userController.leaveAcdMessageAsTodo(name, description, userName, email, phoneNumber)
  },
  copyTodoToRoutingServer ({state, rootGetters}, {boardId, todoSequence, routingChannel}) {
    let feeds = rootGetters['chat/displayFeeds'], feed
    if (feeds) {
      feed = feeds.filter(f => f.isBoardCreate)[0] || {}
    }
    //TODO
    //the name is generated in MainController.ts but it is in the top iframe level and not passed down to web iframe
    //So we are using board create feed to get the name
    //If someday the create feed suppressed, we will have a problem here.
    //The better solution will be top iframe saving the data to inner iframe
    return userController.copyTodoToRoutingServer(boardId, todoSequence, routingChannel, feed.actor)
  },
  getCurrentACDCacheBoard (_, boardId) {
    if (!userController) {
      userController = UserController.getInstance()
    }
    return userController.getCurrentACDCacheBoard(boardId)
  },
  updateUserBoardArchiveStatus (_, {isArchive, userBoardIdentities}) {
    return userController.updateUserBoardArchiveStatus(isArchive, userBoardIdentities)
  },
  updateUserBoardArchiveStatusForAll (_, {isArchive, boardId, forAllInternals}) {
    return userController.updateUserBoardArchiveStatusForAll(isArchive, boardId, forAllInternals)
  },
  readAutoArchiveUserBoards ({commit, dispatch}, {pageStart, pageSize}) {
    return userController.readAutoArchiveUserBoards(pageStart, pageSize)
      .then(({pageUserBoards, nextPageStart}) => {
        commit('updateAutoArchivedUserBoards', pageUserBoards)
        commit('filterOutActiveUbFromAutoArchivedUbs')
        if (nextPageStart !== '-1' && (pageUserBoards.length < pageSize)) {
          return dispatch('readAutoArchiveUserBoards', {pageStart: nextPageStart, pageSize})
        }
      })
  },
  pageQueryTimelineBoards ({commit, state}, {pageSize}) {
    return userController.pageQueryTimelineBoards(pageSize)
      .then(({userBoards, hasNextPage}) => {
        commit('setUserBoards', userBoards)

        if (!hasNextPage) {
          state.hasAllUserBoardsLoaded = true
        }

        return hasNextPage
      })
  },
  isAccessibleBoard (_, binderId) {
    return userController.isAccessibleBoard(binderId)
  },

  readBoard (_, {boardId, filters = [], params = []}) {
    return userController.readBoard(boardId, filters, params)
  },
  updateBoardActiveStatus (_, {boardId, isActive}) {
    return userController.updateBoardActiveStatus(boardId, isActive)
  },

  createWorkspace ({}, payload) {
    const {name, description, members} = payload
    return createWorkspace({name, description, members})
  },
  clearTourConfigIfExist ({}) {
    userController.clearTourConfigIfExist()
  },

  createTempBoardWithViewToken () {
    return userController.createTempBoardWithViewToken()
  },

  updateDefaultWorkspaceNotificationSetting ({}, notiSetting) {
    return userController.updateDefaultWorkspaceNotificationSetting(notiSetting)
  },
  //Adde by Jeffery: 04/02/2024
  //This is providing a global action to query roles by the input user's ids
  getBoardMemberRoles ({}, payload) {
    //payload = users: Defines.UserIdentity[]
    return MxISDK.getCurrentOrg().readMembers(payload).then(group => {
      return group.members && group.members.map(member => {
        return {
          roleSequence: member.role,
          isInternalUser: UserFormatter.isInternalUser(member.user.type),
          id: member.user.id
        }
      })
    })
  },
  updateWorkspacePinnedStatus ({state}, {boardId, isPinned}) {
    return userController.updateWorkspacePinnedStatus(boardId, isPinned)
  },
  isUserBoardExist (_, boardId) {
    return userController.isUserBoardExist(boardId)
  },
  updateFirstWorkspaceId (_, boardId) {
    return userController.updateFirstWorkspaceId(boardId)
  },
  deleteResources (_, resourceSeqs) {
    return userController.deleteResources(resourceSeqs)
  },
  subscribeUserResources ({state}) {
    userController?.subscribeUserResources(userResources => {
      state.userStamps = userResources.filter(resource => resource.isStamp)
    })
  },
  /* TODO START: MV-18866 - Temp code for data migration in v10.0, expected to remove it in v10.3 */
  cleanAndUpdateUserNotification ({getters}) {
    let boardNotificationLevel = void 0
    const {board_notification_level, default_notification_setting_tag} = getters.currentUser || {}
    if (default_notification_setting_tag?.sequence) {
      if (!board_notification_level) {
        switch (default_notification_setting_tag.string_value) {
          case '0':
            boardNotificationLevel = Defines.NotificationLevel.NOTIFICATION_LEVEL_ALL
            break
          case '10':
            boardNotificationLevel = Defines.NotificationLevel.NOTIFICATION_LEVEL_RELATED
            break
          case '20':
            boardNotificationLevel = Defines.NotificationLevel.NOTIFICATION_LEVEL_NOTHING
            break
        }
      }
      userController.deleteTag('default_notification_settings')
    }
    return boardNotificationLevel
  },
  /* TODO END */
  createClientGroup({}, {name, description, clients}) {
    return userController.createClientGroup(name, description, clients)
  },
  editClientGroup({}, {groupId, name, description}) {
    return userController.editClientGroup(groupId, name, description)
  }
}
