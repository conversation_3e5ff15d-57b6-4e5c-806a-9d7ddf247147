<template>
  <global-right-panel-layout :on-back="backStep">
    <template slot="header">
      <div>
        <div class="mx-text-h1">{{ team.name }}</div>
        <div class="mx-text-h2">{{ subTitle }}</div>
        <div class="divider mx-branding-color" />
      </div>
    </template>
    <template slot="content">
      <div class="search">
        <el-input
          :placeholder="$t('Search_Members')"
          prefix-icon="micon-search"
          size="small"
          v-model="searchKey"
          v-mx-debounce:keyup="searchServer" />
      </div>
      <div
        class="select-all"
        v-if="showRemovePanel && !isLoading">
        <span
          :class="selectedMembersLength > 0 ? 'mx-semibold': 'mx-color-secondary'">{{ selectedCount }}</span>
        <el-checkbox
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
          class="pull-right"
          v-if="!isLoading"
          v-model="checkAll" />
      </div>
      <div
        :class="['section-heading', {'collapse': isManagerCollapse}]"
        class="mx-clickable"
        @click="toggleMembersPanel(true)">
        <i class="micon-mep-accordion-left" />
        {{ managerSubTitle }}
        <span
          v-show="isLoading"
          v-mx-loading-spinner="isLoading"
          class="spinner" />
      </div>
      <div
        class="member-selector"
        v-show="!isManagerCollapse">
        <div v-show="showManagerSelector">
          <el-row
            justify="space-between"
            style="width: calc(100% + 10px);"
            type="flex">
            <el-col :span="22">
              <mx-user-selector
                v-if="showManagerSelector"
                ref="mxUserSelectorManager"
                :input-options="{disabled: errorTip }"
                :has-error="errorTip"
                :invited-members="actualTeamMemebrs"
                :include-suggested-user="false"
                :include-relation-user="false"
                :including-pending-user="true"
                :include-me="true"
                user-type="internal"
                @select="addMember(true, ...arguments)"
              />
            </el-col>
            <el-col
              :span="2"
              style="margin-top:4px; margin-left: 6px">
              <el-button
                @click="showManagerSelector = false"
                class="icon-wrapper">
                <i class="micon-close mx-clickable mx-color-primary mx-branding-color-fg" />
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div
          class="error-tips"
          v-if="errorTip && showManagerSelector">
          <div class="icon">
            <span class="micon-close-d3"></span>
          </div>
          <div class="info">{{ maxUserErrMessage }}</div>
        </div>
        <div
          class="assign-clients"
          v-show="!showManagerSelector">
          <el-button
            :disabled="disableSelector"
            @click="showManagerSelector = true"
            type="text"
            v-show="!showRemovePanel">
            <i class="micon-mep-plus" />
            {{ $t('add_manager') }}
          </el-button>
        </div>
        <div
          :class="['operation-results-wrapper', operationStatus.type]"
          v-if="operationStatus && operationStatus.isAdmin">
          <div class="message-tip mx-margin-top-sm">
            <div class="icon-wrapper mx-margin-right-md">
              <i
                :class="[operationStatus.type==='success' ?'micon-mep-check-mark':'micon-exclamation-mark']"
              />
            </div>
            <div class="message">
              <div class="mx-ellipsis">{{ $t(operationStatus.title) }}</div>
              <small
                class="mx-color-secondary mx-ellipsis"
                v-if="operationStatus.subtitle">{{ $t(operationStatus.subtitle) }}</small>
            </div>
            <el-button
              @click="retryOperation"
              class="try-again mx-bold"
              type="text"
              v-if="operationStatus.retry">
              {{ $t('try_again') }}
              <i class="micon-refresh" />
            </el-button>
          </div>
        </div>
        <div class="reorder-relation">
          <div class="member-list">

            <ul
              :class="{'extra-margin': showRemovePanel}"
              class="mx-admin-member-list">
              <li
                :key="member.sequence"
                v-for="(member,index) in managers">
                <mx-user-avatar
                  size="40"
                  :alt="member.displayName"
                  :user-avatar="member.avatar" />
                <div :class="['user' , {'disabled' : member.disabled}]">
                  <div class="mx-ellipsis">{{ member.displayName }}</div>
                  <small class="mx-color-secondary">{{ formatSubTitle(member) }}</small>
                </div>
                <el-checkbox
                  @change="toggleSelectUser($event,member)"
                  v-if="showRemovePanel"
                  v-model="member.__selected__" />
                <el-dropdown
                  @command="handleActionCommand($event, member, false)"
                  @visible-change="toggleDropdown($event, index)"
                  placement="bottom-end"
                  size="small"
                  trigger="click"
                  v-else>
                  <i
                    :class="['mx-clickable micon-mep-arrow-down', {'micon-mep-arrow-up':dropDownIndex===index}]"
                  />
                  <el-dropdown-menu
                    :append-to-body="false"
                    placement="bottom-end"
                    slot="dropdown"
                    transform-origin="center center">
                    <el-dropdown-item
                      class="primary"
                      :disabled="member.disabled"
                      command="changeMamber">
                      <span>{{ $t('change_to_teammate') }}</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      class="danger"
                      command="removeMember">
                      <span>{{ $t('remove_team_user') }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div
        :class="['section-heading', {'collapse': isTeammateCollapse}]"
        class="mx-clickable"
        @click="toggleMembersPanel(false)">
        <i class="micon-mep-accordion-left" />
        {{ teammateSubTitle }}
        <span
          v-show="isLoading"
          v-mx-loading-spinner="isLoading"
          class="spinner" />
      </div>
      <div
        class="member-selector"
        v-show="!isTeammateCollapse">
        <div v-show="showTeammateSelector">
          <el-row
            justify="space-between"
            style="width: calc(100% + 10px);"
            type="flex">
            <el-col :span="22">
              <mx-user-selector
                v-if="showTeammateSelector"
                ref="mxUserSelectorTeammate"
                :invited-members="actualTeamMemebrs"
                :input-options="{disabled: errorTip }"
                :has-error="errorTip"
                :include-suggested-user="false"
                :include-relation-user="false"
                :including-pending-user="true"
                :include-me="true"
                user-type="internal"
                @select="addMember(false, ...arguments)"
              />
            </el-col>
            <el-col
              :span="2"
              style="margin-top:4px; margin-left: 6px">
              <el-button
                @click="showTeammateSelector = false"
                class="icon-wrapper">
                <i class="micon-close mx-clickable mx-color-primary mx-branding-color-fg" />
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div
          class="error-tips"
          v-if="errorTip && showTeammateSelector">
          <div class="icon">
            <span class="micon-close-d3"></span>
          </div>
          <div class="info">{{ maxUserErrMessage }}</div>
        </div>
        <div
          class="assign-clients"
          v-show="!showTeammateSelector">
          <el-button
            :disabled="disableSelector"
            @click="showTeammateSelector = true"
            type="text"
            v-show="!showRemovePanel">
            <i class="micon-mep-plus" />
            {{ $t('add_teammate') }}
          </el-button>
        </div>
        <div
          :class="['operation-results-wrapper',operationStatus.type]"
          v-if="operationStatus && !operationStatus.isAdmin">
          <div class="message-tip mx-margin-top-sm">
            <div class="icon-wrapper mx-margin-right-md">
              <i
                :class="[operationStatus.type==='success' ?'micon-mep-check-mark':'micon-exclamation-mark']"
              />
            </div>
            <div class="message">
              <div class="mx-ellipsis">{{ $t(operationStatus.title) }}</div>
              <small
                class="mx-color-secondary mx-ellipsis"
                v-if="operationStatus.subtitle">{{ $t(operationStatus.subtitle) }}</small>
            </div>
            <el-button
              @click="retryOperation"
              class="try-again mx-bold"
              type="text"
              v-if="operationStatus.retry">
              {{ $t('try_again') }}
              <i class="micon-refresh" />
            </el-button>
          </div>
        </div>
        <div class="reorder-relation">
          <div :class="['member-list', {'extra-margin-bottom': showRemovePanel}]">
            <ul
              :class="{'extra-margin': showRemovePanel}"
              class="mx-admin-member-list">
              <li
                :key="member.sequence"
                v-for="(member,index) in teammates">
                <mx-user-avatar
                  size="40"
                  :alt="member.displayName"
                  :user-avatar="member.avatar" />
                <div :class="['user' , {'disabled' : member.disabled}]">
                  <div class="mx-ellipsis">{{ member.displayName }}</div>
                  <small class="mx-color-secondary">{{ formatSubTitle(member) }}</small>
                </div>
                <el-checkbox
                  @change="toggleSelectUser($event,member)"
                  v-if="showRemovePanel"
                  v-model="member.__selected__" />
                <el-dropdown
                  @command="handleActionCommand($event, member, true)"
                  @visible-change="toggleDropdown($event, index)"
                  placement="bottom-end"
                  size="small"
                  trigger="click"
                  v-else>
                  <i
                    :class="['mx-clickable micon-mep-arrow-down', {'micon-mep-arrow-up':dropDownIndex===index}]"
                  />
                  <el-dropdown-menu
                    :append-to-body="false"
                    placement="bottom-end"
                    slot="dropdown"
                    transform-origin="center center"
                  >
                    <el-dropdown-item
                      class="primary"
                      :disabled="member.disabled"
                      command="changeMamber">
                      <span>{{ $t('change_to_manager') }}</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      class="danger"
                      command="removeMember">
                      <span>{{ $t('remove_team_user') }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </template>
    <template slot="footer">
      <div
        class="transfer-panel mx-edit-profile-footer"
        v-if="showRemovePanel">
        <div class="transfer-label">{{ confirmRemoveMembers }}</div>
        <div class="mx-remove-info">{{ $t('team_member_remove_info') }}</div>
        <div class="button-wrapper">
          <el-button
            @click="cancelRemoveAction()"
            class="cancel-button"
            size="small"
            type="default">{{ $t('cancel') }}
          </el-button>
          <modal-primary-button
            :button-options="{disabled: !selectedMembersLength,type: 'danger',size: 'small'}"
            :button-text="$t('remove')"
            :call="removeSelectedMembers"
            :error-message="$t('Failed')"
            :loading-message="$t('Removing')"
            @success="onSuccess" />
        </div>
      </div>
      <div
        class="mx-view-footer"
        v-else>
        <el-button
          @click="viewTeamDetails"
          class="btn-block"
          size="medium"
          type="secondary">{{ $t('view_team_details') }}
        </el-button>
        <admin-team-dropdown
          :is-show-remove-members="true"
          :is-hide-delete="true"
          :from-panel="true"
          :team="team"
          @on-click-item="handleDropdownItem"
          size="medium" />
      </div>
    </template>
  </global-right-panel-layout>
</template>
<script>
import RightPanelMixins from '@views/common/components/admin/rightPanel';
import ModalPrimaryButton from '@views/common/components/modals/ModalPrimaryButton';
import util from '@views/common/utils/utils';
import {mapActions, mapGetters, mapMutations} from 'vuex';
import MxUserSelector from '@views/common/components/userSelector/MxUserSelector'
import MxUserAvatar from '@views/common/components/userAvatar/MxUserAvatar';
import AdminTeamDropdown from '../teamItemDropdown';
import {ObjectUtils} from '@commonUtils/object';
import mixins from './mixins/mixins';

const operations = {
  REMOVE: 'REMOVE',
  WARNING: 'WARNING',
  NA: 'NA',
  SUCCESS: 'SUCCESS'
};
export default {
  mixins: [RightPanelMixins, mixins],
  props: {
    value: {
      type: Object
    }
  },
  name: 'ViewTeams',
  components: {
    ModalPrimaryButton,
    MxUserSelector,
    MxUserAvatar,
    AdminTeamDropdown
  },
  data () {
    return {
      isLoading: true,
      searchKey: '',
      dropDownIndex: null,
      team: this.value,
      operations,
      isManagerCollapse: true,
      isTeammateCollapse: true,
      operationStatus: null,
      showTeammateSelector: false,
      showManagerSelector: false,
      currentOperation: 'NA',
      operationInProgress: false,
      multipleChecked: {}
    };
  },
  created () {
    this.loadTeamMembers();
  },
  watch: {
    operationStatus (value) {
      if (value && !value.retry) {
        setTimeout(() => {
          this.operationStatus = null;
        }, 3000);
      }
    }
  },
  computed: {
    ...mapGetters('teamMgr', ['teamMembers', 'actualTeamMemebrs', 'managers', 'teammates']),
    ...mapGetters('user', ['currentUser']),
    checkAll: {
      get() {
        const checkedCount = Object.keys(this.multipleChecked).length
        const memberChoices = this.teamMembers
        return checkedCount > 0 && checkedCount === memberChoices.length
      },
      set(newValue) {
        return newValue
      }
    },
    subTitle () {
      return this.currentOperation === operations.REMOVE
        ? this.$t('Remove_Members')
        : util.format(this.$t('members_count'), {
          number: this.actualTeamMemebrs.length
        });
    },
    selectedMembers () {
      return this.teamMembers.filter(member => member.__selected__);
    },
    isIndeterminate () {
      return (
        !!this.selectedMembers.length &&
        this.selectedMembers.length !== this.teamMembers.length
      );
    },
    selectedMembersLength () {
      return this.selectedMembers ? this.selectedMembers.length : 0;
    },
    selectedCount () {
      return this.selectedMembersLength
        ? this.selectedMembersLength + ' ' + this.$t('selected')
        : '0 ' + this.$t('selected');
    },
    confirmRemoveMembers () {
      if (this.selectedMembersLength === 1) {
        return this.$t('confirm_remove_one_memebr');
      } else if (this.selectedMembersLength === 0) {
        return this.$t('confirm_remove_memebr');
      } else {
        return util.format(this.$t('confirm_remove_multiple_members'), {
          number: this.selectedMembersLength
        });
      }
    },
    managerSubTitle () {
      return util.format(this.$t('managers_count'), {
        number: this.managers.length
      });
    },
    teammateSubTitle () {
      return util.format(this.$t('teammates_count'), {
        number: this.teammates.length
      });
    },
    disableSelector () {
      return this.currentOperation !== operations.NA;
    },
    showWarningPanel () {
      return this.currentOperation === operations.WARNING;
    },
    showRemovePanel () {
      return this.currentOperation === operations.REMOVE;
    },
    errorTip () {
      return this.actualTeamMemebrs.length >= this.currentUser.userCap.team_users_max ? true : false;
    },
    maxUserErrMessage () {
      const userCap = this.currentUser.userCap.team_users_max
      return util.format(this.$t('Member_limit_reached'), {
        totalMember: userCap,
        maxMember: userCap
      })
    }
  },
  methods: {
    ...mapActions('teamMgr', [
      'readTeamMembers',
      'removeTeamMember',
      'updateMember',
      'addTeamMembers'
    ]),
    ...mapMutations('teamMgr', ['setActualTeamMembers', 'removeFromActualTeamMembers']),
    retryOperation () {
      if (typeof this.operationStatus.retry === 'function') {
        this.operationStatus.retry(this.operationStatus.retryPayload, this.operationStatus.isAdmin);
        this.operationStatus = null;
      }
    },
    formatSubTitle (member) {
      return member.disabled ? this.$t('user_deactivated') : ObjectUtils.getByPath(member, 'subTitle');
    },
    addMember (isAdmin, member) {
      this.setActualTeamMembers([member]);
      this.addTeamMembers({
        teamId: this.team.id,
        users: [{id: member.userId}],
        isManager: isAdmin
      }).then(()=>{
        if (isAdmin) {
          this.$refs.mxUserSelectorTeammate && this.$refs.mxUserSelectorTeammate.onExternalAddUser(member)
          } else {
          this.$refs.mxUserSelectorManager && this.$refs.mxUserSelectorManager.onExternalAddUser(member)
        }
      }).catch((e) => {
        if (isAdmin) {
          this.$refs.mxUserSelectorManager && this.$refs.mxUserSelectorManager.onExternalRemoveUser(member)
          } else {
          this.$refs.mxUserSelectorTeammate && this.$refs.mxUserSelectorTeammate.onExternalRemoveUser(member)
        }
        this.removeFromActualTeamMembers(member);
        if (e.detailCode !== 'EXCEED_GROUP_USERS_MAX') {
          this.operationStatus = {
            type: 'failed',
            title: member.displayName,
            subtitle: this.formatSubTitle(member),
            retry: this.addMember,
            retryPayload: member,
            isAdmin: isAdmin
          };
        }
      });      
    },
    viewTeamDetails () {
      this.team.total_members = this.actualTeamMemebrs.length;
      this.gotoView('ViewTeamDetails');
    },
    loadTeamMembers () {
      this.readTeamMembers({
        teamId: this.team.id,
        searchKey: this.searchKey
      })
        .then(() => {
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        });
    },
    searchServer () {
      this.isLoading = true;
      this.loadTeamMembers();
    },
    handleCheckAllChange (val) {
      this.teamMembers.forEach(member => {
        this.$set(member, '__selected__', val)
        if (val) {
          this.$set(this.multipleChecked, member.userId, val)
        } else {
          this.$delete(this.multipleChecked, member.userId)
        } 
      })
    },
    toggleMembersPanel (isAdmin) {
      if (isAdmin) {
        this.isManagerCollapse = !this.isManagerCollapse;
      } else {
        this.isTeammateCollapse = !this.isTeammateCollapse;
      }
    },
    handleDropdownItem (command) {
      if (command === 'removeMembers') {
        this.showTeammateSelector = false;
        this.showManagerSelector = false;
        this.currentOperation = operations.REMOVE;
      }
    },
    handleActionCommand (command, member, isManager) {
      if (command === 'changeMamber') {
        this.updateMember({
          teamId: this.team.id,
          user: member,
          isManager: isManager
        }).then(() => {
          let userType = isManager ? 'manager' : 'teammate';
          let msg = util.format(this.$t('change_member_success_message'), {
            name: member.displayName,
            type: userType
          });

          this.$mxMessage.success(msg);
        });
      } else if (command === 'removeMember') {
        this.showTeammateSelector = false;
        this.showManagerSelector = false;
        this.$set(member, '__selected__', true);
        this.$set(this.multipleChecked, member.userId, true);
        this.currentOperation = operations.REMOVE;
      }
    },
    toggleDropdown ($event, index) {
      if ($event) {
        this.dropDownIndex = index;
      } else {
        this.dropDownIndex = null;
      }
    },
    toggleSelectUser ($event, member) {
      if ($event) {
        this.$set(this.multipleChecked, member.userId, true)
      } else {
        this.$delete(this.multipleChecked, member.userId)
      }
    },
    handleCommand (command, member) {
      if (command === 'setManager') {
        this.updateMember({teamId: this.team.id, user: member});
      }
    },
    cancelRemoveAction () {
      if (this.currentOperation !== this.operations.REMOVE) {
        this.closeDialog();
      } else {
        this.handleCheckAllChange(false);
        this.currentOperation = operations.NA;
      }
      this.dropDownIndex = null;
      this.resetMultipleChecked()
    },
    removeSelectedMembers () {
      return new Promise((resolve, reject) => {
        let requests = [];
        this.selectedMembers.forEach(member => {
          let user = {
            email: member.email,
            id: member.userId,
            unique_id: member.unique_id,
            phone_number: member.phone_number
          };
          requests.push(
            this.removeTeamMember({
              teamId: this.team.id,
              user: user
            }).catch(reject)
          );
        });
        Promise.all(requests).finally(() => {
          resolve();
        });
      });
    },
    onSuccess () {
      this.selectedMembersLength === 1
        ? this.$mxMessage.success(this.$t('one_member_successfully_removed'))
        : this.$mxMessage.success(
        util.format(this.$t('members_successfully_removed'), {
          number: this.selectedMembersLength
        })
        );

      this.currentOperation = operations.NA;
      this.dropDownIndex = null
      this.isLoading = true;
      this.resetMultipleChecked()
      this.readTeamMembers({ teamId: this.team.id }).finally(() => {
          this.isLoading = false;
        })
    },
    backStep () {
      this.team.total_members = this.actualTeamMemebrs.length;
      this.gotoView('ViewTeamDetails');
    },
    resetMultipleChecked() {
      this.multipleChecked = {}
    }
  }
};
</script>

<style
  lang="scss"
  scoped>
.search {
  padding-bottom: 30px;
}

.select-all {
  padding: 0;
  display: block;
  height: 20px;
  margin-bottom: 23px;
}

.extra-margin {
  margin-bottom: 100px;
}

.mx-admin-member-list {
  margin-top: 13px;
  margin-bottom: 22px;

  .mx-thumbnail-container {
    .mx-thumbnail {
      width: 32px;
      height: 32px;
      border: 2px solid $mx-color-gray-04;

      .mx-thumbnail-outer {
        width: 100%;
        height: 100%;
      }
    }
  }

  > li {
    display: flex;
    margin-bottom: 18px;
    align-items: center;

    .user {
      flex: 1;
      overflow: hidden;
      padding: 6px 16px;
      max-width: 275px;

      small {
        line-height: 16px;
      }
    }
  }

  .el-dropdown {
    i {
      font-size: 16px;
    }
  }

  .disabled > * {
    opacity: 0.5;
  }
}

.mx-edit-profile-footer {
  position: absolute;
  bottom: 0;
  display: flex;
  padding: 0px 75px 23px 0px;
  width: 100%;
  background: white;

  > .el-button {
    display: block;
    flex: 1 1 50%;
  }

  &.transfer-panel {
    z-index: 2;
    flex-direction: column;
    border-radius: 6px;
    width: 344px;
    padding: 0 32px 24px;
    background-color: #F7F8FA;

    .el-button {
      flex: 1 1 auto;
    }

    .el-button + .el-button {
      margin-left: 16px;
    }

    .transfer-label {
      padding-top: 17px;
      font-weight: 600;
      padding-bottom: 11px;
    }
  }

  .button-wrapper {
    display: flex;
    margin-top: 20px;

    > * {
      width: 50%;

      ::v-deep button {
        width: 100%;
      }
    }

    .cancel-button {
      margin-right: 20px;
    }
  }
}

.section-heading {
  color: rgb(0, 0, 0);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  user-select: none;

  i {
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    color: $mep-admin-branding-color;
    transform: rotate(-90deg);
    margin-top: -8px;
    margin-right: 6px;
  }
}

.collapse {
  margin-bottom: 20px;
  min-height: 26px;

  i {
    transform: rotate(180deg);
    margin-top: 0px;
    width: 12px;
    margin-right: 10px;
  }
}

.member-selector {
  margin-left: 22px;
}

.extra-margin-bottom {
  margin-bottom: 80px;
}

.error-tips {
  display: flex;
  color: $mx-color-red;
  font-weight: normal;
  line-height: 20px;

  span {
    font-size: 16px;
  }
}

.operation-results-wrapper {
  .message-tip {
    display: flex;
    justify-content: space-between;
    height: 56px;
    display: flex;
    margin-left: -46px;
    align-items: center;
    padding: 12px 17px 12px 47px;
    width: calc(100% + 70px);
    background-color: rgba(90, 98, 245, 0.1);

    .message {
      flex: 1;
      width: 175px;
    }

    .icon-wrapper {
      width: 32px;
      height: 32px;
      background-color: rgba(90, 98, 245, 0.2);
    }
  }

  &.failed {
    .message-tip {
      background-color: rgba(219, 70, 70, 0.1);

      .icon-wrapper {
        flex: 0 0 auto;
        background-color: #DB464633;
      }

      i {
        color: $mx-color-red;
      }
    }

    .try-again {
      color: $mx-color-red;
      font-size: 14px;
      flex: 1;
    }
  }
}

::v-deep .spinner > div {
  margin: 0 0 0 5px !important;
}
</style>
