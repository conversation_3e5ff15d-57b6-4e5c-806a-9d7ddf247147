<template>
  <div class="num-wrap">
    <a
        v-if="!(firstPageSelected() && hidePrevNext)"
        :class="['item', firstPageSelected() ? 'indicater-disable' : '']"
        tabindex="0"
        @click="prevPage()"
        @keyup.enter="prevPage()">
      <i
          class="micon-arrow-left-sf"
          style="font-size:16px"/>
    </a>
    <template v-for="(page,index) in pages">
      <a
          v-if="page.breakView"
          :key="index"
          :class="['item', page.disabled ? disabledClass : '']"
          tabindex="0">
        <slot name="breakViewContent">{{ breakViewText }}</slot>
      </a>
      <a
          v-else-if="page.disabled"
          :key="index"
          :class="['item', page.selected ? activeClass : '', disabledClass]"
          tabindex="0">{{ page.content }}</a>
      <a
          v-else
          :key="index"
          :class="['item', page.selected ? activeClass : '']"
          tabindex="0"
          @click="handlePageSelected(page.index + 1)"
          @keyup.enter="handlePageSelected(page.index + 1)">{{ page.content }}</a>
    </template>
    <a
        v-if="!(lastPageSelected() && hidePrevNext)"
        :class="['item', lastPageSelected() ? 'indicater-disable' : '']"
        tabindex="0"
        @click="nextPage()"
        @keyup.enter="nextPage()">
      <i
          class="micon-arrow-right-sf"
          style="font-size:16px"/>
    </a>
  </div>
</template>

<script>
export default {
  name: 'SelfPagination',
  props: {
    value: {
      type: Number,
      default: 1
    },
    branding: {
      type: Boolean,
      default: true
    },
    pageCount: {
      type: Number,
      required: true
    },
    forcePage: {
      type: Number
    },
    breakViewText: {
      type: String,
      default: '…'
    },
    disabledClass: {
      type: String,
      default: 'disabled'
    },

    hidePrevNext: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      innerValue: 1,
      activeClass: this.branding ? 'active mx-branding-background-action' : 'active active-background'
    }
  },
  computed: {
    selected: {
      get: function () {
        return this.value || this.innerValue
      },
      set: function (newValue) {
        this.innerValue = newValue
      }
    },
    pages: function () {
      let items = {}
      if (this.pageCount <= 9) {
        for (let i = 0; i < this.pageCount; i++) {
          this.setPageItem(i, items)
        }
      } else {
        if (this.selected < 4) {
          // console.log("1 2 3 4 ... 67 68 69 70")
          for (var i = 0; i < 4; i++) {
            this.setPageItem(i, items)
          }
          this.setBreakView(5, items)
          for (let i = this.pageCount - 4; i < this.pageCount; i++) {
            this.setPageItem(i, items)
          }
        } else if (this.selected > this.pageCount - 3) {
          // console.log("1 2 3 4 ... 67 68 69 70")
          // 1 2 3 4
          for (var i = 0; i < 4; i++) {
            this.setPageItem(i, items)
          }
          this.setBreakView(this.pageCount - 5, items)
          // 67 68 69 70
          for (var i = this.pageCount - 4; i < this.pageCount; i++) {
            this.setPageItem(i, items)
          }
        } else {
          // console.log("1 ... "+(position-2) +" "+(position-1) +" "+ (position) + " "+ (parseInt(position)+1) +" "+(parseInt(position)+2) + "... 70");
          // first and last
          this.setPageItem(0, items)
          this.setPageItem(this.pageCount - 1, items)
          // ...
          this.setBreakView(1, items)
          this.setBreakView(this.pageCount - 2, items)
          if (this.selected >= this.pageCount - 4) {
            // five numbers in middle
            for (var i = this.selected - 3; i < this.selected + 1; i++) {
              this.setPageItem(i, items)
            }
          } else {
            // five numbers in middle
            for (var i = this.selected - 2; i <= this.selected + 2; i++) {
              this.setPageItem(i, items)
            }
          }
        }
      }
      return items
    }
  },
  beforeUpdate() {
    if (this.forcePage === undefined) return
    if (this.forcePage !== this.selected) {
      this.selected = this.forcePage
    }
  },
  methods: {
    handlePageSelected(selected) {
      if (this.selected === selected) return
      this.innerValue = selected
      this.$emit('input', selected)
      this.$emit('clickHandler', selected)
    },
    prevPage() {
      if (this.selected <= 1) return
      this.handlePageSelected(this.selected - 1)
    },
    nextPage() {
      if (this.selected >= this.pageCount) return
      this.handlePageSelected(this.selected + 1)
    },
    firstPageSelected() {
      return this.selected === 1
    },
    lastPageSelected() {
      return (this.selected === this.pageCount) || (this.pageCount === 0)
    },
    selectFirstPage() {
      if (this.selected <= 1) return
      this.handlePageSelected(1)
    },
    selectLastPage() {
      if (this.selected >= this.pageCount) return
      this.handlePageSelected(this.pageCount)
    },

    setPageItem(index, items) {
      let page = {
        index: index,
        content: index + 1,
        selected: index === (this.selected - 1)
      }
      items[index] = page
    },
    setBreakView(index, items) {
      let breakView = {
        disabled: true,
        breakView: true
      }
      items[index] = breakView
    }
  }
}
</script>

<style lang="scss" scoped>
a {
  cursor: pointer;
}

.num-wrap {
  display: inline-block;
  background: #ffffff;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.06);
  padding: 4px;
  border-radius: 6px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  a:not(.active):hover {
    background: $mx-color-var-fill-tertiary;
  }
}

a.item {
  display: inline-block;
  color: $mx-color-var-label-secondary;
  text-align: center;
  margin: auto 4px;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  padding: 2px;
  width: 24px;
  border-radius: 3px;
  &.active {
    line-height: 20px;
    color: #ffffff;
  }

  &.active-background {
    background: $mx-color-var-branding;
  }
}

a.indicater-disable {
  color: $mx-color-var-label-quaternary;
  cursor: not-allowed;
}

</style>