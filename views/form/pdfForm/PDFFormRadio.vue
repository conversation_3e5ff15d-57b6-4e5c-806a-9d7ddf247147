<script  lang="ts">
import {defineComponent} from "@vue/composition-api";

export default defineComponent({
  name: "PDFFormRadio",
  props: {
    checked:{
      type: Boolean,
      default: false
    },
    scale:{
      type: Number,
      default: 1
    },
    padding: {
      type: Number,
      default: 2
    },
    width:{
      type: Number,
      default: 17
    },
    showBackground: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let padding = this.padding;
    const localScale =  this.width / 16
    if(!this.showBackground) {
      return {
        realWidth: this.width * this.scale, realHeight:  (this.width /localScale)  * this.scale
      }
    }
    const realWidth = Math.ceil(localScale * (16- padding *2)  * this.scale);
    const realHeight = Math.ceil(localScale * (17- padding* 2)  * this.scale);
    return {
      realWidth, realHeight
    }
  }
})
</script>

<template>
  <svg :width="realWidth" :height="realHeight" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path v-if="showBackground" d="M16 8.5C16 12.9183 12.4183 16.5 8 16.5C3.58172 16.5 0 12.9183 0 8.5C0 4.08172 3.58172 0.5 8 0.5C12.4183 0.5 16 4.08172 16 8.5Z" fill="white"/>
    <path v-if="showBackground" fill-rule="evenodd" clip-rule="evenodd" d="M8 15.5C11.866 15.5 15 12.366 15 8.5C15 4.63401 11.866 1.5 8 1.5C4.13401 1.5 1 4.63401 1 8.5C1 12.366 4.13401 15.5 8 15.5ZM8 16.5C12.4183 16.5 16 12.9183 16 8.5C16 4.08172 12.4183 0.5 8 0.5C3.58172 0.5 0 4.08172 0 8.5C0 12.9183 3.58172 16.5 8 16.5Z" fill="#C6C6C6"/>
    <circle v-if="checked" cx="8" cy="8.5" r="4" fill="#616161"/>
  </svg>

</template>

<style scoped lang="scss">

</style>