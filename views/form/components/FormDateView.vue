<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import { nameMaxLength } from '@views/common/appConst.js'
import { FormElementEventParams } from '../common/types'
import { BrowserUtils } from '@commonUtils'
import DatePicker from '@views/common/components/datePicker/DatePicker'
import TimeInput from '@views/common/components/TimeInput'
import { mapGetters } from 'vuex'
import { DateValue, FormElementDateModel } from '@model/form/defines/FormDate'
import moment from 'moment/moment'
import FormInputElementBase from '../common/FormInputElementBase'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementSupporting from "@views/form/components/FormElementSupporting.vue";
import FormProtectedAlert from "@views/form/components/FormProtectedAlert.vue";
import FormElementError from "@views/form/components/FormElementError.vue";
import FormAutoFillLabelPlacehoder from "@views/form/components/FormAutoFillLabelPlacehoder.vue";

export default defineComponent({
  name: 'FormDateView',
  components: {
    FormAutoFillLabelPlacehoder,
    FormElementError,
    FormProtectedAlert,
    FormElementSupporting,
    FormElementLabel, FormUserFocusStatusMarker, DatePicker, TimeInput },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementDateModel>,
      required: true,
      default: null
    }
  },
  data () {
    return {
      focusedField: '',
      focusedValue: '',
      modelValue: { ...this.element.value } as DateValue,
      nameMaxLength
    }
  },
  computed: {
    ...mapGetters('user', ['userTimezone']),
    isMobileView () {
      return BrowserUtils.isMobile
    },
    hours () {
      if (this.element.timeFormat === '24') {
        return new Array(23).fill().map((_, i) => (i < 9 ? `0${i + 1}` : `${i + 1}`))
      }
      return new Array(12).fill().map((_, i) => (i < 9 ? `0${i + 1}` : `${i + 1}`))
    },
    minutes () {
      return new Array(60).fill().map((_, i) => (i < 10 ? `0${i}` : `${i}`))
    },
    timePlaceholder () {
      if (this.isPreviewCompleted) {
        return ''
      }
      return '00'
    }
  },
  watch: {
    'element.value': function (val) {
      Object.keys(val).forEach(key => {

        this.modelValue[key] = val[key]
      })
    }
  },
  methods: {
    handleUserChange (currentValue, changedProperty: string, enforce: boolean) {
      switch (changedProperty) {
        case 'dateStr':
          if(currentValue) {
            /**
             * only save the formated date, We need to ensure that what the user sees is exactly what gets saved.
             */
            this.modelValue.dateStr = moment.tz(currentValue, this.userTimezone).format(this.element.dateFormat)
            /**
             *
             */
            this.modelValue.timestamp = currentValue
          }else{
            this.modelValue.dateStr = currentValue
          }
          break
      }
      this.$emit('change', {
        element: this.element,
        currentValue: this.modelValue,
        enforce,
        changedProperty
      } as FormElementEventParams)
      if (enforce) {
        this.handleUserBlur(currentValue, changedProperty, enforce)
      }
    },
    handleUserBlur (event, changedProperty: string, enforce: boolean) {
      // this.focusedField = ''
      // this.focusedValue = ''
      this.$emit('blur', {
        element: this.element,
        currentValue: this.modelValue,
        enforce,
        changedProperty
      } as FormElementEventParams)

    },
    handleUserFocus (event, changedProperty) {
      // this.focusedField = changedProperty
      // this.focusedValue = this.modelValue[changedProperty]
      this.$emit('focus', this.getEventInfo(event, changedProperty))
    },

    getEventInfo (event, changedProperty, enforce = false) {
      return {
        element: this.element,
        currentValue: this.modelValue,
        enforce,
        changedProperty
      } as FormElementEventParams
    }
  }
})
</script>

<template>
  <div>
    <div
      :class="['form-date-field-wrap', { mobile: isMobileView }]"
      :aria-readonly="isReadonly">
      <FormElementLabel :element="element"></FormElementLabel>
      <div class="form-date-field">
      <div
        v-if="element.withDate"
        style="position:relative;width:100%">
        <el-form-item
          :error="errors.dateStr"
          :show-message="false">
          <FormUserFocusStatusMarker :highlight="highlight.dateStr">
            <FormAutoFillLabelPlacehoder
                v-if="isNeedShowMask"
                :mask="true"
                :showBG="false"
                :text="modelValue.dateStr"></FormAutoFillLabelPlacehoder>
            <date-picker
              :value="modelValue.timestamp"
              :allow-select-year="true"
              :class="{ 'normal-style': false }"
              :placeholder="element.placeholder"
              :timezone="userTimezone"
              :formater="element.dateFormat"
              :disable-past-time="false"
              :calendar-clear="true"
              :disabled="isReadonly"
              @focus="handleUserFocus($event, 'dateStr')"
              @change="handleUserChange($event, 'dateStr', true)" />
          </FormUserFocusStatusMarker>
        </el-form-item>
      </div>
      <div
        v-if="element.withTime"
        class="with-time">
        <el-form-item
          :error="errors.hour"
          :show-message="false">
          <FormUserFocusStatusMarker :highlight="highlight.hour">
            <FormAutoFillLabelPlacehoder
                v-if="isNeedShowMask"
                :mask="true"
                :showBG="false"
                :text="modelValue.hour"></FormAutoFillLabelPlacehoder>
            <TimeInput
              v-model="modelValue.hour"
              :class="{ 'normal-style': false }"
              :placeholder="timePlaceholder"
              :disabled="isReadonly"
              @change="handleUserChange($event, 'hour')"
              @focus="handleUserFocus($event, 'hour')"
              @blur="handleUserBlur($event, 'hour')" />
          </FormUserFocusStatusMarker>
        </el-form-item>
        :
        <el-form-item
          :error="errors.minute"
          :show-message="false">
          <FormUserFocusStatusMarker :highlight="highlight.minute">
            <FormAutoFillLabelPlacehoder
                v-if="isNeedShowMask"
                :mask="true"
                :showBG="false"
                :text="modelValue.minute"></FormAutoFillLabelPlacehoder>

            <TimeInput
              v-model="modelValue.minute"
              :class="{ 'normal-style': false }"
              :placeholder="timePlaceholder"
              :disabled="isReadonly"
              @change="handleUserChange($event, 'minute')"
              @focus="handleUserFocus($event, 'minute')"
              @blur="handleUserBlur($event, 'minute')" />
          </FormUserFocusStatusMarker>
        </el-form-item>
        <el-form-item
          v-if="!element.enable24Hour"
          :show-message="false">
          <FormUserFocusStatusMarker :highlight="highlight.timeFormat">
            <FormAutoFillLabelPlacehoder
                v-if="isNeedShowMask"
                :mask="true"
                :showBG="false"
                layout="select"
                :text="modelValue.timeFormat"></FormAutoFillLabelPlacehoder>
            <el-select
              v-model="modelValue.timeFormat"
              :placeholder="$t('select')"
              :class="{ 'normal-style': false }"
              :disabled="isReadonly"
              @focus="handleUserFocus($event, 'timeFormat')"
              @change="handleUserChange($event, 'timeFormat', true)">
              <el-option
                :label="$t('am_value')"
                value="AM" />
              <el-option
                :label="$t('pm_value')"
                value="PM" />
            </el-select>
          </FormUserFocusStatusMarker>
        </el-form-item>
      </div>
      </div>
    </div>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>

    <FormElementSupporting v-if="element.supporting" :content="element.supporting"></FormElementSupporting>
    <FormProtectedAlert v-if="element.isProtected"></FormProtectedAlert>
  </div>
</template>

<style scoped lang="scss">
.form-date-field {
  display: flex;
  gap: 12px;
  position: relative;
  align-items: center;

  .el-form-item {
    margin-bottom: 0 !important;
  }

  .with-time {
    display: flex;
    gap: 6px;
    align-items: center;
    height: 36px;
  }

  &.mobile {
    flex-direction: column;
    margin-bottom: 5px;
  }
}

::v-deep {
  .el-form-item__error {
    position: relative;
    padding: 0;
    margin-left: 1px;
    margin-top: -5px;
  }

  .mx-date-picker {
    width: 100%;
  }

  .el-dropdown,
  .el-input {
    height: 36px;
    align-items: center;
  }
}

.normal-style {
  ::v-deep {
    .el-input.is-disabled .el-input__inner,
    .el-input__inner {
      box-shadow: none !important;
      border-color: #8a8a8a;
      color: #000;
      background-color: white;
    }

    * {
      cursor: not-allowed !important;
    }
  }

  * {
    cursor: not-allowed !important;
  }
}

.el-dropdown-menu {
  max-height: 200px;
}
</style>
