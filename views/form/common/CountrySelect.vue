<script  lang="ts">

import {defineComponent} from "@vue/composition-api";
import {getCountryList} from "@views/form/common/country";

export default defineComponent({
  name: 'CountrySelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'middle'
    },
    clearable:{
      type: Boolean,
      default: false
    },

    disabled: {
      type: Boolean,
      default: false
    },
  },
  setup(props, { emit }) {
    return {
      countryList: getCountryList({supportOther: true,extraForm: true})
    }
  },
  watch: {
    value(val){
      this.localValue = val
    }
  },
  data() {
    return {
      localValue: this.value,
    }
  },methods:{
    handleClear(){

    },
    handleFocus(event){
      this.$emit('focus', this.localValue);
    },
    onCountryChange(){
      this.$emit('input',this.localValue);
      this.$emit('change',this.localValue);
    }
  }
})
</script>

<template>
  <el-select
      v-model="localValue"
      :clearable="clearable"
      v-mx-ta="{page:'member',id:'country_code'}"
      :placeholder="placeholder"
      :disabled="disabled"
      :size="size"
      @focus="handleFocus"
      @clear="handleClear"
      @blur="$emit('blur')"
      @change="onCountryChange">
    <el-option
        v-for="country in countryList"
        :key="country.code"
        :label="country.name"
        :value="country.code">
        <span
            v-mx-ta="{page:'member',id:`country_code_${country.code}`}"
            class="country-item" >
          <span class="name">{{ country.name }}</span>
        </span>
    </el-option>
  </el-select>
</template>

<style scoped lang="scss">
.country-item {
  width: calc(100% - 20px);
  min-width: 210px;
  float: left;
  margin-left: -10px;
  display: flex;
  .number {
    margin-left: auto;
    white-space: nowrap;
  }
  .name {
    width: 180px;
    padding-left: 4px;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
::v-deep {
  input:disabled, input[disabled]{
    color: #8a8a8a;
    -webkit-opacity:1;
    opacity: 1;
  }
}
.country-disabled{
  ::v-deep {
    * {
      cursor: not-allowed!important;
    }
  }
}
</style>