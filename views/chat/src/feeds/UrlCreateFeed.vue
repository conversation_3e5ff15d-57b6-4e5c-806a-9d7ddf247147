<template>
  <div
    ref="mainFeed"
    :id="`feed${feed.sequence}`"
    tabindex="0"
    role="article"
    :aria-labelledby="`feed${feed.sequence}`"
    :class="{'mx-my-msg': isMe}"
    class="mx-hover-show mx-chat-item mx-chat-file mx-chat-page clearfix">
    <FeedHeaderIndicate :feed="feed" />
    <div class="mx-chat-image-wrap">
      <MxUserAvatar
        v-if="!isMe"
        :userAvatar="feed.actor.avatar"
        :alt="feed.actor.name"
        :class="{'mx-clickable': isAvatarClickable}"
        :tabindex="getTabindex(feed)"
        role="button"
        :aria-label="$t('view_user',{name: feed.actor.name})"
        @keypress.native.enter="viewProfile(feed)"
        @click.native="viewProfile(feed)"/>
      <div class="mx-chat-content">
        <div class="mx-chat-content-wrap">
          <ReadReceipt
            v-if="showReadStatus"
            :boardBriefViewModel="boardBriefViewModel"
            :feed="feed" />
          <div
            v-if="page.thumbnail"
            class="mx-chat-file-preview">
            <a
              v-safe-href="url"
              class="mx-page-thumbnail"
              target="_blank">
              <img
                :src="appendImageUrl(page.thumbnail)"
                :alt="page.name || ''"
                style="max-width:320px; max-height:280px; height:280px;"
                ondragstart="return false;">
            </a>
          </div>
          <div class="mx-chat-image-title">
            <span
              v-if="page.name"
              class="mx-url-name"><a
              v-safe-href="url"
              target="_blank">{{ page.name }}</a></span>
            <a
              v-if="page.description"
              v-safe-href="url"
              class="mx-url-description"
              target="_blank"><span class="mx-ellipsis-2line">{{ page.description }}</span></a>
            <span>
              <a class="mx-url" v-safe-href="url" target="_blank" @click.stop>{{ url }}</a>
            </span>
          </div>
        </div>
      </div>
      <div
        ref="action"
        class="mx-chat-action">
        <span
          v-if="!isSocialBinder && !boardBriefViewModel.isInbox"
          class="mx-chat-item-reply mx-clickable mx-hover-hide mx-icon-btn mx-branding-text-hover"
          tabindex="0"
          @keypress.enter="viewFlow(feed)"
          :aria-label="$t('reply_to_this_message')"
          @click="viewFlow(feed)">
          <i class="micon-comment">
            <span class="sr-only">{{$t('reply_to_this_message')}}</span>
          </i>
        </span>
        <ReactionSelector 
          v-if="showReactionSelectorInThreeDot" 
          :myReactions="reactionModel.myReactions" 
          @select="updateMyReaction($event)" />
        <FileDropdownAction
          v-if="showReactionSelectorInDropdown || (showOptionMenu && !isAudit)"
          ref="dropdown"
          :freezeAction="page.isConverting"
          :hideDownload="true"
          :feed="feed"
          :showReactionSelectorInDropdown="showReactionSelectorInDropdown"
          @triggerReaction="triggerReaction"
          @hideOptionMenu="showOptionMenu = false"
          @showOptionMenu="showOptionMenu = true"
          :boardBriefViewModel="boardBriefViewModel"
          :canPinFeed="canPinFeed"/>
      </div>
    </div>
    <ReactionBadges 
      v-if="reactionModel" 
      :isMe="isMe"
      :feed="feed"
      :allReactions="reactionModel.feedReactions" 
      :myReactions="reactionModel.myReactions" 
      :showReactionSelector="showReactionSelector"
      :boardBriefViewModel="boardBriefViewModel"
      queryParentWidth=".mx-chat-session-wrap"
      @select="updateMyReaction($event)" />
  </div>

</template>

<script>
  import templateFormatter from '@views/common/utils/formatter/templateFormat'
  import FileDropdownAction from '../components/FileDropdownAction'
  import propsMixin from '../mixins/props'
  import viewUserId from '../../../binderDetail/src/components/viewUserId';
  import dropdownMixin from '../mixins/dropdown'

  export default {
    name: 'UrlCreateFeed',
    mixins: [propsMixin, dropdownMixin, viewUserId],
    components: {
      FileDropdownAction
    },
    data () {
      return {
        showOptionMenu: true
      }
    },
    computed: {
      page () {
        return this.feed.baseObject || {}
      },
      linkUrl () {
        return templateFormatter.linkFormat(this.feed.relatedPage.url)
      },
      url () {
        return templateFormatter.linkParse(this.feed.relatedPage.url)
      }
    }
  }
</script>

