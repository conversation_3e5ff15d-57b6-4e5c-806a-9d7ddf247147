<template>
  <div
    v-if="isVisible"
    class="pending-invitation-banner-container"
    tabindex="0"
    role="complementary"
    aria-labelledby="invitation_banner_info"
    :class="{ 'pending': isPending, 'inactive': isInActive }">
    <div class="information-area" id="invitation_banner_info">
      <div class="status-title mx-semibold">{{ statusMsg }}</div>
      <div class="status-subtitle">{{ lastSentTime }}</div>
    </div>

    <template v-if="isWebSDK && hasResendInvitationCallback">
      <el-button
        type="primary"
        size="small"
        @click="resendCallback">
        {{ $t(resendBtnTextKey) }}
      </el-button>
    </template>

    <template v-else>

      <template v-if="toResendView">
        <transition name="fade" v-on:after-leave="onAnimationEnd">
          <el-button
            v-show="showResendBtn"
            type="primary"
            size="small"
            @click="visibleProxy = true">
            {{ $t(resendBtnTextKey) }}
          </el-button>
        </transition>
        <transition name="fade" v-on:after-leave="onAnimationEnd">
          <div class="submit-parent" v-show="showInviteSent">
            <div class="mx-submit-status__common" style="line-height: 36px;">
              <div class="mx-submit-status__common-message mx-semibold">
                {{ $t('invite_sent')}}
                <i class="micon-mep-large-checkmark"></i>
              </div>
            </div>
          </div>
        </transition>
      </template>
      
      <div v-else-if="enableSendRequestReason">
        <modal-primary-button
          ref="collectReasonBtn"
          :call="resendRequestWithReason"
          :needValidation="true"
          :succeed-message="$t('Request_sent')"
          :loading-message="$t('Sending')"
          :error-message="$t('unable_to_Send')"
          :button-text="$t(resendBtnTextKey)"
          :button-options="{size: 'small'}"
          @validate="popReasonView"
        />
      </div>
      <modal-primary-button
        v-else
        :call="resendInvitations"
        :succeed-message="$t('invite_sent')"
        :loading-message="$t('Sending')"
        :error-message="$t('Unable_to_create')"
        :button-text="$t(resendBtnTextKey)"
        :button-options="{size: 'small'}"
      />

      <base-modal
        :modal-option="{top: '72px'}"
        :visible.sync="visibleProxy"
        :modalOption="{
          'custom-class': 'invitation-request-list'
        }"
        @open="onModalOpen"
        @closed="onModalClosed">
        <template slot="title">
          {{ $t(resendBtnTextKey) }}
        </template>
        <template slot="content">
          <InvitationResendView
            :key="magicKey"
            :relation="pendingRelation"
            :cancelButtonText="$t('cancel')"
            :confirmButtonText="$t('resend')"
            @back="onBack"/>
        </template>
        <template slot="footer"/>
      </base-modal>
      <ReasonCollectForSocialResend
        v-if="showCollectReason"
        :visible.sync="showCollectReason"
        @getReason="getReason"
      ></ReasonCollectForSocialResend>
    </template>
  </div>
</template>

<script>
import ModalPrimaryButton from '@views/common/components/modals/ModalPrimaryButton'
import InvitationResendView from '@views/common/components/pendingInvitation/InvitationResendView'
import ReasonCollectForSocialResend from '@views/chat/src/components/ReasonCollectForSocialResend.vue'
import moment from 'moment-timezone'
import { Defines } from 'isdk'
import { ArrayUtils } from "@commonUtils"
import { mapGetters, mapActions } from 'vuex'
import utils from '@views/common/utils/utils'

export default {
  name: 'PendingInvitationBanner',
  components: {
    ModalPrimaryButton,
    InvitationResendView,
    ReasonCollectForSocialResend
  },
  props: {
    boardBriefViewModel: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data () {
    return {
      magicKey: 1,
      isPending: true,
      isInActive: false,
      lastSentTime: '',
      statusMsg: '',
      visibleProxy: false,
      pendingRelation: {},
      showResendBtn: true,
      showInviteSent: false,
      resendBtnTextKey: '',
      resolve: ()=>{},
      reject: ()=>{},
      showCollectReason: false,
      reasonString: '',
      pendingUser: null
    }
  },
  computed: {
    ...mapGetters('user', ['userRelations']),
    ...mapGetters('group', ['isPhoneNumberEnabled', 'isPhoneNumberPrimary', 'isEnableWhatsAppReasonToContact']),
    ...mapGetters('application', ['isWebSDK']),
    ...mapGetters('websdk', ['hasResendInvitationCallback']),
    toResendView() {
      let toResendView = false
      if (!this.pendingUser.socialType) {
        if (this.isPhoneNumberEnabled) {
          toResendView = true
        } else {
          if (this.pendingUser.phoneNum && !this.pendingUser.email) {
            toResendView = true
          }
        }
      }
      return toResendView
    },
    isVisible() {
      return !this.isWebSDK || (this.isWebSDK && this.hasResendInvitationCallback)
    },
    enableSendRequestReason(){
      return this.boardBriefViewModel.isSocial
        && this.boardBriefViewModel.socialType === Defines.SocialType.SOCIAL_TYPE_WHATSAPP
        && this.boardBriefViewModel.isSocialSuspended
        && this.isEnableWhatsAppReasonToContact
    }
  },
  watch: {
    'boardBriefViewModel.allUsers': {
      handler (updatedUsers) {
        let clientUser
        for (let i = updatedUsers.length - 1; i >=0; i--) {
          if (updatedUsers[i].isClientUser) {
            clientUser = updatedUsers[i]
            break
          }
        }
        if (clientUser) {
          if (!clientUser.isUserDeletedFromBoard) {
            if (!clientUser.isSocialSuspended) {
              this.statusMsg = this.$t('Waiting_for_client_to_join')
              this.lastSentTime = this.$t('Last_invitation_sent', {
                time: utils.formatDisplayedTime(clientUser.invitedTime, this.$t, {displayToday: true})
              })
              this.resendBtnTextKey = 'Resend_Invite'
            } else {
              this.statusMsg = this.$t('Conversation_timed_out')
              this.lastSentTime = this.$t('Ask_the_client_to_reactivate_this_conversation')
              this.resendBtnTextKey = 'send_request'
            }
          } else if (clientUser.socialType === Defines.SocialType.SOCIAL_TYPE_WECHAT) {
            this.statusMsg = this.$t('Client_unfollowd_account')
            this.lastSentTime = this.$t('Send_an_invitation_link_to_reactivate_again_for_wechat')
            this.resendBtnTextKey = 'Resend_Invite'
          } else if (clientUser.socialType === Defines.SocialType.SOCIAL_TYPE_WHATSAPP) {
            this.statusMsg = this.$t('Client_left_conversation')
            this.lastSentTime = this.$t('Send_an_invitation_link_to_reactivate_again_for_whatsapp')
            this.resendBtnTextKey = 'Resend_Invite'
          } else if (clientUser.socialType === Defines.SocialType.SOCIAL_TYPE_LINE) {
            this.statusMsg = this.$t('client_blocked_account')
            this.lastSentTime = this.$t('Send_an_invitation_link_to_reactivate_again_for_line')
            this.resendBtnTextKey = 'send_request'
          }
        }
      },
      deep: true
    }
  },
  created () {
    const boardUsers = this.boardBriefViewModel.allUsers
    for (let i = boardUsers.length - 1; i >=0; i--) {
      if (boardUsers[i].isClientUser) {
        this.pendingUser = boardUsers[i]
        break
      }
    }
    const pendingUser = this.pendingUser
    if (pendingUser) {
      if (!pendingUser.isUserDeletedFromBoard) {
        if (!pendingUser.isSocialSuspended) {
          this.statusMsg = this.$t('Waiting_for_client_to_join')
          this.lastSentTime = this.$t('Last_invitation_sent', {
            time: utils.formatDisplayedTime(pendingUser.invitedTime, this.$t, {displayToday: true})
          })
          this.resendBtnTextKey = 'Resend_Invite'
        } else {
          this.statusMsg = this.$t('Conversation_timed_out')
          this.lastSentTime = this.$t('Ask_the_client_to_reactivate_this_conversation')
          this.resendBtnTextKey = 'send_request'
        }
        this.isPending = true
        this.isInActive = false
      } else if (this.boardBriefViewModel.isSocial) {
        if (pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_WECHAT) {
          this.statusMsg = this.$t('Client_unfollowd_account')
          this.lastSentTime = this.$t('Send_an_invitation_link_to_reactivate_again_for_wechat')
          this.resendBtnTextKey = 'Resend_Invite'
        } else if (pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_WHATSAPP) {
          this.statusMsg = this.$t('Client_left_conversation')
          this.lastSentTime = this.$t('Send_an_invitation_link_to_reactivate_again_for_whatsapp')
          this.resendBtnTextKey = 'Resend_Invite'
        } else if (pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_LINE) {
          this.statusMsg = this.$t('client_blocked_account')
          this.lastSentTime = this.$t('Send_an_invitation_link_to_reactivate_again_for_line')
          this.resendBtnTextKey = 'send_request'
        }
        this.isPending = false
        this.isInActive = true
      }
    }
  },
  methods: {
    ...mapActions('user', ['resendInvitationByRM', 'createSocialConnection', 'reactivateSocialConnection']),
    ...mapActions('websdk',['triggerResendInvitationEvent']),
    resendRequestWithReason () {
      const payload = {
        binderId: this.boardBriefViewModel.boardId,
        reason: this.reasonString
      }
      if (this.pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_WECHAT) {
        payload.isWeChat = true
      } else if (this.pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_WHATSAPP) {
        payload.isWhatsapp = true
      } else if (this.pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_LINE) {
        payload.isLine = true
      }
      return this.reactivateSocialConnection(payload)
    },
    resendInvitations () {
      const vm = this
      const pendingUser = this.pendingUser

      if (!this.boardBriefViewModel.isSocial) {
        return this.resendInvitationByRM({sequence: pendingUser.relationSequence, viaEmail: true})
      } else {
        if (!this.boardBriefViewModel.isSocialSuspended) {
          const { userId:id, email } = pendingUser
          return this.createSocialConnection({
            socialType: pendingUser.socialType,
            user: { id, email },
            isResend: true
          })
        } else {
          const payload = {
            binderId: this.boardBriefViewModel.boardId
          }
          if (pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_WECHAT) {
            payload.isWeChat = true
          } else if (pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_WHATSAPP) {
            payload.isWhatsapp = true
          } else if (pendingUser.socialType === Defines.SocialType.SOCIAL_TYPE_LINE) {
            payload.isLine = true
          }
          return this.reactivateSocialConnection(payload)
        }
      }
    },
    resendCallback() {
      const {socialType, relationSequence} = this.pendingUser
      this.triggerResendInvitationEvent({chatType: socialType, relationID: relationSequence})
    },
    onModalOpen() {
      let pendingUser = ArrayUtils.find(this.boardBriefViewModel.allUsers, user => user.isClientUser)
      let pendingRelation = {
        binderId: this.boardBriefViewModel.boardId,
        name: pendingUser.name,
        id: pendingUser.userId,
        email: pendingUser.email,
        phoneNumber: pendingUser.phoneNum,
        sequence: pendingUser.relationSequence,
        invited_time: pendingUser.invitedTime,
        avatar: pendingUser.avatar
      }
      if (!pendingRelation.phoneNumber || !pendingRelation.email) {
        let relation = this.userRelations.find(relation => relation.sequence === pendingRelation.sequence)
        if (relation) {
          pendingRelation.email = relation.email
          pendingRelation.phoneNumber = relation.phone_number
        }
      }
      this.pendingRelation = pendingRelation
    },
    onModalClosed() {
      this.magicKey = this.magicKey > 0 ? -1 : 1
    },
    onBack(ret) {
      this.visibleProxy = false
      if (ret && ret.success) {
        this.showResendBtn = false
        this.nextStep().then(() => {
          this.showInviteSent = true
          setTimeout(() => {
            this.showInviteSent = false
            this.nextStep().then(() => {
              this.showResendBtn = true
            })
          }, 1250)
        })
      }
    },
    nextStep() {
      return new Promise((resolve, reject)=>{
        this.resolve = resolve
        this.reject = reject
      })
    },
    onAnimationEnd() {
      this.resolve && this.resolve()
    },
    popReasonView(){
      this.showCollectReason = true
      this.$refs.collectReasonBtn.doReset()
    },
    getReason(reason){
      this.reasonString = reason
      this.$refs.collectReasonBtn.submit('', true)
    }
  }
}
</script>

<style lang="scss" scoped>
 .invitation-request-list {
    ::v-deep .el-dialog__body {
      padding: 0;
      height: calc(100vh - 200px);
      overflow: auto;
    }
  }
  .pending-invitation-banner-container {
    display: flex;
    align-items: center;
    background-color: $mx-color-var-bg-primary;
    border-radius: 6px;
    border-left-width: 8px;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-style: solid;
    box-shadow: 0px 1px 4px 0px rgba($mx-color-var-label-primary, 0.16);
    padding:9px 16px 8px 12px;
    margin: 12px 12px 0 12px;
    height: auto;

    &.pending {
      border-color: $mx-color-var-caution;
    }

    &.inactive {
      border-color: $mx-color-var-negative;
    }

    .information-area {
      flex: 1;
      text-align: left;

      .status-title {
        font-size: 14px;
        color: $mx-color-var-label-primary;
        height: auto;
        line-height: 20px;
        margin-bottom: 3px;
      }

      .status-subtitle {
        color: $mx-color-var-label-secondary;
        font-size: 12px;
        height: auto;
        line-height: 16px;
      }

    }
    .submit-parent {
      .mx-submit-status__common {
        color: $mx-color-var-label-secondary;
        .micon-mep-large-checkmark {
          font-size: 24px;
          color: $mx-color-var-positive;
        }
      }
    }
  }
  .fade-enter-active, .fade-leave-active {
    transition-delay: .1s;
    transition-duration: .2s;
    transition-property: opacity;
  }
  .fade-enter, .fade-leave-to {
    opacity: 0;
  }

</style>


