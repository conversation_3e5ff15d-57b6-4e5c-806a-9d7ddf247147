<template>
  <div>
    configuration here
    config 1<el-checkbox></el-checkbox>
    config 2<el-checkbox></el-checkbox>
  </div>
</template>
<script>

  export default {
    name: "MxAdminConfiguration",
    components: {

    },
    data() {
      return {
        //activeName: "chat"
      };
    },
    computed: {

    },
    created() {

    },
    methods: {
      handleClick(component) {
        //this.$router.push({ name: "audit", params: { module: component.name } });
      }
    }
  };
</script>

<style lang="scss" scoped>

</style>

