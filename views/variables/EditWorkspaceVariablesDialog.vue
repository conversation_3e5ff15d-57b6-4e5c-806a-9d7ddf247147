<script>
import { useFlowWorkSpaceDetailStore } from '@views/stores/flowWorkspaceDetail'
import { mapActions as piniaMapActions, mapState as piniaMapState } from 'pinia'
import MxDialog from '../contentLibrary/component/MxDialog.vue'
import { Defines } from '@commonUtils'
import cloneDeep from 'lodash/cloneDeep'
import uuid from 'uuid/v4'
import { EWorkspaceVariableType } from '@model/workflow'

export default {
  name: 'EditWorkspaceVariablesDialog',
  components: { MxDialog },
  props: {
    value:{
      type: Array,
      default:()=>[]
    }
  },
  computed: {
    ...piniaMapState(useFlowWorkSpaceDetailStore, ['workspaceVariables']),
  },
  methods: {
    ...piniaMapActions(useFlowWorkSpaceDetailStore, ['updateWorkspaceVariables']),
    onCancel() {
      this.close()
    },
    handleNoneCase(ev, item){
      if(ev === this.mockedNoneId){
        item.value =''
      }
    },
    onSubmit() {
      this.inSubmitting = true
      this.updateWorkspaceVariables(this.variables).then(() =>{
        this.close()
        this.$mxMessage.success(this.$t('workspace_variables_saved'))
      }).catch(err=>{
        this.$mxMessage.error(this.$t('something_went_wrong_retry'))
      }).finally(() =>{
        this.inSubmitting = false
      })
    },
    close() {
      this.$emit('close')
    },
    setDisplayedVariables (items) {
      if(items?.length){
        const variables = cloneDeep(items)
        this.variables = variables.map(item => {
          if(item && item.workspaceVariableOptions){
            if(item.workspaceVariableOptions.type === this.lisType) {
              if(item.value && item.workspaceVariableOptions.options.length && !item.workspaceVariableOptions.options.includes(item.value)){
                item.value = ''
              }
            }
            return item
          }
        })
      }
    }
  },
  data () {
    return {
      variables: [],
      inSubmitting: false,
      mockedNoneId: uuid(),
      lisType: EWorkspaceVariableType.LIST,
      textType: EWorkspaceVariableType.TEXT
    }
  },
  created () {
    this.setDisplayedVariables(this.workspaceVariables)
  }
}
</script>

<template>
  <MxDialog
    @close="$emit('close')">
    <span slot='title' class="custom-dialog-title">{{$t('edit_workspace_variables')}}</span>
    <div class="variables-content">
      <ElForm label-position="top">
        <ElFormItem
          v-for="item in variables"
          :class="{ 'variables-text-input': item.workspaceVariableOptions.type === textType }"
          :key="item.sequence">
          <div slot="label">
              <div
                class="variable-label mx-ellipsis">
                <el-tooltip
                  class="mx-ellipsis"
                  popper-class="my-custom-tooltip"
                  :content="item.label"
                  effect="dark"
                  placement="top">
                  <span v-safe-text="item.label"></span>
                </el-tooltip>
              </div>

          </div>
          <ElSelect
            v-if="item.workspaceVariableOptions.type === lisType"
            :placeholder="item.workspaceVariableOptions.placeholder"
            v-model="item.value" @change="handleNoneCase($event, item)"
            clearable>
            <ElOption
              v-for="(option,index) in item.workspaceVariableOptions.options"
              :key="index"
              :value="option"
              :label="option" />
          </ElSelect>
          <ElInput
            v-else
            v-model="item.value" 
            type="textarea"
            class="variables-text-input"
            :maxlength="300"
            :placeholder="item.workspaceVariableOptions.placeholder"
            :autosize="{ minRows: 1 }" />
        </ElFormItem>
      </ElForm>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button
        type="secondary"
        :disabled="inSubmitting"
        @click="onCancel"> {{ $t('cancel') }}</el-button>
      <el-button
        type="primary"
        :disabled="inSubmitting"
        @click="onSubmit">
        {{ $t('save_changes') }}
      </el-button>
    </div>

  </MxDialog>
</template>

<style scoped lang="scss">
.variables-content{
  padding: 20px;
  min-height: 300px;
  .el-select{
    width: 100%;
  }
  overflow: auto;
}
.el-select {
  ::v-deep .el-icon-circle-close {
    font-size: 20px;
    transform: translate(8px, 0px);
  }
}
.variables-text-input {
  ::v-deep .el-form-item__content {
    line-height: initial;
  }
}
.dialog-footer{
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  flex-grow: 1;
  .el-button {
    width: 100%;
  }
}
</style>
