import { mapActions } from 'vuex'
import {ROUTER_MAP} from '@views/common/appConst';

export default {
  data(){
    return{
      BASE_OBJECT_TYPE : {
        TODO: 'TO_DO',
        ESIGN: 'E_SIGN',
        TRANSACTION: 'TRANSACTION',
        APPROVAL: 'APPROVAL',
        FILEREQUEST: 'FILE_REQUEST',
        ACKNOWLEDGE: 'ACKNOWLEDGE',
        FORMREQUEST: 'FORM_REQUEST',
        PDFFORM: 'PDF_FORM',
        DOCUSIGN: 'DOCUSIGN',
        LAUNCHWEBAPP: 'LAUNCH_WEB_APP',
        JUMIO: 'Jumio',
        MEET_REQUEST:'MEET_REQUEST',
        TODO_TRANSACTION: 'TODO_TRANSACTION',
        INTEGRATION: 'Integration',
        AWAIT: 'AWAIT',
        DECISION: 'DECISION'
      }
    }
  },

  methods: {
    isCommonTransaction (type){
      return type === this.BASE_OBJECT_TYPE['TRANSACTION'] || type === this.BASE_OBJECT_TYPE['APPROVAL'] || type === this.BASE_OBJECT_TYPE['FILEREQUEST'] || type === this.BASE_OBJECT_TYPE['ACKNOWLEDGE'] || type === this.BASE_OBJECT_TYPE['FORMREQUEST'] || type === this.BASE_OBJECT_TYPE['PDFFORM'] || type === this.BASE_OBJECT_TYPE['DOCUSIGN'] || type === this.BASE_OBJECT_TYPE['LAUNCHWEBAPP'] ||  type === this.BASE_OBJECT_TYPE['JUMIO'] ||
       type === this.BASE_OBJECT_TYPE['MEET_REQUEST'] || type === this.BASE_OBJECT_TYPE['TODO_TRANSACTION'] || type === this.BASE_OBJECT_TYPE['INTEGRATION'] || type === this.BASE_OBJECT_TYPE['DECISION'] || type === this.BASE_OBJECT_TYPE['AWAIT']
    },
    openInBinder (baseObj) {
      let queryObj = {
        tab:'chat',
        viewflow:true,
        from:'search',
        timestamp:Date.now()
      }
      if(baseObj.type === this.BASE_OBJECT_TYPE['ESIGN']){
        queryObj.signatureSequence = baseObj.sequence
        queryObj.seq = baseObj.feedSequence
      }else if(baseObj.type === this.BASE_OBJECT_TYPE['TODO']){
        if (baseObj.isACDLeaveMessage) {
          queryObj = {
            tab: 'action',
            from: 'ACD',
            seq: baseObj.sequence,
            isCompleted: !!baseObj.isCompleted
          }
        } else if (baseObj.feedSequence) {
          queryObj.seq = baseObj.feedSequence
        } else if (baseObj.sequence) {
          queryObj.todoSequence = baseObj.sequence
        }
      }else{
        if (baseObj.feedSequence) {
          queryObj.seq = baseObj.feedSequence
        } else if (baseObj.sequence) {
          queryObj.transactionSequence = baseObj.sequence
        }
      }

      let routeName = ROUTER_MAP.project.name
      this.$router.push({
        name: routeName,
        params: {
          id: baseObj.binderId
        },
        query: queryObj
      })
    },

  }
}