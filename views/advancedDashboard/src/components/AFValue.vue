<template>
  <div class="af-value-wrapper">
    <DropdownWorkspaceTypes
      v-if="lineItem.filter === 'workspace_type'"
      v-model="lineItem.value"
      :is-multiple="true"
      @change="(value) => onValueInput(value)"
    />
    <DropdownWorkspaceStatus
      v-else-if="lineItem.filter === 'workspace_status'"
      v-model="lineItem.value"
      :is-multiple="true"
      @change="(value) => onValueInput(value)"
    />
    <DropdownActionStatus
      v-else-if="lineItem.filter === 'action_status'"
      v-model="lineItem.value"
      :is-multiple="true"
      @change="(value) => onValueInput(value)"
    />
    <DropdownYesNo
      v-else-if="
        (lineItem.filter === 'external_only' ||
          lineItem.filter === 'assigned_to_me' ||
          lineItem.filter === 'owned_by_me') &&
          !conditionIsEmptyOrNotEmpty
      "
      v-model="lineItem.value"
      :is-multiple="false"
      @change="(value) => onValueInput(value)"
    />
    <DropdownWorkspaceTags
      v-else-if="
        lineItem.tag_type &&
          lineItem.tag_type === 'PROPERTY_TYPE_LIST' &&
          !conditionIsEmptyOrNotEmpty
      "
      v-model="lineItem.value"
      :is-multiple="true"
      :filter="lineItem.filter"
      @change="(value) => onValueInput(value)"
    />
    <DropdownTeams
      v-else-if="lineItem.filter === 'team_id'"
      :value="lineItem.value"
      :is-multiple="false"
      @change="(value) => onValueInput(value)"
    />
    <DropdownWorkspaceOwners
      v-else-if="lineItem.filter === 'workspace_owner_id'"
      v-model="lineItem.value"
      :is-multiple="false"
      @change="(value) => handleWorkspaceOwnerChange(value)"
    />
    <DropdownWorkspaceMembers
      v-else-if="lineItem.filter === 'workspace_member_id'"
      v-model="lineItem.value"
      :is-multiple="true"
      @change="(value) => onValueInput(value)"
    />
    <DropdownWorkspaceMembers
      v-else-if="lineItem.filter === 'assigned_to'"
      v-model="lineItem.value"
      :is-multiple="true"
      @change="(value) => onValueInput(value)"
    />
    <DropdownTemplates
      v-else-if="lineItem.filter === 'template_id' && isDropDown && !conditionIsEmptyOrNotEmpty"
      v-model="lineItem.value"
      @change="(value) => onValueInput(value)"
    />
    <DropdownMilestones
      v-else-if="
        lineItem.filter === 'template_milestone_seq' &&
          !conditionIsEmptyOrNotEmpty &&
          getHasMilestone
      "
      @onChange="(value) => onValueInput(value)"
      :lineItem="lineItem"
      v-model="lineItem.value"
    />

    <DropdownTemplateSteps
      v-else-if="
        lineItem.filter === 'template_step_seq' &&
          !conditionIsEmptyOrNotEmpty &&
          (getHasStep || !getHasMilestone)
      "
      v-model="lineItem.value"
      :is-multiple="true"
      @onChange="(value) => onValueInput(value)"
    />

    <DropdownActionTypes
      v-else-if="lineItem.filter === 'action_type'"
      v-model="lineItem.value"
      :is-multiple="true"
      :type="'ACTION_TYPES'"
    />

    <div
      class="currency-selector"
      v-else-if="lineItem.type === 'days' && !conditionIsEmptyOrNotEmpty"
    >
      <MxPreciseNumber
        width="100%"
        v-model="daysInputValue"
        :disable-auto-update="true"
        :only-pad="true"
        :prevent-invalid-char="true"
        :only-truncate="true"
        :inputNumberSize="3"
        :max="180"
        :min="1"
        @input="onDaysInput"
        :suffixText="$t('days')"
      >
      </MxPreciseNumber>
    </div>

    <div v-else-if="lineItem.type === 'date'">
      <DateFilter :lineItem="lineItem" @change="onDateSelection" />
    </div>
    <el-input
      v-else-if="
        lineItem.type === 'text' ||
          lineItem.tag_type == 'PROPERTY_TYPE_TEXT' ||
          conditionIsEmptyOrNotEmpty
      "
      :placeholder="$t('Value')"
      :clearable="textInputValue ? true : false"
      :disabled="conditionIsEmptyOrNotEmpty"
      v-model="textInputValue"
      @input="onTextInput"
    ></el-input>

    <el-select
      v-else
      v-model="initialDropdown"
      :placeholder="$t('Value')"
      class="default-initial-selector"
      :loading-text="$t('choose_with_ellipsis')"
      :no-data-text="$t('choose_with_ellipsis')"
      :no-match-text="$t('choose_with_ellipsis')"
    >
    </el-select>
  </div>
</template>
<script>
import DropdownWorkspaceTypes from '@views/advancedDashboard/src/components/DropdownWorkspaceTypes.vue'
import DropdownWorkspaceStatus from '@views/advancedDashboard/src/components/DropdownWorkspaceStatus.vue'
import DropdownActionStatus from '@views/advancedDashboard/src/components/DropdownActionStatus.vue'
import DropdownYesNo from '@views/advancedDashboard/src/components/DropdownYesNo.vue'
import DropdownWorkspaceTags from '@views/advancedDashboard/src/components/DropdownWorkspaceTags.vue'
import DropdownTeams from '@views/advancedDashboard/src/components/DropdownTeams.vue'
import DropdownWorkspaceOwners from '@views/advancedDashboard/src/components/DropdownWorkspaceOwners.vue'
import DropdownWorkspaceMembers from '@views/advancedDashboard/src/components/DropdownWorkspaceMembers.vue'
import DropdownTemplates from '@views/advancedDashboard/src/components/DropdownTemplates.vue'
import DropdownTemplateSteps from '@views/advancedDashboard/src/components/DropdownTemplateSteps.vue'
import DropdownMilestones from '@views/advancedDashboard/src/components/DropdownMilestones.vue'
import DropdownActionTypes from '@views/advancedDashboard/src/components/DropdownActionTypes.vue'

import DateFilter from '@views/advancedDashboard/src/components/DateFilter'
import MxPreciseNumber from '@views/common/components/input/MxPreciseNumber'

import { mapGetters } from 'vuex'
export default {
  name: 'AFValue',
  components: {
    DropdownWorkspaceTypes,
    DropdownTemplates,
    DropdownTemplateSteps,
    DropdownMilestones,
    DropdownYesNo,
    DropdownTeams,
    DropdownWorkspaceTags,
    DropdownActionTypes,
    DateFilter,
    MxPreciseNumber,
    DropdownWorkspaceStatus,
    DropdownActionStatus,
    DropdownWorkspaceOwners,
    DropdownWorkspaceMembers
  },
  props: {
    lineItem: {
      type: Object,
      default: () => {
        {
        }
      }
    },
    completeLineItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      initialDropdown: '',
      lineItemData: {},
      textInputValue: '',
      daysInputValue: ''
    }
  },
  computed: {
    ...mapGetters('advancedDashboard', ['getHasMilestone', 'getHasStep']),
    isDropDown() {
      return this.lineItem.type === 'dropdown' || this.lineItem.tag_type == 'PROPERTY_TYPE_LIST'
    },
    conditionIsEmptyOrNotEmpty() {
      return this.lineItem.condition === 'is_empty' || this.lineItem.condition === 'is_not_empty'
    }
  },
  watch: {
    conditionIsEmptyOrNotEmpty(newValue) {
      if (newValue) {
        this.lineItem.value = ''
        this.textInputValue = ''
      }
    }
  },
  created() {
    this.lineItemData = this.lineItem

    if (this.lineItem.type === 'text') {
      this.textInputValue = this.lineItem.value
    }

    if (this.lineItem.type === 'days') {
      this.daysInputValue = this.lineItem.value
    }
    if (this.lineItem.type === 'workspace_tags') {
      this.textInputValue = this.lineItem.value
    }
  },
  methods: {
    handleWorkspaceOwnerChange(value) {
      this.lineItem.value = value
      this.$emit('handleWorkspaceOwnerChange', value)
    },
    onValueInput(value) {
      if (this.lineItem.filter === 'template_id') {
        const stepSeqItem = this.completeLineItems.find(item => item.filter === 'template_step_seq');
        if (stepSeqItem) stepSeqItem.value = '';
      }

      this.lineItem.value = value
    },
    onDaysInput() {
      this.lineItem.value = this.daysInputValue
    },
    onTextInput() {
      this.lineItem.value = this.textInputValue
    },
    onDateSelection(date) {
      this.lineItem.value = date
    }
  }
}
</script>
<style lang="scss" scoped>
.mx-date-range-btn {
  border: 1px solid #8a8a8a;
}

.mx-date-range-btn:hover {
  border: 2px solid #8a8a8a;
}

::v-deep {
  .el-input__suffix {
    right: 8px;
  }

  .el-select-dropdown__empty {
    padding: 10px 10px !important;
    text-align: left !important;
  }
}

.currency-selector {
  ::v-deep {
    .el-input-number {
      width: 100%;
    }

    .el-input-number .el-input__inner {
      text-align: left !important;
      padding-left: 10px !important;
    }

    .el-input--suffix .el-input__inner:hover,
    .el-input--suffix .el-input__inner:focus {
      padding-right: 40px !important;
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }
  }
}

.default-initial-selector {
  width: 100%;
}

.af-value-wrapper {
  width: 100%;
}
</style>
