<template>
  <div class="advanced-filter-holder filter-container-box" ref="dropdownContent">
    <div class="header-section">
      <div class="header-first-section mx-text-c1">
        {{ $t('advanced_filters') }}
      </div>
      <div class="header-second-section mx-text-c1">
        <el-button
          :class="{
            'mx-branding-text': isResetEnabled,
            'mx-gray-color': !isResetEnabled
          }"
          type="text"
          @click="handleClearAllFilter"
          :disabled="!isResetEnabled"
        >
          <span>{{ $t('reset') }}</span>
        </el-button>

        <div class="breaker"></div>

        <el-button
          :class="{
            'mx-branding-text': !isApplyDisabled,
            'mx-gray-color': isApplyDisabled
          }"
          :disabled="isApplyDisabled"
          type="text"
          @click="handleApplyFilter"
        >
          <span>{{ $t('apply') }}</span>
        </el-button>
      </div>
    </div>
    <div class="additional-row-container">
      <div class="content-row-item-new">
        <div class="content-left-section-phase3-filters mx-text-c2">
          {{ $t('date_range') }}
        </div>
        <div class="content-right-section-phase3-filters">
          <FilterDateRange
            :value="advancedFilter.date_range"
            :lineItem="dateFilterLineItem"
            @change="onDateSelection"
          />
        </div>
      </div>
      <div v-if="currentTabConfig.view_type === 'MANAGER'" class="content-row-item-new">
        <div class="content-left-section-phase3-filters mx-text-c2">
          {{ $t('team') }}
        </div>
        <div class="content-right-section-phase3-filters">
          <DropdownTeams :value="advancedFilter.team_id" @change="onTeamChange" />
        </div>
      </div>
    </div>
    <div v-if="advancedFilter && advancedFilter.line_items" class="content-section">
      <div
        class="row-container-grid"
        v-for="(item, index) in advancedFilter.line_items"
        :key="`line-item-${index}`"
      >
        <div class="row-element-1" style="width: 100%;">
          <span v-if="index == 0">{{ $t('where') }}</span>
          <span v-if="index == 1" style="width: 100%;">
            <el-select
              v-model="advancedFilter.operator"
              :placeholder="$t('Select')"
              class="dropdown-element"
              :popper-append-to-body="true"
            >
              <el-option
                v-for="advitem in advancedFilterOperator"
                :key="advitem.value"
                :label="advitem.label"
                :value="advitem.value"
                class="dropdown-option-list"
              >
                <div class="team-row-item">
                  <span style="float: left" class="label-text">{{ advitem.label }}</span>
                </div>
              </el-option>
            </el-select>
          </span>
          <span v-if="index > 1" class="capitalize-css">{{
            advancedFilter.operator.toLowerCase()
          }}</span>
        </div>
        <div class="row-element-2" style="width: 100%;">
          <AFFilter :line-item="item" :key="index" :completeLineItems="advancedFilter.line_items" />
        </div>
        <div class="row-element-3" style="width: 100%;">
          <AFCondition
            :line-item="item"
            :key="item.filter"
            :completeLineItems="advancedFilter.line_items"
          />
        </div>
        <div class="row-element-4" style="width: 240px;">
          <AFValue
            :line-item="item"
            :key="item.filter"
            :completeLineItems="advancedFilter.line_items"
            @handleWorkspaceOwnerChange="handleWorkspaceOwnerChange"
          />
        </div>
        <div class="row-element-5" style="width: 100%;">
          <i
            :title="advancedFilter.line_items.length > 1 ? $t('remove_this_filter') : ''"
            class="micon-close mx-clickable clear-filter-icon"
            :class="advancedFilter.line_items.length <= 1 ? 'disabled-icon' : ''"
            @click="advancedFilter.line_items.length > 1 ? removeLineItem(index) : null"
          ></i>
        </div>
      </div>
      <div class="row-container">
        <el-button
          class="mx-branding-text"
          :disabled="hasEmptyObject || advancedFilter.line_items.length >= 10"
          size="small"
          type="text"
          @click="addNewFilter"
        >
          <el-tooltip
            v-if="advancedFilter.line_items.length >= 10"
            popper-class="my-custom-tooltip"
            :content="$t('maximum_10_filters')"
            style="max-width: fit-content;"
            effect="dark"
            class="mx-ellipsis"
            placement="top"
          >
            <span class="new-advanced-filter">
              <i class="micon-plus-thick new-advanced-filter-icon"></i>
              {{ $t('new_filter') }}
            </span>
          </el-tooltip>
          <span v-else class="new-advanced-filter">
            <i class="micon-plus-thick new-advanced-filter-icon"></i>
            {{ $t('new_filter') }}
          </span>
        </el-button>
      </div>
    </div>
    <div class="footer-section">
      <div class="footer-first-section"></div>
      <div class="footer-second-section mx-text-c1">
        <el-button
          :brandingText="true"
          type="text"
          @click="processFilterTypeHandle"
          class="switch-filters-btn"
        >
          <span class="mx-ellipsis">{{ $t('switch_to_quick_filters') }}</span>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions, mapMutations, mapState } from 'vuex'
import AFFilter from '@views/advancedDashboard/src/components/AFFilter.vue'
import AFCondition from '@views/advancedDashboard/src/components/AFCondition.vue'
import AFValue from '@views/advancedDashboard/src/components/AFValue.vue'
import FilterDateRange from '@views/advancedDashboard/src/components/FilterDateRange.vue'
import DropdownTeams from '@views/advancedDashboard/src/components/DropdownTeams.vue'
import { ObjectUtils } from '@controller/utils'
import moment from 'moment-timezone'

export default {
  name: 'AdvancedFilters',
  components: {
    AFFilter,
    AFCondition,
    AFValue,
    FilterDateRange,
    DropdownTeams
  },
  data() {
    return {
      dateFilterLineItem: {
        condition: 'is_between',
        value: ''
      },
      advancedFilterOperator: [
        {
          label: 'And',
          value: 'AND'
        },
        {
          label: 'Or',
          value: 'OR'
        }
      ]
    }
  },
  computed: {
    ...mapGetters('advancedDashboard', [
      'getDashboardType',
      'currentTabConfig',
      'getSavedWorkspaceConfigs',
      'getSavedActionConfigs',
      'getHasMilestone',
      'getHasStep'
    ]),
    ...mapState('advancedDashboard', ['inProgressFilterConfig', 'advancedFilter', 'quickFilter']),
    ...mapGetters('group', ['groupBasicInfo']),
    ...mapGetters('user', ['currentUser', 'accessibleTeams']),
    hasEmptyObject() {
      return false
    },
    isResetEnabled() {
      let returnValue = false
      const lastYearStartMs = moment()
        .subtract(12, 'months')
        .add(1, 'day')
        .startOf('day')
        .valueOf()
      const nowDateTime = moment()
        .endOf('day')
        .valueOf()
      const date_range = lastYearStartMs + '-' + nowDateTime

      const defaultTeamID =
        this.currentTabConfig?.view_type == 'MANAGER' ? this.teamList[0]?.value || '' : ''

      let savedConfig = {}
      if (this.getDashboardType === 'advancedWorkspaceReport') {
        savedConfig = this.getSavedWorkspaceConfigs.find(
          (t) => t.view_seq === this.currentTabConfig.view_seq
        )
      } else {
        savedConfig = this.getSavedActionConfigs.find(
          (t) => t.view_seq === this.currentTabConfig.view_seq
        )
      }

      // Early return if savedConfig is not found
      if (!savedConfig) {
        return returnValue
      }

      if (this.currentTabConfig.view_seq === 'mepx_dashboard_default_tab') {
        if (this.advancedFilter.date_range != date_range) {
          returnValue = true
        }

        if (this.advancedFilter.team_id != defaultTeamID) {
          returnValue = true
        }
      } else {
        if (this.advancedFilter.date_range != savedConfig.date_range) {
          returnValue = true
        }

        if (this.advancedFilter.team_id != savedConfig.team_id) {
          returnValue = true
        }
      }

      // Deep comparison for line_items array
      const lineItems = this.advancedFilter?.line_items.map((item) => {
        const newItem = { ...item }
        if (typeof newItem.value === 'string') {
          newItem.value = newItem.value
            .split(',')
            .sort((a, b) => a.localeCompare(b))
            .join(',')
        }
        return {
          newItem
        }
      })
      const savedItems = savedConfig?.filter_config?.advanced_params?.line_items.map(
        (item) => {
          const newItem = { ...item }
          if (typeof newItem.value === 'string') {
            newItem.value = newItem.value
              .split(',')
              .sort((a, b) => a.localeCompare(b))
              .join(',')
          }
          return {
            newItem
          }
        }
      )
      
      const areEqual = ObjectUtils.isEqual(lineItems, savedItems)

      if (!areEqual) {
        returnValue = true
      }

      return returnValue
    },
    teamList() {
      return this.accessibleTeams
        .filter((team) => team.isMember && team.isManager)
        .map((team) => ({
          ...team,
          avatar: team.avatar || '',
          title: team.name,
          value: team.id
        }))
    },
    isApplyDisabled() {
      const lineItems = this.advancedFilter?.line_items.map((item) => {
        const newItem = { ...item }
        if (typeof newItem.value === 'string') {
          newItem.value = newItem.value
            .split(',')
            .sort((a, b) => a.localeCompare(b))
            .join(',')
        }
        return {
          newItem
        }
      })
      const savedItems = this.currentTabConfig?.filter_config?.advanced_params?.line_items.map(
        (item) => {
          const newItem = { ...item }
          if (typeof newItem.value === 'string') {
            newItem.value = newItem.value
              .split(',')
              .sort((a, b) => a.localeCompare(b))
              .join(',')
          }
          return {
            newItem
          }
        }
      )
      let areEqual = ObjectUtils.isEqual(lineItems, savedItems)
      let hasValue = true
      if (this.advancedFilter?.line_items?.length === 1) {
        hasValue = this.advancedFilter?.line_items?.every(({ filter, condition, value }) => {
          if (!filter && !condition && !value) {
            return false
          } else if (!filter || !condition) return true
          const isEmptyCondition = ['is_empty', 'is_not_empty'].includes(condition)
          return isEmptyCondition ? !!value : !value
        })
      } else {
        hasValue = this.advancedFilter?.line_items?.some(({ filter, condition, value }) => {
          if (!filter || !condition) return true
          const isEmptyCondition = ['is_empty', 'is_not_empty'].includes(condition)
          return isEmptyCondition ? !!value : !value
        })
      }
      if (
        this.currentTabConfig?.date_range !== this.advancedFilter.date_range ||
        (this.currentTabConfig?.team_id ?? '') !== (this.advancedFilter?.team_id ?? '')
      ) {
        areEqual = false
      }

      if (
        this.advancedFilter.operator !=
        this.currentTabConfig?.filter_config?.advanced_params?.operator
      ) {
        areEqual = false
      }

      return areEqual || hasValue
    }
  },
  watch: {
    getHasMilestone(val) {
      if (!val) {
        const count = this.advancedFilter.line_items.filter(
          (item) => item.filter === 'template_milestone_seq'
        ).length
        if (count > 0) {
          for (let i = 0; i < count; i++) {
            const index = this.advancedFilter.line_items.findIndex(
              (item) => item.filter === 'template_milestone_seq'
            )
            if (index >= 0) {
              this.advancedFilter.line_items.splice(index, 1)
            }
          }
        }
      }
    },
    getHasStep(val) {
      if (!val) {
        const count = this.advancedFilter.line_items.filter(
          (item) => item.filter === 'template_step_seq'
        ).length
        if (count > 0) {
          for (let i = 0; i < count; i++) {
            const index = this.advancedFilter.line_items.findIndex(
              (item) => item.filter === 'template_step_seq'
            )
            if (index >= 0) {
              this.advancedFilter.line_items.splice(index, 1)
            }
          }
        }
      }
    }
  },
  created() {
    this.renderFilterData()
  },
  methods: {
    ...mapActions('advancedDashboard', ['fetchWorkspaces', 'fetchActions', 'searchOwners']),
    ...mapMutations('advancedDashboard', [
      'setCurrentPage',
      'setCachedFilterConfig',
      'setInProgressFilterConfig',
      'setHasMilestone',
      'setHasStep',
      'setAdvancedFilter',
      'resetFilters'
    ]),
    compareLineItems(current, saved) {
      // If both are null/undefined, they're equal
      if (!current && !saved) return true

      // If one is null/undefined and other isn't, they're different
      if (!current || !saved) return false

      // If lengths are different, they're different
      if (current.length !== saved.length) return false

      // Compare each item in the arrays
      for (let i = 0; i < current.length; i++) {
        const currentItem = current[i]
        const savedItem = saved[i]

        // Compare each property of the line item
        if (
          currentItem.filter !== savedItem.filter ||
          currentItem.condition !== savedItem.condition ||
          currentItem.tag_type !== savedItem.tag_type ||
          currentItem.type !== savedItem.type ||
          currentItem.value !== savedItem.value
        ) {
          return false
        }
      }

      return true
    },
    renderFilterData() {
      this.resetFilters()

      // When open Advance Filter
      let filter = JSON.parse(JSON.stringify(this.currentTabConfig.filter_config.advanced_params))
      this.advancedFilter.date_range = this.currentTabConfig.date_range
      filter.date_range = this.currentTabConfig.date_range
      if (this.currentTabConfig.view_type === 'MANAGER') {
        this.advancedFilter.team_id = this.currentTabConfig.team_id
        filter.team_id = this.currentTabConfig.team_id
      }
      // When open Advance Filter

      // When switch to Advance Filter
      if (this.inProgressFilterConfig.advancedParams) {
        filter = this.inProgressFilterConfig.advancedParams
      }
      // When switch to Advance Filter

      this.setAdvancedFilter(JSON.parse(JSON.stringify(filter)))
    },
    onTeamChange(team_id) {
      if (team_id) {
        if (this.currentTabConfig.view_type === 'MANAGER') {
          const owner = this.advancedFilter.line_items.find(
            (item) => item.filter === 'workspace_owner_id'
          )
          const member = this.advancedFilter.line_items.find(
            (item) => item.filter === 'workspace_member_id'
          )
          if (owner) 
          {
            owner.value = ''
            if (member) member.value = ''
          }
        }

        this.quickFilter.team_id = team_id
        this.advancedFilter.team_id = team_id
        this.searchOwners({ searchKey: '', isLoadMore: false, teamId: team_id })
      }
    },
    removeLineItem(lineIndex) {
      const removedItem = this.advancedFilter.line_items.find((item, index) => index === lineIndex)
      this.advancedFilter.line_items = this.advancedFilter.line_items.filter(
        (item, index) => index !== lineIndex
      )
      if (removedItem.filter === 'template_id') {
        this.setHasMilestone(false)
        this.setHasStep(false)
      }
      this.$nextTick(() => {
        if (this.advancedFilter.line_items.length == 0) {
          this.advancedFilter.line_items = [
            {
              filter: '',
              condition: '',
              value: '',
              tag_type: '',
              type: ''
            }
          ]
        }
      })
    },
    handleWorkspaceOwnerChange(value) {
      const ownerItem = this.advancedFilter.line_items.find(
        (item) => item.filter === 'workspace_owner_id'
      )
      const workspace_owner_id = ownerItem?.value

      if (this.currentTabConfig.view_type === 'MANAGER' && !workspace_owner_id) {
        for (const key of ['workspace_member_id', 'assigned_to']) {
          const item = this.advancedFilter.line_items.find((i) => i.filter === key)
          if (item) item.value = ''
        }
      }
    },
    onDateSelection(date) {
      this.advancedFilter.date_range = date
    },
    addNewFilter(event) {
      if (this.advancedFilter.line_items.length >= 10) {
        return
      }

      if (event) {
        event.stopPropagation()
        event.preventDefault()
      }

      const newFilter = {
        filter: '',
        condition: '',
        value: '',
        tag_type: '',
        type: ''
      }
      this.advancedFilter.line_items.push(newFilter)
    },
    handleClearAllFilter() {
      const lastYearStartMs = moment()
        .subtract(12, 'months')
        .add(1, 'day')
        .startOf('day')
        .valueOf()
      const nowDateTime = moment()
        .endOf('day')
        .valueOf()
      const date_range = lastYearStartMs + '-' + nowDateTime

      const defaultTeamID =
        this.currentTabConfig?.view_type == 'MANAGER' ? this.teamList[0]?.value || '' : ''

      this.advancedFilter.line_items = [
        {
          filter: '',
          condition: '',
          value: '',
          tag_type: '',
          type: ''
        }
      ]

      let savedConfig = {}
      if (this.getDashboardType === 'advancedWorkspaceReport') {
        savedConfig = this.getSavedWorkspaceConfigs.find(
          (t) => t.view_seq === this.currentTabConfig.view_seq
        )
      } else {
        savedConfig = this.getSavedActionConfigs.find(
          (t) => t.view_seq === this.currentTabConfig.view_seq
        )
      }

      if (this.currentTabConfig.view_seq === 'mepx_dashboard_default_tab') {
        this.advancedFilter.date_range = date_range
        this.advancedFilter.team_id = defaultTeamID
      } else {
        this.advancedFilter.date_range = savedConfig.date_range
        this.advancedFilter.team_id = savedConfig.team_id
      }

      this.advancedFilter.line_items = JSON.parse(
        JSON.stringify(savedConfig?.filter_config?.advanced_params?.line_items)
      )
    },
    processFilterTypeHandle() {
      const quickParams = []
      const validFilters = [
        'workspace_status',
        'action_status',
        'workspace_type',
        'external_only',
        'assigned_to_me',
        'owned_by_me',
        'workspace_owner_id',
        'workspace_member_id',
        'assigned_to',
        'template_id',
        'template_milestone_seq',
        'template_step_seq',
        'last_activity',
        'action_type',
        'start_date',
        'overdue'
      ]

      const possibleValues = {
        workspace_status: ['open', 'due_today', 'overdue', 'completed', 'cancelled'],
        action_status: [
          'open',
          'due_today',
          'due_tomorrow',
          'due_in_7_days',
          'overdue',
          'completed'
        ],
        workspace_type: ['DIRECT_BINDER', 'GROUP_BINDER', 'FLOW_BINDER']
      }

      this.advancedFilter.line_items
        .filter((item) => validFilters.includes(item.filter))
        .forEach((item) => {
          let value = item.value

          if (item.condition === 'is_not' && possibleValues[item.filter]) {
            const selectedValues = item.value.split(',').map((v) => v.trim())
            value = possibleValues[item.filter].filter((v) => !selectedValues.includes(v)).join(',')
          } else if (item.condition !== 'is') {
            return
          }

          if (this.advancedFilter.operator === 'OR') {
            quickParams.push({ name: item.filter, string_value: value })
          } else if (this.advancedFilter.operator === 'AND') {
            if (!quickParams.some((qf) => qf.name === item.filter)) {
              quickParams.push({ name: item.filter, string_value: value })
            }
          }
        })

      quickParams.push({ name: 'date_range', string_value: this.advancedFilter.date_range })

      if (this.currentTabConfig.view_type === 'MANAGER') {
        quickParams.push({ name: 'team_id', string_value: this.advancedFilter.team_id })
      }

      this.setInProgressFilterConfig({ advancedParams: null, quickParams, filterType: 'quick' })
      this.$emit('processFilterType', 'quick')
    },
    handleApplyFilter() {
      this.setCachedFilterConfig(null)
      this.setInProgressFilterConfig({
        advancedParams: null,
        quickParams: null,
        filterType: 'advanced'
      })
      this.advancedFilter.line_items = this.advancedFilter.line_items.filter(
        (item) => item.filter && item.condition
      )
      if (!this.advancedFilter.line_items.length) {
        this.advancedFilter.line_items = [
          {
            filter: '',
            condition: '',
            value: '',
            tag_type: '',
            type: ''
          }
        ]
      }
      this.setCurrentPage(1)
      this.currentTabConfig.filter_config.advanced_params = []
      this.currentTabConfig.filter_config.advanced_params = JSON.parse(
        JSON.stringify(this.advancedFilter)
      )
      delete this.currentTabConfig.filter_config.advanced_params.date_range
      delete this.currentTabConfig.filter_config.advanced_params.team_id
      this.currentTabConfig.date_range = this.advancedFilter.date_range
      this.currentTabConfig.team_id = this.advancedFilter.team_id
      this.$emit('closeFilters')
      this.currentTabConfig.filter_config.applied_filter_type = 'advanced'
      if (this.getDashboardType === 'advancedWorkspaceReport') {
        this.fetchWorkspaces().catch(() => {
          this.$mxMessage.error(this.$t('system_unknown_error'))
        })
      } else {
        this.fetchActions().catch(() => {
          this.$mxMessage.error(this.$t('system_unknown_error'))
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.advanced-filter-holder {
  height: inherit;
  min-width: 400px;
  width: min(100vw - 350px, 750px);
  background-color: #ffffff;
  z-index: 2000;
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.12);
  border-radius: 6px;
  position: relative;
}

.header-section {
  display: flex;
  height: 44px;
  padding: 12px 16px 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.content-section {
  padding: 16px 16px 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  max-height: 50vh;
}

.footer-section {
  width: 100%;
  display: flex;
  height: 44px;
  padding: 12px 16px 12px 16px;
  border-top: 1px solid #e0e0e0;
}

.header-second-section {
  margin-left: auto;
  display: flex;
  flex-direction: row;
  gap: 12px;
  color: $mx-color-var-label-tertiary;
  align-items: center;
  justify-content: center;
}

.mx-gray-color {
  color: #8a8a8a;
}

.footer-second-section {
  margin-left: auto;
}

.row-container {
  padding-left: 0px;
  padding-top: 10px;
  padding-bottom: 10px;
}

.row-container-grid {
  width: 100%;
  display: flex;
  align-items: center;
  margin: 0 auto;
  max-width: 100%;
  gap: 8px;
}

.row-element-1 {
  flex: 1 1 88px;
}

.row-element-2 {
  flex: 1 1 200px;
}

.row-element-3 {
  flex: 1 1 130px;
}

.row-element-4 {
  flex: 1 1 240px;
}

.row-element-5 {
  flex: 1 1 24px;
}

.content-row-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.status-heading {
  color: $mx-color-var-label-secondary;
}

.horizontal-line {
  height: 1px;
  border-bottom: 1px solid;
  border-color: #e9e9ea;
  width: calc(100% - 20px);
  margin-left: 20px;
}

.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.mt-16 {
  margin-top: 16px;
}

.footer-second-section {
  margin-left: auto;
}

.clear-filter-icon {
  color: $mx-color-var-label-secondary;
}

.clear-filter-icon:hover {
  color: $mx-color-var-black;
}

.capitalize-css {
  text-transform: capitalize;
}

.valaa {
  font-size: 8px;
}

.breaker {
  background-color: #e0e0e0;
  width: 1px;
  height: 14px;
}

.new-advanced-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.new-advanced-filter-icon {
  font-size: 14px;
}

.disabled-icon {
  user-select: none !important;
  cursor: no-drop !important;
  color: #c6c6c6;
}

.disabled-icon:hover {
  color: #c6c6c6 !important;
}

::v-deep {
  .single-label {
    max-width: 190px !important;
  }

  .footer-second-section {
    .el-button,
    .btn {
      text-transform: none !important;
    }
  }
}

.additional-row-container {
  padding: 16px 0px;
  background: #fbfbfb;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.additional-row-container .content-row-item:hover {
  background-color: transparent !important;
}

.content-row-item-new {
  height: 40px;
  max-height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  padding: 0px 20px;
}

.content-left-section-phase3-filters {
  user-select: none;
  display: flex;
  align-items: center;
  width: 98px;
}

.content-right-section-phase3-filters {
  align-items: center;
  display: flex;
  justify-content: start;
  width: 340px;
}
</style>
