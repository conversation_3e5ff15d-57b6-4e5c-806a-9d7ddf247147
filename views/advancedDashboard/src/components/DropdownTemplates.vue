<template>
  <div class="component-holder">
    <DropdownComponent v-model="selection" :is-multiple="false" :list-items="getTemplateList" :clearable="true"
      :type="'TEMPLATES'" :isLoading="isLoading" @change="handleOnSelection" @scroll-end="handleOnScroll"
      @onSearch="handleSearch" />
  </div>
</template>

<script>
import DropdownComponent from '@views/advancedDashboard/src/components/DropdownComponent.vue';
import _debounce from 'lodash/debounce'
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
export default {
  name: 'DropdownTemplates',
  components: {
    DropdownComponent
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    isMultiple: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      selection: null,
      searchText: ''
    };
  },
  computed: {
    ...mapGetters('advancedDashboard', ['getDashboardType', 'getTemplateList', 'getTemplatesLoading', 'getMilestonesLoading', 'getCurrentStepsLoading', 'getHasMilestone', 'getHasStep']),
    ...mapState('advancedDashboard',['quickFilter']),
    isLoading() {
      return this.getHasMilestone
        ? this.getMilestonesLoading || this.getTemplatesLoading
        : this.getTemplatesLoading || this.getCurrentStepsLoading;
    }

  },
  watch: {
    selection() {
      this.$emit('input', this.selection)
      this.$emit('change', this.selection)
    },
    value() {
      if (this.selection != this.value) {
        this.selection = this.value
      }
    }
  },
  mounted() {
    this.selection = this.value
  },
  methods: {
    ...mapMutations('advancedDashboard', [
      'setHasMilestone',
      'setHasStep'
    ]),
    ...mapActions('advancedDashboard', [
      'fetchMilestones', 'fetchTemplateSteps', 'fetchTemplates'
    ]),
    handleOnSelection(item) {
      this.quickFilter.template_milestone_seq = ''
      this.quickFilter.template_step_seq = ''
      this.setHasMilestone(item.total_milestones > 0);
      this.setHasStep(item.total_steps > 0);
      if (item.total_milestones > 0) {
        this.fetchMilestones({ templateId: item.flow_tmpl_binder_id })
      }
      else if (item.total_steps > 0) {
        this.fetchTemplateSteps({ templateId: item.flow_tmpl_binder_id })
      }
    },
    handleOnScroll() {
      this.fetchTemplates({ searchKey: this.searchText, isLoadMore: true })
    },
    handleSearch(searchText) {
      this.searchText = searchText
      this.processSearch()
    },
    processSearch: _debounce(function (event) {
      this.fetchTemplates({ searchKey: this.searchText, isLoadMore: false })
    }, 200),
  }
};
</script>

<style>
.component-holder {
  width: 100% !important;
}
</style>