<template>
  <div class="component-holder">
    <DropdownComponent v-model="selection" :is-multiple="isMultiple" :list-items="list" :clearable="true"
      :type="'LIST'" />
  </div>
</template>

<script>
import DropdownComponent from '@views/advancedDashboard/src/components/DropdownComponent.vue';
export default {
  name: 'DropdownWorkspaceStatus',
  components: {
    DropdownComponent
  },
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    isMultiple: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      selection: null
    };
  },
  computed: {
    list() {
      const statuses = [
        { title: this.$t('In_Progress'), value: 'open' },
        { title: this.$t('Due_Today_upper'), value: 'due_today' },
        { title: this.$t('Overdue'), value: 'overdue' },
        { title: this.$t('Completed'), value: 'completed' },
        // { title: this.$t('Canceled'), value: 'cancelled' }
      ]

      return statuses
    }
  },
  watch: {
    selection() {
      this.$emit('input', this.selection)
      this.$emit('change', this.selection)
    },
    value() {
      if (this.selection != this.value) {
        this.selection = this.value
      }
    }
  },
  mounted()
  {
    this.selection = this.value
  }
};
</script>

<style>
.component-holder {
  width: 100% !important;
}
</style>