<template>
  <div ref="milestone" class="component-holder">
    <DropdownComponent v-model="selection" :is-multiple="false" :list-items="getMilestoneList" :clearable="true"
      :type="'TEMPLATE_MILESTONES'" :isLoading="getMilestonesLoading || getCurrentStepsLoading"
      @change="handleOnSelection" @scroll-end="handleOnScroll" @onSearch="handleSearch" />
  </div>
</template>

<script>
import DropdownComponent from '@views/advancedDashboard/src/components/DropdownComponent.vue';
import _debounce from 'lodash/debounce'
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
export default {
  name: 'DropdownMilestones',
  components: {
    DropdownComponent
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    isMultiple: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      selection: null,
      searchText: ''
    };
  },
  computed: {
    ...mapGetters('advancedDashboard', ['getMilestoneList', 'currentTemplateId', 'currentMilestoneId', 'getMilestonesLoading', 'getCurrentStepsLoading']),
    ...mapState('advancedDashboard',['quickFilter']),
  },
  watch: {
    selection() {
      this.$emit('input', this.selection)
    },
    value() {
      if (this.selection != this.value) {
          this.selection = this.value?String(this.value):undefined
      }
    },
    getMilestoneList() {
      this.scrollToSelf()
    }
  },
  mounted() {
    this.selection = this.value?String(this.value):undefined
  },
  methods: {
    ...mapMutations('advancedDashboard', [
    ]),
    ...mapActions('advancedDashboard', [
      'fetchTemplateSteps', 'fetchMilestones'
    ]),
    handleOnSelection(item) {
      this.quickFilter.template_step_seq = ''
      this.fetchTemplateSteps({ templateId: this.currentTemplateId, milestoneId: item.value })
    },
    handleOnScroll() {
      this.fetchMilestones({ templateId: this.currentTemplateId, searchKey: this.searchText, isLoadMore: true })
    },
    handleSearch(searchText) {
      this.searchText = searchText
     // this.processSearch()
    },
    processSearch: _debounce(function (event) {
      this.fetchMilestones({ templateId: this.currentTemplateId, searchKey: this.searchText, isLoadMore: false })
    }, 300),
    scrollToSelf() {
      if (this.$refs.milestone) {
        this.$refs.milestone.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    },
  }
};
</script>

<style>
.component-holder {
  width: 100% !important;
}
</style>