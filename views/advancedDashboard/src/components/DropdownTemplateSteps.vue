<template>
  <div ref="templateSteps" class="component-holder">
    <DropdownComponent
      v-model="selection"
      :is-multiple="isMultiple"
      :list-items="templateStepList"
      :clearable="true"
      :type="'TEMPLATE_STEPS'"
      :isLoading="getCurrentStepsLoading"
      @change="handleOnSelection"
      @scroll-end="handleOnScroll"
      @onSearch="handleSearch"
    />
  </div>
</template>

<script>
import DropdownComponent from '@views/advancedDashboard/src/components/DropdownComponent.vue'
import _debounce from 'lodash/debounce'
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import actionImages from '@views/theme/src/images/base_action/index'
import {
  ACKNOWLEDGEMENT,
  APPROVAL,
  FILEREQUEST,
  TODO,
  TRANSACTION,
  ESIGN,
  FORMREQUEST,
  DOCUSIGNWRAPPER,
  LAUNCH<PERSON><PERSON><PERSON><PERSON>,
  MEETREQUEST,
  DECISI<PERSON>,
  WAIT,
  DEFAULTINTEGRATIONAPP,
  SHADOWFLOW,
  PDFFOR<PERSON>,
  Jumio
} from '@views/theme/src/images/base_action/index'
export default {
  name: 'DropdownTemplateSteps',
  components: {
    DropdownComponent
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    isMultiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selection: null,
      searchText: ''
    }
  },
  computed: {
    ...mapGetters('advancedDashboard', [
      'getDashboardType',
      'getTemplateStepList',
      'getCurrentStepsLoading',
      'currentTemplateId',
      'currentMilestoneId'
    ]),
    ...mapState('advancedDashboard', ['quickFilter', 'advancedFilter']),
    templateStepList() {
      return this.getTemplateStepList.map((element) => ({
        ...element,
        icon: this.processIcon(element)
      }))
    }
  },
  watch: {
    selection(newVal) {
      if (newVal !== this.value) {
        this.$emit('input', newVal)
      }
    },
    value(newVal) {
      if (newVal !== this.selection) {
        this.selection = newVal ? String(newVal) : undefined
      }
    },
    getTemplateStepList(newVal, oldVal) {
      if (newVal !== oldVal && newVal.length) {
        this.scrollToSelf()
      }
    }
  },
  mounted() {
    this.selection = this.value ? String(this.value) : undefined
  },
  methods: {
    ...mapMutations('advancedDashboard', ['', '']),
    ...mapActions('advancedDashboard', ['fetchTemplateSteps']),
    handleOnSelection(item) {},
    processIcon(item) {
      if (item.flow_tmpl_step_name?.toUpperCase().includes('JUMIO'))
      {
          return actionImages['Jumio'] || null
      }

      const iconMap = {
        WORKFLOW_STEP_TYPE_APPROVAL: APPROVAL,
        WORKFLOW_STEP_TYPE_ACKNOWLEDGE: ACKNOWLEDGEMENT,
        WORKFLOW_STEP_TYPE_FILE_REQUEST: FILEREQUEST,
        WORKFLOW_STEP_TYPE_MEET_REQUEST: MEETREQUEST,
        WORKFLOW_STEP_TYPE_DOCUSIGN: DOCUSIGNWRAPPER,
        WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP: LAUNCHWEBAPP,
        WORKFLOW_STEP_TYPE_CARD: TRANSACTION,
        WORKFLOW_STEP_TYPE_INTEGRATION: DEFAULTINTEGRATIONAPP,
        WORKFLOW_STEP_TYPE_TODO: TODO,
        WORKFLOW_STEP_TYPE_FORM_REQUEST: FORMREQUEST,
        WORKFLOW_STEP_TYPE_PDF_FORM: PDFFORM,
        WORKFLOW_STEP_TYPE_DECISION: DECISION,
        WORKFLOW_STEP_TYPE_SIGNATURE: ESIGN,
        WORKFLOW_STEP_TYPE_AWAIT: WAIT,
        WORKFLOW_STEP_TYPE_TODO_TRANSACTION: TODO,
        WORKFLOW_STEP_TYPE_SHADOW_FLOW: SHADOWFLOW
      }
      return iconMap[item.flow_tmpl_step_type] || null
    },
    handleOnScroll() {
      this.fetchTemplateSteps({
        templateId: this.currentTemplateId,
        milestoneId: this.currentMilestoneId,
        searchKey: this.searchText,
        isLoadMore: true
      })
    },
    handleSearch(searchText) {
      this.searchText = searchText
     // this.processSearch()
    },
    processSearch: _debounce(function(event) {
      this.fetchTemplateSteps({
        templateId: this.currentTemplateId,
        milestoneId: this.currentMilestoneId,
        searchKey: this.searchText,
        isLoadMore: false
      })
    }, 200),
    scrollToSelf() {
      if (this.$refs.templateSteps) {
        this.$refs.templateSteps.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }
  }
}
</script>

<style>
.component-holder {
  width: 100% !important;
}
</style>
