<template>
  <div
    class="quick-filter-holder filter-container-box"
    ref="dropdownContent"
    :class="getShowSpinner ? 'disabled-selection' : ''"
  >
    <div class="header-section">
      <div class="header-first-section mx-text-c1">
        {{ $t('quick_filters') }}
      </div>
      <div class="header-second-section mx-text-c1">
        <el-button
          :class="{
            'mx-branding-text': isResetEnabled,
            'mx-gray-color': !isResetEnabled
          }"
          type="text"
          :disabled="!isResetEnabled"
          @click="handleClearAllFilter"
        >
          <span>{{ $t('reset') }}</span>
        </el-button>

        <div class="breaker"></div>
        <el-button
          :class="{
            'mx-branding-text': !isApplyDisabled,
            'mx-gray-color': isApplyDisabled
          }"
          :disabled="isApplyDisabled"
          type="text"
          @click="handleApplyFilter"
        >
          <span>{{ $t('apply') }}</span>
        </el-button>
      </div>
    </div>
    <div class="content-section">
      <div class="additional-row-container">
        <div class="content-row-item">
          <div class="content-left-section-phase3-filters mx-text-c2">
            {{ $t('date_range') }}
          </div>
          <div class="content-right-section-phase3-filters">
            <FilterDateRange
              :value="quickFilter.date_range"
              :lineItem="dateFilterLineItem"
              @change="onDateSelection"
            />
          </div>
        </div>
        <div v-if="currentTabConfig.view_type === 'MANAGER'" class="content-row-item">
          <div class="content-left-section-phase3-filters mx-text-c2">
            {{ $t('team') }}
          </div>
          <div class="content-right-section-phase3-filters">
            <DropdownTeams :value="quickFilter.team_id" @change="onTeamChange" />
          </div>
        </div>
      </div>
      <div v-if="getDashboardType == 'advancedActionReport'" class="first-row-container">
        <div class="status-heading mx-text-c4">{{ $t('action_status') }}</div>
        <el-checkbox-group v-model="actionStatusArray" class="vertical-checkbox-group">
          <el-checkbox
            v-for="status in actionStatusGroups"
            :label="status.value"
            :key="status.value"
            class="content-row-item-checkboxes"
            style="flex-direction: row-reverse;"
            >{{ status.label }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
      <div v-if="getDashboardType == 'advancedActionReport'" class="horizontal-line" />
      <div class="first-row-container">
        <div class="status-heading mx-text-c4">{{ $t('workspace_status') }}</div>
        <el-checkbox-group v-model="workspaceStatusArray" class="vertical-checkbox-group">
          <el-checkbox
            v-for="status in workspaceStatusGroups"
            :label="status.value"
            :key="status.value"
            class="content-row-item-checkboxes"
            style="flex-direction: row-reverse;"
            >{{ status.label }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
      <div class="horizontal-line" />
      <div class="second-row-container">
        <div class="status-heading mx-text-c4">
          {{ $t('Conversation_Type') }}
        </div>
        <el-checkbox-group v-model="workspaceTypeArray" class="vertical-checkbox-group">
          <el-checkbox
            v-for="item in workspaceTypeGroups"
            :label="item.value"
            :key="item.value"
            class="content-row-item-checkboxes"
            style="flex-direction: row-reverse;"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
      <div class="horizontal-line" />
      <div class="third-row-container">
        <div class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('client_workspace') }}
          </div>
          <div class="content-right-section">
            <el-checkbox
              v-model="quickFilter.external_only"
              true-label="true"
              false-label=""
              @change="handleQuickFilterChange"
            />
          </div>
        </div>
        <div v-if="getDashboardType == 'advancedActionReport'" class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('Assigned_to_Me') }}
          </div>
          <div class="content-right-section">
            <el-checkbox
              v-model="quickFilter.assigned_to_me"
              true-label="true"
              false-label=""
              @change="handleQuickFilterChange"
            />
          </div>
        </div>
        <div v-if="getDashboardType == 'advancedWorkspaceReport'" class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('owned_by_me') }}
          </div>
          <div class="content-right-section">
            <el-checkbox
              v-model="quickFilter.owned_by_me"
              true-label="true"
              false-label=""
              @change="handleQuickFilterChange"
            />
          </div>
        </div>

        <div class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('conversation_owner') }}
          </div>
          <div class="content-right-section-dropdowns">
            <DropdownWorkspaceOwners
              v-model="quickFilter.workspace_owner_id"
              @change="handleWorkspaceOwnerChange"
            />
          </div>
        </div>

        <div class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('workspace_member') }}
          </div>
          <div class="content-right-section-dropdowns">
            <DropdownWorkspaceMembers
              v-model="quickFilter.workspace_member_id"
              @change="handleQuickFilterChange"
            />
          </div>
        </div>

        <div v-if="getDashboardType == 'advancedActionReport'" class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('assigned_to') }}
          </div>
          <div class="content-right-section-dropdowns">
            <DropdownWorkspaceMembers
              v-model="quickFilter.assigned_to"
              @change="handleQuickFilterChange"
            />
          </div>
        </div>

        <div class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('Template') }}
          </div>
          <div class="content-right-section-dropdowns">
            <DropdownTemplates
              v-model="quickFilter.template_id"
              @onChange="handleQuickFilterChange"
            />
          </div>
        </div>

        <div
          v-show="quickFilter.template_id && getHasMilestone && !getMilestonesLoading"
          class="content-row-item"
        >
          <div class="content-left-section mx-text-c2">
            {{ $t('current_milestone') }}
          </div>
          <div class="content-right-section-dropdowns">
            <DropdownMilestones
              v-model="quickFilter.template_milestone_seq"
              @onChange="handleQuickFilterChange"
            />
          </div>
        </div>

        <div v-show="showCurrentStep && !getCurrentStepsLoading" class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('current_step') }}
          </div>
          <div class="content-right-section-dropdowns">
            <DropdownTemplateSteps
              v-model="quickFilter.template_step_seq"
              @onChange="handleQuickFilterChange"
            />
          </div>
        </div>
        <div v-if="getDashboardType == 'advancedActionReport'" class="content-row-item">
          <div class="content-left-section mx-text-c2">
            {{ $t('action_type') }}
          </div>
          <div class="content-right-section-dropdowns">
            <DropdownActionTypes
              v-model="quickFilter.action_type"
              @change="handleQuickFilterChange"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="footer-section">
      <div class="footer-first-section"></div>
      <div class="footer-second-section mx-text-c1">
        <el-button
          :brandingText="true"
          type="text"
          @click="handleAdvancedFilterSwitch"
          class="switch-filters-btn"
        >
          <span class="mx-ellipsis">{{ $t('switch_to_advanced_filters') }}</span>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions, mapMutations, mapState } from 'vuex'
import DropdownTeams from '@views/advancedDashboard/src/components/DropdownTeams.vue'
import DropdownWorkspaceOwners from '@views/advancedDashboard/src/components/DropdownWorkspaceOwners.vue'
import DropdownWorkspaceMembers from '@views/advancedDashboard/src/components/DropdownWorkspaceMembers.vue'
import DropdownTemplates from '@views/advancedDashboard/src/components/DropdownTemplates.vue'
import DropdownTemplateSteps from '@views/advancedDashboard/src/components/DropdownTemplateSteps.vue'
import DropdownMilestones from '@views/advancedDashboard/src/components/DropdownMilestones.vue'
import DropdownActionTypes from '@views/advancedDashboard/src/components/DropdownActionTypes.vue'
import moment from 'moment-timezone'

import FilterDateRange from '@views/advancedDashboard/src/components/FilterDateRange.vue'
import { ObjectUtils } from '@controller/utils'

import { defaultActionConfig, defaultWorkspaceConfig } from '@views/common/appConst'

export default {
  name: 'QuickFilters',
  components: {
    DropdownTemplates,
    DropdownTemplateSteps,
    DropdownMilestones,
    DropdownTeams,
    DropdownActionTypes,
    FilterDateRange,
    DropdownWorkspaceOwners,
    DropdownWorkspaceMembers
  },
  data() {
    return {
      defaultWorkspaceConfig,
      defaultActionConfig,
      dateFilterLineItem: {
        condition: 'is_between',
        value: ''
      }
    }
  },
  computed: {
    ...mapGetters('advancedDashboard', [
      'getShowSpinner',
      'getDashboardType',
      'getWorkspaceConfig',
      'getActionConfig',
      'getSavedWorkspaceConfigs',
      'getSavedActionConfigs',
      'currentTabConfig',
      'getHasMilestone',
      'getHasStep',
      'getWorkspaceOwnerLoading',
      'getWorkspaceMemberLoading',
      'getTemplatesLoading',
      'getMilestonesLoading',
      'getCurrentStepsLoading',
      'currentTabSavedConfig'
    ]),
    ...mapGetters('user', ['currentUser', 'accessibleTeams']),
    ...mapState('advancedDashboard', ['inProgressFilterConfig', 'quickFilter', 'advancedFilter']),
    teamList() {
      return this.accessibleTeams
        .filter((team) => team.isMember && team.isManager)
        .map((team) => ({
          ...team,
          avatar: team.avatar || '',
          title: team.name,
          value: team.id
        }))
    },
    workspaceStatusGroups() {
      const allOptions = [
        { label: this.$t('In_Progress'), value: 'open' },
        { label: this.$t('Due_Today_upper'), value: 'due_today' },
        { label: this.$t('Overdue'), value: 'overdue' },
        { label: this.$t('Completed'), value: 'completed' },
        // { label: this.$t('Canceled'), value: 'cancelled' }
      ]

      return allOptions
    },
    actionStatusGroups() {
      const allOptions = [
        { label: this.$t('In_Progress'), value: 'open' },
        { label: this.$t('Due_Today_upper'), value: 'due_today' },
        { label: this.$t('Due_Tomorrow_upper'), value: 'due_tomorrow' },
        { label: this.$t('due_in_seven_days'), value: 'due_in_7_days' },
        { label: this.$t('Overdue'), value: 'overdue' },
        { label: this.$t('Completed'), value: 'completed' }
      ]

      return allOptions
    },
    workspaceStatusArray: {
      get() {
        return this.quickFilter.workspace_status
          ? this.quickFilter.workspace_status.split(',').filter((s) => s)
          : []
      },
      set(val) {
        this.quickFilter.workspace_status = val.join(',')
      }
    },
    actionStatusArray: {
      get() {
        return this.quickFilter.action_status
          ? this.quickFilter.action_status.split(',').filter((s) => s)
          : []
      },
      set(val) {
        this.quickFilter.action_status = val.join(',')
      }
    },
    workspaceTypeGroups() {
      const allOptions = [
        { label: this.$t('Flow_conversation'), value: 'FLOW_BINDER' },
        { label: this.$t('Group_Workspace'), value: 'GROUP_BINDER' },
        { label: this.$t('one_to_one'), value: 'DIRECT_BINDER' }
      ]
      return allOptions
    },
    workspaceTypeArray: {
      get() {
        return this.quickFilter.workspace_type
          ? this.quickFilter.workspace_type.split(',').filter((s) => s)
          : []
      },
      set(val) {
        this.quickFilter.workspace_type = val.join(',')
      }
    },
    showCurrentStep() {
      if (this.quickFilter.template_id) {
        if (this.getHasMilestone) {
          if (this.getHasStep && this.quickFilter.template_milestone_seq) {
            return true
          } else {
            return false
          }
        } else {
          if (this.getHasStep) {
            return true
          } else {
            return false
          }
        }
      } else {
        return false
      }
    },
    isResetEnabled() {
      const filters = this.quickFilter || {}

      const isManager = this.currentTabConfig?.view_type === 'MANAGER'
      const defaultTeamID = isManager ? this.teamList?.[0]?.value || '' : ''

      const lastYearStartMs = moment()
        .subtract(12, 'months')
        .add(1, 'day')
        .startOf('day')
        .valueOf()
      const nowDateTime = moment()
        .endOf('day')
        .valueOf()
      const defaultDateRange = `${lastYearStartMs}-${nowDateTime}`

      // Helper function to normalize string values
      const normalizeStringValue = (value) => {
        if (typeof value === 'string' && value.includes(',')) {
          return value
            .split(',')
            .sort((a, b) => a.localeCompare(b))
            .join(',')
        }
        return value || ''
      }

      let returnValue = false

      if (this.currentTabConfig.view_seq === 'mepx_dashboard_default_tab') {
        if (this.getDashboardType === 'advancedWorkspaceReport') {
          // 1. Check team_id
          if (isManager && filters.team_id !== defaultTeamID) {
            returnValue = true
          }

          // 2. Check date_range
          if (filters.date_range !== defaultDateRange) {
            returnValue = true
          }

          // Compare current filters with default workspace config
          const defaultParams = this.defaultWorkspaceConfig.filter_config.quick_params || []

          // Check each filter against default values
          const filtersToCheck = [
            'workspace_status',
            'workspace_type',
            'external_only',
            'owned_by_me',
            'workspace_owner_id',
            'workspace_member_id',
            'template_id',
            'template_milestone_seq',
            'template_step_seq'
          ]

          for (const filterName of filtersToCheck) {
            const defaultParam = defaultParams.find((p) => p.name === filterName)
            const defaultValue = normalizeStringValue(defaultParam?.string_value)
            const currentValue = normalizeStringValue(filters[filterName])

            if (currentValue !== defaultValue) {
              returnValue = true
              break
            }
          }
        } else {
          // 1. Check team_id
          if (isManager && filters.team_id !== defaultTeamID) {
            returnValue = true
          }

          // 2. Check date_range
          if (filters.date_range !== defaultDateRange) {
            returnValue = true
          }

          // For action report - compare with default action config
          const defaultParams = this.defaultActionConfig.filter_config.quick_params || []

          const filtersToCheck = [
            'workspace_status',
            'action_status',
            'workspace_type',
            'external_only',
            'owned_by_me',
            'assigned_to_me',
            'assigned_to',
            'workspace_owner_id',
            'workspace_member_id',
            'template_id',
            'template_milestone_seq',
            'template_step_seq',
            'action_type'
          ]

          for (const filterName of filtersToCheck) {
            const defaultParam = defaultParams.find((p) => p.name === filterName)
            const defaultValue = normalizeStringValue(defaultParam?.string_value)
            const currentValue = normalizeStringValue(filters[filterName])

            if (currentValue !== defaultValue) {
              returnValue = true
              break
            }
          }
        }
      } else {
        // For non-default tabs, compare with saved config for this specific tab
        if (this.getDashboardType === 'advancedWorkspaceReport') {
          const savedConfig = this.getSavedWorkspaceConfigs.find(
            (t) => t.view_seq === this.currentTabConfig.view_seq
          )

          // Safety check if savedConfig exists
          if (!savedConfig) {
            returnValue = false
          }
          else
          {

          // 1. Check team_id
          if (isManager && filters.team_id !== (savedConfig.team_id || '')) {
            returnValue = true
          }

          // 2. Check date_range
          if (filters.date_range !== (savedConfig.date_range || '')) {
            returnValue = true
          }

          const savedParams = savedConfig?.filter_config?.quick_params || []

          const filtersToCheck = [
            'workspace_status',
            'workspace_type',
            'external_only',
            'owned_by_me',
            'workspace_owner_id',
            'workspace_member_id',
            'template_id',
            'template_milestone_seq',
            'template_step_seq'
          ]

          for (const filterName of filtersToCheck) {
            const savedParam = savedParams.find((p) => p.name === filterName)
            const savedValue = normalizeStringValue(savedParam?.string_value)
            const currentValue = normalizeStringValue(filters[filterName])

            if (currentValue !== savedValue) {
              returnValue = true
              break
            }
          }
        }
        } else {
          // For action report - compare with saved action config
          const savedConfig = this.getSavedActionConfigs.find(
            (t) => t.view_seq === this.currentTabConfig.view_seq
          )

          // Safety check if savedConfig exists
          if (!savedConfig) {
            return false
          }
          else
          {

          // 1. Check team_id
          if (isManager && filters.team_id !== (savedConfig.team_id || '')) {
            returnValue = true
          }

          // 2. Check date_range
          if (filters.date_range !== (savedConfig.date_range || '')) {
            returnValue = true
          }

          const savedParams = savedConfig?.filter_config?.quick_params || []

          const filtersToCheck = [
            'workspace_status',
            'action_status',
            'workspace_type',
            'external_only',
            'owned_by_me',
            'assigned_to_me',
            'assigned_to',
            'workspace_owner_id',
            'workspace_member_id',
            'template_id',
            'template_milestone_seq',
            'template_step_seq',
            'action_type'
          ]

          for (const filterName of filtersToCheck) {
            const savedParam = savedParams.find((p) => p.name === filterName)
            const savedValue = normalizeStringValue(savedParam?.string_value)
            const currentValue = normalizeStringValue(filters[filterName])

            if (currentValue !== savedValue) {
              returnValue = true
              break
            }
          }
        }
        }
      }

      return returnValue
    },
    isApplyDisabled() {
      let savedFilters = [...(this.currentTabConfig?.filter_config?.quick_params || [])]
      let appliedFiltes = this.getProcessedFilterData()
      savedFilters = savedFilters
        .sort((a, b) => a.name.localeCompare(b.name))
        .filter((item) => !!item.string_value)
      appliedFiltes = appliedFiltes
        .sort((a, b) => a.name.localeCompare(b.name))
        .filter((item) => !!item.string_value)

      let areEqual = true
      if (
        this.currentTabConfig?.date_range === this.quickFilter.date_range &&
        (this.currentTabConfig?.team_id ?? '') === (this.quickFilter?.team_id ?? '')
      ) {
        areEqual = true
      } else {
        areEqual = false
      }

      appliedFiltes?.forEach((param) => {
        if (typeof param.string_value === 'string') {
          param.string_value = param.string_value
            .split(',')
            .sort((a, b) => a.localeCompare(b))
            .join(',')
        }
      })

      savedFilters?.forEach((param) => {
        if (typeof param.string_value === 'string') {
          param.string_value = param.string_value
            .split(',')
            .sort((a, b) => a.localeCompare(b))
            .join(',')
        }
      })

      return areEqual && ObjectUtils.isEqual(appliedFiltes, savedFilters)
    }
  },
  mounted() {
    this.renderFilterData()
  },
  methods: {
    ...mapActions('advancedDashboard', ['fetchWorkspaces', 'fetchActions', 'searchOwners']),
    ...mapMutations('advancedDashboard', [
      'setInProgressFilterConfig',
      'setWorkspaceConfig',
      'setActionConfig',
      'setSelectedTemplateRelatedFilterData',
      'setCurrentPage',
      'setCachedFilterConfig',
      'setQuickFilters',
      'resetFilters'
    ]),
    handleWorkspaceOwnerChange() {
      if (this.currentTabConfig.view_type === 'MANAGER' && !this.quickFilter.workspace_owner_id) {
        this.quickFilter.workspace_member_id = ''
        this.quickFilter.assigned_to = ''
      }
    },
    getProcessedFilterData() {
      const quickParamsData = Object.entries(this.quickFilter)
        .filter(([key, value]) => !!value && !['date_range', 'team_id'].includes(key))
        .map(([key, value]) => ({
          name: key,
          string_value: String(value)
        }))
        .sort((a, b) => a.name.localeCompare(b.name))

      const order = [
        'workspace_status',
        'action_status',
        'workspace_type',
        'external_only',
        'assigned_to_me',
        'owned_by_me',
        'workspace_owner_id',
        'workspace_member_id',
        'assigned_to',
        'template_id',
        'template_milestone_seq',
        'template_step_seq',
        'last_activity',
        'action_type',
        'start_date',
        'overdue'
      ]

      const sorted = quickParamsData
        .filter((item) => item && item.name) // null/undefined/empty name check
        .sort((a, b) => order.indexOf(a.name) - order.indexOf(b.name))
      return sorted
    },
    handleQuickFilterChange() {
      if (!this.quickFilter.template_id) {
        this.quickFilter.template_milestone_seq = ''
        this.quickFilter.template_step_seq = ''
      }
    },
    renderFilterData() {
      this.resetFilters()
      const quickParams =
        this.inProgressFilterConfig.quickParams ||
        this.currentTabConfig?.filter_config?.quick_params ||
        []

      quickParams.forEach(({ name, string_value }) => {
        const allowedMultiple = ['workspace_status', 'action_status', 'workspace_type']
        if (allowedMultiple.includes(name)) {
          this.quickFilter[name] = string_value
        } else {
          this.quickFilter[name] =
            typeof string_value === 'string' ? string_value.split(',')[0] : string_value
        }
      })

      if (!this.quickFilter.date_range) {
        this.quickFilter.date_range = this.currentTabConfig?.date_range
      }
      if (!this.quickFilter.team_id) {
        this.quickFilter.team_id = this.currentTabConfig?.team_id
      }
    },
    handleClearAllFilter() {
      const lastYearStartMs = moment()
        .subtract(12, 'months')
        .add(1, 'day')
        .startOf('day')
        .valueOf()
      const nowDateTime = moment()
        .endOf('day')
        .valueOf()
      const date_range = lastYearStartMs + '-' + nowDateTime

      const defaultTeamID =
        this.currentTabConfig?.view_type == 'MANAGER' ? this.teamList[0]?.value || '' : ''
      this.quickFilter.team_id = defaultTeamID

      const commonFilters = {
        workspace_type: '',
        external_only: false,
        assigned_to_me: false,
        assigned_to: '',
        owned_by_me: false,
        action_type: '',
        workspace_owner_id: '',
        workspace_member_id: '',
        template_id: '',
        template_milestone_seq: '',
        template_step_seq: ''
      }

      if (this.currentTabConfig.view_seq === 'mepx_dashboard_default_tab') {
        commonFilters.date_range = date_range
        commonFilters.team_id = defaultTeamID
        if (this.getDashboardType === 'advancedWorkspaceReport') {
          const params = this.defaultWorkspaceConfig.filter_config.quick_params || []
          commonFilters.workspace_status = params.find(
            (p) => p.name === 'workspace_status'
          )?.string_value
          commonFilters.workspace_type = params.find(
            (p) => p.name === 'workspace_type'
          )?.string_value
          commonFilters.external_only = params.find((p) => p.name === 'external_only')?.string_value
          commonFilters.owned_by_me = params.find((p) => p.name === 'owned_by_me')?.string_value
          commonFilters.workspace_owner_id = params.find(
            (p) => p.name === 'workspace_owner_id'
          )?.string_value
          commonFilters.workspace_member_id = params.find(
            (p) => p.name === 'workspace_member_id'
          )?.string_value
          commonFilters.template_id = params.find((p) => p.name === 'template_id')?.string_value
          commonFilters.template_milestone_seq = params.find(
            (p) => p.name === 'template_milestone_seq'
          )?.string_value
          commonFilters.template_step_seq = params.find(
            (p) => p.name === 'template_step_seq'
          )?.string_value
        } else {
          const params = this.defaultActionConfig.filter_config.quick_params || []
          commonFilters.workspace_status = params.find(
            (p) => p.name === 'workspace_status'
          )?.string_value
          commonFilters.action_status = params.find((p) => p.name === 'action_status')?.string_value
          commonFilters.workspace_type = params.find(
            (p) => p.name === 'workspace_type'
          )?.string_value
          commonFilters.external_only = params.find((p) => p.name === 'external_only')?.string_value
          commonFilters.owned_by_me = params.find((p) => p.name === 'owned_by_me')?.string_value
          commonFilters.assigned_to_me = params.find(
            (p) => p.name === 'assigned_to_me'
          )?.string_value
          commonFilters.assigned_to = params.find((p) => p.name === 'assigned_to')?.string_value
          commonFilters.workspace_owner_id = params.find(
            (p) => p.name === 'workspace_owner_id'
          )?.string_value
          commonFilters.workspace_member_id = params.find(
            (p) => p.name === 'workspace_member_id'
          )?.string_value
          commonFilters.template_id = params.find((p) => p.name === 'template_id')?.string_value
          commonFilters.template_milestone_seq = params.find(
            (p) => p.name === 'template_milestone_seq'
          )?.string_value
          commonFilters.template_step_seq = params.find(
            (p) => p.name === 'template_step_seq'
          )?.string_value
        }
      } else {
        if (this.getDashboardType === 'advancedWorkspaceReport') {
          const savedConfog = this.getSavedWorkspaceConfigs.find((t) => t.view_seq === this.currentTabConfig.view_seq)
          const params = savedConfog?.filter_config?.quick_params || []

          commonFilters.date_range = savedConfog.date_range
          commonFilters.team_id = savedConfog.team_id

          commonFilters.workspace_status = params.find(
            (p) => p.name === 'workspace_status'
          )?.string_value
          commonFilters.workspace_type = params.find(
            (p) => p.name === 'workspace_type'
          )?.string_value
          commonFilters.external_only = params.find((p) => p.name === 'external_only')?.string_value
          commonFilters.owned_by_me = params.find((p) => p.name === 'owned_by_me')?.string_value
          commonFilters.workspace_owner_id = params.find(
            (p) => p.name === 'workspace_owner_id'
          )?.string_value
          commonFilters.workspace_member_id = params.find(
            (p) => p.name === 'workspace_member_id'
          )?.string_value
          commonFilters.template_id = params.find((p) => p.name === 'template_id')?.string_value
          commonFilters.template_milestone_seq = params.find(
            (p) => p.name === 'template_milestone_seq'
          )?.string_value
          commonFilters.template_step_seq = params.find(
            (p) => p.name === 'template_step_seq'
          )?.string_value
        } else {
          const savedConfog = this.getSavedActionConfigs.find((t) => t.view_seq === this.currentTabConfig.view_seq)
          const params = savedConfog?.filter_config?.quick_params || []
          commonFilters.date_range = savedConfog.date_range
          commonFilters.team_id = savedConfog.team_id
          commonFilters.workspace_status = params.find(
            (p) => p.name === 'workspace_status'
          )?.string_value
          commonFilters.action_status = params.find((p) => p.name === 'action_status')?.string_value
          commonFilters.workspace_type = params.find(
            (p) => p.name === 'workspace_type'
          )?.string_value
          commonFilters.external_only = params.find((p) => p.name === 'external_only')?.string_value
          commonFilters.owned_by_me = params.find((p) => p.name === 'owned_by_me')?.string_value
          commonFilters.assigned_to_me = params.find(
            (p) => p.name === 'assigned_to_me'
          )?.string_value
          commonFilters.assigned_to = params.find((p) => p.name === 'assigned_to')?.string_value
          commonFilters.workspace_owner_id = params.find(
            (p) => p.name === 'workspace_owner_id'
          )?.string_value
          commonFilters.workspace_member_id = params.find(
            (p) => p.name === 'workspace_member_id'
          )?.string_value
          commonFilters.template_id = params.find((p) => p.name === 'template_id')?.string_value
          commonFilters.template_milestone_seq = params.find(
            (p) => p.name === 'template_milestone_seq'
          )?.string_value
          commonFilters.template_step_seq = params.find(
            (p) => p.name === 'template_step_seq'
          )?.string_value
        }
      }

      this.setQuickFilters({
        ...commonFilters
      })

      const data = {
        template_id: '',
        template_milestone_seq: '',
        template_step_seq: ''
      }
      this.setSelectedTemplateRelatedFilterData(data)

      this.handleQuickFilterChange()
    },
    handleAdvancedFilterSwitch() {
      const quickParams = this.getProcessedFilterData()

      let advancedParams = { line_items: [], operator: 'AND', team_id: '', date_range: '' }
      if (quickParams.length) {
        quickParams.forEach((param) => {
          if (param.name != 'date_range' && param.name != 'team_id') {
            advancedParams.line_items.push({
              condition: 'is',
              filter: param.name,
              type: 'dropdown',
              tag_type: '',
              value: String(param.string_value)
            })
          }
        })
      } else {
        advancedParams.line_items.push({
          condition: '',
          filter: '',
          type: '',
          tag_type: '',
          value: ''
        })
      }

      advancedParams.date_range = this.quickFilter.date_range
      advancedParams.team_id = this.quickFilter.team_id

      this.setInProgressFilterConfig({ advancedParams, quickParams: null, filterType: 'advanced' })
      const order = ['template_id', 'template_milestone_seq', 'template_step_seq']

      if (Array.isArray(this.currentTabConfig?.filter_config?.advancedParams?.line_items)) {
        this.currentTabConfig.filter_config.advancedParams.line_items.sort((a, b) => {
          return order.indexOf(a.filter) - order.indexOf(b.filter)
        })
      }
      this.$emit('processFilterType', 'advanced')
    },
    handleApplyFilter() {
      this.setCachedFilterConfig(null)
      this.setCurrentPage(1)
      this.currentTabConfig.filter_config.applied_filter_type = 'quick'
      this.currentTabConfig.filter_config.quick_params = []
      this.currentTabConfig.filter_config.quick_params = this.getProcessedFilterData()
      this.currentTabConfig.date_range = this.quickFilter.date_range
      this.currentTabConfig.team_id = this.quickFilter.team_id
      this.$emit('closeFilters')
      this.setInProgressFilterConfig({
        advancedParams: null,
        quickParams: null,
        filterType: 'quick'
      })
      if (this.getDashboardType === 'advancedWorkspaceReport') {
        this.setWorkspaceConfig(this.getWorkspaceConfig)
        this.fetchWorkspaces()
          .then(() => {
            this.currentTabConfig.filter_config.quick_params = this.getProcessedFilterData()
          })
          .catch(() => {
            this.$mxMessage.error(this.$t('system_unknown_error'))
          })
      } else {
        this.setActionConfig(this.getActionConfig)
        this.fetchActions()
          .then(() => {
            this.currentTabConfig.filter_config.quick_params = this.getProcessedFilterData()
          })
          .catch(() => {
            this.$mxMessage.error(this.$t('system_unknown_error'))
          })
      }
    },
    onDateSelection(date) {
      this.quickFilter.date_range = date
    },
    onTeamChange(team_id) {
      if (team_id) {
        if (this.currentTabConfig.view_type == 'MANAGER' && this.quickFilter.workspace_owner_id) 
        {
          this.quickFilter.workspace_owner_id = ''
          this.quickFilter.workspace_member_id = ''
        }

        this.quickFilter.team_id = team_id
        this.advancedFilter.team_id = team_id
        this.searchOwners({ searchKey: '', isLoadMore: false, teamId: team_id })
      }
    },
    fetchListingData() {
      if (this.getDashboardType === 'advancedWorkspaceReport') {
        this.fetchWorkspaces().catch(() => {
          this.$mxMessage.error(this.$t('system_unknown_error'))
        })
      } else {
        this.fetchActions().catch(() => {
          this.$mxMessage.error(this.$t('system_unknown_error'))
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.quick-filter-holder {
  height: inherit;
  min-width: 300px;
  width: min(100vw - 150px, 420px);
  background-color: #ffffff;
  z-index: 2000;
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.12);
  border-radius: 6px;
  position: relative;
}

.header-section {
  display: flex;
  height: 44px;
  padding: 12px 16px 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.content-section {
  max-height: 57vh;
  overflow-y: auto;
  padding-bottom: 20px;
}

.footer-section {
  width: 100%;
  display: flex;
  height: 44px;
  padding: 12px 16px 12px 16px;
  border-top: 1px solid #e0e0e0;
}

.header-second-section {
  margin-left: auto;
  display: flex;
  flex-direction: row;
  gap: 12px;
  color: #8a8a8a;
  align-items: center;
  justify-content: center;
}

.mx-gray-color {
  color: $mx-color-var-label-tertiary;
}

.footer-second-section {
  margin-left: auto;
}

.first-row-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.second-row-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.third-row-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.content-row-item {
  height: 40px;
  max-height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 0px 20px;
}

.content-row-item-checkboxes {
  height: 40px;
  max-height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 0px 17px;
}

.content-row-item:hover {
  background-color: #f4f4f4;
}

.status-heading {
  color: $mx-color-var-label-secondary;
  padding: 0px 20px;
  height: 24px;
  display: flex;
  align-items: center;
}

.horizontal-line {
  height: 1px;
  max-height: 1px;
  border-bottom: 1px solid;
  border-color: #e9e9ea;
  width: calc(100% - 20px);
  margin-left: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
}

.footer-second-section {
  margin-left: auto;
}

::v-deep {
  .el-select-dropdown__item {
    .micon-mep-check-mark {
      display: none;
    }
  }
}

.disabled-selection {
  pointer-events: none;
  opacity: 0.5;
}

.breaker {
  background-color: #e0e0e0;
  width: 1px;
  height: 14px;
}

.content-right-section {
  align-items: end;
  display: flex;
  justify-content: end;
}

.content-right-section-phase3-filters {
  align-items: end;
  display: flex;
  justify-content: end;
  width: 220px;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: 200px 0;
  }
}

.content-right-section-shimmer {
  display: flex;
  height: 28px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f4f4f4 25%, #e0e0e0 50%, #f4f4f4 75%);
  background-size: 400px 100%;
  animation: shimmer 4.8s infinite linear;
  width: 200px;
}

.content-right-section-dropdowns {
  width: 200px;
  align-items: end;
  display: flex;
  justify-content: end;
}

.content-left-section-phase3-filters {
  user-select: none;
  display: flex;
  align-items: center;
}

.content-left-section {
  user-select: none;
  display: flex;
  align-items: center;
  width: 160px;
}

.content-left-section-shimmer {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, #f4f4f4 25%, #e0e0e0 50%, #f4f4f4 75%);
  background-size: 400px 100%;
  animation: shimmer 4.8s infinite linear;
  height: 20px;
  width: 128px;
  border-radius: 4px;
}

::v-deep {
  .el-checkbox + .el-checkbox {
    margin-left: 0px;
  }

  .footer-second-section {
    .el-button,
    .btn {
      text-transform: none !important;
    }
  }
}

.additional-row-container {
  padding: 16px 0px;
  background: #fbfbfb;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.additional-row-container .content-row-item:hover {
  background-color: transparent !important;
}
</style>
