<template>
  <CollapsedEvent
    v-if="meet.children"
    :meet="meet"
    tabindex="0"
    role="listitem"
    @viewMeet="$emit('click', $event)" />
  <li
    v-else
    v-mx-ta="{ page: 'calendar', id: `${meet.topic}`}"
    :class="['meet-block', {'meet-past': meet.isPast && !meet.isRestartable && !meet.children}, 'mx-calendar-event']"
    :style="meet.style"
    tabindex="0"
    role="listitem"
    :aria-labelledby="'meetItem'+meet.board_id"
    @keypress.enter="$emit('click', meet)"
    @click="$emit('click', meet)">
    <div
      class="mx-flex-container meet-block-inner"
      :class="{'small-block': isSmallBlock, 'declined': meet.isDeclined, 'active': isSelected}">
      <div
        class="text-container mx-ellipsis"
        :class="{'text-one-line': textInOneLine}">
        <div
          class="meet-name mx-ellipsis">
          <SvgIcon
            v-if="meet.isFlowBoard"
            class="mx-margin-right-xxs"
            width="20"
            height="20"
            :style="{'opacity': meet.isPast?'0.2':'1'}"
            :icon-class="meet.isPast?'Flow_Gray': 'Flow_Indicator'" />
          <span style="vertical-align: top">{{ meet.topic }}</span>
          <span
            :id="'meetItem'+meet.board_id"
            class="sr-only">
            {{ ariaLabel }}
          </span>
        </div>
        <div class="meet-detail mx-ellipsis">
          <template v-if="meet.isFlowBoard">
            <span> {{ $t('binder_owner') }}: {{ hostName }} |&nbsp;</span>
            <span> {{ formattedParticipants }} </span>
          </template>
          <template v-else>
            <span> {{ $t('meet_host') }}: {{ hostName }} |&nbsp;</span>
            <span> {{ meet.isStarted ? $t('in_progress') : formattedDuration }} |&nbsp;</span>
            <span> {{ formattedParticipants }} </span>
          </template>
        </div>
      </div>
      <template>
        <div
          v-if="!meet.isFlowBoard"
          class="meet-actions">
          <a
              v-if="meet.indicateAllDeclined || showRecordingPlayIcon">
            <i
                v-if="meet.indicateAllDeclined"
                class="micon-mep-warning" style="font-size: 14px; margin-left: 4px" />
            <span
                v-if="showRecordingPlayIcon"
                tabindex="0"
                @keypress.stop.prevent="play"
                @click.stop.prevent="play">
              <svg
                  class="hover-svg"
                  width="14"
                  height="10"
                  viewBox="0 0 14 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M5.5 3.32355V6.67634C5.5 6.82501 5.65646 6.92171 5.78944 6.85522L9.14223 5.17883C9.28964 5.10512 9.28964 4.89476 9.14223 4.82106L5.78944 3.14466C5.65646 3.07817 5.5 3.17487 5.5 3.32355Z"
                  fill="#616161" />
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M0 2C0 0.89543 0.895431 0 2 0H12C13.1046 0 14 0.895431 14 2V8C14 9.10457 13.1046 10 12 10H2C0.89543 10 0 9.10457 0 8V2ZM2 1H12C12.5523 1 13 1.44772 13 2V8C13 8.55228 12.5523 9 12 9H2C1.44772 9 1 8.55229 1 8V2C1 1.44772 1.44772 1 2 1Z"
                    fill="#616161"/>
              </svg>
            </span>
          </a>
          <template>
            <a v-if="meetStatus.showJoinButton">
              <el-button
                  type="primary"
                  size="mini"
                  @click="joinMeet">{{ joinMeetingText }}</el-button>
            </a>
            <a v-if="meetStatus.showStartButton">
              <el-button
                  type="primary"
                  size="mini"
                  @click="startMeet">{{ meetStatus.startButtonText }}</el-button>
            </a>
            <a v-if="meetStatus.showRestartButton">
              <el-button
                  type="primary"
                  size="mini"
                  :disabled="restarting"
                  @click="restartMeet">{{ meetStatus.restartButtonText }}</el-button>
            </a>
          </template>
        </div>
      </template>
    </div>
  </li>
</template>

<script>
import util from '@views/common/utils/utils'
import { Meet } from '@views/providers/eventNames.js'
import {ObjectUtils, MxConsts} from '@commonUtils'
import { mapGetters, mapActions, mapState, mapMutations } from 'vuex'
import getJoinMeetingText from './getJoinMeetingText';
import {TimeUtils} from '../../../../commonUtils';
import SvgIcon from '@views/common/components/svgIcon'
import CollapsedEvent from './CollapsedEvent.vue';

export default {
  name: 'MxCalendarEvent',
  components: {SvgIcon, CollapsedEvent},
  mixins: [getJoinMeetingText],
  props: {
    meet: {
      type: Object,
      required: true,
      default: () => {}
    },
    openedMeetUniqueKey: String
  },
  computed: {
    ...mapGetters('group',['hideMeetingRecording']),
    ...mapState('calendar',['restartingMeets']),
    isSelected () {
      if (this.openedMeetUniqueKey) {
        const client_uuid = this.openedMeetUniqueKey.split('_')[0]
        return this.meet.client_uuid === client_uuid
      }
    },
    isSmallBlock () {
      if (this.meet.style?.height) {
        return parseInt(this.meet.style?.height) <= 25
      }
    },
    textInOneLine () {
      if (this.meet.style?.height) {
        return parseInt(this.meet.style?.height) <= 41 && parseInt(this.meet.style?.height) > 25
      }
    },
    currentSessionKey () {
      return this.meet.session_key
    },
    formattedDuration () {
      const duration = this.meet.duration
      if (duration) {
        return util.formatDuration(duration, this._genLangItems(['day', 'days', 'hr', 'hrs', 'min', 'mins']))
      } else {
        return ''
      }
    },
    formattedParticipants () {
      const count = this.meet.totalMembers
      if (count === 1) {
        return this.$t(this.meet.isFlowBoard?'members_one':'one_participant')
      } else {
        return this.$t(this.meet.isFlowBoard?'members_other':'multi_participants', {
          number: count,
          count
        })
      }
    },
    hostName () {
      return ObjectUtils.getByPath(this.meet, 'host.displayDeletedUser') ? `[${this.$t('Deleted_User')}]` :
    (ObjectUtils.getByPath(this.meet, 'host.name') || ObjectUtils.getByPath(this.meet,'host.displayName'))
    },
    meetStatus () {
      const status = {
        showJoinButton: false,
        showStartButton: false,
        showRestartButton: false,
        startButtonText: this.$t('start'),
        restartButtonText: this.$t('restart')
      }
      if (this.meet.vendor_service_type !== MxConsts.VendorServiceType.SERVICE_OFFLINE) {
        if (this.meet.isJoinable && !this.meet.isDeclined) {
          if (this.meet.isStarted) {
            status.showJoinButton = true
          } else {
            if (this.meet.isHost) {
              status.showStartButton = true
            } else {
              status.showJoinButton = true
            }
          }
        } else if (this.meet.isStartable) {
          status.showStartButton = true
        } else if (this.meet.isRestartable) {
          status.showRestartButton = true
        }
      }
      return status
    },
    showRecordingPlayIcon () {
      return this.meet.recordingUrl && !this.hideMeetingRecording
    },
    restarting () {
      if (this.restartingMeets.length) {
        return this.restartingMeets.findIndex(meet => meet.board_id === this.meet.board_id) > -1
      } else {
        return false
      }
    },
    ariaLabel () {
      let firstPart = this.$t('Meeting_topic_is_topic_', {topic: this.meet.topic}), secondPart = ''
      /*
      * "Started_on_startTime_and_ended_on_end":
      * "Started on {{startTime}} and ended on {{endDate}}. {{count}} participants in total. Host is {{hostName}}",
      * "Scheduled_on_startTime_and_duration_is_duration_":
      * "Scheduled on {{startTime}} and duration is {{duration}}. {{count}} participants in total. Host is {{hostName}}",
      * "Scheduled_on_startTime_and_currently_in_progress":
      * "Scheduled on {{startTime}} and currently in-progress. Host is {{hostName}}",
      * */

      if (this.meet.scheduled_start_time) {
        const startTime = TimeUtils.formatShortDateTime(this.meet.scheduled_start_time)
        const endDate = TimeUtils.formatShortDate(this.meet.scheduled_end_time)
        if (this.meet.isStarted) {
          secondPart = this.$t('Scheduled_on_startTime_and_currently_in_progress', {
            startTime,
            hostName: this.hostName
          })
        } else if (this.meet.isEnded) {
          secondPart = this.$t('Started_on_startTime_and_ended_on_end', {
            startTime,
            endDate,
            count: this.meet.totalMembers,
            hostName: this.hostName
          })
        } else {
          secondPart = this.$t('Scheduled_on_startTime_and_duration_is_duration_', {
            startTime,
            duration: this.formattedDuration,
            count: this.meet.totalMembers,
            hostName: this.hostName
          })
        }
      }
      let thirdPart = ''
      if (this.meet.isAccepted) {
        thirdPart = '. Meeting is accepted'
      } else if (this.meet.isDeclined) {
        thirdPart = '. Meeting is declined'
      }
      return firstPart + '.' + secondPart + thirdPart
    }
  },
  methods: {
    ...mapActions('meet', ['sendServerLog']),
    ...mapMutations('calendar', ['recordRestartMeet']),
    play () {
      this.$emit('play', {
        recordingUrl: this.meet.recordingUrl,
        meetTopic: this.meet.topic,
        meetId: this.meet.board_id
      })
    },
    joinMeet (event) {
      event.stopPropagation()
      this.$bus.$emit(Meet.JOIN_MEET, {
        topic: this.meet.topic,
        session_key: this.meet.session_key,
        //Add for zoom meeting integration
        vendor_join_url:this.meet.vendor_join_url
      })
      this.sendServerLog({meetId: this.meet.session_key, place: 'MX-Calendar-Event', isStart: false})
    },
    startMeet (event) {
      event.stopPropagation()
      this.doStartMeet()
    },
    restartMeet (event) {
      event.stopPropagation()
      this.doStartMeet(true)
      const {session_key, board_id} = this.meet
      this.recordRestartMeet({session_key, board_id})
    },
    doStartMeet (isRestart) {
      const params = {
        topic: this.meet.topic,
        session_key: this.meet.session_key,
        //Add for zoom meeting integration
        vendor_start_url:this.meet.vendor_start_url
      }
      if (this.meet.original_binder_id) {
        params.original_binder_id = this.meet.original_binder_id
        //Pass the parameters to send chat message to the original binder
        params.vendor_join_url = this.meet.vendor_join_url;
        params.vendor_service_type = this.meet.vendor_service_type;
      }
      if (isRestart) {
        params.is_restart = isRestart
        if (this.meet.isRecurrent) {
          // for MVB-19596, deleted recurring meet still can be restarted
          params.board_id = this.meet.board_id
        }
      } else {
        params.scheduled_start_time = this.meet.scheduled_start_time
      }
      this.$bus.$emit(Meet.START_MEET, params)
      this.sendServerLog({meetId: params.session_key, place: 'MX-Calendar-Event', isStart: true})
    },
    _genLangItems (keysArr) {
      const langsObj = {}
      for (const key of keysArr) {
        langsObj[key] = this.$t(key)
      }
      return langsObj
    }
  }
}
</script>

<style lang="scss" scoped>
.meet-name {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  &.declined {
    text-decoration: line-through;
  }
}
.meet-block-inner {
  border-radius: 3px;
  background-color: #F4F4F4 !important;
  height: 100%;
  padding: 2px 8px;
  &.declined {
    background-color: white!important;
    border: 1px $mx-color-var-text-tertiary dashed;
    color: $mx-color-var-text-tertiary;
    .meet-name {
      text-decoration: line-through;
    }
  }
  &:hover,
  &.active {
    background-color: #E0E0E0!important;
  }
}

.meet-block-inner.small-block {
  padding: 0 8px;
  .meet-name {
    font-size: 12px;
    line-height: 16px;
  }
  .meet-detail {
    display: none;
  }
}
  .mx-calendar-event {
    cursor: pointer;
    user-select: none;
    .micon-play-with-circle {
      padding-top: 4px;
    }
    .micon-mep-warning {
      font-size: 18px;
      padding-top: 4px;
      color: $mx-color-var-caution;
    }

    line-height: 1.1;
    .meet-detail {
      font-size: 12px;
      line-height: 16px;
    }

    &.meet-past {
      color: $mx-color-var-label-tertiary;
    }
    .meet-actions {
      display: flex;
      margin-left: auto;
      flex-flow: row-reverse;
      margin-bottom: auto;
    }
  }
  .text-container {
    max-width: 100%;
    overflow: hidden;
  }
.text-one-line {
  display: flex;
  align-items: flex-start;
  .meet-name {
    flex: 0 1 auto;
  }
  .meet-detail {
    line-height: 20px;
    margin-left: 8px;
    flex: 1 1 26px;
    min-width: 26px;
  }
}
.meet-block-inner {
  container-type: size;
  container-name: meetblockinner;
}
@container meetblockinner (max-width: 119px) {
  .meet-actions .el-button {
    display: none;
  }
}

@container meetblockinner (max-height: 23px) {
  .meet-actions .el-button {
    display: none;
  }
}
.hover-svg:hover {
  path {
    fill: var(--duo-primary-color);
  }
}
</style>

