<template>
    <ul class="picker-menu "  v-a11y-list="{
							itemSelector: '.picker-item',
							defaultFocus: true,
							role: 'list',
          		subRole : 'listitem',
              horizontal: true
							}">
        <li v-for="size in fonts" :class="{active:value==size}" @click="onSelect(size)">
            <div class="picker-item" :style="{fontSize:size}">{{size}}px</div>
        </li>
    </ul>
</template>

<script>
  export default {
    name: "FontSizeSelector",
    props:{
      value:{
        type:Number|String,
        default:12
      }
    },
    data:function () {
      return {
        fonts:[8, 10, 12, 14, 16, 18, 20, 22, 24, 28, 32, 36, 40, 48]
      }
    },
    methods: {
      onSelect(size){
        this.$emit('change',size)
      }
    }
  }
</script>

<style scoped>

</style>
