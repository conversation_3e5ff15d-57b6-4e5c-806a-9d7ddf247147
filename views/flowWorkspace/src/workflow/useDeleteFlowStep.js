import {useI18n} from "vue-i18n-composable";
import {getCurrentInstance} from "@vue/composition-api";
import {safeHtml} from "../../../common/directives/safeHtml";

export function useConfirmAndDeleteFlowStep(removeFlowStep, checkStepDeletable) {
    const {t} = useI18n()
    const vm = getCurrentInstance()
    const showDeleteLimitAlert = () =>{
        vm.proxy.$mxConfirm(t('you_cannot_remove_the_only_step_in_a_flow_please_add_at_least_one_more_step_to_remove_this_one_'), t('unable_to_remove_step'), {
            customClass: 'new-style',
            confirmButtonText: t('Dismiss'),
            showCancelButton: false,
        })
    }
    return (step) => {
        return new Promise((resolve, reject) => {

            if(!checkStepDeletable(step)) {
                showDeleteLimitAlert()
                return resolve()
            }
            let title = t('this_action_cannot_be_undone_')
            let info = t('Delete_step', {
                stepName: step.title || step.name
            })
            let opts = {
                customClass: 'new-style',
                confirmButtonText: t('delete'),
                confirmButtonType: 'danger'
            }
            if(step.type =='WORKFLOW_STEP_TYPE_MILESTONE') {
                title = t('Delete_milestone_and_actions')
                info = t('delete_milestone_confirm_info', {
                    title: safeHtml(step.title || step.name)
                })
                opts.dangerouslyUseHTMLString =true
            }
            vm.proxy.$mxConfirm(
                info,title,
                opts).then(() => {
                    const successInfo = t('Step_removed')
                removeFlowStep(step).then(() => {
                    vm.proxy.$mxMessage.success(successInfo)
                    resolve()
                }).catch(err => {
                    if (err.onlyOne) {
                        showDeleteLimitAlert()
                    } else if(err.alreadyDeleted) {
                        //do nothing
                    } else {
                        vm.proxy.$mxMessage.success(t('Unable_to_remove_step'))
                    }
                })
            })
        })
    }
}
