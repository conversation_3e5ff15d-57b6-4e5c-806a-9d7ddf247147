import {
  createFormCard as _createFormCard,
} from '@views/contentLibrary/plugins/form/defines'
import { processPlaceholder } from '@views/contentLibrary/plugins/form/common/util'
import moment from 'moment-timezone'
import { useStore } from '@views/common'

const MOXO_STEP = '@moxo.step'
const isMoxoStep = (field) => field?.fieldSpecific?.isMoxoStep

export enum FormCardType {
  PAGE = 'Page',
  SINGLELINETEXT = 'SingleLineText',
  MULTILINETEXT = 'MultiLineText',
  NUMBER = 'Number',
  DATE = 'Date',
  DROPDOWNLIST = 'DropdownList',
  HEADING = 'Heading',
  MOXOSTEP = 'MoxoStep'
}

/**
 * integration Forms map to Web forms
 */
export enum FormMapping {
  string = FormCardType.SINGLELINETEXT,
  text = FormCardType.MULTILINETEXT,
  number = FormCardType.NUMBER,
  date = FormCardType.DATE,
  time = FormCardType.DATE,
  'date-time' = FormCardType.DATE,
  select = FormCardType.DROPDOWNLIST,
  heading = FormCardType.HEADING,
  '@moxo.step' = FormCardType.MOXOSTEP
}

export enum IntegrationFormType {
  SELECT = 'select',
  TIME = 'time',
  DATE = 'date',
  DATETIME = 'date-time',
  HEADING = 'heading',
  STRING = 'string',
  TEXT = 'text',
  NUMBER = 'number',
  MOXOSTEP = '@moxo.step'
}

const createFormCard = (type: FormCardType) => {
  return _createFormCard(type)
}

interface SelectOption {
  label: string;
  value: string;
}

interface FormProps {
  options?: SelectOption[];
  style?: string;
  title?: string;
  description?: string;
}

export interface IntegrationFormModel {
  type: string;
  label?: string;
  key?: string;
  tip?: string;
  required?: boolean;
  default?: string;
  props?: FormProps;
  placeholder?: string;
  precision?: number;
}

const transformIntegrationFormToWebForm = (integrationForm = [], t, moxoSteps = []): any[] => {
  const fields = []
  integrationForm.forEach((form) => {
    if(FormMapping[form.type]) {
        const type: FormCardType = FormMapping[form.type] as FormCardType
        let field
        /**
         * TODO: special type (temp solution, will be removed later)
         *  if the form type is "@moxo.step",  need to return the "FormCardType.DROPDOWNLIST" type and options are optional steps
         */
        if(type === FormCardType.MOXOSTEP) {
          field = processPlaceholder(createFormCard(FormCardType.DROPDOWNLIST), t)
        } else {
          field = processPlaceholder(createFormCard(type), t)
        }
        setFieldValue(field, form, moxoSteps)
        fields.push(field)
    }
  })
  return fields
}

//defaultFieldSetting
const setFieldValue = (field, formDefaultVal = {} as IntegrationFormModel, moxoSteps = []) => {
  const currentField = field.$values
  if (currentField && !currentField.fieldSpecific.condition) {
    currentField.fieldSpecific.condition = {
      enable: false,
      visible: true,
      rule: true,
      rules: []
    }
  }

  const { type, key, label, tip, placeholder, required, default: defaultValue, props, precision } = formDefaultVal

  currentField.label = label
  //tip
  currentField.fieldSpecific.placeholder = placeholder
  currentField.fieldSpecific.supporting = tip

  //required
  currentField.fieldSpecific.required = required ? true : false
  currentField.fieldSpecific.readonly = false

  if(type === IntegrationFormType.NUMBER) {
    currentField.fieldSpecific.precision = precision
  }

  //default
  if(type === IntegrationFormType.SELECT) {
    if(Array.isArray(defaultValue) && defaultValue.length) {
      currentField.value = defaultValue[0]
    } else {
      currentField.value = defaultValue
    }
  } else if(type === IntegrationFormType.MOXOSTEP) {
    currentField.fieldSpecific.isMoxoStep = true
    //support integration system hardcode logic
    /**
      key: "moxo_step_type"
      value: {
        @moxo.step: {
          client_uuid: "4cb0dab3-052d-49e1-abf3-ef5cebd733de"
        }
      }
     */
    /**
     * defaultValue:
     *  @moxo.step: {
     *    client_uuid: "4cb0dab3-052d-49e1-abf3-ef5cebd733de"
     *  }
     */
    if(defaultValue) {
      currentField.value = defaultValue?.[MOXO_STEP]?.client_uuid
    }
  } else {
    const _type = type as IntegrationFormType
    if(![IntegrationFormType.DATE, IntegrationFormType.TIME, IntegrationFormType.DATETIME].includes(_type)) {
      if([IntegrationFormType.STRING, IntegrationFormType.TEXT, IntegrationFormType.NUMBER].includes(_type)) {
        currentField.defaultValue = defaultValue
      } else {
        currentField.value = defaultValue
      }
    }
  }

  //key
  currentField.id = key

  if(type === IntegrationFormType.SELECT) {
    //reset options
    currentField.fieldSpecific.options.splice(0)

    //push new options
    const { options } = props
    currentField.fieldSpecific.options.push(...(options || []))
  }

  if(type === IntegrationFormType.MOXOSTEP) {
    //reset options
    currentField.fieldSpecific.options.splice(0)

    //push new options

    const options = moxoSteps.map(step => ({ label: step.title, value: step.clientUuid }))
    currentField.fieldSpecific.options.push(...options)
  }

  if(type === IntegrationFormType.TIME) {
    try {
      currentField.fieldSpecific.withTime = true
      currentField.fieldSpecific.withDate = false
      if(defaultValue) {
        const parsedTime = moment(defaultValue, "HH:mm:ss");
        const hour = parsedTime.format("hh");
        const minute = parsedTime.format("mm");
        const timeFormat = parsedTime.format("A");
    
        currentField.defaultValue = {
          hour,
          minute,
          timeFormat,
          timestamp: 1702314802086, //hard code for varify form required feild
          timestampWithTime:0
        }
      } else {
        currentField.defaultValue = {
          hour: null,
          minute: null,
          timeFormat: 'AM',
          timestamp: 1702314802086, //hard code
          timestampWithTime:0
        }
      }
    } catch (error) {
      console.error(error)
    }
  }

  if(type === IntegrationFormType.DATE) {
    currentField.fieldSpecific.withTime = false
    try {
      if(defaultValue) {
        currentField.defaultValue = {
          dateStr: defaultValue,
          timestamp:  moment(defaultValue, "YYYY-MM-DD").valueOf(),
          timestampWithTime:0
        }
      } else {
        currentField.defaultValue = {
          timestampWithTime: 0,
          timestamp: 0
        }
      }
    } catch (error) {
      console.error(error)
    }
  }

  if(type === IntegrationFormType.DATETIME) {
    currentField.fieldSpecific.withTime = true

    try {
      if(defaultValue) {
        const parsedDateTime = moment(defaultValue);
  
        const hour = parsedDateTime.format("hh");
        const minute = parsedDateTime.format("mm");
        const timeFormat = parsedDateTime.format("A");
        currentField.defaultValue = {
          dateStr: defaultValue,
          hour,
          minute,
          timeFormat,
          timestamp:  moment(defaultValue).valueOf(),
          timestampWithTime:0
        }
      } else {
        currentField.defaultValue = {
          timeFormat: 'AM',
          timestamp:  0,
          timestampWithTime:0
        }
      }
    } catch (error) {
      console.error(error)
    }
  }

  if(type === IntegrationFormType.HEADING) {
    currentField.label = props?.title
    currentField.fieldSpecific.supporting = props?.description
    // switch(style) {
    //   case 'title':
    //     currentField.label = props.title
    //     currentField.fieldSpecific.supporting = props.description
    //     break;
    //   case 'subtitle':
    //     currentField.label = ''
    //     currentField.fieldSpecific.supporting = label
    //     break;
    // }
  }
}

//After go back, the 'endPageIndex' page data cannot be passed to build fucntion
const transformResult = (formPages = [], endPageIndex) => {
  const userStore = useStore('user')
  // const resFormModel = {}
  const resFormModel = []
  formPages.forEach((page, index) => {
    if(index > endPageIndex) return

    const fields = page.fields || []
    fields.forEach((field) => {
      //select need return array
      if(field.type === 'select') {
        // resFormModel[field.id] = field.value? [field.value]: []
        resFormModel.push({
          key: field.id,
          label: field.label,
          value: field.value? [field.value]: []
        })
      }
      if(field.fieldSpecific?.withDate && !field.fieldSpecific?.withTime) {
        // date
        // resFormModel[field.id] = moment(field.value?.timestamp).format("YYYY-MM-DD")
        const _value = field.value?.timestamp? moment(field.value?.timestamp).format("YYYY-MM-DD"): void 0

        resFormModel.push({
          key: field.id,
          label: field.label,
          value: _value
        })
      } else if(!field.fieldSpecific?.withDate && field.fieldSpecific?.withTime) {
        // time 
        const {hour, minute, timeFormat } = field.value || {}
        if(hour && minute) {
          const timeMoment = moment({ hour: hour, minute: minute });
          if (timeFormat === "PM") {
            timeMoment.add(12, 'hours');
          }
          // resFormModel[field.id] = timeMoment.format("HH:mm:ss")
          resFormModel.push({
            key: field.id,
            label: field.label,
            value: timeMoment.format("HH:mm:ss")
          })
        } else {
          resFormModel.push({
            key: field.id,
            label: field.label,
            value: void 0
          })
        }
      } else if(field.fieldSpecific?.withDate && field.fieldSpecific?.withTime) {
        //date-time
        const userTimezone = userStore.userTimezone?.value
        const _value = field.value?.timestamp? moment(field.value.timestamp).tz(userTimezone).format(): void 0

        // resFormModel[field.id] = moment(field.value.timestamp).tz(userTimezone).format();
        resFormModel.push({
          key: field.id,
          label: field.label,
          value: _value
        })
      } else if(field.type === FormCardType.DROPDOWNLIST && isMoxoStep(field)) {
        // handle @moxo.step type
        // resFormModel[field.id] = {
        //   [MOXO_STEP]: {
        //     "client_uuid": field.value
        //   }
        // }
        const _value = {
          [MOXO_STEP]: {
            "client_uuid": field.value
          }
        }
        const resValue = field.value ? _value: void 0
        resFormModel.push({
          key: field.id,
          label: field.label,
          value: resValue
        })
      } else {
        // resFormModel[field.id] = field.value
        resFormModel.push({
          key: field.id,
          label: field.label,
          value: field.value
        })
      }
    })
  })

  return resFormModel
}

export { createFormCard, transformIntegrationFormToWebForm, transformResult }
