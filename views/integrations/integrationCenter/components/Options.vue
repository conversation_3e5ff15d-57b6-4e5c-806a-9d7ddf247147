<template>
  <div class="options-container">
    <ul v-for="item in options"
        :key="item.value"
        class="options-group__wrap">
      <li v-if="item.label"
          class="mx-color-secondary mx-text-c4 mx-padding-left-xs cate options-group__title">
        {{ item.label }}
      </li>
      <li>
        <ul class="options-group">
          <li v-for="option in item.options"
              :key="option.value"
              class="options-group__item"
              :class="{'active': value === option.value}"
              @click="showCategory(option)">
            <span>
              {{ option.label }}
            </span>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'IntegrationOptions',
  components: {},
  model: {
    event: 'change',
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    options: {
      type: Array,
      default: () => [],
    },
  },
  computed: {},
  watch: {},
  methods: {
    showCategory (item) {
      this.$emit('change', item.value)
    },
  },
}
</script>

<style scoped lang="scss">
.options-group__wrap {
  .options-group__title {
    height: 16px;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
    color: $mx-color-var-text-tertiary;
    padding: 0px;
    margin: 16px 0px 8px 10px;
  }

  .options-group__item {
    height: 36px;
    display: flex;
    align-items: center;
    padding-left: 10px;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;

    &:hover,
    &.active {
      cursor: pointer;
      background: $mx-color-var-fill-quaternary;
      border-radius: 4px;
    }
  }
}
</style>
