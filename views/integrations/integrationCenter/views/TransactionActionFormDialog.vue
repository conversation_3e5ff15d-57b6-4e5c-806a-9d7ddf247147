<template>
  <BaseModal
    :visible.sync="visibleProxy"
    :modal-option="{
      width: '540px',
      beforeClose: beforeClose
    }"
    class="mobile-fullscreen-dialog integration-dialog"
    :class="{ 'in-preview': isPreview }"
    :showClose="isWorkflow || !isPreview"
    @close="onClose"
  >
    <template slot="title">
      <div class="flex-center" style="width: 100%; justify-content: space-between;">
        <div style="display: flex; align-items: center; flex: 1; width: 90%;">
          <i
            v-if="showGoback"
            class="micon-left-arrow-new go-back-nav-icon"
            :class="{ disabled: isProcessing }"
            @click="handleGoback"
          />
          <IntegrationIcon :imgSrc="compImgSrc" />
          <div class="mx-ellipsis">
            {{ integrationTitle }}
          </div>
        </div>
      </div>
    </template>
    <template slot="content">
          <IntegrationFormView
            ref="formRef"
            class="integration-form-view"
            :fill-form-options="actionFuncParam"
            :action-type="IntegrationFormActionType.FillForm"
            @success="onClose"
          />
        </template>
  </BaseModal>
</template>

<script>
import {
  ref,
  defineComponent,
  getCurrentInstance,
  computed,

} from '@vue/composition-api'
import { visibleMixin } from '@views/common/components/modals/mixins'
import { useI18n } from '@views/common'
import focusToErrorItem from '@views/workflows/utils/focusToErrorItem'
import IntegrationIcon from '../components/IntegrationIcon'
import actionImages from '@views/theme/src/images/base_action/index'

import IntegrationFormView from '@views/integrations/integrationCenter/views/IntegrationFormView.vue'
import { IntegrationFormActionType } from '@views/integrations/integrationCenter/common/transformIntegrationForm'

export default defineComponent({
  name: 'TransactionActionFormDialog',
  components: {
    IntegrationFormView,
    IntegrationIcon,
  },
  mixins: [visibleMixin, focusToErrorItem],
  props: {
    presetObject: {
      type: Object,
      default: () => ({})
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    defaultForm: {
      type: Array,
      default: () => []
    },
    actionFuncParam: {
      type: Object,
      default: () => ({})
    }
  },
  data(){
    return {
      IntegrationFormActionType
    }
  },
  computed:{
    integrationOptions(){
      return
    }
  },
  setup(props, { emit }) {
    const i18n = useI18n()
    const vm = getCurrentInstance()
    const basicFormRef = ref(null)

    const integrationApp = ref(props.actionFuncParam?.customData || {})

    const compImgSrc = computed(() => {
      if (integrationApp.value.app_id) {
        return `/integration/framework/v1/apps/${integrationApp.value.app_id}/logo`
      } else {
        return actionImages.DefaultIntegrationApp
      }
    })

    const integrationTitle = computed(() => {
      return integrationApp.value?.action_title || ''
    })

    const currentViewIndex = ref(0)

    const showGoback = computed(() => {
      return props.showBack || currentViewIndex.value > 0
    })



    const goback = () => {
      currentViewIndex.value -= 1
    }

    const handleGoback = () => {
      currentViewIndex.value > 0 ? goback() : emit('back')
    }

    const onClose = (e) => {
      emit('close', e)
    }

    const beforeClose = (hide) => {
      emit('closeOnClick')
      hide()
    }







    const loading = ref(false)
    const isSending = ref(false)





    return {
      basicFormRef,
      handleGoback,
      compImgSrc,
      currentViewIndex,
      integrationApp,
      loading,
      isSending,

      showGoback,
      integrationTitle,

      onClose,
      beforeClose,
    }
  }
})
</script>

<style lang="scss" scoped>
.integration-dialog {
  ::v-deep .el-dialog {
    .el-dialog__header .left {
      width: 100%;
    }
    .el-dialog__footer {
      box-shadow: 0px -1px 0px #e0e0e0;
    }
    .el-dialog__body {
      padding-bottom: 0;
      overflow: auto;
      padding: 0px;
    }
  }
  .integration-form-view{
    min-height: 400px;
  }
  &.in-preview {
    ::v-deep .el-dialog {
      .el-dialog__body {
        padding: 24px;
        background-color: $mx-color-var-fill-quaternary;
      }
      .el-dialog__footer {
        display: none;
      }
    }
  }
}
.flex-center {
  display: flex;
  align-items: center;
}

.object-icon {
  margin-right: 10px;
  width: 24px;
}

.title-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.go-back-nav-icon {
  font-size: 16px;
  margin-right: 4px;
  cursor: pointer;
  color: $mx-color-var-text-secondary;

  &.disabled {
    cursor: default !important;
  }
}
</style>