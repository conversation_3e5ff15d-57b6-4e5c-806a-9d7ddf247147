<script>
import { useWorkspacePropertiesStore } from '@views/stores/workspaceProperties.ts'

import { mapActions as piniaMapActions } from 'pinia'
import MxDialog from '../contentLibrary/component/MxDialog.vue'
import { Defines } from '@commonUtils'
import cloneDeep from 'lodash/cloneDeep'
import uuid from 'uuid/v4'

export default {
  name: 'EditWorkspacePropertiesDialog',
  components: { MxDialog },
  props: {
    boardId: {
      type: String,
      default: '',
      required: true
    },
    value:{
      type: Array,
      default:()=>[]
    },
    saveDirectly: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    ...piniaMapActions(useWorkspacePropertiesStore, ['readBoardProperties', 'updateBoardProperties']),
    onCancel() {
      this.close()
    },
    handleNoneCase(ev, item){
      if(ev === this.mockedNoneId){
        item.value =''
      }
    },
    onSubmit() {
      if(this.saveDirectly) {
        this.inSubmitting = true
        this.updateBoardProperties(this.boardId, this.properties).then(() =>{
          this.$mxMessage.success(this.$t('Workspace_tags_saved'))
          this.close()
        }).catch(err=>{
          this.$mxMessage.error(this.$t('something_went_wrong_retry'))
        }).finally(() =>{
          this.inSubmitting = false
        })
      }else{
        this.$emit('update', this.properties)
        this.close()
      }
    },
    close() {
      this.$emit('close')
    },
    setDisplayedProperties(items) {
      let properties = cloneDeep(items)
      this.properties = properties.map(item => {
        if(item.type === Defines.PropertyType.PROPERTY_TYPE_LIST) {
          if(item.value && item.options.length && !item.options.includes(item.value)){
            item.value = ''
          }
        }
        return item
      })
    }
  },
  data () {
    return {
      properties: [],
      PropertyType: Defines.PropertyType,
      loading: false,
      inSubmitting: false,
      mockedNoneId: uuid()
    }
  },
  created () {
    this.loading = true
    if(!this.saveDirectly && this.value?.length) {
      //in flow template builder
      this.setDisplayedProperties(this.value)
      this.loading = false
    }else {
      this.readBoardProperties(this.boardId).then(([properties]) => {
        this.setDisplayedProperties(properties)
        this.loading = false
      })
    }
  }
}
</script>

<template>
  <MxDialog
    @close="$emit('close')">
    <span slot='title' class="custom-dialog-title">{{$t('Edit_tags')}}</span>
    <div class="properties-content" v-loading="loading">
      <ElForm v-if="!loading" label-position="top">
        <ElFormItem
          v-for="item in properties"
          :key="item.sequence"
          :class="{ 'property-text-input': item.type === PropertyType.PROPERTY_TYPE_TEXT }" >
          <div slot="label">
              <div
                class="property-label mx-ellipsis">
                <el-tooltip
                  class="mx-ellipsis"
                  popper-class="my-custom-tooltip"
                  :content="item.name"
                  effect="dark"
                  placement="top">
                  <span v-safe-text="item.name"></span>
                </el-tooltip>
              </div>

          </div>
          <ElSelect
            v-if="item.type === PropertyType.PROPERTY_TYPE_LIST"
            :placeholder="$t('Please_choose')"
            v-model="item.value" @change="handleNoneCase($event, item)">
            <ElOption
              :value="mockedNoneId"
              :label="$t('none')" />
            <ElOption
              v-for="(option,index) in item.options"
              :key="index"
              :value="option"
              :label="option" />
          </ElSelect>
          <ElInput
            v-else
            v-model="item.value" 
            type="textarea"
            :maxlength="300"
            :placeholder="$t('Please_enter')"
            :autosize="{ minRows: 1 }" />
        </ElFormItem>
      </ElForm>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button
        type="secondary"
        :disabled="inSubmitting"
        @click="onCancel"> {{ $t('cancel') }}</el-button>
      <el-button
        type="primary"
        :disabled="inSubmitting"
        @click="onSubmit">
        {{ $t('save_changes') }}
      </el-button>
    </div>

  </MxDialog>
</template>

<style scoped lang="scss">
.property-text-input {
  ::v-deep .el-form-item__content {
    line-height: initial;
  }
}
.properties-content{
  padding: 20px;
  min-height: 300px;
  .el-select{
    width: 100%;
  }
  overflow: auto;
}
.dialog-footer{
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  flex-grow: 1;
  .el-button {
    width: 100%;
  }
}
</style>
