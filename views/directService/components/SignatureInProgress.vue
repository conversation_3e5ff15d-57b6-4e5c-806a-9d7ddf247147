<template>
  <div
    v-show="currentFileName"
    class="in-progress"
    :class="{ 'has-border': hasBorder }">
    <MxAggregateThumbnail
      v-show="showRetry || !errorMessage"
      style="margin-left: 0"
      :file-type="fileType"
      :source="currentBoardFile.thumbnail || ''" />
    <div class="details">
      <template v-if="directUpload && errorMessage && !showRetry">
        <div class="mx-text-c1 mx-ellipsis">
          {{ errorMessage }}
        </div>
        <div class="mx-text-c2 mx-ellipsis mx-color-secondary">
          {{ $t('remove_and_select_different_one') }}
          <a
            v-if="showLearnMore"
            class="learn-more"
            @click="showAllowedFileTypes()"
            >{{$t('learn_more')}}</a> 
        </div>
      </template>
      <template v-else-if="errorMessage && !showRetry">
        <div class="mx-text-c1 mx-ellipsis">
          {{ $t('cannot_support_esign_file') }}
        </div>
        <div class="mx-text-c2 mx-ellipsis mx-color-secondary">
          {{ errorMessage }}
        </div>
      </template>
      <template v-else>
        <div class="mx-text-c2 mx-ellipsis">
          {{ currentFileName }}
        </div>
        <div
          :class="['mx-text-c2',showRetry ? 'mx-color-danger':'mx-color-secondary']"
          role="status">
          {{ showRetry ? $t('Upload_failed') : totalSize }}
        </div>
      </template>
    </div>
    <div class="status">
      <el-tooltip
        v-if="showRetry"
        :content="$t('try_again')"
        placement="top"
        popper-class="overflow-control-tooltip">
        <el-button
          type="gray"
          square
          size="small"
          style="margin-right: 16px; font-size: 14px"
          @click="tryAgain">
          <img 
            :src="require('../../theme/src/fonts/icons/source/refresh.svg').default" 
            height="15" 
            style="vertical-align: top">
        </el-button>
      </el-tooltip>
      <div
        v-if="showProgress"
        style="position: relative;">
        <el-progress
          class="progress-bar"
          type="circle"
          :show-text="false"
          :stroke-width="5"
          :percentage="progress.percentage"
          :color="progress.color"
          :width="20" />
      </div>
      <template v-else>
        <el-tooltip
          v-if="canReplaceFile"
          placement="top-end"
          popper-class="overflow-control-tooltip">
          <div slot="content">
            <div class="mx-text-c1 text-left">
              {{ $t('replace') }}
            </div>
            <div
              class="mx-text-c4 text-left"
              style="color: rgba(255, 255, 255, 0.60)">
              {{ $t('edit_esign_replace_doc_tips') }}
            </div>
          </div>
          <UploadBaseFileDropdown 
            place="action"
            :currentBoard="currentBoard"
            @uploading="doReplaceUpload">
            <el-button 
              type="gray"
              size="small"
              :square="true">
              <i class="micon-reopen" />
            </el-button>
          </UploadBaseFileDropdown>
        </el-tooltip>
        <el-tooltip
          v-if="!hideRemoveBtn"
          placement="top-end"
          popper-class="overflow-control-tooltip"
          :disabled="!canReplaceFile">
          <div slot="content">
            <div class="mx-text-c1 text-left">
              {{ $t('delete') }}
            </div>
            <div
              class="mx-text-c4 text-left"
              style="color: rgba(255, 255, 255, 0.60)">
              {{ $t('edit_esign_delete_doc_tips') }}
            </div>
          </div>
          <el-button 
            type="gray"
            size="small"
            :square="true"
            @click="beforeCancelUpload">
            <i :class="[deleteIcon, 'mx-color-danger']" />
          </el-button>
        </el-tooltip>
      </template>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex'
import MxAggregateThumbnail from '../../aggregate/components/previewImage'
import {MxConsts} from '@commonUtils'
import {BoardResourceStatus} from 'isdk/src/proto/generated/BoardResourceStatus';
import errorMessageUtil from '../../common/components/uploader/utils';
import {FunctionUtil} from '../../../commonUtils';
import {doUpload} from '../../binderDetail/src/components/remoteUploader';
import utils from '@views/common/utils/utils'
import UploadBaseFileDropdown from '@views/signature/src/new/assign/components/uploader/UploadBaseFileDropdown.vue'

const ErrorCode = MxConsts.ErrorCode

export default {
  name: 'SignatureInProgress',
  components: {
    MxAggregateThumbnail,
    UploadBaseFileDropdown
  },
  props:{
    deleteIcon: {
      type: String,
      default: 'micon-delete'
    },
    directUpload: {
      type: Boolean,
      default: false
    },
    hideRemoveBtn: {
      type: Boolean,
      default: false
    },
    canReplaceFile: {
      type: Boolean,
      default: false
    },
    hasBorder: {
      type: Boolean,
      default: true
    },
    currentBoard: {
      type: Object,
      default: () => ({})
    },
    beforeRemove: Function
  },
  data () {
    return {
      progress: {
        percentage: 0,
        color: ''
      },
      deletedFiles: [],
      doReplaceDoc: false,
      doRemoveDoc: false
    }
  },
  computed: {
    ...mapGetters('directService',
        [
          'currentBinder',
          'isConversionFailed',
          'boardFiles',
          'isConvertedDone',
          'currentBoardFileReadyForSign'
        ]),
    ...mapGetters('group', ['groupTags', 'remoteUploadOptions']),
    ...mapGetters('privileges', ['allowedFileTypesAll']),
    showProgress () {
      if (this.errorMessage || this.showRetry) {
        return false
      } else {
        return this.isInProgress
      }
    },
    isInProgress () {
      if (this.currentBoardFile.sequence) {
        return [BoardResourceStatus.BOARD_RESOURCE_STATUS_CONVERTING, BoardResourceStatus.BOARD_RESOURCE_STATUS_QUEUED].indexOf(this.currentBoardFile.resourceStatus) > -1
      } else {
        return true
      }
    },
    showRetry () {
      const ErrorCode = MxConsts.ErrorCode
      if (this.currentBoardFile.error) {
        return [ErrorCode.ExceedClientBodyMax, ErrorCode.ExceedFileSizeMax, ErrorCode.ExceedUserCloudMax, ErrorCode.UploadDetectedVirus, ErrorCode.FileTypeNotSupport, ErrorCode.ExceedFileMaxSize].indexOf(this.currentBoardFile.error.code) === -1
      }
    },
    currentBoardFile () {
      return this.boardFiles.filter(f=>{
        return this.deletedFiles.indexOf(f.client_uuid) === -1
      })[0] || {}
    },
    currentFileName () {
      return this.currentBoardFile.name || ''
    },
    fileType () {
      let name = this.currentFileName
      if (name && /.\w+$/.test(name)) {
        return name.match(/.\w+$/)[0].slice(1)
      } else {
        return ''
      }
    },
    errorMessage () {
      if (this.currentBoardFile.error) {
        return errorMessageUtil.getErrorMessage(this.currentBoardFile.error) || this.$t('upload_file_failed')
      } else if (this.isConversionFailed && !this.directUpload) {
        return this.$t('remove_and_select_different_one')
      }
    },
    totalSize () {
      const file = this.currentBoardFile
      if (file.totalSize) {
        return file.totalSize
      } else if (file.pageType === 'PAGE_TYPE_WHITEBOARD') {
        return this.$t('whiteboard')
      } else {
        let fileSize
        if (file.isContentLibrary) {
          fileSize = file.file?.raw?.fileSize
        } else if (file.file) {
          fileSize = file.file.size
        } else if (file.size) {
          fileSize = file.size
        }
        if (fileSize) {
          const {number, unit} = utils.getSizeObj(fileSize, true)
          return `${number}${unit}`
        }
        return ''
      }
    },
    showLearnMore(){
      return this.currentBoardFile.error?.code === ErrorCode.FileTypeNotSupport
    }
  },
  watch: {
    currentBoardFile: {
      handler (file) {
        this.setProgress()
      },
      deep: true
    },
    boardFiles (files) {
      files.forEach((f)=>{
        if (this.deletedFiles.indexOf(f.client_uuid) > -1) {
          this.deleteFile(f)
        }
      })
    },
    showProgress (newStatus, oldStatus) {
      if (!newStatus && oldStatus) {
        if (this.currentBoardFile.resourceStatus === 'BOARD_RESOURCE_STATUS_CONVERTED' || this.currentBoardFile.fileType === 'WHITEBOARD') {
          this.$emit('fileChange', {file: this.currentBoardFile, doReplace: this.doReplaceDoc, doRemove: this.doRemoveDoc})
        }
      }
    }
  },
  methods: {
    ...mapMutations('directService', ['updateBoardFiles']),
    ...mapActions('directService', ['deleteFile', 'makeUploadUrl', 'copyFiles']),
    setProgress () {
      let file = this.currentBoardFile, percentage = 0
      if (file.percentage !== undefined) {
        percentage = file.percentage * 0.5
      } else if (file.totalPages && file.convertedPage) {
        percentage = file.convertedPage / file.totalPages * 50 + 50
      } else {
        percentage = 50
      }
      this.progress.percentage = percentage
      /*if (percentage < 40) {
        this.progress.color = '#ff8d5a'
      } else if (percentage <= 75) {
        this.progress.color = '#ffd417'
      } else {
        this.progress.color = '#40ad75'
      }*/
      this.progress.color = "#1C72E3"
    },
    tryAgain () {
      if (this.currentBoardFile.isRemote) {
        let {name, url, id, token, type} = this.currentBoardFile.remoteFileDetail
        this.cancelUpload()
        doUpload(name, url, FunctionUtil.uuid(), token, type)
      } else if (this.currentBoardFile.isContentLibrary) {
        this.copyFiles(this.currentBoardFile.copyFilesParam)
      } else {
        this.makeUploadUrl({file:this.currentBoardFile.file, retryId: this.currentBoardFile.client_uuid, noFeed: true})
      }
    },
    determineReplaceOrRemove (type) {
      // the action type will be determined by last click
      if (this.canReplaceFile) {
        if (type === 'replace') {
          this.doReplaceDoc = true
          this.doRemoveDoc = false
        }
        if (type === 'remove') {
          this.doRemoveDoc = true
          this.doReplaceDoc = false
        }
      }
    },
    doReplaceUpload () {
      this.determineReplaceOrRemove('replace')
      this.cancelUpload()
      this.$emit('uploading')
    },
    beforeCancelUpload () {
      this.determineReplaceOrRemove('remove')
      if (this.beforeRemove) {
        this.beforeRemove(this.cancelUpload)
      } else {
        this.cancelUpload()
      }
    },
    cancelUpload () {
      const currentBoardFile = this.currentBoardFile
      const clientId = currentBoardFile.client_uuid
      if (currentBoardFile.sequence) {
        this.deleteFile(currentBoardFile)
      }
      if (currentBoardFile.xhr) {
        currentBoardFile.xhr.abort()
      }
      this.deletedFiles.push(clientId)
      this.updateBoardFiles({
        files: [{
          ...currentBoardFile,
          is_deleted: true
        }]
      })
      this.$emit('cancel')
    },
    showAllowedFileTypes(){
      const fileTypes = `<strong>${this.allowedFileTypesAll.join(',').toUpperCase()}</strong>`
      const title = this.$t('allowed_file_types')
      const message = this.$t('allowed_file_types_to_share', {fileTypes})
      this.$mxConfirm(message, title, {
        confirmButtonText:this.$t('dismiss'),
        customClass:'new-style file-types-show',
        showCancelButton: false,
        confirmButtonType: 'gray',
        dangerouslyUseHTMLString: true
      })
    }
  }
}
</script>

<style scoped lang="scss">
.in-progress {
  width: 500px;
  height: 92px;
  border-radius: 6px;
  position: relative;
  display: flex;
  align-items: center;
  padding: 24px 26px;

  &.has-border {
    border: 2px dashed $mx-color-var-text-quaternary;
  }

  .details {
    flex: 1 1 100%;
    overflow: hidden;
    margin-right: 16px;
  }
  .status {
    margin-left: auto;
    display: flex;
    gap: 8px;
  }
  .learn-more{
    text-decoration: underline;
    cursor: pointer;
    font-weight: bold;
  }
}
</style>
<style lang="scss">
.file-types-show{
  top: 88px !important;
}
</style>