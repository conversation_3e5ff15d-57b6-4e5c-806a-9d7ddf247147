<template>
  <div
      class="field-card mx-branding-border-hover important"
      :data-ta="field.id">
    <i class="micon-handle mx-color-secondary"/>
    <div class="content-container">
      <div class="image-container">
        <img :src="require(`@views/theme/src/images/form/${field.icon}.svg`).default"/>
      </div>
      <div class="field-name mx-text-c4">{{ field.name }}</div>
    </div>
    <div class="drag-card-item">
      <svg-icon
          v-if="field.id === 'PageBreak'"
          size="24"
          icon-class="Page_Break"></svg-icon>
      <svg-icon
          v-else
          size="24"
          icon-class="Drop_Add"></svg-icon>
      <div>{{ dropElementText }}</div>
    </div>
  </div>
</template>

<script>
import PageBreak from '@views/theme/src/images/form/page-break.svg'
import DropAdd from '@views/theme/src/images/form/drop-add.svg'
import SvgIcon from '@views/common/components/svgIcon'

export default {
  name: 'FormFieldCard',
  props: {
    field: {
      type: Object,
      required: true
    }
  },
  components: {
    SvgIcon
  },
  computed: {
    dropElementText() {
      return this.field.id === 'PageBreak' ? this.$t('split_page_here') : this.$t('drop_to_add_an_element_here')
    },
    dropElementIcon() {
      return this.field.id === 'PageBreak' ? PageBreak : DropAdd
    }
  }
}
</script>


<style scoped lang="scss">
.field-card {
  display: flex;
  padding: 0px;
  height: 80px;
  background: #ffffff;
  cursor: grab;
  align-items: center;
  border: 1px solid transparent;

  > i {
    font-size: 16px;
    margin-left: 3px;
    visibility: hidden;
  }

  &:hover {
    // border: 1px solid $mx-color-branding!important;
    > i {
      visibility: visible;
    }
  }

  > div {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 8px;
    width: 118px;
    height: 48px;
    margin: 16px 6px 16px 4px;

    .image-container {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
    }
  }

  .field-name {
    color: $mx-color-var-text-primary;
  }

  &:first-child {
    border-top-left-radius: 6px;
  }

  &:last-child, &[data-ta="Number"] {
    border-bottom-right-radius: 6px;
  }

  &:nth-child(2) {
    border-top-right-radius: 6px;
  }

  &:nth-last-child(2):not([data-ta="Number"]), &[data-ta="Currency"] {
    border-bottom-left-radius: 6px;
  }

  .drag-card-item {
    display: none;

    .micon-plus {
      color: white;
      height: 24px;
      width: 24px;
      border-radius: 50%;
    }
  }
}

.field-card.mx-branding-border-hover.sortable-ghost {
  border-color: transparent !important;
}
</style>
