<template>
  <div class="option-item-wrap">
    <div class="options-item">
      <el-switch
          :disabled="disabled"
          v-model="localValue"
          @change="onChange" />
      <label class="mx-margin-left-xs" @click.stop>{{label}}</label>
      <el-tooltip
          v-if="!!tooltip"
          placement="top"
          popper-class="set-config-modal"
          :content="tooltip">
        <i class="micon-mep-info-square square-icon" />
      </el-tooltip>
    </div>
    <div class="options-slot" v-if="$slots.default" v-show="localValue">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "AdditionalOptionsItem",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    label:{
      type: String,
      default: ''
    },
    tooltip:{
      type: String,
      default: ''
    }
  },
  watch:{
    value(val) {
      this.localValue = val
    }
  },
  data() {
    return {
      localValue: this.value
    }
  },
  methods: {
    onChange() {
      this.$emit('change', this.localValue)
      this.$emit('input', this.localValue)
    }
  }
}
</script>

<style scoped lang="scss">
.option-item-wrap {
  margin-bottom: 14px;
  .options-item{
    display: flex;
    align-items: center;
    label{
      margin-bottom: 0;
    }
  }
  .options-slot{
    padding: 10px 0 5px 45px;
  }
  .micon-mep-info-square {
    font-size: 15px;
    color: $mx-color-var-label-secondary;
    margin-left: 5px;
  }
}
</style>
