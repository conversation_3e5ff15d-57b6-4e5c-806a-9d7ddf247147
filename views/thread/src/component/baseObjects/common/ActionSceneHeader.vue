<template>
  <div 
    class="action-scene-header" 
    :class="{'submitted-files': isFormSubmittedFiles}"
    :style="customStyle">
    <div class="action-scene-header__title">
      <label 
        :class="[
          isFormSubmittedFiles ? 'mx-color-secondary mx-text-c4 sub-title' : 'mx-text-tiny1',
          { 'mx-uppercase': titleUppercase && !isFormSubmittedFiles }
        ]">
        {{ title }}
      </label>
      <span v-if="badge" class="counts">{{ badge }}</span>
    </div>
    <span
      v-if="statusText"
      class="status"
      :class="[
        `${statusText.toLowerCase()}`,
        ['canceled', 'completed', 'skipped'].includes(statusText.toLowerCase())
          ? 'mx-text-c3'
          : 'mx-text-c4'
      ]">
      {{ statusText }}
    </span>
    <div v-else-if="renderActions.length" class="operate-icons">
      <el-tooltip
        v-for="(action, index) in renderActions"
        :key="`${action.tooltipContent}-${index}`"
        placement="top"
        :disabled="!action.tooltipContent"
        :content="action.tooltipContent"
        popper-class="overflow-control-tooltip">
        <i
          tabindex="0"
          class="mx-clickable"
          :class="action.mxIcon"
          @click="action.exec"
          @keypress.enter="action.exec" />
      </el-tooltip>
    </div>
    <slot v-else></slot>
  </div>
</template>

<script>
export default {
  name: 'ActionSceneHeader',
  props: {
    title: {
      type: String,
      required: true,
      default: ''
    },
    titleUppercase: {
      type: Boolean,
      default: true
    },
    badge: {
      type: String,
      default: ''
    },
    /**
     * actions:
     *  [{mxIcon: '', tooltipContent: '', exec: () => {}, isShow: false}]
     */
    actions: {
      type: Array,
      default: () => []
    },
    customStyle: {
      type: Object,
      default: () => ({})
    },
    statusText: {
      type: String,
      default: '' //'canceled', 'completed', 'skipped', "0/1"
    },

    isFormSubmittedFiles: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
  computed: {
    renderActions () {
      return this.actions.filter((action) => action.isShow)
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.action-scene-header {
  height: 32px;
  border-radius: 3px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: $mx-color-var-fill-quaternary;
  margin: 20px 16px 12px;
  
  &.submitted-files {
    background: transparent;
    border-radius: 0;
    border-top: 1px solid #e0e0e0;
    padding-top: 10px;
  }

  &._without-background {
    background: transparent;
    height: 36px;
    letter-spacing: 0px;
    padding: 9px 18px 7px 20px;
    margin: 4px 0px 0px;
    .action-scene-header__title {
      label {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0px;
      }
      span {
        font-size: 14px;
        color: $mx-color-var-text-secondary
      }
      .counts {
        margin-left: 4px;
      }
    }
  }

  &__title {
    font-size: 11px;
    label {
      margin-bottom: 0px;
    }
  }
  .status {
    &.canceled {
      color: $mx-color-var-negative;
    }
    &.completed,
    &.skipped {
      color: $mx-color-var-positive;
    }
  }
  i {
    font-size: 14px;
    color: $mx-color-var-text-secondary;
  }
  .operate-icons {
    line-height: 16px;
    i:not(:first-child) {
      margin-left: 14px;
    }
  }

  .status {
    &.done {
      color: $mx-color-var-positive;
    }
    &.canceled {
      color: $mx-color-var-negative;
    }
  }
}
</style>
