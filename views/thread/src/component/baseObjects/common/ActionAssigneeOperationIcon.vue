<template>
  <el-dropdown
    class="transfer-option"
    :class="[isMobile ? '' : 'mx-hover-hide']"
    trigger="click"
    @command="handleCommand">
    <i
      tabindex="0"
      class="micon-more mx-clickable"
      :aria-label="$t('more_options')"
      aria-haspopup="true"
      @keyup.enter.stop />
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-mx-ta="{ page: 'actionDetail', id: `reassign` }"
        command="reassign">
        <div class="flex align-center">
          <i class="micon-reassign mx-color-secondary margin-right-8px" />
          <span>{{ $t('reassign') }}</span>
        </div>
      </el-dropdown-item>
      <el-dropdown-item
        v-if="isShowRemindSender"
        v-mx-ta="{ page: 'actionDetail', id: `remindSend` }"
        command="remindSend"
        divided>
        <div
          class="remind-box flex align-center">
          <template v-if="!reminderLoading">
            <i class="micon-bell-outline mx-color-secondary" />
            <div>
              <div class="mx-text-c2">
                {{ $t('send_reminder') }}
              </div>
              <div class="mx-text-c3 mx-color-secondary">
                <span v-if="displayTime">{{ $t('last_sent_time', {time: displayTime}) }}</span>
              </div> 
            </div>
          </template>
          <template v-else>
            <img
              :src="require('@views/theme/src/images/button-spinner.gif').default"
              alt="loading"
              width="20">
            {{ $t('sending_reminder') }}
          </template>
        </div>  
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import {BrowserUtils} from '@commonUtils';
import util from '@views/common/utils/utils'
import { ObjectFeedType } from 'isdk/src/api/defines';
import { mapState } from 'vuex'
import { useBoardCommonActionsStore } from '@views/stores/boardCommonActions'
import {
  mapActions as piniaMapActions
} from 'pinia'


import { Defines } from '@commonUtils'

const WorkflowStepType = Defines.WorkflowStepType

export default {
  name: 'ActionAssigneeOperationIcon',
  components: {
  },
  props: {
    isShowRemindSender: {
      type: Boolean,
      default: false
    },
    assignee: {
      type: Object,
      default: () => ({})
    }
  },
  events: ['command'],
  data () {
    return {
      isMobile: BrowserUtils.isMobile,
      reminderLoading: false
    }
  },
  computed: {
     ...mapState('thread', ['currentActivities']),
    displayTime () {
      const time = this.getLastSendTypeFeed?.created_time
      return time ? util.formatDisplayedTime(time, {displayToday: true}) : ''
    },
    getLastSendTypeFeed () {
      const currentActivities = this.currentActivities || []
      for (let i = currentActivities.length -1; i >= 0; i--) {
        const item = currentActivities[i]
        const activityAssignee = item?.baseObject?.steps?.find(item => item.sequence === this.assignee?.sequence)?.assignee || item?.baseObject?.editor
        console.log(this.assignee, activityAssignee)
        if (item.type === ObjectFeedType.FEED_TRANSACTION_RESEND_REMINDER && (activityAssignee.id === this.assignee?.source?.id || activityAssignee.id === this.assignee?.source?.teamId)) {
          return item
        }
      }
      
      return null
    }
  },
  methods: {

    ...piniaMapActions(useBoardCommonActionsStore,[
      'resendActionReminder',
    ]),
    handleCommand (commandName) {
      if (commandName === 'reassign') {
        this.$emit('command', commandName)
      }
      if (commandName === 'remindSend') {
        this.resendReminder()
      }
    },
   

    async resendReminder () {
      
      const baseObjectSequence = this.assignee.baseObjectSequence
      const stepSequence = this.assignee.sequence
      const boardId = this.assignee.boardId
      const isEsignAction = this.assignee.step.type === WorkflowStepType.WORKFLOW_STEP_TYPE_SIGNATURE
      const payload = {
        isEsignAction: isEsignAction,
        baseObjSeq: baseObjectSequence,
        stepSeq: stepSequence,
        isPreparer: this.assignee.isPreparer,
      }
      try {
        this.reminderLoading = true
        await this.resendActionReminder(boardId, payload)
      } catch (error) {
        console.error(error)
      } finally {
        this.reminderLoading = false
      }
     
    },
   
  }
}
</script>

<style scoped lang="scss">
  .remind-box{
    gap: 8px;
    height: 44px;
  }
</style>
