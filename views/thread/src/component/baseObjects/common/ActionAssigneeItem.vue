<template>
  <div class="assignee-list-item" :class="{
          disable: assignee.isCompleted,
          'is-deleted-user': assignee.isDeleted,
          'is-disabled-user': assignee.isInactiveUser && !assignee.isUserDeleted
        }">
    <div class="assignee-user-col">
      <MxTeamItem
          v-if="isTeam(assignee.source)"
          :team="assignee.source"
          :showTeamMember="true"
          :subTitle="assignee.subTitle"
          :disabled="assignee.isSkipped || assignee.isDeclined">
        <i slot="afterAvatar" v-if="(assignee.isCompleted && !assignee.isSkipped) || assignee.isDeclined || reviewerStepIsDone" :class="[badgeClass, 'badge-after-avatar']" />
      </MxTeamItem>
      <MxUserItem
          v-else
          :user="assignee.source"
          :subTitle="assignee.subTitle"
          :showYou="true"
          :disabled="assignee.isCompleted || assignee.isSkipped  || assignee.isDeclined">
        <i slot="afterAvatar" v-if="(assignee.isCompleted && !assignee.isSkipped) || assignee.isDeclined || reviewerStepIsDone" :class="[badgeClass, 'badge-after-avatar']" />
      </MxUserItem>
    </div>
    <div class="assignee-action-col" >
      <div
          v-if="showOrderNum"
          class="index mx-text-c3 text-center mx-margin-left-sm"
          :class="{ 'turn-order': assignee.canReassign && !isMobile }">
        {{ assignee.stepTurnOrder }}
      </div>
      <ActionAssigneeOperationIcon
        v-if="assignee.canReassign"
        :is-show-remind-sender="isShowRemindSender"
        :assignee="assignee"
        @command="handleCommand" />
    </div>
  </div>
</template>

<script>
import MxTeamItem from '@views/common/components/MxTeamItem.vue'
import MxUserItem from '@views/common/components/MxUserItem.vue'
import ActionAssigneeOperationIcon from './ActionAssigneeOperationIcon.vue';
import { mapGetters } from 'vuex'
import { isTeam } from '@commonUtils/assignee'
import { BrowserUtils } from "@commonUtils";

export default {
  name: 'ActionAssigneeItem',
  components: {
    MxTeamItem,
    MxUserItem,
    ActionAssigneeOperationIcon
  },
  props: {

    assignee: {
      required: true,
      type: Object,
      default: null
    },
    showOrderNum: {
      type: Boolean,
      default: false
    },
    // check is reviewer step
    isReviewer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isMobile: BrowserUtils.isMobile,
    }
  },
  computed: {
    ...mapGetters('application', ['roleAvatar']),
    ...mapGetters('user', ['currentUser']),

    // check is reviewer step and is done
    reviewerStepIsDone () {
      const { isReviewerStep, isDone } = this.assignee
      return isReviewerStep && isDone
    },
    badgeClass() {
      const { isSkipped, isCompleted, isDeclined, isDone } = this.assignee

      if (this.reviewerStepIsDone) return 'micon-positive'

      return !isSkipped && isCompleted  && !isDeclined ? 'micon-positive' : 'micon-error'
    },
    userAvatar() {
      const assignee = this.assignee
      if (!assignee) {
        return ''
      }
      if (assignee.isAssigneeRole || assignee.isRole || assignee.isNoRole) {
        return this.roleAvatar
      }
      return assignee.avatar
    },
    isShowRemindSender () {
      const {isMySelf = false, isAssigneeRole = false} = this.assignee?.source || {}
      return this.assignee.isInProgress && !isMySelf && !isAssigneeRole && this.currentUser.isInternalUser
    }

  },
  methods: {
    isTeam,
    handleCommand(commandName) {
      this.$emit('command', commandName, this.assignee)
    }
  }
}
</script>

<style scoped lang="scss">
.assignee-item {
  display: flex;
  position: relative;
}
.text-capitalize{
  text-transform: capitalize;
}
.assignee-list-item{
  display: flex;
  align-items: center;
  padding: 4px 18px;
  .assignee-user-col{
    flex-grow: 1;
    width: 100%;
    overflow: hidden;
  }
  .assignee-action-col{
    flex-shrink: 0;
  }



  .badge-after-avatar{
    font-size: 16px;
    position: absolute;
    bottom: -2px;
    right: -2px;
    border-radius: 50%;
    background-color: $mx-color-var-white;
    &.micon-positive {
      color: $mx-color-var-positive;
    }
    &.micon-error {
      color: $mx-color-var-negative;
    }
  }
  .transfer-option.mx-hover-hide {
    display: none;
  }
  &:hover,
  :focus {
    .transfer-option.mx-hover-hide {
      display: inline-block;
    }
    .turn-order {
      border: 1px solid black;
      display: none;
    }
  }
  .index {
    width: 24px;
    height: 24px;
    border-radius: 3px;
    line-height: 24px;
    color: $mx-color-var-text-secondary;
    background-color: $mx-color-var-fill-quaternary;
  }

  &.is-deleted-user {
    .mx-transaction-assignee-info {
      opacity: 0.5;
    }
    .assignee-name-wrapper {
      color: $mx-color-var-text-secondary;
    }
    .mx-transaction-assignee-status .status .assignee-status {
      color: $mx-color-var-text-tertiary;
    }
  }
  &.is-disabled-user {
    .assignee-name-wrapper {
      color: $mx-color-var-text-secondary;
    }
  }
}

.assignee-avatar {
  margin-right: 12px;
}

.assignee-info {
  display: flex;
  flex-direction: column;
}
</style>
