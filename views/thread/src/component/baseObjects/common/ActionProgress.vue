<template>
  <div class="action-progress-container">
    <slot name="alert" />
    <ul class="assignee-member">
      <li
        v-for="assignee in assigneeList"
        :key="assignee.sequence">
        <ActionAssigneeItem :assignee="assignee" :showOrderNum="showOrderNum" @command="handleCommand($event, assignee)"></ActionAssigneeItem>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {BrowserUtils, ObjectUtils} from '@commonUtils'
import extBadge from '@views/theme/src/images/ext_badge_mep.svg'
import { useAnonymousUser } from '@controller/user'

import { getDeletedTeamTitle } from '@views/common/utils/team'
import ActionAssigneeItem from "@views/thread/src/component/baseObjects/common/ActionAssigneeItem.vue";
import {isMyTeam, isTeam} from "@commonUtils/assignee";
import {needHandleFormRelatedLogic} from "@controller/utils/common";
export default {
  name: 'ActionProgress',
  components: {
    ActionAssigneeItem,
  },
  inject: ['boardId'],
  props: {
    baseObject: {
      type: Object,
      required: true,
      default: () => ({
        steps: []
      })
    },
    isPreparing: {
      type: Boolean,
      default: false
    },
    needIndication: {
      type: Boolean,
      default: false
    },
    needShowOrderNum: {
      type: Boolean,
      default: true
    },
    config: {
      type: Object,
      default: () => ({})
    },
    showTransferOption: {
      type: Boolean,
      default: false
    },
    customStepStatus: {
      type: String,
      default: ''
    },
    customCompletedStepStatus: {
      type: String,
      default: ''
    },
    canReassgin: {
      type: Boolean,
      default: true
    },
    reassginNeedCheckStepInfo: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isMobile: BrowserUtils.isMobile,
      imageStyle: {
        'background-image': `url(${extBadge})`,
        'margin-top': '0'
      },
      anonymousUserInfo: useAnonymousUser()
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    showOrderNum (){
      if(!this.baseObject.steps || !this.needShowOrderNum) return false

      // exclude reviewerStep
      const normalSteps = this.baseObject.steps.filter(k => !k.isReviewerStep) || []
      const stepLen = normalSteps.length
      const isSequential = normalSteps[0].orderNumber !== normalSteps[stepLen-1].orderNumber
      return stepLen > 1 && isSequential
    },
    assigneeList() {
      const { steps: OriginSteps, isMock, sequence: baseObjectSequence } = this.baseObject

      // used for checking whether includes reviewerStep
      const hasReviewerStep = (OriginSteps || []).find(k => k.isReviewerStep)

      // exclude reviewer step
      const steps = (this.baseObject?.steps || []).filter(k => !k.isReviewerStep)

      return (steps || []).map(step => {
        const assignee = step.assignee
        let  subTitle = this.stepStatus(step)
        if(isTeam(assignee)) {
          let memberCounts
          if(ObjectUtils.isDefine(assignee.memberCounts)) {
            memberCounts = assignee.memberCounts
          }else if(ObjectUtils.isDefine(assignee.totalMembers)){
            memberCounts = assignee.totalMembers
          }
          if(memberCounts === 0){
            subTitle = ''
          }
          if(assignee.isUserDeleted || assignee.isDeleted){
            subTitle = getDeletedTeamTitle(assignee, this.$t)
          }
        }

        return {
          source: assignee,
          isDeleted: assignee.isUserDeleted,
          isSkipped: step.isSkipped,
          isCompleted: step.isCompleted || this.baseObject.isCompleted,
          isInProgress: this.config.isInProgress || this.isStepInProgress(step), // for flow thread direct use config, for binder action use seconf
          // use for showing decline icon
          isDeclined: step.isCanceled || this.baseObject.isCanceled,
          subTitle,
          canReassign: this.canBeReassgin(step),
          // when approval allow decline, exclude reviewerStep, serial number minus 1
          stepTurnOrder: hasReviewerStep ? step.turnOrder - 1 :  step.turnOrder,
          isInactiveUser: assignee.isInactiveUser,
          isAssigneeRole: assignee.isAssigneeRole,
          sequence: step.sequence,
          baseObjectSequence,
          step,
          boardId: this.boardId
        }
      }) || []
    }
  },
  methods: {

    handleCommand(commandType, assignee) {
      const step = this.baseObject.steps.find(step=> step.sequence == assignee.sequence)
      console.debug('command:',commandType, step)
      let excludeUsers
      if(needHandleFormRelatedLogic(this.baseObject.transactionType)){
        excludeUsers = this.assigneeList.map(assignee => assignee.source)
      }
      // now only reassign
      this.$emit(commandType, step, excludeUsers)
    },
    getBadgeClass(step) {
      return this.isStepDone(step) &&
          !step.isSkipped &&
          (step.isCompleted || this.baseObject.isCompleted ? 'micon-positive' : 'micon-error')
    },

    getAssigneeName (assignee) {
      if (assignee.displayDeletedUser) {
        return `[${this.$t('Deleted_User')}]`
      } else if (assignee.name) {
        return assignee.name
      } else if (assignee.isAssigneeRole) {
        return this.$t('Unassigned')
      }
      return ''
    },
    isStepDone (step) {
      return step.isDone || this.baseObject.isCompleted
    },

    isStepInProgress (step) {
      return this.baseObject.currentTurnOrder === step.orderNumber && !this.isPreparing 
    },
    stepStatus (step) {
      if (step.assignee.isUserDeleted) {
        return isTeam(step.assignee) ? getDeletedTeamTitle(step.assignee, this.$t): this.$t('USER_DELETED')
      } else if (step.assignee.disabled) {
        return this.$t('USER_DEACTIVATED')
      }
      if (step.isCompleted) {
        return this.customCompletedStepStatus || this.$t('Completed')
      } else if (step.isCanceled || this.baseObject.isCanceled) {
        return this.$t('declined')
      } else if (step.isSkipped) {
        return this.$t('Skipped')
      } else if (this.baseObject.isCompleted) {
        return this.$t('Completed')
      } else if (this.config.isNotStarted) {
        return this.$t('Not_started')
      } else if (this.config.isCanceled) {
        return this.$t('Canceled')
      } else if (this.config.isSkipped) {
        return this.$t('Skipped')
      } else if (this.baseObject.nextOrder === step.orderNumber) {
        return this.customStepStatus || this.$t('Waiting_to_review')
      } else if (this.isStepInProgress(step)) {
        return `${this.$t('in_progress')}...`
      } else if (this.baseObject.isCanceled) {
        return this.$t('Canceled')
      } else {
        return this.$t('Waiting_')
      }
    },
    canBeReassgin (step) {
      if(!this.canReassgin) return false

      if (this.anonymousUserInfo.isAnonymousUser) {
        return false
      }
      const isCreator = this.baseObject?.creator?.isMySelf
      const assigneeIsEnable = step.assignee?.isMySelf || (isTeam(step.assignee) && isMyTeam(step.assignee))|| this.currentUser.isInternalUser || isCreator
      let objectIsEnable = true
      // const isLWATakenAction = this.isLaunchWebApp && step.actionLogs?.length
      // const isJumioTakenAction = this.isJumio && step.actionLogs?.length
      const isLWATakenAction = this.reassginNeedCheckStepInfo && step.actionLogs?.length
      const isJumioTakenAction = this.reassginNeedCheckStepInfo && step.actionLogs?.length
      if (this.config.isFlowStep) {
        objectIsEnable = !this.config.isCanceled && !step.isDone
      } else {
        objectIsEnable = !step.isDone
      }
      return (
        this.showTransferOption &&
        assigneeIsEnable &&
        objectIsEnable &&
        !isLWATakenAction &&
        !isJumioTakenAction
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.action-progress-container {
  padding: 0px 8px 20px 8px;
  .alert-decription {
    color: $mx-color-var-text-secondary;
    padding: 0px 0px 8px 20px;
  }

  .assignee-member {
    li {



      .mx-transaction-assignee-status {
        display: flex;
        flex: 1 1 auto;
        overflow: hidden;
        align-items: center;
        justify-content: space-between;
        .status {
          display: flex;
          flex: 1;
          flex-direction: column;
          > div:last-child {
            color: $mx-color-var-text-secondary;
          }
          .assignee-name-wrapper {
            display: flex;
            align-items: center;
          }
        }
        .index {
          width: 24px;
          height: 24px;
          border-radius: 3px;
          line-height: 24px;
          color: $mx-color-var-text-secondary;
          background-color: $mx-color-var-fill-quaternary;
        }
      }
      &.disable {
        ::v-deep img {
          opacity: 0.5;
        }
        .status {
          div:first-child {
            color: $mx-color-var-text-secondary;
          }
        }
      }
      &.is-deleted-user {
        .mx-transaction-assignee-info {
          opacity: 0.5;
        }
        .assignee-name-wrapper {
          color: $mx-color-var-text-secondary;
        }
        .mx-transaction-assignee-status .status .assignee-status {
          color: $mx-color-var-text-tertiary;
        }
      }
      &.is-disabled-user {
        .assignee-name-wrapper {
          color: $mx-color-var-text-secondary;
        }
      }
    }
    & > :not(li:last-child) {
      margin-bottom: 4px;
    }
  }
}
</style>
