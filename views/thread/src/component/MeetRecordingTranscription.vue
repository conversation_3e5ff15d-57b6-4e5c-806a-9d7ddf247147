<template>
  <div class="meet-recording-transcription h-full flex flex-col">
    <div
      class="search-input-box"
      :class="{ active: showResults }">
      <el-input
        v-model="searchValue"
        :placeholder="$t('search_sranscript')"
        :aria-label="$t('search_sranscript')"
        size="small"
        prefix-icon="micon-timeline-search"
        @input="debounceFilterSearch"
        @focus="cleanChidlrenCancelFn">
        <i
          v-if="searchValue"
          slot="suffix"
          role="button"
          :aria-label="$t('Clear')"
          class="micon-error mx-clickable"
          :tabindex="searchValue?0:-1"
          @keydown.enter="clearSearchInput"
          @click="clearSearchInput" />
      </el-input>
      <MxGridCollapse
        :show.sync="showResults"
        :has-header="false">
        <div class="result-box flex align-center margin-top-6px relative leading-20px">
          <div
            class="flex-1"
            tabindex="0"
            :aria-label="results">
            {{ results }}
          </div>
          <div
            class="result-count"
            tabindex="0"
            :aria-label="`${currentIndex}/${totalCount}`">
            {{ currentIndex }}/{{ totalCount }}
          </div> 
          <div>
            <el-button
              type="gray"
              square
              size="mini"
              :aria-label="$t('prev')"
              class="direction-btn"
              :disabled="disableUp"
              @click="prevHighlight">
              <i class="micon-arrow-dropdown-centered up" />
            </el-button><el-button
              type="gray"
              square
              size="mini"
              :aria-label="$t('next')"
              class="direction-btn"
              :disabled="disableDown"
              @click="nextHighlight">
              <i class="micon-arrow-dropdown-centered " />
            </el-button>
          </div>
        </div>
      </MxGridCollapse>  
    </div>
    <div
      ref="transcriptionList"
      class="transcription-list-box flex-1 overflow-auto"
      :class="{ active: showResults }">
      <div
        v-for="(item, index) in transcriptions"
        :key="item.id"
        ref="transcriptionItem"
        class="transcription-list"
        :class="{active: index === currentSubtitleIndex}"> 
        <MeetRecordingTranscriptionItem
          ref="meetRecordingTranscriptionItem"
          :item="item"
          :transcriptions="transcriptions"
          :index="index"
          @click.native="handleClickItem(index, item.start)"
          v-on="$listeners" />
      </div> 
    </div>   
  </div>
</template>


<script>
import MeetRecordingTranscriptionItem from './MeetRecordingTranscriptionItem.vue';
import MxGridCollapse from '@views/common/components/MxGridCollapse.vue';
import { useHighlightWithDirection, mapStateComputed } from '@views/common/plugins/hightlight/index.js'
import debounce from 'lodash/debounce'
import { useEventListener } from '@views/common/plugins/useEventListener'

export default {
    name: 'MeetRecordingTranscription',
    components: {
        MeetRecordingTranscriptionItem,
        MxGridCollapse
    },
    props: {
      transcriptions: {
        type: Array,
        default: () => []
      },
      meetId: {
        type: String,
        default: ''
      }
    },
    data () {
        return { 
            searchValue: '',
            currentSubtitleIndex: -1,
            unbindScroll: null
        }
    },
    computed: {
      currentIndex () {
        return this.currentHighlightIndex + 1
      },
      totalCount () {
        return this.highlightRanges.length ?? 0
      },
      results () {
        return this.totalCount ? this.$t('count_results', { count: this.totalCount }) : this.$t('No_results')
      },
      showResults () {
        return !!this.searchValue
      },
      ...mapStateComputed('highLightState', [
        'highlightRanges',
        'currentHighlightIndex',
        'disableUp',
        'disableDown'
      ]),
     
    },
    watch: {
      transcriptions: {
        async handler (val) {
          await this.$nextTick()
          this.hightLightInit(this.$refs.transcriptionList)
          // this.hightLightInit(val)
        },  
        deep: true,
        immediate: true
       }
    },
    
    created () {
      const {
        highLightState,
        highlight,
        navigateToHighlight,
        nextHighlight,
        prevHighlight,
        init: hightLightInit,
        clean: cleanHighlight} = useHighlightWithDirection()
      Object.assign(this, { highLightState, highlight, navigateToHighlight, nextHighlight, prevHighlight, cleanHighlight, hightLightInit })  
      // store children cancel function
      this.childrenCancelFnSet = new Set()

    },

    mounted () {
      this.bindScroll()
    },
   
    beforeDestroy () {
      this.unbindScroll && this.unbindScroll()
    },
    methods: {
     
      debounceFilterSearch: debounce(function () {
        this.highlight(this.searchValue)
      }, 500),


      clearAllEditingItem () {
        this.$refs.meetRecordingTranscriptionItem.forEach((item) => { item.editing = false })
      },

      addChildrenCancelFn (fn) {
        this.childrenCancelFnSet.add(fn)
      },
      
      cleanChidlrenCancelFn () {
        this.childrenCancelFnSet.forEach(fn => fn && fn())
        this.childrenCancelFnSet.clear()
      },
      cleanWithEmptySearch () {
        if (this.searchValue) {
          this.searchValue = ''
          this.cleanHighlight()
        }
      },
      handleClickItem (index, time) {
        this.setCurrentSubtitle(index)
        // 0.01 forbit the last track end time === current track start time
        this.$emit('update-current-time', time + 0.01)
      },
      setCurrentSubtitle (index) {
        this.currentSubtitleIndex = index
        // this.$refs.meetRecordingTranscriptionItem?.[index]?.$el?.focus()
        const itHasEditingItem = this.$refs.meetRecordingTranscriptionItem.some((item) => {
          return item.editing
        })
        // manual scroll
        // search word
        // editing item
        // don't auto scroll
        if (!this.scrolling && !this.searchValue && !itHasEditingItem) {
          const el = this.$refs.transcriptionList
          const containerRect = el.getBoundingClientRect();
          const rangeRect = this.$refs.transcriptionItem[index].getBoundingClientRect();
          const relativeTop = rangeRect.top - containerRect.top + el.scrollTop;
          this.unbindScroll && this.unbindScroll()
         
          el.scrollTo({
              top: relativeTop - 50,  // 50 is the threshold
              behavior: 'smooth'
          });
          setTimeout(() => {
            this.bindScroll()
          }, 200);
         
        }
      },
      bindScroll () {
        this.unbindScroll = this.$refs.transcriptionList && useEventListener(this.$refs.transcriptionList,'scroll', this.handleScroll())
      },
      // when scroll, set scrolling to true, set to false until not scolling for 5s
      handleScroll (timestamp = 5000) {
        
        let timer = null
        return () => {
          this.scrolling = true
          timer && clearTimeout(timer)
          timer = setTimeout(() => {
            this.scrolling = false
          }, timestamp)
        }
       
      },
      clearSearchInput () {
        this.searchValue = ''
        this.highlight(this.searchValue)
      }
    }
}
</script>

<style lang="scss" scoped>
  .search-input-box{
    background-color: #fff;
    padding: 20px 20px 12px;
    &.active{ 
      border-bottom: 1px solid #e5e5e5;
      padding-bottom: 8px;
    }
    .el-input {
      &:hover {
        ::v-deep .el-input__suffix i {
          display: block;
        }
      }
    }
    ::v-deep {
      .el-input__suffix {
        font-size: 16px;
        right: 8px;
        i {
          padding-top: 6px;
          display: none;
        }
      }
    }
  }
  .transcription-list {
    padding: 10px 20px;
   &.active {
      background-color: var(--mx-color-var-fill-quaternary);
   }
  } 
  .transcription-list-box{
    padding: 6px 0;
  }
 
  .micon-arrow-dropdown-centered {
    &.up {
      transform: scaleY(-1);
      display: inline-block;
    }
  }
  .direction-btn{
    &:not(:hover) {
      background: none;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
      border-color: transparent;
    }
    + .direction-btn {
      margin-left: 8px !important;
    }
  }
  .result-count{
    color: var(--mx-color-var-text-secondary);
    position: relative;
    padding-right: 8px;
    margin-right: 4px;
    font-size: 12px;
    &::after {
      content: '';
      height: 12px;
      width: 1px;
      background-color: var(--mx-color-var-fill-tertiary);
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      margin: auto;
    }
  }
  
</style>
<style>
 ::highlight(custom-highlight) {
    background: rgba(255, 212, 23, 0.2)
  }
  ::highlight(current-highlight) {
    background: var(--mx-color-var-highlight)
  }
</style>