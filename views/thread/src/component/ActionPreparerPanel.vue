<template>
  <div class="action-preparer">
    <ActionSceneHeader 
      :title="$t('Will_be_prepared_by')" 
      :statusText="progressStatus" />
    <ActionAssigneeItem  
      v-if="!!assignee" 
      :assignee="assignee" 
      :showOrderNum="false" 
      class="prepare-item" 
      @command="handleCommand" />
  </div>
</template>

<script>
import ActionSceneHeader from '@views/thread/src/component/baseObjects/common/ActionSceneHeader.vue'
import ActionAssigneeItem from '@views/thread/src/component/baseObjects/common/ActionAssigneeItem.vue'
import {useAnonymousUser} from '@controller/user'
import { isUserDeleted, isTeam, isAssigneeRole, getIdentityId } from '@commonUtils/assignee'
import isEmpty from 'lodash/isEmpty'
import cloneDeep from 'lodash/cloneDeep'
import {mapGetters} from 'vuex'

export default {
  name: 'ActionPreparerPanel',
  components: {
    ActionSceneHeader,
    ActionAssigneeItem
  },
  props: {
    step:{
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      default: () => ({})
    },
    isNoAction: {
      type: Boolean,
      default: false
    },
    isPreview: {
      type: Boolean,
      default: false
    },
  },
  data () {
    const {isAnonymousUser} = useAnonymousUser()
    return {isAnonymousUser}
  },
  computed: {
    ...mapGetters('user', ['currentUser']),

    isCompleted () {
      //special case: If this is in prepare mode and in preview, will treat it as completed
      const step = this.step
      if(step.isPrepareMode && this.isPreview) {
        return true
      }
      return step.enablePreparation && step.isPrepareCompleted
    },
    canReassign () {
      if(this.isNoAction) return false;
      if(this.isAnonymousUser || this.isCompleted){
        return false
      }
      return this.currentUser.isInternalUser
    },

    assignee () {
      const step = this.step
      const editor = step.editor || {}

      return {
        source: this.mockUserData(),
        subTitle: this.getSubTitle(editor),
        isDeleted: editor.isRole ? false: isUserDeleted(editor),
        isSkipped: false,
        isCompleted: this.isCompleted,
        canReassign: this.canReassign,
        stepTurnOrder: 0,
        isInactiveUser: false,
        isAssigneeRole: editor.isRole,
        sequence: step.sequence,
        isInProgress: step.isInProgress || step.isPreparing,
        baseObjectSequence: this.$parent?.baseObject?.sequence,
        step,
        boardId: this.$parent?.baseObject?.boardId,
        isPreparer: step.isPreparing,
      }
    },
    progressStatus () {
      if (this.isCompleted) {
        return this.$t('Completed')
      } else {
        return '0/1'
      }
    }
  },
  methods: {
    handleCommand (actionName, assignee){
      switch (actionName) {
        case 'reassign':
          this.$emit('reassign', {assignee: this.step.editor} ,null, {isReassignEditor:true})
          break;
      }
    },
    mockUnassignedAssigneeData (isRole) {
      return {
        name: isRole ? this.$t('No_Role_Assigned') : this.$t('No_User_Assigned'),
        avatar: '',
        isNoRole: true
      }
    },
    mockEmptyPreparer () {
      return {
        name: this.$t('Preparer'),
        avatar: '',
        isInternalUser: true, // to prevent show the client user badge
      }
    },
    mockUserData () {
      const editor = cloneDeep(this.step.editor)

      if (isEmpty(editor)) {
        return this.mockEmptyPreparer()
      }

      // for editor is team
      if (isTeam(editor)) {
        const editorTeam = editor.team
        if (editorTeam.isDeleted && this.isPreview) {
          return this.mockUnassignedAssigneeData()
        } else {
          return editorTeam
        }
      }

      // for editor is role
      if (editor.isRole) {
        const editorRole = editor.role
        const isRoleAssignedWithUser = !isEmpty(editorRole.assigneeUser)
        if (isRoleAssignedWithUser) {
          return editorRole.assigneeUser
        } else {
          const isInvalidRole = editorRole.isDeleted || !editorRole.displayName
          if (this.isPreview && isInvalidRole) {
            return this.mockUnassignedAssigneeData(true)
          } else {
            const { defaultUser, ...removeDefaultUserRole } = editorRole;
            return removeDefaultUserRole;
          }
        }
      }

      // for editor is user
      if (getIdentityId(editor.user)) {
        const editorUser = editor.user
        if (this.isPreview && editorUser.isDeleted) {
          return this.mockUnassignedAssigneeData()
        } else {
          return editorUser
        }
      }

      return editor
    },
    getSubTitle (editor) {
      //todo, we need to calculate the status during data preparation instead of make the logic check in render stage
      if(this.isCompleted) {
        return this.$t('Completed')
      } else if(isEmpty(editor) || this.config.notInFlowWorkspace) {
        return this.$t('Not_started')
      } else if(this.step.isInProgress || this.step.isPreparing) {
        return this.$t('In_progress')
      } else if(this.step.isNotStarted) {
        return this.$t('Waiting_To_Prepare')
      }
    },
  }
}
</script>

<style scoped lang="scss">
.action-preparer{
  padding-bottom: 0;
  .action-preparer{
    margin-top: 0;
  }
  .prepare-item{
    padding: 4px 26px;
  }
}

</style>
