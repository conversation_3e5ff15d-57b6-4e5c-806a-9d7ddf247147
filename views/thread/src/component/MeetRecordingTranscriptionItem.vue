<template>
  <div
    class="ranscription-item flex cursor-pointer"
    tabindex="0">
    <MxUserAvatar
      :alt="user.name"
      :user-avatar="user.avatar"
      size="28" />
    <div class="flex-1 margin-left-8px">
      <div class="header mx-color-var-text-secondary fs-12px leading-16px margin-bottom-4px flex align-center">
        <div class="header-desc flex-1">
          <span
            class="mx-ellipsis" 
            tabindex="0"
            :aria-label="user.name">{{ user.name }}</span>
          <span
            class="margin-left-8px flex-shrink-0"
            tabindex="0"
            :aria-label="formatMilliseconds(item.start * 1000)">{{ formatMilliseconds(item.start * 1000) }}</span>
        </div>
        <el-button
          v-if="canEdit()"
          v-show="!editing"
          class="edit-button margin-left-8px"
          size="small"
          square
          type="gray"
          :aria-label="$t('Edit')"
          @click="handleEdit">
          <i class="micon-edit-xs" />
        </el-button>
      </div>
      <div
        v-if="!editing"
        class="flex leading-20px">
        <div
          class="flex-1"
          tabindex="0"
          :aria-label="item.edited ? item.text + $t('Edited') : item.text"
          style="overflow-wrap: anywhere;">
          <!-- {{ item.text }} -->
          <bbcode 
            :content-text="item.text" 
            aria-hidden="true" /><i
              v-if="item.edited"
              class="margin-left-4px mx-color-var-text-tertiary fs-12px"
              style="white-space: nowrap;">
              -{{ $t('Edited') }}
            </i>
        </div>
      </div>
      <MxDivInput 
        v-else
        v-model="editContent" 
        :save-loading="updateTranscription.loading"
        @cancel="handleCancel"
        @save="handleSave" />
    </div>
  </div>
</template>

<script>
import MxUserAvatar from '@views/common/components/userAvatar/MxUserAvatar';
import MxDivInput from '@views/common/components/input/MxDivInput.vue'
import { mapActions } from 'vuex';
import { useState } from '@commonUtils/useAsyncState'
import bbcode from '@views/common/components/bbcode'


export default {
  name: 'MeetRecordingTranscriptionItem',
  components: {
    MxUserAvatar,
    MxDivInput,
    bbcode
  },
  inject: ['meetId', 'canEdit', 'getVideo'],
  props: { 
    item: {
      type: Object,
      default: () => ({})
    },
    transcriptions: {
      type: Array,
      default: () => ([])
    },
    index : {
      type: Number, 
      default: -1
    }
    
  },
  data () {
      return {
        editing: false,
        editContent: '',
        updateTranscription: useState({}, () => this.updateMeetTranscriptionByItem),
        videoStatus: false,
        pauseByEdit: false
      }
  },

  computed: {
      user () {
          return this.item.user || {}
      }
  },
  async mounted () {
    await this.$nextTick()
    this.video = this.getVideo()
  },
  methods: {
    ...mapActions('meet', [
      'updateMeetTranscription',
      'getMeetRecordingMaterials'
    ]),
    handleEdit () {
      this.$parent?.clearAllEditingItem?.() 
      this.editing = true
      this.editContent = this.item.text
      this.$parent?.cleanWithEmptySearch?.()
      this.$parent?.addChildrenCancelFn?.(this.handleCancel)
      this.videoStatus = this.video?.isPlay()
      if (!this.videoStatus) {
        this.video?.pause()
        this.pauseByEdit = true
      }
      
    },
    handleCancel () {
      this.editing = false
      this.videoStatus = this.video?.isPlay()
      if (this.pauseByEdit && this.videoStatus) {
        this.video?.play()
        this.pauseByEdit = false
      }
    },

    formatMilliseconds (ms) {  
      const houtsCalc = Math.floor(ms / 3600000)
      const hours = houtsCalc.toString().padStart(2, '0');  
      const minutes = Math.floor((ms % 3600000) / 60000).toString().padStart(2, '0');  
      const seconds = Math.floor((ms % 60000) / 1000).toString().padStart(2, '0');  
      return houtsCalc > 0 ? `${hours}:${minutes}:${seconds}` : `${minutes}:${seconds}`;  
    },
    
    handleSave () {
      this.$emit('update-transcription', this.updateTranscription.execute)
    },

    async updateMeetTranscriptionByItem () {
      const {vttTranscriptionsUrl} = await this.updateMeetTranscription({meetBoardId: this.meetId, transcriptions: [{
        id: this.item.id,
        text: this.editContent
      }]})
      Object.assign(this.item, {text: this.editContent, edited: true})
      this.handleCancel()
      return {
        vttTranscriptionsUrl,
        recordingsForDownloadAndCopyTo: await this.getMeetRecordingMaterials(this.meetId)
      }
    }

  }
}
</script>
 
<style lang="scss" scoped>
.ranscription-item {
  .v-hidden {
    visibility: hidden;
  }
  &:not(:hover) {
    .edit-button{
     display: none;
    }
  }
  .edit-button{
    &:not(:hover) {
      background-color: transparent;
      border-color: transparent;
    }
  }
  &:hover {
    .header-desc {
      max-width: calc(100% - 36px);
    }
  }
}

.fade-up-enter-active, .fade-up-leave-active {
  transition: all 0.2s ease;
}
.fade-up-enter, .fade-up-leave-to {
  transform: translateY(-10px); 
  opacity: 0; 
}
.edit-button-box{
  width: 28px;
}
.header-desc{
  display: flex;
  width: 0;
  min-width: 0;
}
.header {
  position: relative;
  .el-button {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>