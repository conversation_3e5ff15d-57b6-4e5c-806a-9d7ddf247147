<template>
  <baseModal
    id="transaction_review_atch_modal"
    class="review-ack-attach-dialog mobile-fullscreen-dialog"
    :visible.sync="visibleProxy"
    :modal-option="{
      width: '540px'
    }"
    @close="onModalClose"
    >
    <template slot="title">{{ title }}</template>
    <template slot="content">
      <div class="attach-review-title mx-text-h3">{{ $t('Review_and_Acknowledge') }}</div>
      <div class="attach-review-desc mx-text-c2">{{ $t('Review_the_following_attachments') }}</div>
      <ul class="attach-review-container">
        <li
          v-for="attachment in attachments"
          :key="attachment.sequence"
          :class="['mx-clickable', `attachment-${attachment.sequence}`]"
          @click="review(attachment)">
          <div class="attachment-item">
            <PreviewImage
              v-if="attachment.thumbnailUrl"
              :file="attachment"
              :size="44" />
            <img
              v-else
              :class="{'weblink': attachment.url}"
              :src="defaultThumbnail(attachment)"
              :alt="attachment.name">
            <div class="attachment-info mx-margin-left-md">
              <FileNameWithSuffix :name="attachment.name"/>
              <div
                :title="getAttachmentSubtitle(attachment)"
                class="attachment-subtitle mx-text-c2 mx-ellipsis">
                {{ getAttachmentSubtitle(attachment) }}
              </div>
            </div>
          </div>
          <div class="attachment-action">
            <div
              v-if="reviewed.indexOf(attachment.referenceSequence) > -1"
              class="attachment-checkmark">
              <i class="micon-mep-check-mark mx-font-lg" />
            </div>
            <button
              v-else
              type="button"
              class="mx-branding-text-action mx-text-c1">
              {{ $t('review') }}
            </button>
          </div>
        </li>
      </ul>
    </template>
    <template slot="footer">
      <el-button
        type="primary"
        :debounce-click="300"
        v-mx-ta="{ page: 'transaction', id: `confirm_acknowledge`}"
        @button-click="confirm()">{{ $t('Acknowledge') }}
      </el-button>
    </template>
  </baseModal>
</template>

<script>
import { visibleMixin } from '@views/common/components/modals/mixins'
import templateFormat from '@views/common/utils/formatter/templateFormat'
import linkSVG from '@views/theme/src/fonts/icons/source/Invite-link.svg'
import PreviewImage from '@views/requests/components/image'
import utils from '@views/common/utils/utils'
import FileNameWithSuffix from '@views/common/components/file/FileNameWithSuffix'
import { mapGetters,mapActions} from 'vuex'

export default {
  name: 'ReviewAcknowledgeAttachs',
  mixins: [visibleMixin],
  components: {
    PreviewImage,
    FileNameWithSuffix
  },
  props: {
    title: {
      type: String,
      required: true
    },
    attachments: {
      type: Array,
      required: true
    },
    step:{
      type: Object,
      default: (()=>{})
    },
    transactionSeq: {
      type: String,
      required: true
    },
    ctrlKey:{
      type: String,
      default: ''
    }
  },
  data () {
    return {
      reviewed: []
    }
  },
  mounted() {
    let assigneeId = this.currentUser.id
    this.reviewed = this.currentStepReviewedAttachRecords[assigneeId] || []

  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    currentStepReviewedAttachRecords () {
      return  this.step?.custom_data?.Acknowledgement?.read_history || {}
    }
  },
  methods: {
    ...mapActions('thread',['saveDraftForm']),
    defaultThumbnail (file) {
      if (file.url) {
        return linkSVG
      }
      return templateFormat.fileListThumbnail(file, 'search')
    },
    getAttachmentSubtitle (attachment) {
      let subtitle = ''
      if (attachment.url) {
        return attachment.url
      } else {
        let size = attachment.resourceSize
        if (size) {
          let {number, unit} = utils.getSizeObj(size, true)
          subtitle = number + unit
        } else if (attachment.fileType === 'WHITEBOARD' || attachment.fileType === 'NOTE' || attachment.fileType === 'LOCATION') {
          subtitle = this.$t(attachment.fileType.toLowerCase())
        } else if (size === 0) {
          subtitle = '0B'
        }
      }
      return subtitle
    },
    review (attachment) {
      this.$emit('review', attachment)
      if (this.reviewed.indexOf(attachment.referenceSequence) < 0) {
        this.reviewed.push(attachment.referenceSequence)
      }
    },
    confirm () {
        let noNeedReview = true
        if(this.attachments.length){
          const attachmentsSeqs =[]
          this.attachments.forEach(attach=>{attachmentsSeqs.push(attach.referenceSequence)})
          const validReviewed = this.reviewed.filter(r=>{return attachmentsSeqs.includes(r)})
          if(validReviewed.length === this.attachments.length){
            noNeedReview = true
          }else{
            noNeedReview = false
          }
        }else{
          noNeedReview = true
        }
        if(noNeedReview){
          this.$emit('confirm', this.closeModal)
        }else{
          this.$mxMessage.error(this.$t('Please_review_each_files'))
        }
        this.storeReviewAttach()
    },

    storeReviewAttach () {
      let assigneeId = this.currentUser.id
      let mockCustomData = {
        Acknowledgement: {
          read_history:{
            ...this.currentStepReviewedAttachRecords,
            [assigneeId]:this.reviewed,
          }
        }
      }
      this.saveDraftForm({
        transactionSequence : this.transactionSeq,
        stepSequence: this.step.sequence,
        ctrlKey : this.ctrlKey,
        data: mockCustomData
      })
    },
    onModalClose (){
      this.storeReviewAttach()
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__header {
    height: 52px;
  }
  .el-dialog__body {
    height: 460px;
    overflow: auto;
    padding: 23px 24px 0;
  }
  .el-dialog__footer {
    padding: 12px 28px 20px;
    box-shadow: none;
    button {
      width: 100%;
    }
  }
}
.attach-review-desc {
  margin-top: 6px;
  margin-bottom: 27px;
}
li {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .attachment-item {
    display: flex;
    overflow: hidden;
    align-items: center;
    img {
      width: 44px;
      height: 44px;
      flex: 0 0 auto;
      border-radius: 4px;
      border: 1px solid $mx-color-var-fill-tertiary;
      &.weblink {
        padding: 9px;
        background-color: $mx-color-var-fill-quaternary;
      }
    }
    .attachment-info {
      overflow: hidden;
      padding-right: 50px;
    }
    .attachment-subtitle {
      color: $mx-color-var-text-secondary;
    }
  }
  .attachment-action {
    flex-shrink: 0;
    button {
      border: none;
      padding: 4px 12px;
      border-radius: 6px;
      background-color: $mx-color-var-fill-quaternary;
    }
    .attachment-checkmark {
      border-radius: 6px;
      padding: 2px 24px;
      background-color: rgba(47, 121, 89, 0.1);
      i {
        color: $mx-color-var-positive;
      }
    }
  }
}
</style>