<template>
  <section :class="['flow-panel', {'disable-delete-action': hideDeleteMenu}, {'disable-activity': disableActivity}]">
    <div
      class="flow-detail"
      @keydown.tab="isInAggregate||isInPreview ?'':focusableElementLoop($event)">
      <!--TODO: binderObj should be replaced with boardBriefViewModel-->
      <header
        v-if="!isLoading && !inPreview && !onlyShowActivities && !config.hideHeader"
        ref="header"
        :class="['mx-flex-container', 'mx-thread-header', showCreateInfo?'': 'action-wrapper']">
        <div
          v-if="showBack && breadCrumbsData.length <= 2"
          tabindex="0"
          class="back-icon mx-clickable"
          :aria-label="$t('back')"
          @keydown.enter="$emit('back')"
          @click="$emit('back')">
          <i class="micon-left-arrow-new">
            <span class="sr-only">{{ $t('back') }}</span>
          </i>
        </div>
        <div
          v-show="showCreateInfo"
          class="left-area">
          <div class="title mx-text-c1 mx-ellipsis">
            {{ breadCrumbsData.length <= 2 ? threadHeaderTitle ? threadHeaderTitle : creatorName : $t('preview') }}
          </div>
          <div
            v-if="!config.hideHeaderSubtitle"
            class="time mx-text-c4">
            {{ createdTime }}
          </div>
        </div>
        <div v-show="backTitle">
          <span
            class="micon-left-arrow-new"
            role="button"
            style="cursor: pointer;"
            @click="$emit('goBack')"/>
          <span class="mx-text-c1">{{ backTitle }}</span>
        </div>
        <div class="right-area">
          <template v-if="showHeaderDropdownMenu">
            <el-dropdown
              trigger="click"
              class="mx-hover-hide more-info-dropdown"
              :disabled="disabledMoreBtn"
              @visible-change="visibleChangeEvent"
              @command="moreOptionDropdownSelect">
              <span
                v-show="disabledMoreBtn || moreInfoConfig.length"
                v-mx-ta="{ page: 'thread', id: `dropdown_option_toggle`}"
                :title="$t('more_options')"
                :data-original-title="$t('more_options')"
                class="mx-clickable el-dropdown-link"
                :class="{'without-hover': disabledMoreBtn}">
                <i class="micon-more" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item,index) in moreInfoConfig"
                  :key="index"
                  :data-ta="item.dataTa"
                  :class="item.dropdownClassName"
                  :command="item.commandEvent">
                  <span :class="item.className">{{ item.text }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <span
            v-if="!opts.hideClose"
            v-mx-ta="{ page: 'thread', id: `close`}"
            class="mx-clickable"
            role="button"
            :aria-label="$t('close')"
            :class="[dataObjectInclude? 'right-close-button':'right-close-button-non-scope']"
            tabindex="0"
            @keydown.enter="closeThread"
            @click="closeThread">
            <i :class="closeIcon"/>
          </span>
        </div>
      </header>

      <div
        v-if="isInAggregate && backTitle"
        :class="['mx-flex-container', 'mx-thread-header','header']">
        <div>
          <span
            class="micon-left-arrow-new mx-clickable"
            role="button"
            tabindex="0"
            @keydown.enter="$emit('goBack')"
            @click="$emit('goBack')"/>
          <span class="mx-text-c1">{{ backTitle }}</span>
        </div>
      </div>
      <ActionBreadcrumbs v-if="!isLoading && showBreadCrumb && breadCrumbsData.length > 2"></ActionBreadcrumbs>

      <!--TODO: binderObj should be replaced with boardBriefViewModel-->
      <div
        ref="scrollContainer"
        v-loading="isLoading"
        class="flow-detail-container">
        <div
          ref="body"
          class="flow-body"
          >
          <div
            v-if="loadingFailed"
            class="loading-failed-wrap mx-text-b2 text-center">
            <span role="status">{{ $t('load_failed') }}</span>
            <span
              tabindex="0"
              class="mx-branding-text-action mx-clickable mx-padding-left-xxs"
              @click="readThread()"
              @keypress.enter="readThread()">
              <span
                class="mx-semibold"
                style="padding-right: 2px;">{{ $t('retry') }}</span>
              <i class="micon-mep-rotate"/>
            </span>
          </div>
          <template v-else-if="!isLoading && showComponentDetail && !onlyShowActivities">
            <div
              v-if="(existRoleInCurrentStep || needShowAlert) && !isFinished"
              role="alert"
              class="alert-info-container">
              <i class="micon-mep-warning"/>
              <div class="mx-text-c3 mx-margin-left-xs">{{ alertMessage }}</div>
            </div>
            <!--availableRoles used in props only for MeetRequestDetail.vue-->
            <component
              :is="baseObjectComponent"
              v-if="baseObject"
              ref="objectInfo"
              :stepModel="currentStep"
              :key="baseObject.sequence"
              class="base-object-modal"
              :base-object="baseObject"
              :boardBriefViewModel="boardBriefViewModel"
              :base-object-info="baseObjectInfo"
              :ctrl-key="ctrlKey"
              :binder-obj="binderObj"
              :config="baseObjectConfig"
              :flow-infos="flowInfos"
              :is-in-aggregate="isInAggregate"
              :show-transfer-option="supportTransferConversation"
              :select-user-direct="true"
              :keep-meet-subscribe="keepMeetSubscribe"
              :available-roles="availableRoles"
              :hideAttachmentDownload="shouldHideAttachmentDownload"
              :preSelectedTab="preSelectedTab"
              @close="$emit('close')"
              @viewPage="viewPage"
              @viewFileFolder="viewFileFolder"
              @reassignAction="reassignAction"
              @viewDecisionBranch="viewDecisionBranch"
              @viewBranchAction="viewBranchAction"
              @toMeetDetail="payload=> $emit('toMeetDetail',payload)"
              @copyFile="(payload) => $emit('copyFile', payload)"
              @prepare="prepareStep"
              @reviewerBtnClick="moreOptionDropdownSelect($event)"
              @startSignature="handleStartSignature"
              />
              <ReactionBadges 
                v-if="showBaseObjectReaction && firstFeed" 
                :feed="firstFeed"
                style="margin-left: -16px"
                :isMe="false"
                :allReactions="firstFeed.reactionModel.feedReactions" 
                :myReactions="firstFeed.reactionModel.myReactions" 
                :showReactionSelector="showReactionSelector"
                queryParentWidth=".base-object-modal"
                @select="updateMyReaction($event)" />
          </template>
        </div>
        <div
          v-if="!opts.isAudit && !disableActivity"
          class="flow-message"
          :class="{'nest-branch': breadCrumbsData.length > 2}"
          >
          <div
            class="activity-container"
            @keydown="handleKeyDownForFeed($event,getNextFocusId(),getPrevFocusId())">
            <ul
              v-if="activities"
              role="feed"
              class="activities">
              <div
                style="opacity: 0; height: 0px;"
                role="article">
                comment
              </div>
              <component
                :is="getActivityType(item.Action, item.BaseType)"
                v-for="item in activities"
                :key="item.sequence"
                :activity="item"
                :binder-obj="binderObj"
                :is-binder-owner="isBinderOwner"
                :ctrl-key="ctrlKey"/>
            </ul>
          </div>
        </div>
      </div>
      <div
        v-if="canShowFooterBar"
        class="flow-bottom">
        <template v-if="canAddFile">
          <el-dropdown
            v-mx-ta="{ page: 'todo_thread', id: `dropdown_attachment`}"
            class="mx-thread-upload-dropdown"
            placement="top-start"
            trigger="click"
            @command="onUploadDropdownSelect">
            <div
              v-mx-ta="{ page: 'thread', id: `dropdown_attachment_toggle`}"
              :aria-label="$t('add_attachment')"
              class="mx-clickable el-dropdown-link">
              <i class="micon-attachment font-icon-md"/>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                role="none"
                :disabled="true"
                class="mx-instructions">
                <span>{{ $t('add_file_more') }}</span>
              </el-dropdown-item>
              <el-dropdown-item
                v-if="canUploadFile"
                v-mx-ta="{ page: 'thread', id: `dropdown_attachment_desktop`}"
                icon="micon-desktop">
                <span>
                  {{ $t('desktop') }}
                </span>
                <MxUploadFileSelector
                  :accept="uploadFileAccept"
                  :before-upload="beforeUpload"
                  :upload-url="createUploadUrl"
                  :check-is-mocked-binder="checkIsMockedBinder"/>
              </el-dropdown-item>
              <el-dropdown-item
                v-if="canCopyTo"
                v-mx-ta="{ page: 'thread', id: `dropdown_attachment_binder`}"
                command="binder"
                icon="micon-binder">
                <span>
                  {{ $t('binder') }}
                  <UploadReplyAttachmentFromBinder
                    v-if="baseObject"
                    :binder-id="threadObject.binderId"
                    :object-seq="baseObject.sequence"
                    :type="baseObject.type"
                    :ctrl-key="ctrlKey"
                    :visible.sync="uploaderVisible"/>
                </span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>

        <div :class="{'no-input-bar': !canSendMessage}">
          <el-input
            v-show="canSendMessage"
            ref="sendMessageBox"
            v-model="message"
            v-typeahead
            v-typeheadMsg="canMentionTemplateMsg?{}:{noMention:true}"
            v-mx-ta="{ page: 'thread', id: `comment`}"
            :autosize="autoResizeRows"
            :placeholder="threadInputPlaceholder"
            :style="!showEmoji ? {'padding-right':'12px'} : false"
            autocapitalize="off"
            autocorrect="off"
            name="replyComment"
            class="mx-branding-focus-outline"
            type="textarea"
            @textarea-change="onTextareaChanged"
            @blur="message=$event.target.value"
            @focus="message=$event.target.value"
            @keypress.enter.exact.prevent.native="sendMessage($event)"/>
          <div
            v-if="canSendMessage"
            class="mx-bar-right">
            <Emoji
              v-if="showEmoji"
              @onSelect="selectEmoji"/>
            <el-button
              v-else
              type="text"
              class="send-button"
              :disabled="!message.trim()"
              @click="sendMessage"
              icon="micon-paper-plane"/>
          </div>
        </div>
      </div>
      <presenceStatusAlert
        ref="thread_status_alert"
        :style="{bottom: presenceStatusBottom.bottom}"
        :small="true"
        class="thread-preserence"
        :binder-obj="binderObj"/>
    </div>

    <mx-recording-player
      v-if="showRecordingPlayer"
      :model="recording"
      :visible.sync="showRecordingPlayer"/>

  </section>
</template>

<script>
import util from '@views/common/utils/utils'
import commentActivity from './activities/CommentActivity'
import commonActivity from './activities/CommonActivity'
import {FunctionUtil, ObjectUtils, StringUtils, BrowserUtils, MxConsts, Defines} from '@commonUtils'
import emojiTool from '@views/common/components/emoji/tools'
import Emoji from '@views/common/components/emoji/Emoji.vue'
import typeahead from '@views/common/components/mentionSelector/mentionDirective'
import CopyTodo from '@views/common/components/todo/CopyTodo.vue'
import MxUploadFileSelector from '@views/common/components/uploader/MxUploadFileSelector'
import MxRecordingPlayer from '@views/common/components/media/MxRecordingPlayer'
import {mapActions, mapGetters as mGetters, mapMutations} from 'vuex'
import UploadReplyAttachmentFromBinder from '@views/common/components/UploadReplyAttachmentFromBinder'
import {
  focusTo,
  focusableElementLoop,
  getLastFocusableElement,
  getFirstFocusableElement,
  isFocusByKeyboardNow
} from '@views/common/accessibility'
import {handleKeyDownForFeed} from '@views/common/accessibility/feed'
import popupA11y from '@views/common/accessibility/popup'
import presenceStatusAlert from '@views/common/components/presenceStatusAlert'
import moreActionInfo from './mixins/moreActionInfo'
import utils from '@views/common/utils/utils'
import {usePresenceStatusBottom} from '../../chat/src/functions/usePresenceStatusBottom';
import {mapState} from 'vuex'
import {subscribeFlowStep} from '../../../controller/workflow/instantFlow';
import EventBus from '../../common/plugins/eventBus'
import {getSupportActions} from '@views/workflows/utils/getSupportActions';
import i18n from '@views/i18n/mepIndex'
import {clearAll, hasComponents} from '../../common/useComponent';
import PopupManager from '@vendor/element-ui/src/utils/popup/popup-manager.js'
import typeaheadTplMsgMixin from '@views/common/mixins/typeaheadTplMsgMixin'
import {useAnonymousUser} from '@controller/user';
import scheduleFlowObject from './baseObjects/ScheduleFlowObject'
import {mapState as piniaMapState} from 'pinia'
import { mapActions as piniaMapActions } from 'pinia'
import { useBoardCommonActionsStore } from '@views/stores/boardCommonActions'
import {useFlowWorkSpaceDetailStore} from '@views/stores/flowWorkspaceDetail'
import {toComputedBinderObj, toOldWorkflows} from '@views/common/utils/workflow'
import {transactionActionComponent, transactionActionViewAttachment} from '@views/common/utils/transactionActionUtils'
import {doReassignAction} from '@views/binderDetail/src/actions/reassignActions.js'
import {toBoardBriefViewModel} from '@views/common/utils/workflow'
import { useDDRSupportStore } from '@views/stores/ddrSupport'
import ReactionBadges from '@views/chat/src/components/ReactionBadges.vue'
import { UserObjectType } from '@model/board/boardUserViewModel'
import ActionBreadcrumbs from './component/baseObjects/ActionBreadcrumbs.vue'
import { useBreadCrumbsStore, useBinderBreadCrumbsStore } from '@views/stores/breadCrumbs'



export default {
  name: 'MxFlowThread',
  components: {
    commentObject: () => utils.retry(import(/* webpackChunkName: "thread.comment" */ './baseObjects/CommentObject')),
    fileObject: () => utils.retry(import(/* webpackChunkName: "thread.file" */ './baseObjects/FileObject')),
    meetObject: () => utils.retry(import(/* webpackChunkName: "thread.meet" */ './baseObjects/MeetObject')),
    pageObject: () => utils.retry(import(/* webpackChunkName: "thread.page" */ './baseObjects/PageObject')),
    // positionObject: () => import(/* webpackChunkName: "thread.position" */ './baseObjects/positionObject'),
    signObject: () => utils.retry(import(/* webpackChunkName: "thread.sign" */ './baseObjects/SignObject')),
    todoObject: () => utils.retry(import(/* webpackChunkName: "thread.todo" */ './baseObjects/TodoObject')),
    commentActivity,
    commonActivity,
    Emoji,
    CopyTodo,
    MxUploadFileSelector,
    UploadReplyAttachmentFromBinder,
    MxRecordingPlayer,
    presenceStatusAlert,
    scheduleFlowObject,
    ReactionBadges,
    ActionBreadcrumbs,
    DocuSignObject: () => utils.retry(import(/* webpackChunkName: "thread.transaction.docusign" */ '@views/thread/src/baseObjects/DocuSignObject')),
    ApprovalAction: () => utils.retry(import(/* webpackChunkName: "thread.approval" */ '@views/thread/src/component/baseObjects/ApprovalAction')),
    AcknowledgeAction: () => utils.retry(import(/* webpackChunkName: "thread.acknowledge" */ '@views/thread/src/component/baseObjects/AcknowledgeAction')),
    FileRequestAction: () => utils.retry(import(/* webpackChunkName: "thread.fileRequest" */ '@views/thread/src/component/baseObjects/FileRequestAction')),
    TodoTransactionAction: () => utils.retry(import(/* webpackChunkName: "thread.todoTransaction" */ '@views/thread/src/component/baseObjects/TodoTransactionAction')),
    TimeBookingAction: () => utils.retry(import(/* webpackChunkName: "thread.timeBooking" */ '@views/thread/src/component/baseObjects/TimeBookingAction')),
    FormRequestAction: () => utils.retry(import(/* webpackChunkName: "thread.form" */ '@views/thread/src/component/baseObjects/FormRequestAction')),
    LaunchWebAppAction: () => utils.retry(import(/* webpackChunkName: "thread.launchWebApp" */ '@views/thread/src/component/baseObjects/LaunchWebAppAction')),
    JumioAction: () => utils.retry(import(/* webpackChunkName: "thread.jumio" */ '@views/thread/src/component/baseObjects/JumioAction')),
    IntegrationAction: () => utils.retry(import(/* webpackChunkName: "thread.integration" */ '@views/thread/src/component/baseObjects/IntegrationAction')),
    GenericAction: () => utils.retry(import(/* webpackChunkName: "thread.generic" */ '@views/thread/src/component/baseObjects/GenericAction')),
    DecisionAction: () => utils.retry(import(/* webpackChunkName: "thread.Decision" */ '@views/thread/src/component/baseObjects/DecisionAction')),
    WaitAction: () => utils.retry(import(/* webpackChunkName: "thread.waitAction" */ '@views/thread/src/component/baseObjects/WaitAction')),
  },
  directives: {
    typeahead
  },
  mixins: [popupA11y, moreActionInfo, typeaheadTplMsgMixin],
  provide () {
    return {
      viewUserId: this.viewUserId,
      boardId: this.boardBriefViewModel.boardId,
    }
  },
  props: {
    //todo duplicate props
    // isPreview:{
    //   type: Boolean,
    //   default: false
    // },
    //TODO: please have object definition here!!!
    threadObject: {
      type: Object,
      required: true,
      default: () => ({
        baseObject: {
          type: '',
          sequence: 0,
          spath: ''
        },
        binderId: '',
        flowStepSequence: 0,
        boardViewToken: ''
      })
    },
    //views/stores/threadDetail.ts -> ThreadConfig
    config: {
      type: Object,
      default: () => ({})
    },
    onlyShowActivities: {
      type: Boolean,
      default: false
    },
    showBack: {
      type: Boolean,
      default: false
    },
    disableActivity: {
      type: Boolean,
      default: () => false
    },
    isInPreview: {
      type: Boolean,
      default: false
    },
    //TODO: what does this mean? (by West, means from overview -> actions)
    isInAggregate: {
      type: Boolean,
      default: false
    },
    viewUserId: {
      type: String,
      default: () => null
    },
    //TODO: for non-step basedObject, this value should be null!!!
    flowInfos: {
      type: Object,
      default: () => ({})
      // default: () => ({
      //   flowSequence: 0,
      //   stepSequence: 0,
      //   transformedFlowStep: {
      //       rawData: {},
      //       isNotStarted: false,
      //       isInProgress: false
      //   },
      //   stepIsInitial: false,
      //   workflow: {
      //       isCanceled: false
      //   },
      // })
    },
    backTitle: {
      type: String,
      default: ''
    },
    keepMeetSubscribe: {
      type: Boolean,
      default: false
    },
    binderViewToken: {
      type: String,
      default: ''
    },
    //Checking IBoardBriefViewModel(boardViewModel.ts)
    boardBriefViewModel: {
      type: Object,
      default: () => ({})
    },
    threadHeaderTitle: {
      type: String,
      default: ''
    },
    preSelectedTab: {
      type: String,
      default: null
    },
    showBreadCrumb: {
      type: Boolean,
      default: true
    }
  },

  data () {
    const {isAnonymousUser} = useAnonymousUser()

    return {
      isAnonymousUser,
      threadLoaded: false,
      currentThread: {},
      hideDeleteMenu: false,
      inPreview: false,
      opts: this.config || {},
      baseObject: {},
      activities: [],
      allFeedSeq: [],
      pendingReply: [],
      message: '',
      showComponentDetail: false,
      // baseObjectComponent: this.getBaseObjectType(),
      todoList: {},
      boxHeight: '52px',
      uploaderVisible: false,
      recording: null,
      showRecordingPlayer: false,
      isLoading: !this.isInPreview,
      loadingFailed: false,
      autoResizeRows: {minRows: 1},
      presenceAlertBottom: '57px',
      threadChangedCount: 0,
      objectConfig: {},
      showReassignMember: false,
      reassignTitle: {
        title: this.$t('Reassign_to'),
        subTitle: ''
      },
      fromUser: null,
      reassignInfos: {},
      latestNotStatedStepConfig: null,
      actionType: '',
      flowCanceledConfirm: null,
      // preBaseObjectComponent for fix MVB-37801
      preBaseObjectComponent: '',
      firstFeed: null
    }
  },

  computed: {
    ...piniaMapState(useBreadCrumbsStore, ['breadCrumbsData']),
    ...mGetters('user', ['currentUser']),
    ...mGetters('chat', ['userDrafts']),
    ...mGetters('thread', ['getCurrentThread']),
    ...mGetters('group', ['groupTags', 'canAcdClientAddFile']),
    ...mGetters('privileges', ['showEmoji', 'canSendMessage', 'canUploadFile', 'canShareInternally']),
    ...mapState('thread', ['takingAction']),
    //TODO: if Thread does not distinguish between group or flow workspace, this needs to be changed
    ...piniaMapState(useFlowWorkSpaceDetailStore, [
      'supportTransferConversation',
      'availableRoles',
    ]),
    isFinished () {
      return this.config?.isCanceled || this.config?.isCompleted
    },
    baseObjectComponent () {
      const baseType = ObjectUtils.getByPath(this.threadObject, 'baseObject.type')
      const {transactionType, sub_type} = this.baseObject || {}
      if (!baseType) {
        return this.preBaseObjectComponent
      } else if (baseType === 'TRANSACTION' && !transactionType) {
        // in the flow case, can't create a generic action, so we don't consider it.
        return this.preBaseObjectComponent
      }
      return this.getBaseObjectType(baseType, transactionType, sub_type)
    },
    // ...mapGetters('binder', ['canPinFeed','isBinderOwner']),
    needShowAlert () {
      return this.currentStep.isNotStarted || this.currentStep.isPreparing
    },
    alertMessage () {
      if (this.currentStep.enablePreparation && (this.currentStep.isPreparing || !this.currentStep.isPrepareCompleted)) {
        return this.$t('The_action_is_not_ready')
      } else if (this.existRoleInCurrentStep) {
        return this.$t('Role_need_to_be_set_to_proceed')
      } else if (this.currentUser.isInternalUser && this.currentStep.isHolding) {
        return this.$t('action_referenced_data_is_being_processed')
      } else {
        if (this.currentStep.hasInvalidDDRVariable) {
          return this.$t('not_started_step_referenced_data_no_longer_valid')
        } else {
          return this.$t('Action_not_started');
        }
      }
    },
    canPinFeed () {
      return this.boardBriefViewModel?.canBookmarkFeed
    },
    isBinderOwner () {
      return this.boardBriefViewModel?.isMyselfBinderOwner
    },
    binderObj () {
      //TODO: because you want to the whole IWorkflowBoardViewModel, you may want to add those logic into toComputedBinderObj
      //based on the workflow is existing or not!!!
      const binderObj = toComputedBinderObj(this.boardBriefViewModel || {})

      //TODO: boardBriefViewModel has workflow????
      //Are you using the IWorkflowBoardViewModel??? This will cause this module can't be used by the MxBinderDetail!!!
      binderObj.workflows = toOldWorkflows(this.boardBriefViewModel, this.config.stepSequence)

      return binderObj
    },
    hideActorLastName () {
      //TODO: binderObj should be replaced with boardBriefViewModel!!!
      return (this.binderObj && this.binderObj.is_acd && !(this.currentUser.isInternalUser))
    },
    showHeaderDropdownMenu () {
      return !this.config.isReadOnlyPreview && !this.isLoading && !this.isAnonymousUser && !this.isPreview && !this.inManageView
    },
    baseObjectInfo () {
      //TODO: binderObj should be replaced with boardBriefViewModel!!!
      return {
        boardId: this.binderObj?.id,
        transactionSequence: this.threadObject?.baseObject?.sequence
      }
    },
    //TODO: what difference between this and props.isInPreview
    isPreview () {
      return this.$route.query.preview == 'true';
    },
    canCopyTo () {
      return this.canShareInternally && this.groupTags.enable_copy_to && !this.isAnonymousUser
    },

    addFileCtrlInSpecialBinder () {
      //TODO: binderObj should be replaced with boardBriefViewModel!!!
      if (this.binderObj.is_acd && !this.currentUser.isInternalUser) {
        return this.canAcdClientAddFile
      }
      return true
    },

    canAddFile () {
      return (this.isTodo || this.isESign || this.isTransaction) && (this.canUploadFile || this.canCopyTo) && this.addFileCtrlInSpecialBinder
    },

    canShowFooterBar () {
      //TODO: binderObj should be replaced with boardBriefViewModel!!!
      return this.threadLoaded &&
        // !this.isOutofOfficeHourInAcdBinder &&
        !this.disableFlowReplyMessage &&
        !this.binderObj.isNoAction &&
        (this.canAddFile || this.canSendMessage)
    },
    type: function () {
      return ObjectUtils.getByPath(this.threadObject, 'baseObject.type')
    },
    isTodo: function () {
      return ObjectUtils.getByPath(this.threadObject, 'baseObject.type') === 'TODO'
    },
    isESign () {
      return ObjectUtils.getByPath(this.threadObject, 'baseObject.type') === 'SIGNATURE'
    },
    isTransaction () {
      return ObjectUtils.getByPath(this.threadObject, 'baseObject.type') === 'TRANSACTION'
    },
    replyMessage: function () {
      return this.opts.isReply
    },
    isCommentObject: function () {
      return ObjectUtils.getByPath(this.threadObject, 'baseObject.type') === 'COMMENT'
    },
    disableFlowReplyMessage () {
      return this.opts.showInCalendar || this.config.disableFlowReplyMessage || this.config.showInCalendar
    },
    uploadFileAccept () {
      // TODO return app.getWhitelabelConfig().allowedFileTypes
      return ''
    },
    isClientMobile () {
      return !this.currentUser.isInternalUser && (BrowserUtils.isMobile || BrowserUtils.isTablet)
    },
    isPhone () {
      return BrowserUtils.isMobile
    },
    inManageView () {
      return !!this.viewUserId
    },
    baseObjectConfig () {
      return Object.assign({}, this.objectConfig, this.config, this.latestNotStatedStepConfig)
    },
    closeIcon () {
      if (this.config.closeIcon) {
        return this.config.closeIcon
      } else {
        return this.dataObjectInclude ? 'micon-close close-button' : 'micon-close'
      }
    },
    transactionStatus () {
      return this.baseObject.status
    },
    //TODO: No place to use this?????
    displayType () {
      if (this.flowInfos && this.flowInfos.transformedFlowStep) {
        const {rawData} = this.flowInfos.transformedFlowStep
        return getSupportActions(rawData.type, this.$t).label
      }
      //TODO: need return a value
      return {};
    },
    threadInputPlaceholder () {
      if (this.canMentionTemplateMsg) {
        return this.isCommentObject ? this.$t('mx50_Add_a_reply_with_mention_template_message') : this.$t('mx50_Add_a_comment_with_mention_template_message')
      } else {
        return this.isCommentObject ? this.$t('mx50_Add_a_reply') : this.$t('mx50_Add_a_comment')
      }
    },
    existRoleInCurrentStep () {
      if(this.currentStep.enablePreparation && this.currentStep.editor?.isRole) {
        return true
      }
      if (this.config?.assignees) {
        return this.config.assignees.findIndex(s => {
          return s.isAssigneeRole
        }) >= 0
      }else{
        // MV-17305 For started action
        let hasRoleAssignee = false
        let hasRoleMeetingHost = false 
        if(this.currentStep.subSteps){
          hasRoleAssignee = (this.currentStep.subSteps || []).findIndex(ss => {
            return ss && ss.assignee && ss.assignee.isRole && !ss.assignee.assigneeUser
          }) >= 0
        }
        if(this.currentStep.meetRequestOption){
          const meetingHost = this.currentStep.meetRequestOption.host
          hasRoleMeetingHost = meetingHost && meetingHost.objectType === UserObjectType.Role && !meetingHost.assigneeUser
        }
        return hasRoleAssignee || hasRoleMeetingHost
      }
    },
    currentStep () {
      const stepSequence = this.flowInfos.stepSequence
      const stepInfo = this.boardBriefViewModel.workflow?.steps?.find(step => step.sequence === stepSequence)
      
      if (!stepInfo?.isNotStarted && this.baseObject.editor) {
        return {
          ...stepInfo,
          editor: this.baseObject.editor
        }
      }

      return stepInfo || {}
    },
    disabledMoreBtn () {
      const { type, isInProgress } = this.currentStep || {}
      const isWaitStep = type === Defines.WorkflowStepType.WORKFLOW_STEP_TYPE_AWAIT
      //binder owner needs to be able to Mark as Complete
      return isWaitStep && isInProgress && !this.isBinderOwner
    },
    showBaseObjectReaction () {
      return this.firstFeed && (this.type === 'COMMENT' || this.type === 'POSITION_COMMENT' || this.type === 'FILE' || this.type === 'PAGE')
    },
    showReactionSelector () {
      const {is_service_request, is_acd, isACDLeaveMessage, isSocial, is_inbox, isNoAction} = this.binderObj
      if (this.opts.isAudit || is_service_request || is_acd || isACDLeaveMessage || isSocial || is_inbox || isNoAction) {
        return false
      }
      return true
    },
    shouldHideAttachmentDownload() {
     //in flow workspacedetail Panel show attachment hover download button
      return false; 
    }
  },
  watch: {
    threadObject: {
      handler (newThread, oldThread) {
        if (newThread.binderId !== oldThread.binderId ||
          newThread.baseObject.spath !== oldThread.baseObject.spath ||
          newThread.baseObject.sequence !== oldThread.baseObject.sequence ||
          newThread.flowStepSequence !== oldThread.flowStepSequence
        ) {
          // clear the old thread and reset to new one
          this.resetThread(newThread, oldThread)
          // this.baseObjectComponent = this.getBaseObjectType()

          //let self = this
          const {isClientMobile, $refs} = this;
          setTimeout(() => {
            if (!isClientMobile && !isFocusByKeyboardNow) {
              $refs?.sendMessageBox?.focus();
            }
            //!self.isClientMobile && !isFocusByKeyboardNow && self.$refs?.sendMessageBox?.focus()
            //self = null
          }, 300)
        }
      },
      deep: true
    },
    threadLoaded (nval) {
      //TODO: binderObj should be replaced with boardBriefViewModel!!!
      if (nval) {
        const {name, title, displayName} = this.baseObject
        this.$emit('threadOpened', this.binderObj.isWorkflow ? this.threadHeaderTitle : (name || title || displayName))
      }
    },
    workflows (newFlow) {
      //todo
      // this.checkFlowCanceled(newFlow)

    },
    'binderObj.workflows': {
      handler (newFlows, oldFlows) {
        const newFlow = newFlows && newFlows[0]
        //reset latestNotStatedStepConfig
        this.latestNotStatedStepConfig = null
        if (newFlow) {
          let currentStep
          for (const tStep of newFlow.transformedSteps) {
            if (this.isInAggregate) {
              const baseObjSeq = ObjectUtils.getByPath(this.threadObject, 'baseObject.sequence')
              if (baseObjSeq && tStep.outputObject?.sequence === baseObjSeq) {
                currentStep = tStep
                break
              }
            } else {
              if (tStep.sequence === this.config.stepSequence) {
                currentStep = tStep
                break
              }
            }
          }
          if (currentStep && currentStep.isCanceled) {
            this.latestNotStatedStepConfig = {isCanceled: true}
          }

          this.checkFlowCanceled(newFlow)

          if (this.config.stepSequence) {
            // not started step didn't have the actual object, and can't monitor the assignee's change when subscribe thread
            const noStartedCurrentStep = !currentStep.outputObject && currentStep
            if (!noStartedCurrentStep) {
              return
            }
            if (this.config?.assignees) {
              const changedConfig = {
                ...this.config
              }
              changedConfig.assignees = noStartedCurrentStep.assignees
              this.latestNotStatedStepConfig = {assignees: noStartedCurrentStep.assignees}
              this.baseObject = this.setBaseObject(this.baseObject, changedConfig)
            }

            const assigneeId = noStartedCurrentStep.assignees.map(s => {
              return s.id
            })
            this.$emit('workflowChangedInfo', {assigneeId})
          }

        }
      },
      deep: true
    },
    transactionStatus: {
      handler (val) {
        if (val) {
          this.$store.commit('preview/setTransactionStatus', this.transactionStatus)
        }
      },
      immediate: true
    },
    canShowFooterBar (nVal) {
      if (nVal) {
        const {isClientMobile, $refs} = this;
        this.$nextTick(() => {
          if (!isClientMobile && !isFocusByKeyboardNow()) {
            $refs?.sendMessageBox?.focus();
          }
        })
      }
    },
    'currentStep.clientUuid': {
      handler (nval) {
        const isEsignStep = this.currentStep.type === Defines.WorkflowStepType.WORKFLOW_STEP_TYPE_SIGNATURE
        if (nval && (isEsignStep || this.currentUser.isInternalUser)) {
          this.setDDRSupportFilterInfo()
        }
      },
      immediate: true
    }
  },
  //TODO: to be removed???
  //usePresenceStatusBottom isn't used anymore???
  setup () {
    return usePresenceStatusBottom(28)
  },
  created () {
    this.resetThread()
    // if (this.isInAggregate && this.canAddFile) {
    //   this.init(this.threadObject.binderId)
    // }
    this.showComponentDetail = true
  },
  mounted () {
    this.a11yCacheTrigger()
    let isKeyboard = isFocusByKeyboardNow()
    let self = this
    setTimeout(() => {
      this.boxHeight = self.$refs.sendMessageBox && self.$refs.sendMessageBox.textareaCalcStyle.height
      if (!this.isInAggregate && isKeyboard) {
        focusTo(this.$el, {delay: true})
      } else if (!isKeyboard) {
        !this.isClientMobile && self.$refs?.sendMessageBox?.focus()
      }
      self = null
    }, 300)

    this.$on('focusToComment', this.eventFocusToComment)
    this.$on('loadingEnd', this.eventLoadingEnd)

    this.getAutoResizeRows()
    this.$el.addEventListener('touchstart', this.blurMessageBox)
    this.$emit('mounted')
    // this.$store.commit('preview/setCanceledStatus', !!this.config.isCanceled)
  }, 
  beforeDestroy () {
    this.$off('focusToComment', this.eventFocusToComment)
    this.$off('loadingEnd', this.eventLoadingEnd)

    this.saveDraft()
    this.clearThread(this.ctrlKey)
    this.$el.removeEventListener('touchstart', this.blurMessageBox)
    this.flowStepSubscriber && this.flowStepSubscriber.unsubscribe()
    // if (this.isInAggregate && this.canAddFile) {
    //   this.destroy()
    // }
  },
  methods: {
    ...mapActions('thread', ['deleteTodo', 'subscribeThread', 'clearThread', 'createThreadComment', 'makeUploadToTransReplyFileUrl', 'makeUploadToSignReplyFileUrl']),
    ...mapActions('todos', ['makeUploadToTodoUrl']),
    ...mapMutations('chat', ['setUnsendComment']),
    ...mapActions('chat', ['updateReaction']),
    ...piniaMapActions(useBoardCommonActionsStore, ['getAvailableFilesName']),
    ...mapMutations('thread', ['setTakingAction']),
    ...piniaMapActions(useBinderBreadCrumbsStore, ['backToMainStep']),
    focusableElementLoop, handleKeyDownForFeed,
    updateMyReaction (emoji) {
      this.updateReaction({
        emoji, feed: this.firstFeed
      })
    },
    blurMessageBox () {
      if (this.$refs.sendMessageBox) {
        this.$refs.sendMessageBox.blur()
      }
    },
    getAutoResizeRows () {
      if (document.body.clientHeight > 600) {
        let body = this.$refs.body && this.$refs.body.clientHeight, header = 0
        if (this.baseObjectComponent === 'commentObject') {
          body = 0 //commentObject base comment and reply comment are scrolled altogether
        }
        if (this.$refs.header) {
          header = this.$refs.header.clientHeight
        }
        let height = body + header + 12
        let rows = Math.floor((this.$el.clientHeight - height - 300) / 20)
        this.autoResizeRows.maxRows = Math.max(rows, 5)
      } else {
        this.autoResizeRows.maxRows = 5
      }
    },
    getNextFocusId () {
      return getFirstFocusableElement(this.$el.querySelector('.flow-bottom'))
    },
    getPrevFocusId () {
      return getLastFocusableElement(this.$el.querySelector('.flow-body'))
    },
    beforeUpload (files, done) {
      this.getAvailableFilesName(this.binderObj.id, files).then((newNames) => {
        done(files, newNames)
      })
    },
    saveDraft (thread) {
      thread = thread || this.threadObject
      if (thread && thread.baseObject) {
        const payload = {}
        payload['binderId'] = `${thread.binderId}-${thread.baseObject.sequence}`
        payload['content'] = this.message || ''
        this.setUnsendComment(payload)
      }
    },
    resetThread (newThread, oldThread) {
      let isRefresh = false
      if (oldThread) {
        this.saveDraft(oldThread)
      }
      if (newThread && oldThread) {
        if (newThread.binderId !== oldThread.binderId && newThread.flowStepSequence === oldThread.flowStepSequence) {
          //when not started step refresh, board id changed but flow step sequence is not changed
          isRefresh = true
        }
      }
      this.flowStepSubscriber && this.flowStepSubscriber.unsubscribe()
      this.clearThread(this.ctrlKey)
      this.message = ''
      this.baseObject = {}
      this.activities = []
      this.threadLoaded = false
      this.readThread(isRefresh)
      this.subscribeFlowStep()
      !this.isInPreview && this.setTakingAction(false)
    },
    readThread (isRefresh) {
      this.isLoading = !this.isInPreview && !isRefresh
      this.loadingFailed = false
      this.ctrlKey = Date.now().toString()

      let vm = this
      this.subscribeThread({
        ctrlKey: this.ctrlKey,
        param: this.threadObject,
        callback: (thread, ctrlKey) => {
          if (ctrlKey === this.ctrlKey) {
            vm._processThreadCallback(thread, isRefresh)
          }
        }
      })
      if (Object.keys(vm.flowInfos).length) {
        const flowThreadCtrlKey = Date.now().toString() + 'workflow'
        vm.subscribeThread({
          ctrlKey: flowThreadCtrlKey,
          param: {
            baseObject: {
              sequence: vm.flowInfos.flowSequence,
              spath: `workflows[sequence=${vm.flowInfos.flowSequence}]`,
              type: 'WORKFLOW'
            },
            //TODO: binderObj should be replaced with boardBriefViewModel!!!
            binderId: this.binderObj.id
          },
          callback (workflowThread, ctrlKey) {
            if (flowThreadCtrlKey === ctrlKey) {
              vm._processWorkflowThreadCallback(workflowThread, isRefresh)
            }
          }
        })
      }
    },
    _processWorkflowThreadCallback (workflowThread, isRefresh) {
      const vm = this
      const workflowThreadActivities = []
      if (workflowThread && workflowThread.activities) {
        workflowThread.activities.forEach(activity => {
          const baseObject = activity.baseObject
          if ((activity.Action === 'UPDATE' || activity.Action === 'REASSIGN' || activity.Action === 'STEP_READY') && baseObject.flowStep && baseObject.flowStep.sequence === vm.flowInfos.stepSequence) {
            workflowThreadActivities.push(activity)
          }
        })
        if (workflowThreadActivities.length) {
          this._sortAndFilterActvities(workflowThreadActivities, isRefresh)
        }
      }
    },
    async _processThreadCallback (thread, isRefresh) {
      let vm = this
      this.isLoading = false
      if (thread.cannotAccess) {
        // binder or binder user has been deleted
        vm.closeThread()
        this.$emit('todoIsDelete', true)
      } else if (thread.isError) {
        this.loadingFailed = true
        this.$emit('todoIsLoadError', true)
      } else {
        if (!this.threadLoaded) {
          if (vm.threadObject?.binderId && thread.baseObject?.sequence) {
            const restoreMsg = vm.userDrafts[`${vm.threadObject.binderId}-${vm.threadObject.baseObject.sequence}`]
            if (restoreMsg) {
              vm.message = restoreMsg
            }
          }
        }
        if (thread.firstFeed) {
          this.firstFeed = thread.firstFeed
        }
        if (thread.baseObject) {
          if (thread.baseObject.is_deleted || thread.baseObject.isDeleted) {
            this.$emit('todoIsDelete', true)
            vm.baseObject = thread.baseObject   // deleted infos
            vm.closeThread()
          } else {
            let baseObject = thread.baseObject
            vm.actionType = baseObject.transactionType || baseObject.type
            // to view not started step
            //TODO: binderObj should be replaced with boardBriefViewModel!!!
            if(baseObject.transactionType === MxConsts.TransactionType.TRANSACTION_TYPE_AWAIT) {
              baseObject.awaitOption = this.currentStep.awaitOption
              baseObject = {
                ...baseObject,
                ...this.config.baseObject
              }
            } else if (this.binderObj.isWorkflow || this.binderObj.isFlowTemplate) {
              baseObject = this.setBaseObject(baseObject, this.config, vm)
              if (this.config.boardViewToken) {
                thread.activities = null
              }
            }
            if (ObjectUtils.getByPath(baseObject, 'assignee.displayDeletedUser')) {
              baseObject.assignee.name = `[${this.$t('Deleted_User')}]`
            }
            if (ObjectUtils.getByPath(baseObject, 'creator.displayDeletedUser')) {
              baseObject.creator.name = `[${this.$t('Deleted_User')}]`
            }
            vm.baseObject = baseObject
          }
        } else if (!this.threadLoaded) {
          this.loadingFailed = true
        }
        this.threadLoaded = true
        if (thread.activities) {
          this._sortAndFilterActvities(thread.activities || [], isRefresh)
        }
        if (this.isInAggregate) {
          if (this.baseObject?.isCompleted && this.baseObject?.transactionType === 'TRANSACTION_TYPE_MEET_REQUEST') {
            // meeting request need to open meeting info,shouldn't be count on changed.
          } else {
            this.threadChangedCount++
          }

          if (this.threadChangedCount > 1) {
            this.$emit('objectIsChanged', true)
          }
        }
        this.$emit('loadingEnd', true)
      }
    },
    _sortAndFilterActvities (activities, isRefresh) {
      const vm = this
      let scrollToBottom = true
      if (vm.activities && vm.activities.length) {
        // updated activity data
        activities.map(activity => {
          if (activity.actor && activity.actor.displayDeletedUser) {
            activity.actor.name = `[${this.$t('Deleted_User')}]`
          } else if (this.hideActorLastName && this.currentUser.id !== activity.actor.id) {
            activity.actor.name = activity.actor.first_name || activity.actor.name
          }
          if (ObjectUtils.getByPath(activity, 'baseObject.assignee.displayDeletedUser')) {
            activity.baseObject.assignee.name = `[${this.$t('Deleted_User')}]`
          }
          if (vm.activities) {
            let idx = vm.activities.findIndex(act => {
              if (activity.sequence === act.sequence) {
                return true
              } else if (act.isPending || activity.Action === 'REPLY_DELETE') {
                if (activity.relatedObject &&
                  ObjectUtils.getByPath(activity, 'relatedObject.client_uuid') &&
                  ObjectUtils.getByPath(act, 'relatedObject.client_uuid') &&
                  ObjectUtils.getByPath(activity, 'relatedObject.client_uuid') ===
                  ObjectUtils.getByPath(act, 'relatedObject.client_uuid')) {
                  return true
                }
              }
              return false
            })
            let isOriginalDeletedReplyAct = false
            if (activity.Action === 'DELETE_FILE_REPLY' && ObjectUtils.getByPath(activity, 'baseObject.attachments.0.is_original_deleted')) {
              isOriginalDeletedReplyAct = true
            }
            if (idx >= 0) {
              let relatedObject = activity.relatedObject
              if (relatedObject && relatedObject.is_deleted && activity.RelatedType === 'COMMENT') {
                vm.activities.splice(idx, 1)
                if (activity.Action === 'REPLY_DELETE') {
                  vm.activities.push(activity)
                }
              } else if (relatedObject && relatedObject.is_deleted && activity.BaseType === 'TODO' && activity.Action === 'ATTACHMENT') {
                if (relatedObject.sequence) {
                  vm.activities.splice(idx, 1, activity)
                } else {
                  vm.activities.splice(idx, 1)
                }
              } else if (isOriginalDeletedReplyAct) {
                vm.activities.splice(idx, 1)
              } else {
                vm.activities.splice(idx, 1, activity)
              }
            } else {
              if (activity.relatedObject && activity.relatedObject.is_deleted) {
                if (activity.Action === 'DELETE_ATTACHMENT' || activity.Action === 'ATTACHMENT' || activity.Action === 'REPLY_DELETE') {
                  if (activity.BaseType === 'TODO' && activity.Action === 'ATTACHMENT') {
                    // ignore
                  } else {
                    vm.activities.push(activity)
                  }
                }
              } else if (activity.BaseType === 'TODO' && activity.Action === 'ASSIGN' && activity.created_time === activity.baseObject.created_time) {
                if (activity.baseObject['workflow']) {
                } else {
                  return false
                }
              }
                // else if((activity.BaseType === 'TRANSACTION'|| activity.BaseType === 'SIGNATURE') && activity.Action === 'REOPEN'){
                //   // return false
              // }
              else if ((activity.BaseType === 'TRANSACTION' && activity.Action === 'UPDATE') && ObjectUtils.getByPath(activity, 'baseObject.status') === MxConsts.TransactionStatus.TRANSACTION_STATUS_ACTIVE && ObjectUtils.getByPath(activity, 'baseObject.actionType') === 'Docusign') {
                return false
              } else if (!isOriginalDeletedReplyAct) {
                vm.activities.push(activity)
              }
            }

            // don't scroll to bottom when update the content of TODO
            if (activity.BaseType === 'TODO') {
              if (activity.Action !== 'REPLY' && activity.Action !== 'ATTACHMENT' && activity.Action !== 'DELETE_ATTACHMENT') {
                scrollToBottom = false
              }
              if (activity.Action === 'REPLY' && (ObjectUtils.getByPath(activity, 'relatedObject.is_modified') || ObjectUtils.getByPath(activity, 'relatedObject.is_deleted'))) {
                scrollToBottom = false
              }
            }
          }
        })
      } else {
        // init activity data
        vm.activities = activities.filter(activity => {
          if (activity.Action === 'REPLY' && activity.BaseType === 'TODO') {
            if (activity.relatedObject && activity.relatedObject.is_deleted) {
              return false
            }
          }
          if (activity.Action === 'DELETE_FILE_REPLY' && ObjectUtils.getByPath(activity, 'baseObject.attachments.0.is_original_deleted')) {
            return false
          }
          if (activity.actor && activity.actor.displayDeletedUser) {
            activity.actor.name = `[${this.$t('Deleted_User')}]`
          }
          if (activity.BaseType === 'TODO' && activity.Action === 'ASSIGN' && activity.created_time === activity.baseObject.created_time) {
            if (activity.baseObject['workflow']) {
            } else {
              return false
            }
          } else if ((activity.BaseType === 'TRANSACTION' && activity.Action === 'UPDATE') && ObjectUtils.getByPath(activity, 'baseObject.status') === MxConsts.TransactionStatus.TRANSACTION_STATUS_ACTIVE && ObjectUtils.getByPath(activity, 'baseObject.actionType') === 'Docusign') {
            return false
          }
          // if((activity.BaseType === 'TRANSACTION'|| activity.BaseType === 'SIGNATURE') && activity.Action === 'REOPEN'){
          //   return false
          // }
          return true
        })
      }
      vm.activities = vm.activities.sort((a, b) => a.created_time - b.created_time)
      if (scrollToBottom && !isRefresh) {
        this.$nextTick(() => {
          const flowContainer = this.$refs['scrollContainer']
          if (flowContainer) {
            flowContainer.scrollTop = flowContainer?.scrollHeight
          }
        })
      }
    },
    createUploadUrl (selectFile) {
      if (this.baseObject) {
        let fileName = selectFile.name
        let objectSeq = this.baseObject.sequence
        if (this.isTodo) {
          return this.makeUploadToTodoUrl({
            currentBinderId: this.threadObject.binderId,
            todoSequence: objectSeq,
            fileName
          })
        } else if (this.isESign) {
          return this.makeUploadToSignReplyFileUrl({ctrlKey: this.ctrlKey, signatureSeq: objectSeq, fileName})
        } else if (this.isTransaction) {
          return this.makeUploadToTransReplyFileUrl({ctrlKey: this.ctrlKey, transactionSeq: objectSeq, fileName})
        }
      }
    },
    checkIsMockedBinder () {
      //TODO: binderObj should be replaced with boardBriefViewModel!!!
      return this.binderObj.isMocked;
    },
    selectEmoji (code) {
      const replyInputEl = this.$el.querySelector('[name=replyComment]')
      if (replyInputEl) {
        emojiTool.onSelectEmoji(code, replyInputEl)
      }
    },

    onUploadDropdownSelect (cmd) {
      if (cmd === 'binder') {
        this.uploaderVisible = true
      } else if (cmd === 'desktop') {

      }
    },
    getBaseObjectType: function (baseType, transactionType, subType) {
      let componentName = ''
      // const type = ObjectUtils.getByPath(this.threadObject, 'baseObject.type')
      switch (baseType) {
        case 'TODO':
          componentName = 'todoObject'
          break
        case 'COMMENT':
          componentName = 'commentObject'
          break
        case 'MEET':
          componentName = 'meetObject'
          break
        case 'PAGE':
          componentName = 'pageObject'
          break
        case 'FILE':
          componentName = 'fileObject'
          break
        case 'SIGNATURE':
          componentName = 'signObject'
          break
        //TODO: can't get into this case???
        case 'POSITION':
          componentName = 'positionObject'
          break
        case 'TRANSACTION':
          if (transactionType === MxConsts.TransactionType.TRANSACTION_TYPE_DOCUSIGN) {
            componentName = 'DocuSignObject'
          } else {
            componentName = transactionActionComponent(transactionType, subType)
          }
          // componentName = 'transactionObject'
          break
        case 'EMAIL':
          componentName = 'pageObject'
          break
        case 'SCHEDULE_FLOW':
          componentName = 'scheduleFlowObject'
          break
      }
      //Use to remember the last rendered component, otherwise the change is too fast, the async operation in the child component is not completed,
      //the child component is destroyed, and the '$t' cannot be found (as: "delete action" in group workspace)
      this.preBaseObjectComponent = componentName
      return componentName
    },
    closeThread: function () {
      if (this.breadCrumbsData.length >= 2) {
        this.backToMainStep()
        return
      }
      this.clearThread(this.ctrlKey)
      this.$emit('close')

      if (this.isTodo) {
        this.$router.replace({
          ...this.$route,
          query: {}
        })
      }
      if (this.triggerByKeyboard) {
        this.focusToTrigger()
      }
      // //TODO: binderObj should be replaced with boardBriefViewModel!!!
      // if (!this.binderObj.isWorkflow) {
      //   this.popDeletedWarning()
      // }
    },
    getActivityType: function (action, baseType) {
      let componentName = ''
      if (baseType === 'WORKFLOW') {
        return 'commentActivity'
      }
      switch (action) {
        case 'REPLY':
        case 'REASSIGN':
        case 'RESEND_REMINDER':
        case 'ATTACHMENT':
        case 'STEP_SUBMIT':
        case 'STEP_REOPEN':
        case 'FILE_REPLY':
        case 'EXPIRATION_UPDATE':
        case 'EXPIRATION_DATE_ARRIVE':
        case 'UPDATE_CUSTOM_RESULT':
        case 'UPDATE_REOPEN':
        case 'UPDATE_EDITING':
        case 'UPDATE_READY':
        case 'STEP_READY':
        case 'MARK_AS_COMPLETED':
          componentName = 'commentActivity'
          break
        default:
          componentName = 'commonActivity'
          break
      }
      if ((action === 'UPDATE' && baseType === 'TRANSACTION') || (baseType === 'SIGNATURE' && (action !== 'DELETE_FILE_REPLY' && action !== 'REPLY_DELETE'))) {
        componentName = 'commentActivity'
      }
      if (action === 'REOPEN' && (baseType === 'TRANSACTION' || baseType === 'SIGNATURE')) {
        componentName = 'commentActivity'
      }
      let TodoActions = ['COMPLETE', 'REOPEN', 'ASSIGN', 'DUE_DATE', 'UPDATE', 'DUE_DATE_ARRIVE'] //REPLY ATTACHMENT ,'UPDATE'
      if (TodoActions.includes(action) && baseType === 'TODO') {
        componentName = 'commentActivity'
      }
      if (action === 'CREATE' && baseType === 'TRANSACTION') {
        componentName = 'commentActivity'
      }
      return componentName
    },
    sendMessage: function ($event) {
      let vm = this
      let content = $event.target.value || this.message
      content = StringUtils.trim(content)

      //TODO: binderObj should be replaced with boardBriefViewModel!!!
      let allUserForBoard = this.binderObj.boardUsers || []
      this.$refs.thread_status_alert.initByComment(allUserForBoard, content, this.binderObj)

      if (content.length > 0) {
        let uuid = FunctionUtil.uuid()
        let fakeReplyComment = this._fakeReplyComment(content, uuid)
        if (this.activities) {
          this.activities.push(fakeReplyComment)
        } else {
          this.activities = [fakeReplyComment]
        }
        this.$nextTick(() => {
          const scrollWrapper = this.$refs['scrollContainer']
          scrollWrapper.scrollTop = scrollWrapper?.scrollHeight
        })

        this.createThreadComment({ctrlKey: this.ctrlKey, payload: fakeReplyComment}).catch(err => {
          fakeReplyComment.isFailed = true
          fakeReplyComment.sequence = Date.now()
          if (err.detailCode === MxConsts.CustomDetailCode.ERROR_INTERCEPTOR_REJECTED) {
            const idx = this.activities.findIndex(act => act === fakeReplyComment)
            this.activities.splice(idx, 1)
          }
        })
        this.message = ''
        // this.saveDraft()
      }
    },
    getNextFocusElement () {
      let focusEl = document.getElementById('todo_' + this.baseObject.sequence)
      if (focusEl) {
        focusEl = focusEl.nextElementSibling || focusEl.previousElementSibling
      }
      if (!focusEl) {
        focusEl = document.body.querySelector('.binder-view')
      }
      return focusEl
    },

    _fakeReplyComment (content, uuid) {
      let currentUser = this.currentUser
      let currentTime = Date.now()

      const _relatedObj_ = {
        'client_uuid': uuid,
        'user': {
          'id': currentUser.id,
          'first_name': currentUser.name
        },
        'creator': {
          'user': {
            'id': currentUser.id,
            'first_name': currentUser.name
          }
        },
        'text': content,
        'created_time': currentTime,
        'updated_time': currentTime
      }

      let originalText = util.getRichText(content)
      if (originalText.is_rich_text) {
        _relatedObj_.text = originalText.text
        _relatedObj_.rich_text = originalText.rich_text
        _relatedObj_.rich_text_format = 'TEXT_FORMAT_BBCODE'
      }
      return {
        'Action': 'REPLY',
        'actor': {
          'avatar': currentUser.avatar,
          'id': currentUser.id,
          'email': currentUser.email,
          'name': currentUser.name,
          'isMySelf': true
        },
        'type': 'FEED_BOARD_COMMENT',
        'timestamp': currentTime,
        'created_time': currentTime,
        'updated_time': currentTime,
        'isPending': true,
        'sequence': currentTime,
        'baseObject': {
          'references': []
        },
        'relatedObject': _relatedObj_,
        'RelatedType': 'COMMENT'
      }
    },
    playRecording (recording) {
      this.recording = recording
      this.showRecordingPlayer = true
    },
    setBaseObject (baseObject, config, vm) {
      let result = baseObject
      let assignees = config.assignees
      this.objectConfig.disableActionBtn = false
      if (assignees) {
        result = {
          ...baseObject,
          // ...this.config.baseObject
          ...config.baseObject
        }
        if (baseObject.type === 'TODO') {
          result.assignee = assignees[0]
          this.objectConfig.disableDueDate = true
          if (config.dueDateText === i18n.t('no_due_date')) {
            result.dueDate = 0
          }
        } else if (baseObject.type === 'SIGNATURE') {
          let hasMySelf = false
          for (let index in baseObject.signees) {
            let signee = baseObject.signees[index]
            let assignee = assignees[index]
            if (assignee) {
              if (!hasMySelf) {
                hasMySelf = assignee.id === this.currentUser.id || assignee.isMyTeam
              }
              result.signees[index] = {
                ...signee,
                name: assignee.name,
                avatar: assignee.avatar,
                isCancel: config.isCanceled,
                displayDeletedUser: assignee.displayDeletedUser,
                id: assignee.id,
                isDeactivated: assignee.disabled,
                isUserDeleted: assignee.isUserDeleted || assignee.isDeleted,
                isInternalUser: assignee.isInternalUser,
                isAssigneeRole: assignee.isAssigneeRole,
                isClientTeam: assignee.isClientTeam,
                isTeam: assignee.isTeam,
                type: assignee.type,
                isMyTeam: assignee.isMyTeam,
                memberCounts: assignee.totalMembers
              }
            }
          }
          if (config.isNotStarted) {
            if (baseObject.signByOrder === false) {
              this.objectConfig.disableActionBtn = hasMySelf
            } else {
              this.objectConfig.disableActionBtn = assignees[0]?.id === this.currentUser.id
            }
          }
        } else {
          let steps = result.steps
          if (steps) {
            for (let index in steps) {
              let step = steps[index]
              let assignee = assignees[index]
              if (assignee) {
                step.assignee = assignee
              }
            }
            if (config.isNotStarted) {
              result.isMock = true
              result.currentStep = {
                actions: steps[0].actions,
                isDocuSignCCStep: steps[0].isDocuSignCCStep
              }
              const multipleAssigneeTypes = [
                MxConsts.TransactionType.TRANSACTION_TYPE_APPROVAL,
                MxConsts.TransactionType.TRANSACTION_TYPE_ACKNOWLEDGE,
                MxConsts.TransactionType.TRANSACTION_TYPE_TODO
              ]

              // if check decline option, first step is not first assignee.
              const flag = Object.keys(assignees[0] || {}).length > 0
              let firstId = flag ? assignees[0].id : assignees[1].id
              if (flag && assignees[0]?.isReviewerStep) {
                firstId = assignees[1].id
              }
              result.isMyTurn = firstId === this.currentUser.id

              if (steps.length > 1 && baseObject.transactionType === 'TRANSACTION_TYPE_DOCUSIGN' || multipleAssigneeTypes.includes(baseObject.transactionType)) {
                const isParallelDocusign = steps[0].orderNumber === steps[steps.length - 1].orderNumber
                if (isParallelDocusign) {
                  const myAssigneeInfo = assignees.find(assignee => assignee.id === this.currentUser.id)
                  if (myAssigneeInfo) {
                    result.isMyTurn = true
                    if (baseObject.transactionType === 'TRANSACTION_TYPE_DOCUSIGN') {
                      result.currentStep.isDocuSignCCStep = myAssigneeInfo.isDocuSignCCStep
                    }
                  }
                }
              }
            }
          }
        }
      } else {
        // MV-17305 For the started action, the configuration did not provide assignees. 
        // For subscription updates will not detect reassignments in started action for the workflow and will not provide the latest substep-assignee mapping.
        
        // For assignees
        const steps = result.steps
        const matchingRole = (roleId, roleList) => {
          if(roleId && roleList && roleList.length){
            return roleList.find(binderRole=>{
              return roleId === binderRole.name
            })
          }
        }
        if (steps) {
          steps.forEach(step =>{
            if(step && step.assignee && step.assignee.id && step.assignee.id?.startsWith('${')){
              const roleId = step.assignee.id.replace(/[${}]/g, '')
              const aimedRole = matchingRole(roleId, this.availableRoles)
              if(aimedRole){
                const assigneeRole = {
                    ...step.assignee,
                    id: `\$\{${aimedRole.name}\}`,
                    isAssigneeRole: true,
                    name: aimedRole.displayName,
                    avatar: aimedRole.avatar
                  }
                step.assignee = assigneeRole
              }
            }
          })
        }
        // For meeting
        if(result.custom_data?.meetRequest){
          const matchingStep = (ObjectUtils.getByPath(this.boardBriefViewModel, 'workflow.steps') || []).find(step=> step?.baseObjectSequence === result.sequence && step?.meetRequestOption)
          const isMatchingParticipant = (p, targetRoleId)=>{
            if(p && p.assigneeUser?.userId && (p.roleId || p.name) && (p.roleId || p.name)=== targetRoleId){
              return true
            }
            return false
          }
          if(matchingStep){
            // For time booking host
            const meetingHost = result.custom_data.meetRequest.host
            if(meetingHost && meetingHost.id?.startsWith('${')){
              if(isMatchingParticipant(matchingStep.meetRequestOption.host, meetingHost.id)){
                result.custom_data.meetRequest.host = {
                  ...result.custom_data?.meetRequest?.host,
                  id : matchingStep.meetRequestOption.host.assigneeUser.userId
                }
              }
            }
            const meetingParticipants = result.custom_data.meetRequest.participants
            // For time booking participants
            if(meetingParticipants?.length){
              for (let index = 0; index < meetingParticipants.length; index++){
                if(meetingParticipants[index] && meetingParticipants[index].id?.startsWith('${')){
                  const roleId = meetingParticipants[index].id.replace(/[${}]/g, '')
                  const matchingAssignee = (matchingStep.meetRequestOption.participants || []).find(p=> p && p.name === roleId && p.assigneeUser)?.assigneeUser
                  if(matchingAssignee && isMatchingParticipant(matchingAssignee, roleId)){
                    meetingParticipants[index]={
                      ...meetingParticipants[index],
                      id: matchingAssignee.userId
                    }
                  }
                }
              }
            }
          }
        }
        // For E-sign
        if(baseObject.type === 'SIGNATURE' && result.signees?.length){
          result.signees.forEach(s =>{
            if(s && s.id && s.id?.startsWith('${')){
              const roleId = s.id.replace(/[${}]/g, '')
              const aimedRole = matchingRole(roleId, this.availableRoles)
              if(aimedRole){
                if(s.actor?.user){
                  s.actor.user = {
                    ...s.actor?.user,
                    name: aimedRole.displayName,
                    first_name: aimedRole.displayName,
                    avatar: aimedRole.avatar
                  }
                }
                s.isAssigneeRole = true
                s.name = aimedRole.displayName
                s.first_name = aimedRole.displayName
                s.avatar = aimedRole.avatar
              }
            }
          })
        }
        // For old-todo
        if (baseObject.type === 'TODO' && result.assignee) {
          const assignee = result.assignee
          if(assignee && assignee.id && assignee.id?.startsWith('${')){
            const roleId = assignee.id.replace(/[${}]/g, '')
            const aimedRole = matchingRole(roleId, this.availableRoles)
            if(aimedRole){
              result.assignee = {
                ...assignee,
                isAssigneeRole : true,
                name : aimedRole.displayName,
                avatar : aimedRole.avatar
              }
            }
          }
        }
        if (baseObject.type === 'TODO') {
          this.objectConfig.disableDueDate = !this.isBinderOwner
        }
      }
      /// comment
      if (config?.custom_data) {
        result.custom_data = config.custom_data
      }

      // Manually put unresolved attachments to action detail view - start
      // In audit DB action detail view, this.flowStepModel is not available
      const stepModel = this.flowStepModel || this.currentStep 
      if (this.currentUser.isInternalUser && stepModel?.ddrAttachments?.length) {
        const realFileAttachments = (result.attachments || []).filter(f => !f.isDDRFile)
        const ddrAttachments = stepModel.ddrAttachments
        let attachments = realFileAttachments
        if (ddrAttachments?.length) {
          attachments = ddrAttachments.concat(attachments)
        }
        if (attachments.length) {
          result.attachments = attachments
        }
      }
      // Manually put unresolved attachments to action detail view - end

      return result
    },

    reassignAction (assignee, disabledAssignees, option) {
      //old todo
      let fromAssignee = assignee
      if (assignee.source) {
        // for transaction object
        fromAssignee = assignee.source
      } else if (assignee.assignee) {
        //for role case
        fromAssignee = assignee.assignee
      }
      let supportRole = false
      const reassignOption = {
        fromAssignee,
        baseObjectType: this.baseObject.transactionType || this.baseObject.type,
        baseObjectSequence: this.baseObject.sequence,
        stepSequence: assignee.sequence,
        actionSequence: 0,
        flowSequence: 0,
        isTransferActionEditor: option?.isReassignEditor,
        baseObjectType : this.baseObject.sub_type
      }
      const workflow = this.binderObj.workflows?.[0]
      if (workflow) {
        let currentStep = workflow.transformedSteps[0]
        if (workflow.transformedSteps.length > 1) {
          // in overview the transformedSteps will include all steps
          currentStep = workflow.transformedSteps.find(step => step.sequence === this.flowInfos.stepSequence)
        }
        if (currentStep) {
          if (!currentStep.outputObject && !currentStep.output) {
            reassignOption.flowSequence = workflow.sequence
            reassignOption.actionSequence = currentStep.sequence
            supportRole = true
          } else {
            supportRole = true
            reassignOption.baseObjectSequence = currentStep.outputObject?.sequence || currentStep.output?.sequence
          }
          reassignOption.actionType = currentStep.type
        }
      }
      let boardBriefViewModel = this.boardBriefViewModel

      if (!boardBriefViewModel.boardId) {
        // reassign need boardId
        boardBriefViewModel = toBoardBriefViewModel(this.binderObj)
      }

      // reviewer step reassign only assign internal user
      if (option?.isReviewerStepReassign) {
        boardBriefViewModel = {
          ...boardBriefViewModel,
          users: boardBriefViewModel.users?.filter(k => k.isInternalUser) || []
        }
      }

      doReassignAction({
          boardBriefViewModel,
          binderObj: this.binderObj,
          $t: this.$t,
          supportRole,
          excludeClientTeam: option?.excludeClientTeam,
          disabledAssignees
        }, reassignOption
      ).then(() => {
        this.$emit('objectIsChanged', true)
      })
    },

    signeeChanged () {
      this.$emit('objectIsChanged', true)
    },
    viewPage (att, optionsMenu) {
      switch (this.type) {
        case 'TRANSACTION':
          //MVB-36411: when reviewing acknowledgement attachments thru the dialog menu, should only show one file
          const ackReviewClass = `.review-ack-attach-dialog li.attachment-${att.sequence}`
          const isAcknowledgementReview = !!document.querySelector(ackReviewClass)

          const {baseInfo, menus} = transactionActionViewAttachment({
            attachment: att,
            baseObject: this.baseObject,
            config: this.config,
            flowInfos: this.flowInfos,
            isAcknowledgementReview
          }, optionsMenu)

          this.$emit('viewPage', baseInfo, menus)
          return
        case 'SIGNATURE':
          if (this.config.isNotStarted) {
            if (optionsMenu) {
              optionsMenu.flowStepInfo = {
                boardViewToken: this.threadObject.boardViewToken,
                isNotStarted: true
              }
            } else {
              optionsMenu = {
                flowStepInfo: {
                  boardViewToken: this.threadObject.boardViewToken,
                  isNotStarted: true
                }
              }
            }
            att.boardId = this.threadObject.binderId
          }
          this.$emit('viewPage', att, optionsMenu)
          return
        case 'TODO':
          if (this.config.isNotStarted) {
            if (optionsMenu) {
              optionsMenu.flowStepInfo = {
                boardViewToken: this.threadObject.boardViewToken,
                isNotStarted: true
              }
            } else {
              optionsMenu = {
                flowStepInfo: {
                  boardViewToken: this.threadObject.boardViewToken,
                  isNotStarted: true
                }
              }
            }
            att.boardId = this.threadObject.binderId
          }

          //need to convert this legacy info into a format that is compatible with getObjectAttachmentLists
          const { fullData: { files, todoSequence, ...remainder }, ...other } = att
          const convertedAtt = {
            fullData: {
              type: this.type,
              objectSequence: todoSequence,
              ...remainder
            },
            ...other
          }

          this.$emit('viewPage', convertedAtt, optionsMenu)
          return
        default:
          if (this.config.isNotStarted) {
            att.boardId = this.threadObject.binderId
          }
          this.$emit('viewPage', att, optionsMenu)
          return
      }
    },
    viewFileFolder (foldInfo) {
      this.$emit('viewFileFolder', foldInfo)
    },
    popupFlowFinishedWarning (isStepCancelled) {
      const modalStack = PopupManager.modalStack
      let needPopWarningModal = false
      if (modalStack.length) {
        if (modalStack.length === 1 && modalStack[0].modalClass === 'edit-part-opt') {
        } else {
          needPopWarningModal = true
        }
      }
      const isHavingModalOpened = hasComponents() || needPopWarningModal

      const string1 = this.$t('This_action_was_already_completed_or_deleted', {actionType: this.actionTypeString}),
        string3 = this.$t(this.isAddingStep ? 'unable_to_add' : 'Unable_to_save_changes'),
        string4 = this.$t('Dismiss')
      const allDialogs = document.body.querySelectorAll('.el-dialog-wrapper')
      if (!this.confirmFlowFinishDialog && isHavingModalOpened) {
        this.confirmFlowFinishDialog = this.$mxConfirm(string1, string3, {
          cancelButtonText: string4,
          showConfirmButton: false,
          cancelButtonType: 'gray',
          customClass: 'new-style'
        }).catch(() => {
          if (hasComponents()) {
            clearAll()
          }
          if (allDialogs.length) {
            Array.prototype.forEach.call(allDialogs, (el) => {
              el.parentNode.removeChild(el)
            })
            const modal = document.body.querySelectorAll('.v-modal')
            modal.parentNode.removeChild(modal)
          }
        })
      }
    },
    subscribeFlowStep () {
      let vm = this
      if (this.flowInfos && this.flowInfos.stepSequence) {
        //TODO: binderObj should be replaced with boardBriefViewModel!!!
        //TODO: subscribeFlowStep should go either Thread store. Can't use it in this way!!!
        vm.flowStepSubscriber = subscribeFlowStep({
          boardId: this.binderObj.id,
          stepSequence: this.flowInfos.stepSequence
        }, {
          onFlowFinished ({isCompleted, updatedStepViewModel}) {
            vm.handleRefreshViewStepDetail(updatedStepViewModel)
            if (!isCompleted) {
              vm.popupFlowFinishedWarning(false)
            }
          },
          onEdited: FunctionUtil.throttle(updatedStepViewModel => {
            if (updatedStepViewModel.sequence === vm.flowInfos.stepSequence) {
              vm.handleRefreshViewStepDetail(updatedStepViewModel)
            }
          }, 100),
          onCompleted: FunctionUtil.throttle(updatedStepViewModel => {
            if (updatedStepViewModel.sequence === vm.flowInfos.stepSequence) {
              vm.handleRefreshViewStepDetail(updatedStepViewModel)
              vm.takingAction && vm.setTakingAction(false)
            }
          }, 100),
          onCancelled (updatedStepViewModel) {
            if (updatedStepViewModel.sequence === vm.flowInfos.stepSequence) {
              vm.popupFlowFinishedWarning(true)
              vm.handleRefreshViewStepDetail(updatedStepViewModel)
            }
          },
          onDeleted: FunctionUtil.throttle(() => {
            if (vm.takingAction) {
              const msgBody = vm.$t('This_action_was_already_completed_or_deleted', {actionType: vm.actionTypeString})
              if (!vm.isBinderOwner && !vm.deleteStepWarning) {
                vm.deleteStepWarning = true
                vm.$mxConfirm(msgBody, vm.$t('Unable_to_save_changes'), {
                  customClass: 'new-style',
                  confirmButtonText: vm.$t('Dismiss'),
                  showCancelButton: false
                }).then(() => {
                  vm.closeThread()
                  vm.deleteStepWarning = false
                  vm.takingAction && vm._closeAllActions()
                })
              } else {
                vm.closeThread()
                vm._closeAllActions()
              }
            } else {
              vm.closeThread()
            }

          }, 100),
          onInprogress: () => {
            EventBus.$emit('ACTION_STATUS_CHANGE_TO_INPROGRESS', true)
          }
        })
      }
    },
    handleRefreshViewStepDetail (updatedStepViewModel) {
      if(this.config.isAb) {
        EventBus.$emit('VIEW_STEP_DETAIL_IN_AB', updatedStepViewModel)
      }else if(this.config.isDB){
        EventBus.$emit('VIEW_STEP_DETAIL_IN_DB', updatedStepViewModel)
      }else if(this.config.isCB){
        EventBus.$emit('VIEW_STEP_DETAIL_IN_CB', updatedStepViewModel)
      }
       else {
        EventBus.$emit('VIEW_STEP_DETAIL', updatedStepViewModel)
      }
    },
    _closeAllActions () {
      const inBinder = ['timeline', 'flows'].includes(this.$route.name)
      if (inBinder) {
        this.setTakingAction(false)
        const parentVm = this.$parent
        if (parentVm) {
          if (parentVm.togglePreviewPage) {
            parentVm.togglePreviewPage(false)
          }
          if (parentVm.openSignature) {
            parentVm.openSignature = false
          }
        }
      } else {
        //TODO: binderObj should be replaced with boardBriefViewModel!!!
        this.$router.replace({
          name: this.currentUser.isInternalUser ? 'timeline' : 'flows',
          params: {id: this.binderObj.id}
        })
      }
      this.flowCanceledConfirm = null
    },
    checkFlowCanceled (updatedFlow) {
      const canceledByOwner = updatedFlow.isCanceled && !updatedFlow.hasCanceledStep
      const popCanceledConfirm = canceledByOwner && this.takingAction && !this.isInPreview && !this.flowCanceledConfirm
      if (popCanceledConfirm) {
        this.flowCanceledConfirm = this.$mxConfirm(this.$t('Flow_conversation_canceled_desc'), this.$t('Flow_conversation_canceled'), {
          cancelButtonText: this.$t('dismiss'),
          showConfirmButton: false,
          cancelButtonType: 'gray',
          customClass: 'new-style'
        })
          .catch(() => {
            this._closeAllActions()
          })
      }
    },
    setDDRSupportFilterInfo () {
      const ddrSupportStore = useDDRSupportStore()
      ddrSupportStore.setDDRSourceFilter({
        contextId: {boardId: this.boardBriefViewModel.boardId},
        filterInfo: {curStepClientUuid: this.currentStep.clientUuid }
      })
      // manually trigger Step DDR variables validation
      ddrSupportStore.startDDRVariablesValidation()
    },


    viewDecisionBranch(payload){
      this.$emit('viewDecisionBranch',{
        branchParams: {
          ...payload, 
          dbStepSequence: this.currentStep.sequence
        },
        currentPageProps: this.$props,
        currentPageEvents: this.$listeners
      })
    },
    viewBranchAction(payload){
      this.$emit('viewBranchAction',{
        ...payload,
        branchParams: {
          ...payload.branchParams, 
          dbStepSequence: this.currentStep.sequence
        },
        currentPageProps: this.$props,
        currentPageEvents: this.$listeners
      })
    },
    eventFocusToComment (border) {
      focusTo(getFirstFocusableElement(this.$el.querySelector('.activity-container')), {border})
    },
    eventLoadingEnd () {
      this.$nextTick().then(() => {
        this.getAutoResizeRows()
      })
    },
    handleStartSignature(signOptions) {
      const enhancedOptions = {
        ...signOptions,
        flowInfos: {
          ...this.flowInfos,  
          ...signOptions.flowInfos
        },
        binderObj: this.binderObj,
        stepSequence: this.flowInfos.stepSequence,
        flowSequence: this.flowInfos.flowSequence
      }
      
      this.$emit('startSignature', enhancedOptions)
    }
  }
}
</script>

<style lang="scss">
$border-radius-large: 6px !default;

.mx-branding-focus-outline {
  ::v-deep .is-keyboard-focus {
    outline-style: solid !important;
    outline-width: 2px !important;
    outline-offset: -2px;
  }
}

.viewer-view {
  &.flow-panel {
    .todo-body,
    .flow-baseobject-todo,
    .activity-container {
      .mx-hover-hide,
      .micon-close-d3 {
        display: none !important;
      }
    }

    .flow-todo-assignee-avatar {
      display: inline-block;
    }

    [data-action]:not(.micon-bookmark-badge) {
      cursor: initial;
    }

    [data-action='dismiss'] {
      cursor: pointer !important;
    }

    [data-action='moveTodo'],
    [data-action='removeTodo'],
    [data-action='moveFlow'],
    [data-action='removeFlow'] {
      display: none !important;
    }

    .flow-process {
      .mx-hover-hide {
        display: none !important;
      }
    }

    .flow-bottom {
      display: none;
    }

    .hideForViewer {
      display: none;
    }

    /*        .flow-bottom {
                display: none;
            }
            .flow-message {
                padding-bottom: 0;
            }*/
    .left-area {
      visibility: hidden;
    }
  }
}

.audit-view {
  &.flow-panel {
    .flow-option-menu {
      display: none !important;
    }

    .flow-baseobject-page .dropdown {
      display: none !important;
    }

    .flow-bottom {
      display: none !important;
    }

    [data-action='moveTodo'],
    [data-action='removeTodo'],
    [data-action='moveFlow'],
    [data-action='removeFlow'],
    [data-action='removeAttachment'] {
      display: none !important;
    }
  }
}

.flow-panel {
  overflow: hidden;
  position: relative;

  &.disable-activity {
    .flow-body {
      flex: 1 0 auto;
      height: 100%;
    }
  }

  .comment-content {
    li {
      list-style: inherit;
    }

    overflow-x: auto;
    max-width: 100%;
    // display: inline-block;
    vertical-align: bottom;
  }

  .mx-audio-control {
    cursor: pointer;
    text-align: center;

    i {
      border-radius: 50%;

      &.micon-play {
        color: $mx-color-var-bg-primary;
      }

      &.micon-stop {
        background: $mx-color-var-bg-primary;
      }
    }

    .paused {
      display: inline;
    }

    .playing {
      display: none;
    }

    &.playing {
      .paused {
        display: none;
      }

      .playing {
        display: inline;
      }
    }
  }

  .mx-audio-progress {
    $barColor: $mx-color-var-fill-primary;
    padding-top: 10px;

    .audio-progress-bar {
      width: 100%;
      height: 10px;
      border-radius: $border-radius-large;
      border: 1px solid $barColor;

      > div {
        height: 100%;
        background-color: $mx-color-var-label-secondary;
        border-radius: $border-radius-large;

        .transition-default {
          -webkit-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out;
        }
      }
    }
  }

  .mx-audio-time {
    padding-top: 5px;
  }

  .task-container {
    margin-left: 40px;

    + a {
      margin-left: 28px;
      font-size: 12px;
    }
  }

  .add-subtask-list,
  .task-container {
    margin-top: $mx-spacing-sm;

    li {
      padding-bottom: $mx-spacing-sm;
      display: flex;

      > span:first-child {
        margin-right: $mx-spacing-xs;
        flex: 0 0 16px;
        color: $mx-icon-color-secondary;
      }

      .todo-square {
        font-size: 16px;
        width: 16px;
        border: 2px solid $mx-color-var-branding;
        border-radius: 3px;
        height: 16px;
      }

      .mx-hover-hide {
        i {
          font-size: 16px;
        }
      }

      > div {
        flex: 1 1 auto;
        line-height: 16px;
      }

      textarea {
        font-size: 12px;
        border: none;
        outline: none;
        width: 100%;
        display: none;
        resize: none;
        line-height: 20px;
        overflow: auto;
        border-bottom: 1px solid $mx-color-border-secondary;
      }

      .task-name {
        display: block;
      }

      &.editing,
      &.adding {
        .task-name {
          display: none;
        }

        textarea {
          display: block;
        }

        > span:last-child {
          display: none;
        }

        .create-tips {
          display: inline-block;
          color: $mx-color-warning-dark;
          font-size: 12px;
        }
      }

      .create-tips {
        display: none;
      }

      .micon-tick {
        display: none;
      }

      &.task-completed {
        > div {
          text-decoration: line-through;
        }

        > span {
        }

        .micon-tick {
          display: inherit;
          color: white;
          background: $mx-color-blue;
          margin-right: 8px;
          border-radius: 3px;
          height: 16px;
          font-size: 10px;
          padding: 3px;
          width: 16px;
        }

        .todo-square {
          display: none;
        }
      }
    }
  }
}

// Apply three places: position comments, flow detail, tracking list.
.activity-container {
  padding: $mx-spacing-xxs 0 $mx-spacing-xs;

  ul.activities > li {
    padding-bottom: $mx-spacing-xs;
  }
}

.nest-branch {
  display: none !important;
}

.flow-detail {
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  > header,
  .header {
    flex: 0 0 auto;
    border-bottom: 1px solid $mx-color-var-fill-tertiary;
    display: flex;
    align-items: center;
    height: 52px;
    padding-left: 20px;
    padding-right: 16px;

    > :first-child:not(.back-icon) {
      flex: 1 1 auto !important;
    }
    > .back-icon .micon-left-arrow-new {
      color: $mx-color-var-label-secondary;
    }

    .left-area {
      > span {
        margin-right: $mx-spacing-sm;
      }

      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      max-width: calc(100% - 74px);

      .time {
        color: $mx-color-var-label-secondary;
        margin-top: -2px;
      }
    }

    .right-area {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-end;
      // margin-left: auto;

      .right-close-button,
      .el-dropdown-link {
        color: $mx-color-var-label-secondary;
        display: inline-block;
        width: 28px;
        height: 28px;
        text-align: center;
        border-radius: 6px;

        i {
          line-height: 28px;
        }
      }

      .right-close-button {
        background-color: $mx-color-var-fill-quaternary;
        margin-left: 12px;

        .close-button {
          font-size: 16px;
          color: $mx-color-var-label-secondary;
        }
      }

      .right-close-button-non-scope {
        padding: 12px;
        color: $mx-color-var-label-secondary;
        display: inline-block;
      }

      .el-dropdown-link {
        &:hover {
          background-color: $mx-color-var-fill-quaternary;
        }

        &:active {
          background: $mx-color-var-fill-secondary;
        }

        &[aria-expanded='true'] {
          background-color: $mx-hover-selected;
        }
      }
      .el-dropdown-link.without-hover {
        color: $mx-color-var-text-quaternary;
        cursor: not-allowed;
        &:hover {
          background-color: transparent;
        }
      }

      .dropdown {
        padding-left: 0;
      }

      .close {
        background: $mx-color-bg-selected;
        color: $mx-icon-color-secondary;
      }

      .more-info-dropdown {
        align-self: center;
      }
    }
  }

  .header {
    color: $mx-color-var-text-secondary;
  }

  .flow-baseobject-container {
    &.todo-completed {
      .flow-todo-name {
        text-decoration: line-through;
        word-break: break-word;
      }

      > div > span {
        background-color: $mx-color-gray-40;
        border-color: $mx-color-gray-40;
        opacity: 0.5;
      }

      .flow-todo-actions {
        .mx-hover-hide,
        .micon-close-d3 {
          display: none !important;
        }
      }

      .flow-todo-assignee-avatar {
        display: inline-block;
      }

      .flow-todo-actions [data-action] {
        cursor: initial;
      }
    }
  }

  .flow-detail-container {
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;

    .micon-close-d3 {
      color: $mx-color-white;
      background-color: $mx-color-var-label-secondary;
      border-radius: 50%;
    }
  }

  .flow-body {
    flex: 0 0 auto;
    padding-bottom: 16px;

    .flow-baseobject-header {
      .mx-hover-hide {
        flex: 1 1 auto;
        text-align: right;

        * {
          text-align: left;
        }
      }
    }

    .alert-info-container {
      margin: 16px 16px 0;
      display: flex;
      padding: 8px;
      align-items: center;
      border-radius: 6px;
      border: 1px solid $mx-color-var-caution;
      background-color: rgba($mx-color-var-caution, 0.1);

      i {
        font-size: 16px;
        color: $mx-color-var-caution;
      }
    }

    .reply-binder-info {
      padding-top: 8px;
      display: inline-block;
      color: $mx-color-gray-40;

      a {
        font-weight: $mx-font-weight-bold;
        color: $mx-color-gray-60;
        font-size: 11px;
      }

      .external-flag {
        margin-left: 4px;
        margin-bottom: -3px;
      }
    }

    .loading-failed-wrap {
      padding: 22px 0;
    }
  }

  .todo-toggle-panel {
    &.active {
      color: $mx-color-gray-90;
    }

    display: flex;
    padding: 12px 24px;
    border-bottom: 1px solid $mx-color-border-primary;
    flex: 0 0 auto;
    color: $mx-color-gray-40;

    > span {
      flex: 1 1 auto;
    }

    > div {
      flex: 0 0 auto;
    }
  }

  .todo-body {
    > div {
      padding: $mx-spacing-xs 20px $mx-spacing-xs 24px;
      display: flex;
      flex: 0 0 auto;

      > i {
        margin-right: 16px;
        color: $mx-color-gray-20;
      }

      .flow-todo-assign-box {
        background-color: white;
      }

      &:hover {
        background-color: rgba($mx-color-gray-04, 0.8);

        .flow-todo-assign-box {
          background-color: rgba($mx-color-gray-04, 0) !important;
        }
      }

      &.selecting {
        background: none;

        > i:first-child {
          color: $mx-color-blue;
        }

        > .flow-todo-assignee-avatar > i {
          color: $mx-color-blue;
        }
      }
    }

    .flow-todo-creator {
      padding: 0;
      margin-left: 60px;
      font-size: 12px;
      color: $mx-color-font-primary;

      .mx-user-name {
        font-weight: 600;
        padding-right: $mx-spacing-xs;
      }

      .mx-created-time {
        color: $mx-color-font-secondary;
      }
    }

    .flow-todo-desc {
      overflow: hidden;

      > span {
        flex: 1 1 auto;
        word-break: break-word;
      }

      span:after {
        content: ' ';
        display: inline-block;
      }

      + .inline-editor {
        flex: 1 1 auto;

        form {
          textarea {
            font-size: 12px;
            border: none;
            outline: none;
            width: 100%;
            resize: none;
            border-radius: 0;
            line-height: 20px;
            box-shadow: none;
            overflow: auto;
            border-bottom: 1px solid $mx-color-blue;
          }

          width: 100%;
        }
      }
    }

    .flow-todo-priority,
    .flow-todo-reminder,
    .flow-todo-name,
    .flow-todo-desc {
      &.active {
        color: $mx-color-font-primary;

        > i:first-child {
          color: $mx-icon-color-secondary;
        }
      }
    }

    .flow-duedate {
      span {
        flex: 1 1 auto;
        color: $mx-color-gray-40;
        overflow: hidden;
      }

      .mx-hover-hide {
        visibility: hidden !important;
      }

      &.active {
        > i:first-child {
          color: $mx-icon-color-secondary;
        }

        > span {
          color: $mx-color-font-primary;
          font-weight: 500;
        }

        &:hover {
          .mx-hover-hide {
            visibility: visible !important;
          }
        }
      }

      &.past-time {
        span {
          color: $mx-color-var-negative !important;
        }
      }

      .dropdown-menu {
        left: 56px;
      }
    }

    .flow-todo-name {
      > i {
        flex: 0 0 auto;
      }

      .todo-square {
        font-size: 20px;
        border: 2px solid $mx-color-var-branding;
        border-radius: 3px;
        width: 20px;
        height: 20px;
      }

      .micon-tick {
        display: none;
      }

      .flow-title,
      .inline-editor {
        flex: 1 1 auto;
        overflow: hidden;
      }

      textarea {
        font-size: 12px;
        border: none;
        outline: none;
        width: 100%;
        resize: none;
        border-radius: 0;
        line-height: 20px;
        box-shadow: none;
        overflow: auto;
        border-bottom: 1px solid $mx-color-var-branding;

        &:hover {
          background: $mx-color-var-fill-quaternary;
        }
      }

      .error-message {
        display: none;
      }

      .has-error textarea {
        border-color: $mx-color-var-negative;
      }

      .has-error + .error-message {
        display: inline-block;
        color: $mx-color-var-negative;
      }
    }

    .flow-process {
      .panel-heading {
        padding: 0;
      }

      .flow-process-title {
        color: $mx-color-var-label-secondary;
        display: flex;

        .icon-transform {
          transform: rotate(180deg);
        }

        .panel-heading.collapsed .icon-transform {
          transform: rotate(0);
        }

        .inline-editor {
          flex: 1 1 100%;
        }

        textarea {
          outline: none;
          border: none;
          border-bottom: 1px solid $mx-color-var-branding;
          padding-bottom: $mx-spacing-xs;
          background: none;
          box-shadow: none;
          border-radius: 0;
        }

        .checklist-title {
          flex: 1 1 auto;
          margin-left: 16px;
          overflow: hidden;
          display: flex;

          span.checklist-name {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: calc(100% - 25px);
            margin-right: $mx-spacing-sm;
            line-height: 20px;
            height: 20px;
            white-space: nowrap;
            flex: 0 1 auto;
          }

          span.task-stat {
            line-height: 20px;
            height: 20px;
            font-size: 12px;
            flex: 1 0 auto;
            color: $mx-color-var-label-secondary;
          }

          .micon-close-d3 {
            flex: 0 0 auto;
            //transform:scale(0.7);
            font-size: $mx-font-size-base;
            margin: auto 0;
          }
        }
      }

      .panel-collapse {
        color: $mx-color-var-text-primary;

        a.btn-link {
          i.micon-plus {
            margin-right: 3px;
            font-size: $mx-font-size-sm;
          }
        }
      }

      > div {
        position: relative;
        width: 100%;
      }
    }

    .flow-attachments {
      &:hover {
        background-color: inherit;
      }

      color: $mx-color-gray-40;
      border-top: solid 1px $mx-color-gray-08;

      > div {
        width: 100%;
      }

      padding: 8px 20px 0 24px;
      overflow: hidden;

      .flow-attachment-header {
        display: flex;
        overflow: hidden;
        margin-top: 8px;

        > i {
          margin-right: 16px;
          flex: 0 0 auto;
        }

        .flow-attachment-list {
          flex: 1 1 auto;
          overflow: hidden;
        }
      }

      ul {
        flex: 1 1 auto;
        overflow: hidden;

        li {
          display: flex;
          align-items: center;
          line-height: 22px;
          height: 22px;
          overflow: hidden;
          margin-bottom: 4px;

          .file-name {
            flex: 1 1 auto;
            color: $mx-color-blue;
            overflow: hidden;
          }

          > span {
            flex: 0 0 auto;
          }
        }
      }

      &.no-attachment {
        display: none;
      }
    }

    .flow-todo-priority {
      .priority-inactive {
        display: inherit;
        color: $mx-color-gray-40;
      }

      .priority-active {
        display: none;
      }

      &.active {
        i {
          color: #fe9d67 !important;
          background-color: inherit;
        }

        .priority-inactive {
          display: none;
        }

        .priority-active {
          display: inherit;
        }
      }
    }

    .flow-todo-assignee {
      &:hover {
        .flow-todo-assign-box:not(:focus) ~ .micon-close-d3 {
          display: inline-block;
        }
      }

      &:not(.active) {
        .micon-close-d3 {
          display: none !important;
        }
      }

      .micon-close-d3 {
        display: none;
      }

      > span {
        position: relative;
        flex: 1 1 auto;
        display: flex;
      }

      input {
        border: none;
        outline: none;
        flex: 1 1 85%;
      }

      .dropdown-menu {
        .list-group-item {
          border: 0;
          padding: 0;
        }
      }
    }

    .flow-todo-reminder {
      .micon-close-d3 {
        display: none;
      }

      &.active {
        .micon-close-d3 {
          display: inline-block;
          border: none;
        }

        .reminder-info {
          flex: 1 1 auto;
        }
      }

      > span {
        display: flex;
        align-items: center;
        flex: 1 0 auto;

        input {
          display: block;
          float: left;
          border: none;

          &.reminders-date {
            min-width: 70px;
          }

          &.reminders-time {
            min-width: 50px;
          }
        }
      }
    }

    .flow-todo-assignee-avatar {
      i {
        color: $mx-color-gray-20;
      }

      .mx-thumbnail-container {
        display: block;
      }

      margin-right: 16px;
    }

    .addChecklistContainer {
      height: 100%;
      background-color: rgba(255, 255, 255, 0.7);
      width: 100%;
      position: absolute;
      left: 0;
      z-index: 1;
      color: initial;

      .checklist-template.has-value {
        display: none;

        & + .checklist-template-container {
          display: none;
        }
      }

      .checklist-template {
        display: inline-block;
        position: absolute;
        right: 16px;
        font-weight: 600;
        top: 40px;
      }

      .checklist-template-container {
        box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12), 0 8px 8px 0 rgba(0, 0, 0, 0.24);
        border-radius: 3px;
        position: absolute;
        right: 16px;
        left: 16px;
        background: white;

        > ul {
          li {
            div:first-child {
              &:hover {
                background-color: $mx-color-gray-04;
              }

              display: flex;

              > span {
                flex: 1 1 auto;
                align-self: center;
                padding-left: 24px;
              }

              > div {
                flex: 0 0 auto;
                padding: $mx-spacing-xs $mx-spacing-sm;

                .icon-transform {
                  transform: rotate(180deg);
                }

                &.panel-heading.collapsed .icon-transform {
                  transform: rotate(0);
                }
              }
            }

            .panel-collapse {
              padding-bottom: 16px;

              li {
                display: flex;
                padding: $mx-spacing-xs 36px 0;
                align-items: flex-start;

                > span:first-child {
                  font-size: $mx-font-icon-sm;
                  width: 16px;
                  border: 2px solid $mx-color-blue;
                  border-radius: 3px;
                  height: 16px;
                  color: $mx-color-border-secondary;
                }

                .task-name {
                  padding-left: $mx-spacing-xs;
                }
              }
            }
          }
        }
      }

      > div {
        background-color: white;
        border-bottom: 1px;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);

        > * {
          padding: $mx-spacing-sm 16px;
        }

        header {
          display: flex;
          align-items: center;
          line-height: 28px;

          .micon-subtask {
            font-size: 20px;
            color: white;
            padding: 4px;
            background: #669ce4;
            margin-right: 16px;
          }

          > span:first-child {
            flex: 1 1 auto;
          }

          height: 52px;
          border-bottom: 1px solid $mx-color-border-primary;
        }

        textarea {
          border-right: 0;
          border: none;
          resize: none;
          width: 100%;
          overflow: auto;
          line-height: 20px;
          max-height: 60px;
          outline: none;
          border-bottom: 1px solid $mx-color-border-secondary;
        }

        .create-tips {
          display: none !important;
        }

        footer {
          height: 48px;
          text-align: right;

          & > span[data-action='cancelCreateChecklist'] {
            margin-right: $mx-spacing-sm;
          }
        }
      }

      .btn.btn-link {
        padding: 7px 0;

        i.micon-plus {
          font-size: $mx-font-size-sm;
          margin-right: 3px;
        }

        &[data-action='cancelCreateChecklist'] {
          color: $mx-color-gray-60;
        }
      }

      .add-subtask-list li {
        input:focus {
          border-color: $mx-color-branding;
        }
      }
    }

    border-bottom: 1px solid $mx-color-gray-08;
    color: $mx-color-gray-40;
    padding: 12px 0;

    &.todo-completed {
      [data-action] {
        cursor: inherit;
      }

      .flow-title {
        text-decoration: line-through;
      }

      .flow-todo-name {
        .todo-square {
          display: none;
        }

        .micon-tick {
          display: inherit;
          color: white;
          background: $mx-color-blue;
          border-radius: 3px;
          font-size: 14px;
          padding: 3px;
          position: relative;
          text-decoration: none;
          cursor: pointer;
          height: 20px;
        }
      }

      .mx-hover-hide,
      .micon-close-d3 {
        display: none !important;
      }
    }
  }

  div:first-child .flow-baseobject {
    padding: 0;
  }

  header + .flow-detail-container {
    .flow-baseobject {
      padding: 16px 20px;
      padding-left: 24px;
    }
  }

  .flow-baseobject {
    .baseobject-info {
      display: flex;
      font-size: 12px;
      align-items: baseline;
      height: 18px;
      overflow: hidden;

      .mx-create-user {
        font-weight: $mx-font-weight-bold;
        padding-right: $mx-spacing-xs;
        flex: 0 0 auto;
        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .mx-created-time {
        flex: 1 1 auto;
        line-height: 18px;
        height: 18px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }

  .flow-message {
    background: linear-gradient(-180deg, $mx-color-var-fill-quaternary 0%, rgb(255, 255, 255) 100%);
    padding: 20px 20px 0;
    flex: 1 0 auto;
    display: flex;
    margin-left: 1px;
    flex-direction: column;

    .activity-container {
      flex: 1 1 auto;
    }
  }

  .flow-bottom {
    padding-bottom: $mx-spacing-sm;
    display: flex;
    background: inherit;
    margin: 0 20px;
    flex: 0 0 auto;

    // a.add-page {
    //   padding: 6px;
    //   flex: 0 0 auto;
    //   color: $mx-color-gray-40;
    // }

    .el-dropdown.mx-thread-upload-dropdown {
      flex: 0 0 auto;
      padding: 6px;
      color: $mx-color-var-label-secondary;
    }

    textarea {
      padding: 0 40px 0 $mx-spacing-sm;

      &:-ms-input-placeholder,
      &::-webkit-input-placeholder,
      &::-moz-placeholder {
        overflow: hidden;
        text-overflow: ellipsis;
        height: 18px;
      }

      border: none;
      resize: none;
      overflow: auto;
      line-height: 20px;
      width: 100%;
      vertical-align: top;
      border-radius: 0;
      @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        min-height: 36px;
      }

      &:focus {
        outline: none;
      }
    }

    .mx-bar-right {
      position: absolute;
      right: 10px;
      top: 1.5px;
      color: $mx-color-var-label-secondary;
    }

    > div {
      border: 2px solid $mx-color-var-fill-tertiary;
      border-radius: 3px;
      position: relative;
    }

    > div:first-child {
      border-right: 0;
      border-radius: 3px 0 0 3px;
      background: white;
    }

    > div:last-child {
      flex: 1 1 auto;
      border-radius: 0 3px 3px 0;
      border-left: 2px solid $mx-color-var-fill-tertiary;
      border-right: 2px solid $mx-color-var-fill-tertiary; //override rule when only one item display. such as no add attachment case.
      padding: 8px 0;
    }
  }

  .notify-all {
    margin-top: $mx-spacing-sm;

    > span:last-child {
      font-size: 12px;
      color: $mx-color-gray-40;
    }
  }

  &.todo-completed {
    .flow-process {
      .mx-hover-hide {
        display: none !important;
      }
    }

    .right-area {
      .dropdown {
        display: none !important;
      }
    }
  }
}

.flow-action-detail-spec {
  > header {
    > div {
      .close {
        flex: 1 1 auto;
        text-align: right;
      }
    }
  }

  .flow-bottom {
    .dropup {
      display: none;
    }
  }
}

.mx-position-comment-panel {
  //position:absolute;
  z-index: 1000;
  text-align: center;
  background: white;
  padding: 16px 24px;
  //box-shadow: 0 0 8px 0 rgba(0,0,0,0.12), 0 8px 8px 0 rgba(0,0,0,0.24);
  border-radius: 5px;
  max-width: 348px;
  min-width: 320px;

  .dropdown-menu,
  .flow-activity-common-container {
    text-align: left;
  }

  .activity-container {
    max-height: 126px;
    overflow-y: auto;
  }

  .mx-position-comment-header {
    margin-bottom: 10px;
    overflow: hidden;

    .position-comment-title {
      color: $mx-color-var-label-secondary;
    }
  }

  .mx-position-comment-footer {
    textarea {
      border-width: 2px;
      resize: inherit;
      background: transparent;
      padding: 12px;
      outline: none;
      width: 300px;
      height: 85px;
      border-radius: 3px;
      display: block;
      padding-right: 35px;
    }

    a {
      margin-top: $mx-spacing-sm;
    }
  }

  &.read-only {
    .mx-position-comment-footer {
      display: none;
    }

    .flow-activity-comment-action {
      display: none !important;
    }

    .position-comment-title {
      display: none;
    }

    .activity-container {
      max-height: 258px;
    }
  }
}

.flow-activity-comment {
  display: flex;
  align-items: flex-start;

  .mx-thumbnail-container {
    flex: 0 0 auto;
    margin-right: $mx-spacing-sm;
  }

  .external-flag-wrap {
    position: relative;

    .external-flag {
      position: absolute;
      width: 20px;
      min-width: 20px;
      height: 11px;
      background-size: contain;
      margin: 0;
      left: -32px;
      top: 16px;
    }
  }

  .flow-activity-comment-container {
    flex: 1 1 auto;
    font-size: 12px;
    overflow: hidden;

    &.attachment-deleted {
      .flow-activity-comment-content {
        color: $mx-icon-color-secondary;

        .attachment-name {
          color: $mx-icon-color-secondary;
        }
      }

      .flow-activity-comment-content + span {
        color: $mx-color-font-secondary;
        font-style: italic;
      }
    }

    .flow-activity-comment-header {
      display: flex;

      .mx-user-name {
        margin-right: $mx-spacing-xs;
        font-weight: $mx-font-weight-medium;
        flex: 0 1 auto;
      }

      div:last-child {
        color: $mx-color-var-label-secondary;
        flex: 0 0 auto;
      }
    }

    > div:last-child {
      padding: 4px 0;
    }
  }

  .flow-activity-comment-action {
    align-self: flex-start;

    i {
      font-size: 16px;
      color: #bcc2cc;
    }

    .failed-tip {
      color: $mx-color-red;
    }

    .spinner {
      margin-top: 8px;
      margin-right: 8px;
      top: 0 !important;
    }
  }

  .in-position-comment-panel {
    min-width: 60px;
  }
}

.flow-activity-comment-content {
  display: flex;
  align-items: flex-start;
  overflow: hidden;

  .micon-attachment {
    color: $mx-color-font-secondary;
  }

  > div {
    overflow: hidden;
    flex: 1 1 auto;
    text-align: left;
  }

  img {
    max-width: 260px;
  }

  .comment-content {
    font-size: 14px;
    white-space: pre-wrap;
  }

  .attachment-name {
    flex: 0 1 auto;
    overflow: hidden;
  }

  .inline-editor {
    flex: 1 1 auto;
  }
}

.flow-activity-common {
  display: flex;
  align-items: flex-start;

  .flow-activity-icon > i {
    font-size: 22px;
    margin-right: 15px;
    margin-left: 3px;

    &.micon-delete-feed {
      color: $mx-color-var-negative !important;
    }

    &.micon-sign-success {
      color: $mx-color-var-positive !important;
    }

    &.micon-sign-failure {
      color: $mx-color-var-negative !important;
    }
  }

  img {
    width: 22px;
    height: 22px;
    margin-right: 15px;
    margin-left: 3px;
  }

  .flow-activity-common-container {
    flex: 1 1 auto;
    font-size: 12px;

    .flow-activity-common-header {
      word-break: break-word;
    }

    .flow-activity-common-time {
      color: $mx-color-var-label-secondary;
    }

    .flow-activity-reason {
      color: $mx-color-var-label-secondary;
      font-size: 12px;
      font-style: italic;
      word-break: break-all;
    }
  }

  .flow-activity-session-recording {
    flex: 1 1 auto;
    // color: $mx-color-blue;
    text-align: right;
    height: 34px;
    line-height: 34px;

    span {
      font-size: 12px;
      //text-transform: uppercase;
    }
  }

  .due-arrive {
    color: $mx-color-red;
  }
}

.reply-typeahead-helper {
  overflow: hidden;
}

.flow-baseobject-position {
  .flow-baseobject-container {
    position: relative;
    flex: 0 1 auto;
    margin-right: 24px;

    .position-comment {
      position: absolute;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      z-index: 999;
      background: $mx-color-blue;
      text-align: center;
      font-weight: 700;
      font-size: 20px;
      color: white;
      cursor: pointer;
      border: 2px solid $mx-color-var-fill-tertiary;
    }
  }
}

.mention-token {
  background: $mx-color-bg-branding;
  border-radius: 10.5px;
  color: $mx-color-white;
  padding: 0 10px;
}

.flow-meet-participants-list {
  .list-group {
    border: 1px solid #e1e5eb;
    max-height: 300px;
    overflow: auto;
  }

  .user-avatar-group,
  .member-item {
    display: flex;
    align-items: center;
    overflow: hidden;

    .list-text {
      overflow: hidden;
    }
  }

  .status {
    color: $mx-color-gray-40;
    text-align: right;
  }

  .invitee-name,
  .team-member-name {
    margin-left: $mx-spacing-lg;
  }

  .invitee-email,
  .action-groups {
    display: none !important;
  }
}

.thread-preserence .small-width .alert-box .action {
  width: 300px !important;
}

.action-wrapper {
  justify-content: flex-end;
}

.drawer-with-header {
  .right-close-button {
    display: none !important;
  }
}

.drawer-with-header.drawer-with-back {
  header.mx-thread-header {
    padding: 8px 50px !important;
  }
}

.drawer-with-header:not(.drawer-with-back) {
  header.mx-thread-header {
    padding: 8px 50px 8px 20px !important;
  }
}
</style>
