<template>
  <li
    :id="'thread_comment_'+objectSequence"
    v-mx-ta="{ page: 'commentActivity', id: 'item' }"
    tabindex="0"
    role="article"
    :class="'comment' + objectSequence"
    :data-param="activity.sequence"
    :aria-label="ariaMessage">
    <div
      class="flow-activity-comment mx-hover-show"
      :class="{'is-transaction-update-activity' : isObjectUpdateActivity}">
      <template
        v-if="isObjectUpdateActivity">
        <div class="indicator mx-branding-background" />
        <div class="info">
          <div class="message">
            {{ commentContent }}
          </div>
          <div
            v-if="declinedSignReason"
            class="mx-color-secondary mx-text-c4 dfn"
            style="font-style: italic;">
            “{{ declinedSignReason }}”
          </div>
          <div class="date">
            {{ createdTime }}
          </div>
        </div>
      </template>
      <template v-else>
        <MxUserAvatar
          :user-avatar="activity.actor.avatar"
          :alt="activity.actor.name" />
        <div :class="['flow-activity-comment-container', {'attachment-deleted': isDeletedAttachment && !config.isAudit}]">
          <div class="flow-activity-comment-header">
            <div
              :title="activity.actor.name"
              class="mx-user-name mx-ellipsis">
              {{ activity.actor.name }}
            </div>
            <div>{{ createdTime }}</div>
          </div>
          <div
            :class="{
              'disable-select': disableCopyPaste
            }"
            class="flow-activity-comment-content">
            <template v-if="activity.Action === 'REPLY'">
              <div v-if="relatedObject.resource">
                <MxCommentAudioPlayer :comment="relatedObject" />
              </div>
              <div v-else>
                <span
                  v-show="!showInlineEditor"
                  v-mx-ta="{ page: 'commentActivity', id: 'content' }"
                  v-safe-html="textMessage"
                  class="comment-content" />
                <inline-editor
                  v-if="showInlineEditor"
                  :options="options"
                  @resize="scrollIntoView"
                  @exitEdit="exitEdit"
                  @submit="editComment" />
                <span
                  v-if="relatedObject.is_modified"
                  class="t-bdf_comment_edited_flag mx-board-comment-indicate">-{{ $t('Edited') }}</span>
                <span
                  v-if="!relatedObject.is_modified"
                  class="t-bdf_comment_unedited_flag hide" />
              </div>
            </template>
            <span v-else-if="commentContent">
              {{ commentContent }}
            </span>
            <template v-else>
              <i class="micon-attachment" />
              <span
                :class="['mx-ellipsis attachment-name', {'mx-clickable mx-branding-text important': !isDeletedAttachment || config.isAudit}]"
                :title="attachmentName"
                :tabindex="(!isDeletedAttachment || config.isAudit)?0:-1"
                @keypress.enter="(!isDeletedAttachment || config.isAudit) ? viewFlowAttachment(): ''"
                @click="(!isDeletedAttachment || config.isAudit) ? viewFlowAttachment(): ''">{{ attachmentName }}</span>
            </template>
          </div>
          <div
            v-if="isFailedReply"
            class="mx-reply-delivery-failed mx-ellipsis failed-message send-fail">
            <div
              class="sending failed-icon">
              <button class="micon-svg-text-bubble4 send-fail" />
            </div>
            <span role="status">{{ $t('binder_chat_delivery_failed') }}</span>
            <a
              class="btn-link js-resend-link mx-clickable mx-branding-text important"
              @click="resendReplyLocal(activity)">{{ $t('resend') }}</a>
          </div>
          <span v-if="isDeletedAttachment">{{ $t('this_file_has_been_deleted') }}</span>
        </div>
        <template v-if="activity.isPending">
          <div class="flow-activity-comment-action">
            <div
              v-if="isPendingReply"
              v-mx-spinner="spinOptions"
              class="spinner-container" />
          </div>
        </template>
        <div v-else-if="allowEditDeleteCommentByTime && (canEditComment || canDeleteComment)" class="flow-comment-container">
          <div
            v-if="isInPositionCommentPanel"
            class="flow-activity-comment-action in-position-comment-panel mx-hover-hide">
            <i
              v-if="canEditComment"
              :data-param="relatedObject.sequence"
              class="micon-edit-xs mx-clickable"
              @click="editComment" />
            <i
              v-if="canDeleteComment"
              :data-param="relatedObject.sequence"
              class="micon-delete-xs mx-clickable"
              @click="deleteComment" />
          </div>
          <div
            v-else
            class="flow-activity-comment-action mx-hover-hide">
            <el-dropdown
              v-if="!viewOnly"
              trigger="click"
              placement="bottom-end"
              :popper-append-to-body="false"
              @command="handleDropdownEvent($event, relatedObject.sequence)">
              <span
                class="mx-icon-action-effect-inherited micon-more-tab" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-if="canEditComment && editCommentControl"
                  v-mx-ta="{ page: 'commentObjectItem', id: `edit`}"
                  command="showInlineEditorPage">
                  <span>{{ $t('edit') }}</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="canDeleteComment"
                  v-mx-ta="{ page: 'commentObjectItem', id: `delete`}"
                  command="deleteComment">
                  <span class="danger">{{ $t('delete') }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <ReactionSelector 
            class="flow-comment-reaction-selector"
            v-if="showReactionSelectorInThreeDot" 
            :myReactions="activity.reactionModel.myReactions"
            @select="updateMyReaction($event)" />
        </div>
        <div
          v-else-if="showReactionSelectorInThreeDot"
          :class="['flow-comment-container', {'with-attachment': activity.Action === 'FILE_REPLY' || activity.Action === 'ATTACHMENT'}]">
          <ReactionSelector 
            class="flow-comment-reaction-selector"
            v-if="showReactionSelectorInThreeDot" 
            :myReactions="activity.reactionModel.myReactions"
            @select="updateMyReaction($event)" />
        </div>
      </template>
    </div>
    <ReactionBadges 
      v-if="showReactionBadge" 
      :feed="activity"
      :isMe="false"
      :allReactions="activity.reactionModel.feedReactions" 
      :myReactions="activity.reactionModel.myReactions" 
      :showReactionSelector="showReactionBadge && activity.reactionModel.hasReactions"
      queryParentWidth=".flow-activity-comment-content"
      @select="updateMyReaction($event)" />
  </li>
</template>

<script>
import tools from '../util/tools'
import { mapActions, mapGetters } from 'vuex'
import util from '@views/common/utils/utils'
import templateFormat from '@views/common/utils/formatter/templateFormat'
import InlineEditor from '@views/common/components/inlineEditor'
import { ObjectUtils, MxConsts, BrowserUtils } from '@commonUtils'
import { isFocusByKeyboardNow, focusTo } from '@views/common/accessibility'
import MxCommentAudioPlayer from '@views/common/components/media/MxCommentAudioPlayer'
import { getFileRequestSubmittedCount, handleStepSubmit } from '@views/common/utils/transaction'
import { MxBaseObjectType, TransactionStepType } from 'isdk/src/api/defines'
import _debounce from 'lodash/debounce'
import ariaMixin from '../../../chat/src/mixins/aria'
import ReactionBadges from '@views/chat/src/components/ReactionBadges.vue'
import ReactionSelector from '@views/chat/src/components/ReactionSelector.vue'

function isYou(assignee){
  return assignee?.isMySelf && !assignee?.isTeam
}
export default {
  components: {
    InlineEditor,
    MxCommentAudioPlayer,
    ReactionBadges,
    ReactionSelector
  },
  mixins: [ariaMixin],
  props: {
    ctrlKey: {
      type: String,
      required: true
    },
    activity: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      default: () => ({})
    },
    //TODO: should be replaced with boardBriefViewModel
    binderObj: {
      type: Object,
      default: () => ({})
    },
    isBinderOwner: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      relatedObject: this.activity.relatedObject,
      showOption: true,
      options: {},
      isResending: false,
      isResendingFaild: true,
      isInPositionCommentPanel: false,
      showInlineEditor: false,
      isActionMenuOpen: false,
      references: ObjectUtils.getByPath(this.activity, 'baseObject.references'),
      spinOptions: {
        lines: 10,
        length: 2,
        width: 1,
        radius: 5,
        scale: 1,
        corners: 0.7,
        speed: 1
      },
      allowEditDeleteCommentByTime: true,
      allowEditDeleteCommentTimerId: null,
      isMobileOrTablet: BrowserUtils.isMobile || BrowserUtils.isTablet
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    ...mapGetters('privileges', ['editInterval', 'disableEditChatMessage', 'disableCopyPaste']),
    showReactionBadge () {
      const {is_service_request, is_acd, isACDLeaveMessage, isSocial, is_inbox, isNoAction} = this.binderObj
      if (is_service_request || is_acd || isACDLeaveMessage || isSocial || is_inbox || isNoAction) {
        return false
      }
      if (this.isDeletedAttachment || this.isFailedReply || this.isObjectUpdateActivity) {
        return false
      }
      return this.activity.reactionModel && this.activity.sequence && !this.viewOnly
    },
    showReactionSelectorInThreeDot () {
      return this.showReactionBadge && this.activity.reactionModel && !this.activity.reactionModel.hasReactions
    },
    viewOnly () {
      return !!this.config?.isAudit
    },
    objectSequence () {
      if (this.relatedObject) {
        return this.relatedObject.sequence
      } else {
        return 0
      }
    },
    isAlreadyFaild () {
      return this.activity.isPending && this.activity.isFailed
    },
    isPendingReply () {
      if (this.isAlreadyFaild) {
        return this.isResending
      } else {
        return this.activity.isPending
      }
    },
    isIntegration () {
      return this.activity?.baseObject?.type === MxConsts.TransactionType.TRANSACTION_TYPE_INTEGRATION
    },
    integrationType () {
      return this.activity?.baseObject?.sub_type
    },
    isFailedReply () {
      if (this.isAlreadyFaild) {
        return this.isResendingFaild
      } else {
        return this.isAlreadyFaild
      }
    },
    commentContent () {
      if (this.relatedObject) {
        return this.relatedObject.text
      } else if (this.activity.BaseType === 'TRANSACTION') {
        return this._getTransactionMessage()
      } else if (this.activity.BaseType === 'SIGNATURE') {
        const signInfo = tools['SIGNATURE'](this.activity, this.$t, this.binderObj)
        return signInfo.text
      } else if (this.activity.BaseType === 'TODO') {
        return this._getTodoMessage()
      }
      //TODO: Jeffery-> this type is specifically for WORKFLOW
      //If we keep this, it will not good for normal workspace
      else if (this.activity.BaseType === 'WORKFLOW') {
        return this._getWorkflowStepMessage().text
      }
      return ''
    },
    textMessage: function () {
      const text = this.relatedObject.rich_text || this.relatedObject.text
      return templateFormat.convertBBcodeAndMention(text, true, this.currentUser)
    },
    createdTime: function () {
      return util.formatDisplayedTime(this.activity.created_time, { displayExactTime: true, displayWithToday: true })
    },
    canEditComment () {
      const isActorMySelf = ObjectUtils.getByPath(this.activity, 'actor.isMySelf')
      const isNonActorInAcd = this.$route.name === 'anonAcd' && !ObjectUtils.getByPath(this.activity, 'actor.id')
      if (isActorMySelf || isNonActorInAcd) {
        if (this.activity.Action !== 'ATTACHMENT') {
          if (this.relatedObject && !this.binderObj.isNoAction) {
            return true
          }
        }
      }
      return false
    },
    canDeleteComment () {
      const isActorMySelf = ObjectUtils.getByPath(this.activity, 'actor.isMySelf')
      const isNonActorInAcd = this.$route.name === 'anonAcd' && !ObjectUtils.getByPath(this.activity, 'actor.id')
      if (isActorMySelf || isNonActorInAcd || (this.currentUser.isInternalUser && this.isBinderOwner)) {
        if (this.activity.Action !== 'ATTACHMENT') {
          if (this.relatedObject && !this.binderObj.isNoAction) {
            return true
          }
        }
      }
      return false
    },
    isDeletedAttachment () {
      if (this.activity.Action === 'ATTACHMENT') {
        if (this.relatedObject && this.relatedObject.is_deleted) {
          return true
        }
      } else if (this.activity.Action === 'FILE_REPLY') {
        return this.activity.baseObject.attachments[0].is_deleted
      }
      return false
    },
    attachmentName () {
      const page = ObjectUtils.getByPath(this.activity, 'board.pages.0')
      if (ObjectUtils.getByPath(this.activity, 'relatedObject.name')) {
        return ObjectUtils.getByPath(this.activity, 'relatedObject.name')
      } else if (page) {
        return util.getDefaultFileName(page, this.$t)
      } else if (this.activity.Action === 'FILE_REPLY') {
        return ObjectUtils.getByPath(this.activity, 'baseObject.attachments.0.name')
      } else {
        return this.$t('url')
      }
    },
    editCommentControl () {
      if (this.disableEditChatMessage) {
        return false
      }
      if (this.activity.Action === 'REPLY' && this.relatedObject.resource_length) {
        return false
      } else {
        return true
      }
    },
    isObjectUpdateActivity () {
      const activity = this.activity
      if (this.activity.BaseType === 'WORKFLOW') {
        return true
      }
      const todoActivity = activity.isTodoReopen || activity.isTodoComplete || activity?.isTodoMarkAsCompleted || activity.isTodoDueDate || activity.isTodoAssign || activity.isTodoUpdate || activity.isTodoDueDateArrive || activity.isTodoDelete
      const signatureActivity = activity.isSignatureUpdate || activity.isSignatureStatusUpdate || activity.isSignatureDueDateArrive || activity.isSignatureDueDateUpdate || activity.isSignatureDelete || activity.isSignatureUpdateReopen || activity.isSignatureMarkAsCompleted
      return activity.isTransactionExpirationDateArrive ||
        activity.isTransactionUpdate || activity.isTransactionUpdateReopen ||
        activity.isTransactionStepSubmit ||activity.isTransactionResendReminder ||
        activity.isTransactionStepReopen || activity.isReassign || activity.isSignatureUpdateReady || activity.isSignatureUpdateEditing ||
        activity.isTransactionExpirationUpdate || activity.isTransactionDelete || activity.isTransactionUpdateEditing || activity.isWorkflowStepReady || activity.isTransactionUpdateReady ||
        activity.isTransactionMarkAsCompleted ||
        signatureActivity || todoActivity || activity.isTransactionReopen || activity.isSignatureReopen || activity.isTransactionCreate || activity.isTransactionUpdateCustomResult
    },
    declinedSignReason () {
      if (this.activity.isSignatureStatusUpdate && this.activity.baseObject.isDeclined) {
        return ObjectUtils.getByPath(this.activity, 'baseObject.signees.0.msg')
      }
      return ''
    },

    feed () {
      return this.activity
    }
  },
  watch: {
    activity: {
      handler (newActivity) {
        this.relatedObject = newActivity.relatedObject
      },
      deep: true
    }
  },
  mounted () {
    const EDIT_TIME_INTERVAL = this.editInterval
    if (!this.relatedObject) {
      this.allowEditDeleteCommentByTime = false
    } else {
      this.allowEditDeleteCommentByTime = (Date.now() - this.relatedObject.created_time) < EDIT_TIME_INTERVAL
      if (this.allowEditDeleteCommentByTime === true) {
        const waitTime = EDIT_TIME_INTERVAL - (Date.now() - this.relatedObject.created_time)
        // FixMe: This is temp solutions, will replace here with other better solutions
        if (waitTime > 2147483647) {
          this.allowEditDeleteCommentByTime = true
        } else {
          this.allowEditDeleteCommentTimerId = setTimeout(() => {
            this.showInlineEditor = false
            this.allowEditDeleteCommentByTime = false
            this.allowEditDeleteCommentTimerId = null
          }, waitTime)
        }
      }
    }
  },
  beforeDestroy () {
    if (this.allowEditDeleteCommentTimerId) {
      clearTimeout(this.allowEditDeleteCommentTimerId)
      this.allowEditDeleteCommentTimerId = null
    }
  },
  methods: {
    ...mapActions('thread', ['updateThreadComment', 'deleteThreadComment', 'createThreadComment']),
    ...mapActions('chat', ['updateReaction']),
    updateMyReaction (emoji) {
      this.updateReaction({
        emoji, feed: this.activity
      })
    },
    _getWorkflowStepMessage () {
      const { workflows } = this.binderObj
      let text = ''
      const currentWorkflow = workflows && workflows[0]
      const feedStep = this.activity.baseObject.flowStep
      const actor = isYou(this.activity.actor) ? this.$t('You') : this.activity.actor.name
      const step = currentWorkflow?.transformedSteps.find(s => s.sequence === feedStep.sequence)
      const type = step ? step.type : feedStep.type
      const isRegardAsAdd = this.activity.isWorkflowUpdate && !feedStep?.is_deleted && !feedStep?.updated_time
      const map = {
        edit: {
          WORKFLOW_STEP_TYPE_APPROVAL: this.$t('actor_edited_this_approval', {actor}),
          WORKFLOW_STEP_TYPE_ACKNOWLEDGE: this.$t('actor_edited_this_ack', {actor}),
          WORKFLOW_STEP_TYPE_FILE_REQUEST: this.$t('actor_edited_this_file_request', {actor}),
          WORKFLOW_STEP_TYPE_SIGNATURE: this.$t('actor_edited_this_esign', {actor}),
          WORKFLOW_STEP_TYPE_TODO: this.$t('actor_edited_this_todo', {actor}),
          WORKFLOW_STEP_TYPE_TODO_TRANSACTION: this.$t('actor_edited_this_todo', {actor}),
          WORKFLOW_STEP_TYPE_FORM_REQUEST: this.$t('actor_edited_this_form', {actor}),
          WORKFLOW_STEP_TYPE_PDF_FORM:  this.$t('actor_edited_this_pdf_form', {actor}),
          WORKFLOW_STEP_TYPE_DOCUSIGN: this.$t('actor_edited_this_docusign', {actor}),
          WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP: this.$t('actor_edited_this_launch_web_app', {actor}),
          WORKFLOW_STEP_TYPE_INTEGRATION: (()=>{
            const subType = ObjectUtils.getByPath(step,'inputObject.sub_type')
            if(subType === 'Jumio'){
              return this.$t('actor_edited_this_jumio', {actor})
            } else if (subType === 'Integration') {
              let app_name = this.$t('Integration')
              try {
                let custome_data = {}
                const baseObject = this.activity.baseObject
                if (baseObject?.custom_data) {
                  custome_data = JSON.parse(baseObject.custom_data)
                } else if (step.inputObject?.custom_data) {
                  custome_data = JSON.parse(step.inputObject.custom_data)
                }
                if (custome_data?.integration?.app_name) {
                  app_name = custome_data.integration.app_name
                }
              } catch (error) {
                console.error(error)
              }
              return this.$t('actor_edited_this_integration', {actor: actor, integration: app_name})
            }

            // the string didn't used for now, just regard as backup
            return this.$t('actor_edited_this_jumio', {actor})
          })(),
          WORKFLOW_STEP_TYPE_MEET_REQUEST: this.$t('actor_edited_this_Meet_Request', {actor}),
          WORKFLOW_STEP_TYPE_AWAIT: this.$t('actor_edited_this_wait', {actor}),
          WORKFLOW_STEP_TYPE_DECISION: this.$t('activity_actor_edited_this_Decision', {actor})
        },
        skip: this.$t('feed_workflow_update_skip', { actor, type }),
        add: {
          WORKFLOW_STEP_TYPE_APPROVAL: this.$t('actor_added_this_approval', {actor}),
          WORKFLOW_STEP_TYPE_ACKNOWLEDGE: this.$t('actor_added_this_ack', {actor}),
          WORKFLOW_STEP_TYPE_FILE_REQUEST: this.$t('actor_added_this_file_request', {actor}),
          WORKFLOW_STEP_TYPE_SIGNATURE: this.$t('actor_added_this_esign', {actor}),
          WORKFLOW_STEP_TYPE_TODO: this.$t('actor_added_this_todo', {actor}),
          WORKFLOW_STEP_TYPE_TODO_TRANSACTION: this.$t('actor_added_this_todo', {actor}),
          WORKFLOW_STEP_TYPE_FORM_REQUEST: this.$t('actor_added_this_form', {actor}),
          WORKFLOW_STEP_TYPE_PDF_FORM: this.$t('actor_added_this_pdf_form', {actor}),
          WORKFLOW_STEP_TYPE_DOCUSIGN: this.$t('actor_added_this_docusign', {actor}),
          WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP: this.$t('actor_added_this_launch_web_app', {actor}),
          WORKFLOW_STEP_TYPE_INTEGRATION: (()=>{
            const subType = ObjectUtils.getByPath(step,'inputObject.sub_type')
            if(subType === 'Jumio') {
              return this.$t('actor_add_this_jumio', {actor})
            } else if (subType === 'Integration') {
              let app_name = this.$t('Integration')
              try {
                let custome_data = {}
                const baseObject = this.activity.baseObject
                if (baseObject?.custom_data) {
                  custome_data = JSON.parse(baseObject.custom_data)
                } else if (step.inputObject?.custom_data) {
                  custome_data = JSON.parse(step.inputObject.custom_data)
                }
                if (custome_data?.integration?.app_name) {
                  app_name = custome_data.integration.app_name
                }
              } catch (error) {
                console.error(error)
              }

              return this.$t('actor_add_this_integration', { actor: actor, integration: app_name })
            }

            // the string didn't used for now, just regard as backup
            return this.$t('actor_add_this_jumio', {actor})
          })(),
          WORKFLOW_STEP_TYPE_MEET_REQUEST: this.$t('actor_added_this_Meet_Request', {actor}),
          WORKFLOW_STEP_TYPE_AWAIT: this.$t('actor_added_this_wait', {actor}),
        },
        reopen: {
          WORKFLOW_STEP_TYPE_TODO: this.$t('actor_reopened_this_todo', { actor }),
          WORKFLOW_STEP_TYPE_TODO_TRANSACTION: this.$t('actor_reopened_this_todo', { actor }),
          WORKFLOW_STEP_TYPE_FILE_REQUEST: this.$t('actor_reopened_this_file_request', { actor })
        },
        reassign:{
          WORKFLOW_STEP_TYPE_TODO: (assigneeName)=>{
            return this.$t('actor_assigned_this_todo_to_user', { actor, user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_TODO_TRANSACTION: (assigneeName)=>{
            return this.$t('actor_assigned_this_todo_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_FILE_REQUEST:(assigneeName)=>{
            return this.$t('actor_assigned_this_File_Request_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_APPROVAL:(assigneeName)=>{
            return this.$t('actor_assigned_this_Approval_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_ACKNOWLEDGE: (assigneeName)=>{
            return this.$t('actor_assigned_this_Acknowledgement_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_FORM_REQUEST: (assigneeName)=>{
            return this.$t('actor_assigned_this_Form_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_PDF_FORM: (assigneeName)=>{
            return this.$t('actor_assigned_this_PDF_Form_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_MEET_REQUEST: (assigneeName)=>{
            return this.$t('actor_assigned_this_Meet_Request_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_INTEGRATION: (assigneeName)=>{
            const subType = ObjectUtils.getByPath(step,'inputObject.sub_type')
            if(subType === 'Jumio'){
              return this.$t('actor_assigned_this_Jumio_to_user', {actor,user: assigneeName})
            } else{
              return this.$t('actor_assigned_this_integration_to_user', { actor,user: assigneeName })
            }
          },
          WORKFLOW_STEP_TYPE_DOCUSIGN: (assigneeName)=>{
            return this.$t('actor_assigned_this_Docusign_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP: (assigneeName)=>{
            return this.$t('actor_assigned_this_Launch_Web_App_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_SIGNATURE: (assigneeName)=>{
            return this.$t('actor_assigned_this_esign_to_user', { actor,user: assigneeName })
          },
          WORKFLOW_STEP_TYPE_DECISION: (assigneeName) => {
            return this.$t('activity_actor_assigned_this_Decision_to_user', { actor, user: assigneeName })
          }
        }
      }
      if(this.activity.Action === 'REASSIGN'){
        const stepReassignedUser = this.activity.baseObject?.stepReassignedUser
        const assigneeName =  isYou(stepReassignedUser) ? this.$t('you_lower_case') : stepReassignedUser.name
        text = map.reassign[type](assigneeName)
      }else if (ObjectUtils.getByPath(this.activity.baseObject, 'steps.0.updated_time')) {
        text = map.edit[type]
      } else if (this.activity.Action === 'STEP_SKIPPED') {
        text = map.skip
      } else if (this.activity.Action === 'STEP_REOPEN') {
        text = map.reopen[type]
      } else if (feedStep.sequence && feedStep.type && isRegardAsAdd) {
        text = map.add[type]
      } else if (this.activity.isWorkflowStepReady) {
        const user =  isYou(feedStep.editor) ? this.$t('You') : feedStep.editor.name
        text = this.$t('user_completed_the_preparation', { user })
      }
      return { text }
    },
    handleDropdownEvent (event, sequence) {
      this[event] && this[event](sequence)
    },
    viewFlowAttachment () {
      let relatedObject = this.activity.relatedObject
      const baseObject = this.activity.baseObject
      if (this.activity.Action === 'FILE_REPLY') {
        relatedObject = ObjectUtils.getByPath(baseObject, 'attachments.0')
      }
      let emitObject = this.$parent
      if (this.config.isAudit) {
        emitObject = this
      }
      let optionMenus = null
      if (this.binderObj.isACDLeaveMessage && !ObjectUtils.getByPath(this.activity, 'actor.isMySelf')) {
        optionMenus = {
          showInfo: true,
          download: true,
          copy: true,
          rotate: true,
          remove: false
        }
      }
      emitObject.$emit('viewPage', {
        name: relatedObject.name,
        fullData: {
          type: this.activity.BaseType,
          fileSequence: relatedObject.sequence,
          objectSequence: baseObject.sequence
        }
      }, optionMenus)
    },


    showInlineEditorPage () {
      const binderObj = this.binderObj
      this.showInlineEditor = true
      this.isActionMenuOpen = false
      this.options = {
        // mentionSupport: !binderObj.isOrgPublicBinder,
        mentionSupport: true, //can't found the isOrgPublicBinder definition.
        canMentionTemplateMsg: binderObj.is_acd && !binderObj.isACDLeaveMessage && this.currentUser.isInternalUser,
        bindingPath: 'text',
        needButton: true
      }

      if (this.relatedObject.rich_text) {
        this.options.value = this.relatedObject.rich_text
      } else {
        this.options.value = this.relatedObject.text
      }
    },
    editComment (message) {
      const relatedObject = this.relatedObject
      if (relatedObject.rich_text) {
        if (relatedObject.rich_text === message) {
          return
        }
      } else if (relatedObject.text === message) {
        return
      }
      const vm = this
      const payload = {
        sequence: relatedObject.sequence
      }
      const revisedTextObj = util.getRichText(message)
      if (revisedTextObj.is_rich_text) {
        payload.text = revisedTextObj.text
        payload.rich_text = revisedTextObj.rich_text
        payload.rich_text_format = 'TEXT_FORMAT_BBCODE'
      } else {
        payload.text = revisedTextObj.text
        if (relatedObject.rich_text) {
          payload.rich_text = ''
        }
      }
      const requestObject = { ctrlKey: this.ctrlKey, payload }
      if (this.activity.BaseType === MxBaseObjectType.FILE) {
        requestObject.baseObject = {
          spath: this.activity.baseObject.SPath,
          type: MxBaseObjectType.FILE
        }
      }
      this.updateThreadComment(requestObject).then(() => {
        const message = vm.$root.$t('update_comment_success')
        this.$mxMessage({
          type: 'success',
          message: message
        })
      }, () => {
        const message = vm.$root.$t('update_comment_failed')
        this.$mxMessage({
          type: 'error',
          message: message
        })
      })
    },
    getNextFocusableElement () {
      let focusEl = this.$el
      focusEl = focusEl.nextElementSibling || focusEl.previousElementSibling
      return focusEl
    },
    deleteComment () {
      const successMsg = this.$t('binder_delete_a_comment_succeed')
      const errorMsg = this.$t('binder_delete_a_comment_failed')
      const focusEl = this.getNextFocusableElement()
      const border = isFocusByKeyboardNow()
      const payload = { sequence: this.relatedObject.sequence }
      const requestObject = { ctrlKey: this.ctrlKey, payload }
      if (this.activity.BaseType === MxBaseObjectType.FILE) {
        requestObject.baseObject = {
          spath: this.activity.baseObject.SPath,
          type: MxBaseObjectType.FILE
        }
      }
      this.$mxConfirm(this.$t('confirm_delete_comment'), this.$t('Confirm')).then(() => {
        this.deleteThreadComment(requestObject).then(() => {
          this.$mxMessage({
            type: 'success',
            message: successMsg
          })
          focusTo(focusEl, { border })
        }).catch(() => {
          this.$mxMessage({
            type: 'error',
            message: errorMsg
          })
        })
      }).catch(() => {

      })
      this.isActionMenuOpen = false
    },
    exitEdit () {
      this.showInlineEditor = false
    },
    resendReplyLocal (activity) {
      this.isResending = true
      this.isResendingFaild = false
      this.createThreadComment({ ctrlKey: this.ctrlKey, payload: activity }).catch(err => {
        this.isResending = false
        this.isResendingFaild = true
      })
    },
    scrollIntoView () {
      this.$nextTick(() => {
        this.$el.scrollIntoView(false)
      })
    },
    _getTransactionMessage () {
      const transaction = ObjectUtils.getByPath(this.activity, 'baseObject')
      const formatDue = (timestamp) => util.formatDue(timestamp, {
        today: this.$t('today'),
        yesterday: this.$t('yesterday'),
        Tomorrow: this.$t('Tomorrow')
      })
      const actActor = this.activity.actor
      const actorName = isYou(actActor) ? this.$t('You') : actActor?.name
      const originalAssignee = ObjectUtils.getByPath(this.activity.recordBaseObject, 'steps.0.assignee') || actActor
      var assigneeName = isYou(originalAssignee) ? this.$t('you_lower_case') : originalAssignee?.name
      const isAdmin = ObjectUtils.getByPath(this, 'binderObj.boardUsers')?.findIndex(user => {
        return user.id === actActor.id
      }) < 0
      const reassignActor = isAdmin ? this.$t('admin') : actorName
      const typeStringMap = {
        TRANSACTION_TYPE_APPROVAL: {
          today: this.$t('Approval_due_today'),
          getDateString: (date) => {
            return this.$t('Approval_due_on_date', { date })
          },
          reopen: () => {
            const originalSteps = ObjectUtils.getByPath(this.activity, 'recordBaseObject.steps')
            //For approval, reviewer is skipped and invisible by default
            const steps = originalSteps.filter(s=>s.type !== TransactionStepType.STEP_TYPE_REVIEWER)
            const firstAssignee = steps[0]?.assignee || actActor
            assigneeName = isYou(firstAssignee) ? this.$t('you_lower_case') : firstAssignee.name
            if (steps?.length >= 2) {
              const isParallel = steps[0].order_number === steps[steps.length - 1].order_number
              if (isParallel) {
                const hasMySelf = steps.some(s => isYou(s.assignee))
                const param = {
                  user: hasMySelf ? this.$t('you_lower_case') : assigneeName
                }
                if (steps.length === 2) {
                  return this.$t('activity_Approval_assigned_to_one_and_another', param)
                } else {
                  return this.$t('activity_Approval_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
                }
              }
            }
            if (assigneeName === this.$t('you_lower_case')) {
              return this.$t('activity_Approval_assigned_to_you')
            } else {
              return this.$t('activity_Approval_assigned_to_user', { user: assigneeName })
            }
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_Approval_to_user', {user: userName, actor: reassignActor})
          },
          create: () => typeStringMap[transaction.type].reopen(),
          update: () => {
            return this.$t('activity_Approval_assigned_to_user', { user: assigneeName })
          },
          updateReopen: this.$t('user_reopened_this_approval',{ user: actorName }),
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('Approval') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('Approval') })
            }
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('Approval') })
          }
        },
        TRANSACTION_TYPE_ACKNOWLEDGE: {
          today: this.$t('Acknowledgement_due_today'),
          getDateString: (date) => {
            return this.$t('Acknowledgement_due_on_date', { date })
          },
          reopen: () => {
            const steps = ObjectUtils.getByPath(this.activity, 'recordBaseObject.steps')
            if (steps?.length >= 2) {
              const isParallel = steps[0].order_number === steps[steps.length - 1].order_number
              if (isParallel) {
                const hasMySelf = steps.some(s => isYou(s.assignee))
                const param = {
                  user: hasMySelf ? this.$t('you_lower_case') : assigneeName
                }
                if (steps.length === 2) {
                  return this.$t('activity_Acknowledgement_assigned_to_one_and_another', param)
                } else {
                  return this.$t('activity_Acknowledgement_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
                }
              }
            }
            if (assigneeName === this.$t('you_lower_case')) {
              return this.$t('activity_Acknowledgement_assigned_to_you')
            } else {
              return this.$t('activity_Acknowledgement_assigned_to_user', { user: assigneeName })
            }
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_Acknowledgement_to_user', {user: userName, actor: reassignActor})
          },
          create: () => typeStringMap[transaction.type].reopen(),
          update: () => {
            return this.$t('activity_Acknowledgement_assigned_to_user', { user: assigneeName })
          },
          updateReopen: this.$t('user_reopened_this_acknowledgement',{ user: actorName }),
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('Acknowledgement') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('Acknowledgement') })
            }
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('Acknowledgement') })
          }
        },
        TRANSACTION_TYPE_FILE_REQUEST: {
          today: this.$t('File_Request_due_today'),
          getDateString: (date) => {
            return this.$t('File_Request_due_on_date', { date })
          },
          reopen: () => {
            const osteps = ObjectUtils.getByPath(this.activity, 'recordBaseObject.steps')
            const steps = osteps.filter((step, index, array) => {
                return step.order_number === array[0].order_number;
            });
            if (steps?.length >= 2) {
              const isParallel = steps[0].order_number === steps[steps.length - 1].order_number
              if (isParallel) {
                const hasMySelf = steps.some(s => isYou(s.assignee))
                const param = {
                  user: hasMySelf ? this.$t('you_lower_case') : assigneeName
                }
                if (steps.length === 2) {
                  return this.$t('activity_File_Request_assigned_to_one_and_other', param)
                } else {
                  return this.$t('activity_File_Request_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
                }
              }
            }
            if (assigneeName === this.$t('you_lower_case')) {
              return this.$t('activity_File_Request_assigned_to_you')
            } else {
              return this.$t('activity_File_Request_assigned_to_user', { user: assigneeName })
            }
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_File_Request_to_user', {user: userName, actor: reassignActor})
          },
          create: (activity) => typeStringMap[transaction.type].reopen(),
          updateReopen: this.$t('User_reopened_this_file_request',{ user: actorName }),
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('File_Request') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('File_Request') })
            }
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('File_Request') })
          }
        },
        TRANSACTION_TYPE_PDF_FORM: {
          today: this.$t('PDF_Form_due_today'),
          getDateString: (date) => {
            return this.$t('PDF_Form_due_on_date', { date })
          },
          reopen: () => {
            const steps = ObjectUtils.getByPath(this.activity, 'recordBaseObject.steps')
            if (steps?.length >= 2) {
              const isParallel = steps[0].order_number === steps[steps.length - 1].order_number
              if (isParallel) {
                const hasMySelf = steps.some(s => isYou(s.assignee))
                const param = {
                  actionType: this.$t('PdfForm'),
                  user: hasMySelf ? this.$t('you_lower_case') : assigneeName
                }
                if (steps.length === 2) {
                  return this.$t('activity_PDF_Form_assigned_to_one_and_another', param)
                } else {
                  return this.$t('activity_PDF_Form_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
                }
              }
            }
            if (isYou(originalAssignee)) {
              return this.$t('activity_pdf_form_assigned_to_you')
            } else {
              return this.$t('activity_pdf_form_assigned_to_user', { user: assigneeName })
            }
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_PDF_Form_to_user', {user: userName, actor: reassignActor})
          },
          create: () => typeStringMap[transaction.type].reopen(),
          update: () => {
            return this.getPdfFormAssignedActivityText(this.activity, originalAssignee)
          },
          updateReopen: this.$t('user_reopened_this_pdf_form',{ user: actorName }),
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('PdfForm_capitalized') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('PdfForm_capitalized') })
            }
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('PdfForm_capitalized') })
          }
        },
        TRANSACTION_TYPE_FORM_REQUEST: {
          today: this.$t('Form_due_today'),
          getDateString: (date) => {
            return this.$t('Form_due_on_date', { date })
          },
          reopen: () => {
            const steps = ObjectUtils.getByPath(this.activity, 'recordBaseObject.steps')
            if (steps?.length >= 2) {
              const isParallel = steps[0].order_number === steps[steps.length - 1].order_number
              if (isParallel) {
                const hasMySelf = steps.some(s => isYou(s.assignee))
                const param = {
                  actionType: this.$t('Form'),
                  user: hasMySelf ? this.$t('you_lower_case') : assigneeName
                }
                if (steps.length === 2) {
                  return this.$t('activity_Form_assigned_to_one_and_another', param)
                } else {
                  return this.$t('activity_Form_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
                }
              }
            }
            if (isYou(originalAssignee)) {
              return this.$t('activity_form_assigned_to_you')
            } else {
              return this.$t('activity_form_assigned_to_user', { user: assigneeName })
            }
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_Form_to_user', {user: userName, actor: reassignActor})
          },
          create: () => typeStringMap[transaction.type].reopen(),
          update: () => {
            return this.getFormAssignedActivityText(this.activity, originalAssignee)
          },
          updateReopen: this.$t('user_reopened_this_form',{ user: actorName }),
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('Form') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('Form') })
            }
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('Form') })
          }
        },
        TRANSACTION_TYPE_DOCUSIGN: {
          today: this.$t('Docusign_due_today'),
          getDateString: (date) => {
            return this.$t('Docusign_due_on_date', { date })
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_Docusign_to_user', {user: userName, actor: reassignActor})
          },
          create: (activity) => {
            const steps = activity.recordBaseObject.steps
            return this.getDocusignAssignedTxt(steps)
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('docu_sign') })
          }
        },
        TRANSACTION_TYPE_LAUNCH_WEB_APP: {
          today: this.$t('Launch_Web_App_due_today'),
          getDateString: (date) => {
            return this.$t('Launch_Web_App_due_on_date', { date })
          },
          reopen: () => {
            return this.$t('activity_Launch_Web_App_assigned_to_user', { user: assigneeName })
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_Launch_Web_App_to_user', {user: userName, actor: reassignActor})
          },
          create: (activity) => {
            const step = activity.recordBaseObject.steps[0]
            return isYou(step.assignee) ? this.$t('activity_Launch_Web_App_assigned_to_you') : this.$t('activity_Launch_Web_App_assigned_to_user', { user: step.assignee.name })
          },
          updateReopen: this.$t('user_reopened_this_lanchwebapp',{ user: actorName }),
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('Web_App') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('Web_App') })
            }
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('Web_App') })
          }
        },
        TRANSACTION_TYPE_INTEGRATION: {
          today: (() => {
            switch (this.integrationType) {
              case 'Jumio': {
                return this.$t('Jumio_due_today')
              }
              case 'Integration': {
                let app_name = this.$t('Integration')
                try {
                  let custome_data = {}
                  if (transaction?.custom_data) {
                    custome_data = JSON.parse(transaction.custom_data)
                  }
                  if (custome_data?.integration?.app_name) {
                    app_name = custome_data.integration.app_name
                  }
                } catch (error) {
                  console.error(error)
                }

                return this.$t('Integration_due_today', {integration: app_name})
              }
            }
          })(),
          getDateString: (date) => {
            switch (this.integrationType) {
              case 'Jumio': {
                return this.$t('Jumio_due_on_date', { date })
              }
              case 'Integration': {
                let app_name = this.$t('Integration')
                try {
                  let custome_data = {}
                  if (transaction?.custom_data) {
                    custome_data = JSON.parse(transaction.custom_data)
                  }
                  if (custome_data?.integration?.app_name) {
                    app_name = custome_data.integration.app_name
                  }
                } catch (error) {
                  console.error(error)
                }

                return this.$t('Integration_due_on_date', {integration: app_name, date: date})
              }
            }
          },
          reopen: () => {
            let reopenMessage = ''
            const isYou = assigneeName === this.$t('you_lower_case')
            if (this.integrationType === 'Jumio') {
              reopenMessage = isYou ?
                this.$t('Jumio_assigned_to_you_activity') : this.$t('Jumio_assigned_to_user_activity', { user: assigneeName })
            } else if (this.integrationType === 'Integration') {
              let app_name = this.$t('Integration')
              try {
                let custome_data = {}
                if (transaction?.custom_data) {
                  custome_data = JSON.parse(transaction.custom_data)
                }
                if (custome_data?.integration?.app_name) {
                  app_name = custome_data.integration.app_name
                }
              } catch (error) {
                console.error(error)
              }

              reopenMessage = isYou ?
                this.$t('activity_Integration_assigned_to_you', {integration: app_name})
                : this.$t('activity_Integration_assigned_to_user', { user: assigneeName, integration: app_name})
            }
            return reopenMessage
          },
          reassign: (userName) => {
            let reassignMessage = ''
            switch (this.integrationType) {
              case 'Jumio': {
                reassignMessage = this.$t('actor_assigned_this_Jumio_to_user', { user: userName, actor: reassignActor })
                break
              }
              case 'Integration': {
                let app_name = this.$t('Integration')
                try {
                  let custome_data = {}
                  if (transaction?.custom_data) {
                    custome_data = JSON.parse(transaction.custom_data)
                  }
                  if (custome_data?.integration?.app_name) {
                    app_name = custome_data.integration.app_name
                  }
                } catch (error) {
                  console.error(error)
                }

                reassignMessage = this.$t('actor_assigned_this_integration_to_user', { user: userName, integration: app_name, actor: reassignActor })
                break
              }
              default: {
                reassignMessage = this.$t('actor_assigned_this_Jumio_to_user', { user: userName, actor: reassignActor })
              }
            }
            return reassignMessage
          },
          create: () => typeStringMap[transaction.type].reopen(),
          update: () => {
            return this.$t('activity_Integration_assigned_to_user', { user: assigneeName })
          },
          markAsCompleted: () => {
            let actionType = this.$t('Integration')
            if (this.integrationType === 'Jumio') {
              actionType = this.$t('Identity_Verification')
            } else {
              try {
                if (transaction?.custom_data) {
                  let parsed = JSON.parse(transaction.custom_data)
                  if (parsed?.integration?.app_name) {
                    actionType = parsed.integration.app_name
                  }
                }
              } catch (error) {
                console.error(error)
              }
            }
            return this.$t('user_marked_as_completed', { user: actorName, actionType })
          }
        },
        TRANSACTION_TYPE_MEET_REQUEST:{
          today: this.$t('Meet_Request_due_today_activity'),
          getDateString: (date) => {
            return this.$t('Meet_Request_due_on_date_activity', { date })
          },
          reopen:()=>{
            return this.$t('actor_assigned_this_Meet_Request_to_user', { user: assigneeName,actor: reassignActor })
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_Meet_Request_to_user', {user: userName, actor: reassignActor})
          },
          create: (activity) => {
            const step = activity.recordBaseObject.steps[0]
            return isYou(step.assignee) ? this.$t('Meet_Request_assigned_to_you_activity') : this.$t('Meet_Request_assigned_to_user_activity', { user: step.assignee.name })
          },
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('meeting_request') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('meeting_request') })
            }
          },
          update: (activity) => {
            const step = activity.recordBaseObject.steps[0]
            return isYou(step.assignee) ? this.$t('Meet_Request_assigned_to_you_activity') : this.$t('Meet_Request_assigned_to_user_activity', { user: step.assignee.name })
          },
          updateReopen: this.$t('user_reopened_this_MeetRequest',{ user: actorName }),
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('Meeting_Request') })
          }
        },
        TRANSACTION_TYPE_TODO: {
          today: this.$t('Todo_due_today'),
          getDateString: (date) => {
            return this.$t('Todo_due_on_date', { date })
          },
          reopen: () => {
            const steps = ObjectUtils.getByPath(this.activity, 'recordBaseObject.steps')
            if (steps?.length >= 2) {
              const isParallel = steps[0].order_number === steps[steps.length - 1].order_number
              if (isParallel) {
                const hasMySelf = steps.some(s => isYou(s.assignee))
                const param = {
                  user: hasMySelf ? this.$t('you_lower_case') : assigneeName
                }
                if (steps.length === 2) {
                  return this.$t('activity_todo_assigned_to_one_and_another', param)
                } else {
                  return this.$t('activity_todo_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
                }
              }
            }
            // if (assigneeName === this.$t('you_lower_case')) {
            //   return this.$t('activity_Approval_assigned_to_you')
            // } else {
              return this.$t('activity_todo_assigned_to_user', { user: assigneeName })
            // }
          },
          reassign: (userName) => {
            return this.$t('actor_assigned_this_todo_to_user', {user: userName, actor: reassignActor})
          },
          create: () => typeStringMap[transaction.type].reopen(),
          update: () => {
            return this.$t('activity_todo_assigned_to_user', { user: assigneeName })
          },
          updateReopen: this.$t('activity_feed_todo_reopen', { user: actorName }),
          updateEditing: () => {
            if (isYou(this.activity.baseObject.editor)) {
              return this.$t('action_awaits_your_preparation_period', { action: this.$t('to_do') })
            } else {
              return this.$t('action_awaits_the_preparation_period', { action: this.$t('to_do') })
            }
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('to_do') })
          }
        },
        TRANSACTION_TYPE_DECISION: {
          today: this.$t('activity_Decision_due_today'),
          getDateString: (date) => {
            return this.$t('activity_Decision_due_on_date', { date })
          },
          reassign: (userName) => {
            return this.$t('activity_actor_assigned_this_Decision_to_user', {user: userName, actor: reassignActor})
          },
          reopen: () => {
            return this.$t('activity_Decision_assigned_to_user', { user: assigneeName })
          },
          create: () => {
            return this.$t('activity_Decision_assigned_to_user', { user: assigneeName })
          },
          update: () => {
            return this.$t('activity_Decision_assigned_to_user', { user: assigneeName })
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('Decision') })
          }
        },
        TRANSACTION_TYPE_AWAIT: {
          today: this.$t('activity_wait_due_today'),
          getDateString: (date) => {
            return this.$t('activity_wait_due_on_date', { date })
          },
          create: () => {
            return this.$t('activity_wait_assigned_to_user', { user: assigneeName })
          },
          update: () => {
            return this.$t('activity_wait_assigned_to_user', { user: assigneeName })
          },
          markAsCompleted: () => {
            return this.$t('user_marked_as_completed', { user: actorName, actionType: this.$t('Wait') })
          }
        }
      }
      if (this.activity.Action === 'UPDATE') {
        if (transaction.display_status) {
          return this.$t('feed_transaction_change_status', { status: transaction.display_status.text })
        } else if (transaction.status === 'TRANSACTION_STATUS_COMPLETED') {
          switch (transaction.type) {
            case 'TRANSACTION_TYPE_APPROVAL':
              return this.$t('activity_Approval_completed')
            case 'TRANSACTION_TYPE_ACKNOWLEDGE':
              return this.$t('activity_Acknowledgement_completed')
            case 'TRANSACTION_TYPE_FILE_REQUEST':
              return this.$t('activity_File_Request_completed')
            case 'TRANSACTION_TYPE_FORM_REQUEST':
              return this.$t('activity_Form_completed')
            case 'TRANSACTION_TYPE_PDF_FORM':
              return this.$t('activity_PDF_Form_completed')
            case 'TRANSACTION_TYPE_DOCUSIGN':
              return this.$t('activity_docusign_completed')
            case 'TRANSACTION_TYPE_LAUNCH_WEB_APP':
              return this.$t('activity_Launch_Web_App_completed')
            case 'TRANSACTION_TYPE_INTEGRATION': {
              return this.getIntegrationCompleteMsg(transaction)
            }
            case 'TRANSACTION_TYPE_MEET_REQUEST':
              return this.$t('activity_Meet_Request_completed')
            case 'TRANSACTION_TYPE_TODO':
              return this.$t('activity_todo_completed')
            case 'TRANSACTION_TYPE_DECISION':
              return this.$t('activity_Decision_completed')
            case 'TRANSACTION_TYPE_AWAIT':
              return this.$t('activity_wait_completed')
          }
        } else if (transaction.status === 'TRANSACTION_STATUS_CANCELED') {
          switch (transaction.type) {
            case 'TRANSACTION_TYPE_APPROVAL':
              return this.$t('activity_Approval_canceled')
            case 'TRANSACTION_TYPE_DOCUSIGN':
              return this.$t('activity_DocuSign_canceled')
            case 'TRANSACTION_TYPE_INTEGRATION': {
              return this.getIntegrationCancelMsg(transaction)
            }
            case 'TRANSACTION_TYPE_AWAIT':
              return this.$t('activity_wait_completed')
          }
        } else if (transaction.status === 'TRANSACTION_STATUS_ACTIVE') {
          if (transaction.type === 'TRANSACTION_TYPE_FILE_REQUEST') {
            if(transaction.steps[0]?.actions[0]?.type === "ACTION_TYPE_UPLOAD") {
              return isYou(transaction.steps[0]?.assignee) ? this.$t('activity_File_Request_assigned_to_you') : this.$t('activity_File_Request_assigned_to_user', { user: transaction.steps[0].assignee.name })
            } else {
              return this.$t('activity_File_Request_ready_for_review')
            }
          } else if (transaction.type === 'TRANSACTION_TYPE_APPROVAL') {
            if(transaction.steps[0]?.type === "STEP_TYPE_REVIEWER") {
              return isYou(transaction.steps[0]?.assignee) ? this.$t('Approval_awaiting_your_review') : this.$t('Approval_awaiting_review')
            } else {
              return typeStringMap[transaction.type].update()
            }
          }
          else {
            if (transaction.type === 'TRANSACTION_TYPE_LAUNCH_WEB_APP') {
              return this.$t('activity_Launch_Web_App_ready_for_review')
            }
            return typeStringMap[transaction.type].update(this.activity)
          }
        } else {
          const actor = isYou(this.activity.actor) ? this.$t('You') : this.activity.actor.name
          const map = {
            'FileRequest': this.$t('actor_edited_this_file_request', { actor }),
            'Acknowledge': this.$t('actor_edited_this_ack', { actor }),
            'Approval': this.$t('actor_edited_this_approval', { actor }),
            'FormRequest': this.$t('actor_edited_this_form', { actor }),
            'PdfForm': this.$t('actor_edited_this_pdf_form', { actor }),
            'Docusign': this.$t('actor_edited_this_docusign', { actor }),
            'LaunchWebApp': this.$t('actor_edited_this_launch_web_app', { actor }),
            'MeetRequest': this.$t('actor_edited_this_Meet_Request', { actor }),
            'Todo': this.$t('actor_edited_this_todo', { actor }),
            'Decision': this.$t('activity_actor_edited_this_Decision', { actor }),
            'Await': this.$t('actor_edited_this_wait', { actor }),
          }
          const integrationMap = {
            'Jumio': this.$t('actor_edited_this_jumio', { actor })
          }
          if (this.isIntegration) {
            if (this.integrationType === 'Jumio') {
              return integrationMap[this.integrationType]
            } else if (this.integrationType === 'Integration') {
              let app_name = this.$t('Integration')
              try {
                let custome_data = {}
                if (transaction?.custom_data) {
                  custome_data = JSON.parse(transaction.custom_data)
                }
                if (custome_data?.integration?.app_name) {
                  app_name = custome_data.integration.app_name
                }
              } catch (error) {
                console.error(error)
              }

              return this.$t('actor_edited_this_integration', {actor: actor, integration: app_name})
            }
          }
          return map[transaction.actionType]
        }
      } else if (this.activity.Action === 'STEP_SUBMIT') {
        let actualTrans = transaction
        if (transaction.type === 'TRANSACTION_TYPE_ACKNOWLEDGE' || transaction.type === 'TRANSACTION_TYPE_APPROVAL' || transaction.type === 'TRANSACTION_TYPE_TODO' || transaction.type === 'TRANSACTION_TYPE_FILE_REQUEST') {
          actualTrans = {
            ...transaction,
            steps: this.activity.recordBaseObject.steps
          }
        }
        if (transaction.type === 'TRANSACTION_TYPE_LAUNCH_WEB_APP') {
          actualTrans = {
            ...transaction,
            recordSteps: this.activity.recordBaseObject.steps
          }
        }
        const action = handleStepSubmit(actualTrans, this.activity?.recordBaseObject)

        if (action?.isApprovalApproved) {
          return this.$t('actor_approved_this_Approval', {
            actor: actorName
          })
        }

        if (action?.isApprovalDeclined) {
          return this.$t('actor_declined_this_Approval', {
            actor: actorName
          })
        }

        if (action?.isAcknowledged) {
          return this.$t('user_acknowledged_this_ack', {
            user: actorName
          })
        }

        if (action?.isFormSubmitted) {
          return this.$t('user_submitted_this_form', {
            user: actorName
          })
        }
        if(action?.isPdfFormSubmitted) {
          return this.$t('user_submitted_this_pdf_form', {
            user: actorName
          })
        }

        if (action?.isFileRequestFileUploaded) {
          const fileCount = getFileRequestSubmittedCount(this.activity)
          if (fileCount === 1) {
            return this.$t('actor_submitted_one_file', {
              actor: actorName
            })
          } else {
            return this.$t('actor_submitted_number_files', {
              actor: actorName,
              count: fileCount
            })
          }
        }

        if (action?.isFileRequestConfirmed) {
          return this.$t('user_reviewed', {
            user: actorName
          })
        }

        if (action?.isDocuSignReceived) {
          return `${this.$t('actor_will_receive_a_copy_by_email', {
            user: actorName
          })}.`
        }

        if (action?.isDocuSignApproved) {
          return this.$t('actor_signed_this_DocuSign', {
            user: actorName
          })
        }
        if (action?.isDocuSignDeclined) {
          return this.$t('actor_declined_this_DocuSign', {
            user: actorName
          })
        }

        if (action?.isLaunchWebAppLaunched) {
          return this.$t('user_launched_web_app', {
            user: actorName
          })
        }
        if (action?.isLaunchWebAppCompleted) {
          return this.$t('user_completed_this_Launch_Web_App', {
            user: actorName
          })
        }
        if (action?.isLaunchWebAppManuallyCompleted) {
          let msg
          if (actActor.id === originalAssignee.id) {
            msg = this.$t('user_manually_completed_this_Launch_Web_App', { user: actorName })
          } else {
            msg = this.$t('user_manually_completed_this_Launch_Web_App_on_behalf', { user: actorName, assignee: assigneeName })
          }
          return msg
        }
        if (action?.isIntegrationSumbitted) {
          switch (action.integrationType) {
            case 'Jumio': {
              const stepAssignee = transaction.steps[0].assignee
              let manualCompleteStr = ''
              const user = isYou(this.activity.actor) ? this.$t('You') : this.activity.actor.name
              if (stepAssignee.id === this.activity.actor.id) {
                manualCompleteStr = this.$t('assignee_manually_comleted_this_Jumio_activity', { user })
              } else {
                const assignee = stepAssignee.id === this.currentUser.id ? this.$t('you_lower_case') :stepAssignee.name
                manualCompleteStr = this.$t('user_manually_comleted_this_Jumio_activity', { user, assignee })
              }
              return action.isManualComplete ? manualCompleteStr : this.$t('actor_has_started_this_Jumio', { actor: user })
            }
            case 'Integration': {
              let app_name = this.$t('Integration')
              try {
                let custome_data = {}
                if (transaction?.custom_data) {
                  custome_data = JSON.parse(transaction.custom_data)
                }
                if (custome_data?.integration?.app_name) {
                  app_name = custome_data.integration.app_name
                }
              } catch (error) {
                console.error(error)
              }

              const stepAssignee = transaction.steps[0].assignee
              const user = isYou(this.activity.actor) ? this.$t('You') : this.activity.actor.name
              if (stepAssignee.id === this.activity.actor.id) {
                if (action.isManualComplete) {
                  return this.$t('assignee_completed_this_Integration_activity', {user: user, integration: app_name})
                } else if (action.isManualCancel) {
                  return this.$t('assignee_declined_this_Integration_activity', {user: user, integration: app_name})
                }
              } else {
                const assignee = stepAssignee.id === this.currentUser.id ? this.$t('you_lower_case') :stepAssignee.name
                if (action.isManualComplete) {
                  return this.$t('user_completed_this_Integration_activity', {user: user, integration: app_name, assignee: assignee})
                } else if (action.isManualCancel) {
                  return this.$t('user_declined_this_Integration_activity', {user: user, integration: app_name, assignee: assignee})
                }
              }
            }
            default: {
              return this.$t('actor_has_started_this_Jumio', { actor: actorName})
            }
          }
        }
        if(action?.isMeetRequestConfirmed){
          return this.$t('user_confirmed_a_meeting_activity', {user:actorName})
        }
        if(action?.isTodoCompleted){
          return this.$t('user_completed_this_todo', {user:actorName})
        }
        if (action?.isDecisionMade) {
          const actionResult = transaction.steps[0].actions.find(actionItem => actionItem.id === transaction.custom_result)
          return this.$t('activity_actor_made_a_decision', { user:actorName, result: actionResult.text })
        }
        if (action?.isAwaitMarkedAsCompleted) {
          return this.$t('user_marked_wait_completed_period', {user:actorName})
        }
        // Default handling
        const step = transaction.steps[0]
        const actionLog = step.action_logs[0]
        for (let i = 0; i < step.actions.length; i++) {
          if (step.actions[i].id === actionLog.click_btn_id) {
            const prefix = this.activity.actor?.id === this.currentUser.id ? this.$t('You') : this.activity.actor?.name
            return prefix + ' ' + step.actions[i].feed_msg
          }
        }
      } else if (this.activity.Action === 'EXPIRATION_DATE_ARRIVE') {
        const expirationDate = transaction.expiration_date
        const { isNear, date } = util.computeDueDate(expirationDate, {
          today: typeStringMap[transaction.type].today
        })

        if (isNear) {
          return date
        } else {
          return typeStringMap[transaction.type].getDateString(date)
        }
      } else if (this.activity.Action === 'STEP_REOPEN') {
        return this.$t('User_reopened_this_file_request', {
          user: actorName
        })
      } else if (this.activity.Action === 'EXPIRATION_UPDATE') {
        const actor = this.activity.actor
        const namePlaceholder = isYou(actor) ? this.$t('You') : actor.name

        if (transaction.expiration_date) {
          const { isNear, date } = util.computeDueDate(transaction.expiration_date, {
            today: this.$t('today')
          })

          const params = {
            user: namePlaceholder,
            due_date: date
          }

          return !isYou(actor) ? this.$t('set_a_due_date', params) : this.$t('Current_user_set_a_due_date', params)
        } else {
          return this.$t('remove_due_date', {
            user: namePlaceholder
          })
        }
      } else if (this.activity.Action === 'DELETE') {
        const map = {
          'FileRequest': 'user_deleted_a_File_Request',
          'Acknowledge': 'user_deleted_an_Acknowledgment',
          'Approval': 'user_deleted_an_Approval',
          'FormRequest': 'user_deleted_a_form',
          'PdfForm': 'user_deleted_a_pdf_form',
          'Todo': 'user_deleted_a_todo',
          'Decision': 'user_deleted_a_Decision'
        }
        const key = map[this.activity.baseObject.actionType] || 'timeline_feed_transaction_delete'
        return this.$t(key, { user: this.activity.actor.name })
      } else if (this.activity.Action === 'REOPEN') {
        return typeStringMap[transaction.type].reopen()
      } else if (this.activity.Action === 'REASSIGN') {
        const editor = this.activity.recordBaseObject.editor
        const newAssignedUser = editor ? editor : originalAssignee
        const newAssignedUserName = isYou(newAssignedUser) ? this.$t('you_lower_case') : newAssignedUser?.name
        return typeStringMap[transaction.type].reassign(newAssignedUserName)
      } else if (this.activity.Action === 'RESEND_REMINDER') {
        const assignee = this.activity.baseObject.steps?.[0]?.assignee || this.activity.baseObject.editor
        if (!assignee) {
          return this.$t('reminder_sent')
        }
        const assigneeName = isYou(assignee) ? this.$t('you_lower_case') : (assignee.name || assignee.email)
       
        return this.$t('reminder_sent_to_someone', {someone: assigneeName})
      } else if (this.activity.Action === 'CREATE') {
        return typeStringMap[transaction.type].create(this.activity)
      } else if (this.activity.Action === 'UPDATE_CUSTOM_RESULT') {
        return this.activity.baseObject.custom_result
      } else if (this.activity.Action === 'UPDATE_REOPEN') {
        return typeStringMap[transaction.type].updateReopen
      } else if (this.activity.Action === 'UPDATE_EDITING') {
        return typeStringMap[transaction.type].updateEditing()
      } else if (this.activity.Action === 'UPDATE_READY') {
        const editor = this.activity.baseObject.editor
        const actorText = isYou(editor) ? this.$t('You') : editor.name
        return this.$t('user_completed_the_preparation', { user: actorText })
      } else if (this.activity.Action === 'MARK_AS_COMPLETED') {
        return typeStringMap[transaction.type].markAsCompleted()
      }
    },
    _getTodoMessage () {
      const actor = this.activity.actor
      const todo = ObjectUtils.getByPath(this.activity, 'baseObject')
      // 'ASSIGN','ATTACHMENT','DUE_DATE'
      const userName = isYou(actor) ? this.$t('You') : actor.name
      if (this.activity.Action === 'COMPLETE') {
        return this.$t('user_completed_this_todo', { user: userName })
      } else if (this.activity.Action === 'MARK_AS_COMPLETED') {
        return this.$t('user_marked_as_completed', { user: userName, actionType: this.$t('to_do') })
      } else if (this.activity.Action === 'REOPEN') {
        return this.$t('activity_feed_todo_reopen', { user: userName })
      } else if (this.activity.Action === 'UPDATE') {
        return this.$t('actor_edited_this_todo', { actor: userName })
      } else if (this.activity.Action === 'DUE_DATE_ARRIVE') {
        const { isNear, date } = util.computeDueDate(todo.due_date, {
          today: this.$t('Todo_due_today')
        })
        if (isNear) {
          return date
        } else {
          return this.$t('Todo_due_on_date', { date })
        }
      } else if (this.activity.Action === 'DUE_DATE') {
        const namePlaceholder = isYou(actor) ? this.$t('You') : actor.name
        if (todo.due_date) {
          const { isNear, date } = util.computeDueDate(todo.due_date, { today: this.$t('today') })
          const params = {
            user: namePlaceholder,
            due_date: date
          }
          return !isYou(actor) ? this.$t('set_a_due_date', params) : this.$t('Current_user_set_a_due_date', params)
        } else {
          return this.$t('remove_due_date', { user: namePlaceholder })
        }
      } else if (this.activity.Action === 'DELETE') {
        return this.$t('user_deleted_a_todo', { user: this.activity.actor.name })
      } else if (this.activity.Action === 'ASSIGN') {
        const assignee = ObjectUtils.getByPath(todo, 'recordTodoAssignee')
        if (assignee) {
          const assigneeName = isYou(assignee) ? this.$t('you_lower_case') : assignee.name
          return this.$t('activity_todo_assigned_to_user', { user: assigneeName })
        } else {
          return this.$t('activity_feed_todo_remove_assign', { user: userName })
        }
      } else if (this.activity.Action === 'REASSIGN') {
        const isAdmin = ObjectUtils.getByPath(this, 'binderObj.boardUsers')?.findIndex(user => {
          return user.id === actor.id
        }) < 0
        const reassignActor = isAdmin ? this.$t('admin') : userName
        const assignee = todo.recordTodoAssignee || todo.assignee
        const assigneeName = isYou(assignee)? this.$t('you_lower_case') : assignee.name
        return this.$t('actor_assigned_this_todo_to_user', { actor: reassignActor, user: assigneeName })
      }
    },

    getDocusignAssignedTxt (steps) {
      let signByOrder = true
      if (steps.length === 1) {
        signByOrder = true
      } else {
        signByOrder = steps[0].order_number !== steps[steps.length - 1].order_number
      }
      let user = isYou(steps[0].assignee) ? this.$t('you_lower_case') : steps[0].assignee.name
      if (!signByOrder) {
        const isUseYou = steps.some(s => {
          return isYou(s.assignee)
        })
        if (isUseYou) {
          user = this.$t('you_lower_case')
        }
        return steps.length > 2 ? this.$t('activity_DocuSign_assigned_to_one_and_others', { user, number: steps.length - 1 }) : this.$t('activity_DocuSign_assigned_to_one_and_another', { user })
      } else {
        return this.$t('activity_DocuSign_assigned_to_user', { user })
      }
    },
    getIntegrationCompleteMsg (transaction) {
      let message = ''
      switch (this.integrationType) {
        case 'Jumio': {
          message = this.$t('Jumio_completed_activity')
          break
        }
        case 'Integration': {
          let app_name = this.$t('Integration')
          try {
            let custome_data = {}
            if (transaction?.custom_data) {
              custome_data = JSON.parse(transaction.custom_data)
            }
            if (custome_data?.integration?.app_name) {
              app_name = custome_data.integration.app_name
            }
          } catch (error) {
            console.error(error)
          }

          message = this.$t('Integration_completed_activity', {integration: app_name})
          break
        }
        default: {
          message = this.$t('Jumio_completed_activity')
        }
      }
      return message
    },
    getIntegrationCancelMsg (transaction) {
      let message = ''
      switch (this.integrationType) {
        case 'Jumio': {
          message = this.$t('Jumio_canceled_activity')
          break
        }
        case 'Integration': {
          let app_name = this.$t('Integration')
          try {
            let custome_data = {}
            if (transaction?.custom_data) {
              custome_data = JSON.parse(transaction.custom_data)
            }
            if (custome_data?.integration?.app_name) {
              app_name = custome_data.integration.app_name
            }
          } catch (error) {
            console.error(error)
          }

          message = this.$t('Integration_canceled_activity', {integration: app_name})
          break
        }
        default: {
          message = this.$t('Jumio_canceled_activity')
        }
      }
      return message
    },
    getPdfFormAssignedActivityText (activity, firstStepAssignee) {
      const steps = ObjectUtils.getByPath(activity, 'recordBaseObject.steps')
      const assigneeName = firstStepAssignee?.name
      if (steps?.length >= 2) {
        const hasMySelf = steps.some(s => isYou(s.assignee))
        const param = {
          user: hasMySelf ? this.$t('you_lower_case') : assigneeName
        }
        if (steps.length === 2) {
          return this.$t('activity_PDF_Form_assigned_to_one_and_another', param)
        } else {
          return this.$t('activity_PDF_Form_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
        }
      }
      if (isYou(firstStepAssignee)) {
        return this.$t('activity_pdf_form_assigned_to_you')
      } else {
        return this.$t('activity_pdf_form_assigned_to_user', { user: assigneeName })
      }
    },
    getFormAssignedActivityText (activity, firstStepAssignee) {
      const steps = ObjectUtils.getByPath(activity, 'recordBaseObject.steps')
      const assigneeName = firstStepAssignee?.name
      if (steps?.length >= 2) {
        const hasMySelf = steps.some(s => isYou(s.assignee))
        const param = {
          user: hasMySelf ? this.$t('you_lower_case') : assigneeName
        }
        if (steps.length === 2) {
          return this.$t('activity_Form_assigned_to_one_and_another', param)
        } else {
          return this.$t('activity_Form_assigned_to_one_and_others', { ...param, number: steps.length - 1 })
        }
      }
      if (isYou(firstStepAssignee)) {
        return this.$t('activity_form_assigned_to_you')
      } else {
        return this.$t('activity_form_assigned_to_user', { user: assigneeName })
      }
    }
  }
}
</script>

<style
    lang="scss"
    scoped>
.attachment-name {
  &.mx-clickable {
    &:hover {
      text-decoration: underline;
    }
  }
}

.flow-activity-comment.is-transaction-update-activity {
  display: block;
}

.spinner-container {
  position: relative;
  width: 20px;
  height: 20px;
}

.mx-reply-delivery-failed {
  display: flex;
  align-items: center;
  margin-top: 3px;

  .failed-icon {
    display: flex;
    color: $mx-color-var-negative;
    margin-right: 10px;

    button {
      border: 0;
      background: transparent;
      font-size: 14px;
      font-weight: bold;
      padding: 0;
    }
  }

  a {
    margin-left: 3px;
  }
}

.indicator {
  width: 8px;
  height: 8px;
  margin: 6px 20px 0 12px;
  float: left;
}

.info {
  padding-left: 40px;
  .message {
    font-size: 14px;
    line-height: 20px;
  }
  .date {
    color: $mx-color-var-text-secondary;
    font-size: 12px;
    line-height: 16px;
  }
}
.flow-comment-container {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  .flow-activity-comment-action {
    align-self: flex-start
  }
  .flow-comment-reaction-selector {
    margin-top: auto;
    margin-bottom: auto;
  }
  ::v-deep {
    .micon-reaction {
      font-size: 18px;
    } 
  }
  &.with-attachment {
    align-self: flex-end;
    .flow-comment-reaction-selector {
      margin-bottom: 4px;
    }
  }
}
</style>
