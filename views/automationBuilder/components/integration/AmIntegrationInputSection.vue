<template>
  <div>
    <MxDDRTextDisplay 
      :text="compPreviewDes"
      :options="{parseLink: false}" />
  </div>
</template>
<script>
  import MxDDRTextDisplay from '@views/ddr/MxDDRTextDisplay.vue'
  export default {
    name: 'AmIntegrationInputSection',
    components: {
      MxDDRTextDisplay
    },
    props:{
      amAction:{  // EventActionInfo-> GmailAmData
        type:Object
      },
    },
    data (){
      return {
      }
    },
    computed:{
      compPreviewDes () {
        return this.amAction.data?.preview_desc || this.$t('No_description')
      },
    }
  }
</script>