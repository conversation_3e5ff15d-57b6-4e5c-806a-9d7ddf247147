import {defineStore} from 'pinia';
import {Defines, MxISDK} from 'isdk';
import {BoardController, FileController, TransactionController, WorkflowBoardController} from '@newController/index';
import {ICreateTransactionAttachmentParam, MOCK_BOARD_PREFIX, MxBaseObject, MxInviteMemberOption, MxSPath, User, BoardTransaction, MxBoardOption } from 'isdk/src/api/defines';
import { IBoardTeamViewModel, IBoardUserViewModel, UserObjectType } from '@model/board/boardUserViewModel';
import { UserFormatter } from '@controller/utils/user';
import { IAttachmentViewModel } from '@model/baseObjects/defines/fileViewModel';
import { BoardSession } from 'isdk/src/proto/generated/BoardSession';
import { ThreadController } from '@newController/index';
import { ICreateWhiteBoardParam } from '@newController/defines/fileController';
import { IBoardBriefViewModel, IBoardShareToken } from '@model/board/boardViewModel';
import vuexStore from '@appStore'
import { WorkspaceNotificationSettings } from '@controller/defines/WorkspaceNotificationSettings';
import {BinderController} from '@controller/binder/src/binderController'
import {getRefFormFieldsFromFormModel, RefFormField} from "@controller/flowLibrary/src/WfRefForm";
import {CFormModel} from "@controller/defines/CFormModel";
import { BoardPropertyUpdatedCallback, IPendingActions, ITransferActionOption } from '@newController/defines/boardController'
import {IBoardAssigneeViewModel} from "@model/board/boardViewModel";
import { IBoardPropertyViewModel } from '@model/board/boardPropertyViewModel'
import { IntegrationAppViewModel } from '@model/workflow/defines/IntegrationViewModel'
import {getAvaliableName} from '@commonUtils/helper';
import { IntegrationCenterController, WorkflowTemplateController } from '@newController/index'
import { IntegrationDefines } from 'integrationSDK'
import { getCacheOrInstantBoard } from '@newController/utils'
import { IWorkflowViewModel } from '@model/workflow';
import { ActionController, IResendActionReminderParam } from '@newController/index'


// from old useFlowWelcome.ts
import get from 'lodash/get'
import {useAnonymousUser} from '@controller/user'

/*
    To make the caller easier and the new controllers are stateless, this store is defined to act as
    the proxy to calling those controllers APIs directly.
    It needs the caller to provide the boardId and other necessary parameters.

    Then these code can be used by the both flow workspace and the normal workspace.

    Attention: only keep those actions which is common in two(or all) types of workspace in this store.
*/

export const useBoardCommonActionsStore = defineStore('boardCommonActions', {
    actions:{
        inviteMember (boardId: string, users: Defines.UserIdentity[], teamIds?: string[], option?: MxInviteMemberOption): Promise<IBoardUserViewModel[]>
        {
            return BoardController.inviteMember(boardId, users, teamIds, option)
        },
        inviteTeamMember (boardId: string, users: Defines.UserIdentity[], teamIds?: string[],  needFeedback?: boolean){
            return BoardController.inviteMember(boardId,users,teamIds || [],{noFeed: needFeedback?needFeedback:false});
        },
        removeMember (boardId: string, user?: IBoardUserViewModel, team?: IBoardTeamViewModel) {
            if(user)
                return BoardController.removeMember(boardId, [user.sequence]);
            else if(team){
                return BoardController.removeTeam(boardId,[team.sequence]);
            }
            return false;
        },
        transforOwner (baordId: string, user?: IBoardUserViewModel){
            return BoardController.transferOwner(baordId,user?.sequence);
        },
        resendInvite (boardId: string,  user?: Defines.UserIdentity){
            return BoardController.resendInvitation(boardId,user);
        },
        async transferActionToMySelf(boardId: string, actionInfo: ITransferActionOption){
            const mxUser = MxISDK.getCurrentUser()
            let boardUser =  await BoardController.getMyUserInfoFormBoard(boardId)
            const opts = {
                ...actionInfo,
                toAssignee: {
                    id: mxUser.id,
                    user:boardUser
                } as IBoardAssigneeViewModel,
                suppressFeed: true
            };
            return BoardController.transferAction(boardId, opts)
        },
        //This is pure common API for any place!!!
        //TODO: this action should belong to global user stores
        getBoardMemberRoles (users: Defines.UserIdentity[]){
            return MxISDK.getCurrentOrg().readMembers(users).then(group => {
                return group.members && group.members.map(member => {
                    return {
                        roleSequence: member.role,
                        isInternalUser: UserFormatter.isInternalUser(member.user.type),
                        id: member.user.id
                    }
                })
            })
        },
        leaveBoard (boardId: string) {
            return BoardController.leaveBoard(boardId)
        },
        deleteBoard (boardId: string) {
            return BoardController.deleteBoard(boardId)
        },
        updateAccessTime (boardId: string, keepUnreadFeedTime?: boolean): Promise<User> {
            return BoardController.updateAccessTime(boardId, keepUnreadFeedTime)
        },
        markFeedAsUnRead (boardId: string, feedSeq: number) {
            return BoardController.markFeedAsUnRead(boardId, feedSeq)
        },
        createWhiteboard (boardId: string, width: number, height: number , folderPath: string,fileName: string){
            return FileController.getAvailableFileName(boardId,folderPath,fileName).then((availFileName)=>{
                const param: ICreateWhiteBoardParam = {fileName:availFileName,width,height,folderPath};
                return FileController.createWhiteboard(boardId,param);
            });
        },
        getDownloadBoardFolderURL (boardId: string, type: string, fileName: string, folder: number, files?: [], pages?: []){
            if(type === 'pdf'){
                return `/board/${boardId}/download?type=pdf&pages=${pages.map(item => item.sequence).join(',')}&d=${fileName}`

            }else{
                return  `/board/${boardId}/download?type=zip&files=${files.map(item => item.sequence).join(',')}&d=${fileName}`
            }
        },
        readFileDetail (boardId: string, fileSPath: string){
            return FileController.readFileDetail(boardId,fileSPath);
        },
        resendActionReminder (boardId: string, param: IResendActionReminderParam) {
            return ActionController.resendActionReminder(boardId, param)
        },
        deleteFiles (boardId: string,files?: MxSPath[], signatureSequence?: number){
            if(signatureSequence){
                return FileController.deleteSignatureFile(boardId,signatureSequence);
            }else{
                return FileController.deleteFiles(boardId,files);
            }
        },
        getAvailableFilesName (boardId, files, toFolderSPath) {
            return FileController.listFolder(boardId, toFolderSPath).then(([res])=>{
                if (res && res.files) {
                    res.files = res.files.filter(f => !f.isDeleted);
                }
            
                const existingFiles = res.files || []
                let existingNames = (vuexStore.state['uploader/files']||[]).map(f => f.name)
                existingNames  = existingNames.concat(existingFiles.map((file)=>{return file.name}))

                const newFiles = files.map((file) => {
                    const info = {sequence:null, name:file.name}
                    if (file.sequence) {
                        info.sequence = file.sequence
                    }
                    info.name = getAvaliableName(file.name, existingNames)
                    return info
                })

                return newFiles
            })
        },
        makeUploadUrl (boardId: string, fileName: string, toFolderSPath: string) {
            return FileController.makeUploadFileUrl(boardId, fileName, toFolderSPath);
        },
        makeUploadToTransactionUrl ({uid, fileName, transactionSeq, boardId, isSupportFile, customData}: { transactionSeq: number; fileName: string; uid?: string; boardId: string; isSupportFile?: boolean; customData?: string}) {
            let url = `${MxISDK.getContextPath()}/board/${boardId}/attach/transaction/${transactionSeq}/${encodeURIComponent(fileName)}?type=original&client_uuid=${uid}`
            if (isSupportFile) {
                url += '&reference_type=support'
            }
            if (customData) {
                url += `&custom_data=${encodeURIComponent(customData)}`
            }
            return url
        },
        createTransactionAttachmentFromFile (fromBoardId: string, files: MxSPath[], toBoardId: string, toTransactionSeq: number, opt?: ICreateTransactionAttachmentParam): Promise<[IAttachmentViewModel[], Defines.Board]> {
            return TransactionController.createTransactionAttachmentFromFile(fromBoardId, files, toBoardId, toTransactionSeq, opt)
        },
        removeTransactionAttachment (boardId: string, transactionSeq: number, attachmentSeqs: number[]): Promise<void> {
            return TransactionController.removeTransactionAttachment(boardId, transactionSeq, attachmentSeqs)
        },
        checkBoardTransaction (payload) {
            return BoardController.checkBoardTransaction(payload)
        },
        checkBoardObject (payload) {
            return BoardController.checkBoardObject(payload.binderId, payload.objectSeq, payload.objectType)
        },
        queryBoardSessions (boardId: string): Promise<BoardSession[]> {
            return BoardController.queryBoardSessions(boardId)
        },
        getBoardUserActivities (boardId: string) {
            return BoardController.getUserActivities(boardId);
        },
        async getCommentBaseObject (boardId: string, commentSeq: number): Promise<MxBaseObject> {
            return ThreadController.getCommentBaseObject(boardId, commentSeq)
        },
        async getFeedBaseObject (boardId: string, sequence: number): Promise<MxBaseObject>  {
            return ThreadController.getFeedBaseObject(boardId,sequence);
        },
        async getFileBaseObject (boardId: string, filePath: MxSPath): Promise<MxBaseObject>  {
            return ThreadController.getFileBaseObject(boardId,filePath);
        },
        async getMeetBaseObject (boardId: string, sequence: number): Promise<MxBaseObject> {
            return ThreadController.getMeetBaseObject(boardId, sequence)
        },
        async getPageBaseObject (boardId: string, sequence: number): Promise<MxBaseObject>  {
            return ThreadController.getPageBaseObject(boardId,sequence);
        },
        async getSignatureBaseObject (boardId: string, signatureSeq: number): Promise<MxBaseObject>  {
            return ThreadController.getSignatureBaseObject(boardId, signatureSeq)
        },
        async getTodoBaseObject (boardId: string, todoSequence: number): Promise<MxBaseObject>  {
            return ThreadController.getTodoBaseObject(boardId, todoSequence)
        },
        async getTransactionBaseObject (boardId: string, transactionSequence: number): Promise<MxBaseObject>  {
            return ThreadController.getTransactionBaseObject(boardId, transactionSequence)
        },
        isBaseObjectDeleted (boardId: string, baseObject: MxBaseObject) {
            return ThreadController.isBaseObjectDeleted(boardId,baseObject);
        },
        canLeaveBinder (boardBriefViewModel: IBoardBriefViewModel,activeAssigneeIds: string[]){
            if (!boardBriefViewModel?.boardId) {
                return false
            }
            const privileges = vuexStore.state.privileges.privileges;
            const curUser = vuexStore.state.user.userBasicInfo;
            const requiredGroupWSClientCounts = vuexStore.getters['group/requiredGroupWorkspaceClientCounts']

            if (boardBriefViewModel.isGroupWorkspace && requiredGroupWSClientCounts > 0 && !boardBriefViewModel.isMyselfBinderOwner) {
              return curUser.isInternalUser
            }

            if (boardBriefViewModel.isMocked || boardBriefViewModel.isMyselfBinderOwner || privileges.disableLeaveChat || (!boardBriefViewModel.isSocialSuspended && boardBriefViewModel.isNoAction)) {
              return false
            }
            if (boardBriefViewModel.isConversation) {
              // 1 on 1 chat between Client and RM, the guest internal user can leave chat
              if (boardBriefViewModel.isExternal && !boardBriefViewModel.isMyselfBinderOwner && curUser.isInternalUser) {
                return true
              } else {
                return false
              }
            }
            let myBinderUserInfo
            const users = boardBriefViewModel.users.filter(u => !u.isBot)
            for (const binderUser of users) {
              if (binderUser.userId === curUser.id || (binderUser.email && binderUser.email === curUser.email)) {
                myBinderUserInfo = binderUser
                break
              }
            }
            if (myBinderUserInfo && myBinderUserInfo.isFromTeam) {
              return false
            }
            if (boardBriefViewModel.isSocialSuspended) {
              return true
            }
            if (boardBriefViewModel.isRelation) {
              return curUser.isInternalUser && !boardBriefViewModel.isMyselfBinderOwner
            } else {
              if (myBinderUserInfo) {
                return !myBinderUserInfo.isClientUser
              } else {
                return false
              }
            }
        },
        canDeleteBinder (boardBriefViewModel: IBoardBriefViewModel){
            const requiredGroupWSClientCounts = vuexStore.getters['group/requiredGroupWorkspaceClientCounts']
            if(boardBriefViewModel.isGroupWorkspace && requiredGroupWSClientCounts >0){
                return false;
            }
            const privileges = vuexStore.state.privileges.privileges;
            return !boardBriefViewModel.isConversation && !boardBriefViewModel.isRelation
                    && !boardBriefViewModel.isSocial && boardBriefViewModel.isMyselfBinderOwner
                    && privileges.canStartChat
        },
        canEditBinderName (boardBriefViewModel: IBoardBriefViewModel) {
            const privileges = vuexStore.state.privileges.privileges;
            const isInternal = vuexStore.state.user.userBasicInfo.isInternalUser;
            return !boardBriefViewModel.isSocial && !boardBriefViewModel.isRelation
                    && (boardBriefViewModel.isMyselfBinderOwner || isInternal)
                    && !boardBriefViewModel.isConversation
                    && privileges.canRenameConversation
        },
        clientShowConversationEamilAddress (boardBriefViewModel: IBoardBriefViewModel) {
            const isInternal = vuexStore.state.user.userBasicInfo.isInternalUser;
            const tags = vuexStore.getters['group/groupTags']
            if(isInternal){
              return false
            }
            if(!tags['Enable_Conversation_Email_Address_for_Client']){
              return false
            }
            if (boardBriefViewModel.isAcd) {
              return false
            }else if(boardBriefViewModel.isServiceRequest && !boardBriefViewModel.isWorkflow){
                return false
            }else if (boardBriefViewModel.socialType) {
                return false
            } else if (boardBriefViewModel.isInbox) {
                return false
            } else if(boardBriefViewModel.isChannelSubscription){
                return false
            } else if (boardBriefViewModel.isLive) {
                return false
            } else if (boardBriefViewModel.isRelation || boardBriefViewModel.isConversation) {
                return true
            } else {
                return true
            }
        },
        clientShowNotificationSetting (boardBriefViewModel: IBoardBriefViewModel){

            const showNotificationSetting = vuexStore.getters['privileges/showNotificationSetting']
            if(!showNotificationSetting){
              return false
            }
            const isInternal = vuexStore.state.user.userBasicInfo.isInternalUser;
            if(isInternal){
              return false
            }
            const tags = vuexStore.getters['group/groupTags']
            if(!tags['Enable_Client_Notification_Settings']){
              return false
            }
            if (boardBriefViewModel.isAcd) {
                return false
            } else if(boardBriefViewModel.isServiceRequest && !boardBriefViewModel.isWorkflow){
                return false
            } else if (boardBriefViewModel.socialType) {
                return false
            } else if (boardBriefViewModel.isLive) {
                return false
            }else if(boardBriefViewModel.isChannelSubscription){ //TODO: also need to check other two flags???
                return false
            }else if (boardBriefViewModel.isInbox) {
                return true
            } else if (boardBriefViewModel.isRelation || boardBriefViewModel.isConversation) {
                return true
            } else {
                return true
            }
        },
        async queryPendingWorksForMembers (boardBriefViewModel: IBoardBriefViewModel): Promise<Record<string, IPendingActions>>{
            if(!boardBriefViewModel) return {};
            const allUserIds = [];
            boardBriefViewModel.users?.forEach(user =>{
                !user.isBot && allUserIds.push(user.userId);
            })
            if(boardBriefViewModel.isWorkflow){
                return await WorkflowBoardController.queryPendingActions(boardBriefViewModel.boardId,allUserIds);
            }
            else{
                return await BoardController.queryPendingActions(boardBriefViewModel.boardId,allUserIds);
            }
        },
        getUserBoardInfo  (boardId: string) {
            return vuexStore.dispatch('user/getTransformedUserBoard', boardId, { root: true })
        },
        getBoardTag (boardId: string,key: string) {
            return BoardController.getTag(boardId, key);
        },
        getBoardType (boardId: string) {
            return BinderController.getBoardType(boardId)
        },
        createUpdateBoardTag (boardId: string, tag: {name: string; value: string|number, useKeyAsClientUuid?: boolean}) {    
            return BoardController.createUpdateTag(boardId, tag);
        },
        async createAIChatBoard () {
            const mxUser = MxISDK.getCurrentUser()
            try {
                const aiBotUser = vuexStore.getters['group/aiBotUser']
                if (!aiBotUser || !aiBotUser.id) {
                    console.log('no ai bot user found')
                    return ''
                }
    
                const boardId = await BoardController.createBoard({
                    isAIChat: true,
                    name: 'AI Assistant',
                    users: [{
                        objectType: UserObjectType.User,
                        userId: aiBotUser.id,
                    }]
                })

                await mxUser.createOrUpdateTag('AI_chat_board_id', boardId)
                return boardId
            } catch (e) {
                console.error(e)
                return ''
            }
        },
        deleteAIChatBoard () {
            const mxUser = MxISDK.getCurrentUser()

            const AI_chat_board_id =mxUser.getTag('AI_chat_board_id') as string
            if (!AI_chat_board_id) {
                console.log('no AI_chat_board_id tag found')
                return Promise.resolve()
            }

            return BoardController.deleteBoard(AI_chat_board_id).then(() => {
                console.log('delete AI chat board', AI_chat_board_id)
                return mxUser.deleteTag('AI_chat_board_id').then(() => {
                    console.log('delete AI_chat_board_id tag')
                })
            })
        },
        updateBoardName (boardId: string, newName: string) {
            return BoardController.updateBoardBasicInfo(boardId, {name:newName});
        },
        updateBoardDescription (boardId: string, description: string) {
            return BoardController.updateBoardBasicInfo(boardId, {description})
        },
        updateBoardDueDate (boardId: string, dueDate: number) {
            return WorkflowBoardController.updateBoardDueDate(boardId, dueDate);
        },
        updateBoardPinEditorType (boardId: string, newType: Defines.BoardEditorType): Promise<void> {
            return BoardController.updateBoardPinEditorType(boardId, newType);
        },
        updateNotificationLevel (boardId: string, level: Defines.NotificationLevel) {
            return BoardController.updateNotificationLevel(boardId, level);
        },
        verifyFilevineIDAndSave (boardId: string, filevineID: string, save?: boolean): Promise<void> {
            return BoardController.verifyFilevineIDAndSave(boardId,filevineID,save);
        },
        updateNotificationSetting (boardId: string, notificationSetting: WorkspaceNotificationSettings) {
            return BoardController.updateNotificationSetting(boardId,notificationSetting);

        },
        updateTransaction (boardId: string, payload: {transactionSeq: number; transaction: Defines.BoardTransaction; suppressFeed?: boolean}) {
            return BoardController.updateTransaction(boardId, payload.transactionSeq, payload.transaction, payload.suppressFeed)
        },
        updateSignature (boardId: string, payload: {sequence: number; signature: Defines.BoardSignature}) {
            return BoardController.updateSignature(boardId, payload.sequence, payload.signature)
        },
        getBoardShareTokenInfo (boardId: string): Promise<IBoardShareToken> {
            return new Promise((resolve, reject)=>{
                return BoardController.getShareToken(boardId).then(token=> {
                    resolve(token);
                });
            })
        },
        generateBoardShareLink (boardId: string, isMemberOnly: boolean, isOnlyCopy?: boolean): Promise<string> {
            return BoardController.generateShareLink (boardId, isMemberOnly, isOnlyCopy);
        },
        transferBoardOwner (boardId: string, ownerUserSeq: number): Promise<void>{
            return BoardController.transferOwner(boardId, ownerUserSeq);
        },
        requestJoinBoard ({boardId, viewToken, verifyCode, user, code, googleJWT, appleJWT}) {
            return BinderController.requestJoinBoard(boardId, viewToken, user, {verifyCode, googleJWT, appleJWT}, code)
        },
        detectCurrentBoard (boardId: string) {
            return BinderController.detectCurrentBoard(boardId)
        },
        decodeBoardViewToken (viewToken: string) {
            return BinderController.decodeBoardViewToken(viewToken)
        },
        createComment (boardId: string, comment: Defines.BoardComment): Promise<Defines.Board> {
            return BoardController.createComment(boardId, comment)
        },
        createPin (boardId: string, baseObject: MxBaseObject): Promise<Defines.Board>  {
            return BoardController.createPin(boardId, baseObject)
        },
        deletePin (boardId: string, baseObject: MxBaseObject): Promise<Defines.Board>  {
            return BoardController.deletePin(boardId, baseObject)
        },
        removeFlowStep (boardId: string, flowSequence: number, stepSequence: number) {
            return WorkflowBoardController.deleteStep(boardId, flowSequence, stepSequence)
        },
        refreshVariablesForFormUpdate (boardId: string, formModel: CFormModel,stepId: string) {
            const refFormFields = getRefFormFieldsFromFormModel(formModel, stepId)
            return WorkflowBoardController.refreshVariablesForFormUpdate(boardId, refFormFields)
        },
        transferAction (boardId: string, option: ITransferActionOption) {
            return BoardController.transferAction(boardId, option)
        },
        readBoardField (fieldInfo: {boardId: string; fieldName: string; sequenceArray: Array<number>}) {
            return WorkflowBoardController.readBoardField(fieldInfo.boardId, fieldInfo.fieldName, fieldInfo.sequenceArray)
        },
        resetBoardOption (boardId: string) {
            vuexStore.commit('user/setBinderViewToken', '')
            return BoardController.resetBoardOption(boardId)
        },
        createViewToken (boardId: string) {
            return BoardController.createViewToken(boardId)
        },
        // integration automation 
        async getIntegrationAutomationApps (type: IntegrationDefines.IntegrationAppType): Promise<IntegrationAppViewModel[]> {
            return IntegrationCenterController.getIntegrationAutomationApps(type)
        },

        // from old useFlowWelcome.ts
        async isFlowWorkspace (boardId: string) {
            if (boardId && boardId.startsWith(MOCK_BOARD_PREFIX)) {
                return false
            }
            const mxUser = MxISDK.getCurrentUser()
            const userObj = await mxUser.readUserBoards([boardId])
            const userBoard = userObj.boards?.[0] ?? {}
            const type = get(userBoard, 'board.type')
            return type === Defines.BoardType.BOARD_TYPE_WORKFLOW    
        },
        async isShouldShowWelcome (boardId: string) {
            const mxUser = MxISDK.getCurrentUser()
            const { boards } = await mxUser.readUserBoards([boardId])
            const userBoard = boards[0]
            const type = get(userBoard, 'board.type')
            if (type !== Defines.BoardType.BOARD_TYPE_WORKFLOW) {
                return false
            }
            const owner = get(userBoard, 'board.users', []).find(bUser => !bUser.is_deleted && bUser.type === Defines.BoardAccessType.BOARD_OWNER)
            if (owner && get(owner, 'user.id') === mxUser.id) {
                return false
            }
            const tags = vuexStore.getters['group/groupTags']
            const showFlowWelcomePage = tags['Show_Flow_Welcome_Page'] ?? true
            if (!showFlowWelcomePage || userBoard.dismissed_time || userBoard.accessed_time) {
                return false
            }
            return true
        },
      
        async markShowWelcomeFistTime (boardId: string) {
            const { isAnonymousUser, token } = useAnonymousUser()
            let boardViewToken = ''
            if (isAnonymousUser) {
                boardViewToken = token
            }
            return MxISDK.getCurrentUser().updateUserBoard(boardId, {
                dismissed_time: Date.now()
            }, boardViewToken)
        },

        updateMeetRequest (boardId: string, spath: MxSPath, param: {customData?: string; isPrepare?: boolean}) {
            return BoardController.updateMeetRequest(boardId, spath, param)
        },
       
        async readAmWorkflow (boardId: string, workflowSeq: number, boardOption?: MxBoardOption, isUse?: boolean): Promise<IWorkflowViewModel> {
            return BoardController.readAmWorkflow(boardId, workflowSeq, boardOption, isUse)
        }
    }
})
