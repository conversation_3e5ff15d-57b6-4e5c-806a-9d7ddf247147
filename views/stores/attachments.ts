import { defineStore } from 'pinia'
import { FileController } from '@controller/files/src/fileController';
import { FunctionUtil, MxConsts } from '@commonUtils'
import { DirectServiceController } from '@controller/directService/src/DirectServiceController'
import { UploadController, UploadStatus } from '@controller/uploader/src/uploadController'
import _findIndex from 'lodash/findIndex';
import { CBoard } from '@controller/defines/CBoard';
import { CBoardFile } from '@controller/defines/CBoardFile';
import {getAvaliableName} from '@commonUtils/helper';
import { useStore } from '@views/common'
import { FileFormatter } from '@controller/utils/file'
import getters from '@controller/moxoMeet/src/mxStore/getters';
import {RequestController} from '@controller/request/RequestController';

function getErrorFile (maxFileSize, allowedFileTypes, uid, file) {
    if (maxFileSize) {
        if (file.size > maxFileSize) {
            return {
                id: uid,
                client_uuid: uid,
                status: UploadStatus.ERROR,
                isMock: true,
                name: file.name,
                fileType: file.name.match(/\.[0-9a-z]+$/i)[0],
                error: {
                    code: MxConsts.ErrorCode.ExceedFileMaxSize
                }
            }
        }
    }
    if (allowedFileTypes) {
        const fileName = file.name || file.file.name
        const fileNameArr = fileName.split('.')
        let fileSuffix = ''
        if (fileNameArr.length > 1) {
            fileSuffix = fileNameArr.pop().toLowerCase()
        }
        if (allowedFileTypes.indexOf(fileSuffix) < 0) {
            return {
                id: uid,
                status: UploadStatus.ERROR,
                client_uuid: uid,
                name: file.name,
                isMock: true,
                fileType: file.name.match(/\.[0-9a-z]+$/i)[0],
                error: {
                    code: MxConsts.ErrorCode.FileTypeNotSupport
                }
            }
        }
    }
    return null
}

export interface AttachmentsState {
  currentBinder: CBoard;
  boardFiles: CBoardFile[];
  deletedFiles: CBoardFile[];
  directServiceController: DirectServiceController;
  fileControllerInstance: FileController;
  existingAttachments: CBoardFile[];
  deletedExistingFiles: CBoardFile[];
}

const AttachmentsStore = {
    state: () => {
      return {
          directServiceController: null,
          fileControllerInstance: null,
          currentBinder: {} as CBoard,
          boardFiles: [] as CBoardFile[],
          deletedFiles: [] as CBoardFile[],
          existingAttachments: [] as CBoardFile[],
          deletedExistingFiles: [] as CBoardFile[]
      }
    },
    getters: {
        existingFiles (state) {
            return state.existingAttachments.filter(file=>{
              if (file.isDDRFile) {
                return state.deletedExistingFiles.indexOf(file.clientUuidKey) === -1
              } else {
                return state.deletedExistingFiles.indexOf(file.referenceSequence) === -1
              }
            })
          }
    },
    actions: {
      async initStore () {
            this.directServiceController = new DirectServiceController
            this.currentBinder = await this.directServiceController.createTempBoard({})
            this.fileControllerInstance = new FileController(this.currentBinder.id)
            this.getBoardFoldersFiles().then(() => {
                this.subscribeBoardFiles(({files}) => {
                    files.forEach(file => {
                        files.forEach(file => {
                            if (file.fileType === 'URL') {
                                this._mergeSingleBoardFile(file)
                            } else if (typeof file.thumbnail === 'string') {
                                this._mergeSingleBoardFile({
                                    client_uuid: file.client_uuid,
                                    thumbnailUrl: file.thumbnail
                                })
                            }
                        })
                    })
                })
            })
      },
      async destroyStore () {
        this.unsubscribeBoardFiles()
        this.$dispose()
      },
      makeUploadUrl ({file, retryId, noFeed, name, existNames}) {
          const uid = retryId || FunctionUtil.uuid()
          let fileName = name || file.name
          if (!retryId) {
              let existFileNames = existNames || []
              if (this.boardFiles.length) {
                  existFileNames = existFileNames.concat(this.boardFiles.map(f=>f.name))
              }
              fileName = getAvaliableName(fileName, existFileNames)
          }
          const url = this.directServiceController.makeUploadUrl(fileName, uid, noFeed)
          this.uploadFile({
              file: file,
              name: fileName,
              url,
              uid
          })
      },
      createWebLinkPage ({url, retryId}) {
        let id = retryId || FunctionUtil.uuid()
        if (!/^https?:\/\//.test(url)) {
          url = 'https://' + url
        }
        let rootHost = url.replace(/^https?:\/\//, '').replace(/[/?].*$/,'')
        this.addTempFiles([{
          name: rootHost,
          webLinkUrl: url,
          uid: id,
          file: {
            name: rootHost
          },
          status: UploadStatus.PENDING
        }])
        return RequestController.getInstance().createWebLinkPage(this.currentBinder.id, url, id, rootHost).then((board)=>{
          this.updateBoardFiles({files: [{
              client_uuid: id,
              name: rootHost,
              webLinkUrl: url,
              file: {
                name: rootHost
              },
              status: UploadStatus.UPLOADED,
              response: {
                object: {
                  board
                }
              }
            }]})
        }).catch((response)=>{
          this._mergeSingleBoardFile({
            client_uuid: id,
            name: rootHost,
            webLinkUrl: url,
            error: {
              detail: response.detail
            },
            status: UploadStatus.ERROR
          })
        })
      },
      uploadFile (payload) {
          const privilegesStore = useStore('privileges')
          const maxFileSize = privilegesStore.maxFileSize.value
          const allowedFileTypes = privilegesStore.allowedFileTypes.value
          const hasWebsdkConfig = maxFileSize || allowedFileTypes
          const uid = payload.uid || FunctionUtil.uuid()
          let uploader
          const fileName = payload.name
          let currentFile
          let errorFile
          if (hasWebsdkConfig) {
              errorFile = getErrorFile(maxFileSize, allowedFileTypes, uid, payload.file)
          }
          if (errorFile) {
              currentFile = errorFile
          } else {
              uploader = this._generateUploaderFactory(uid, payload)
              uploader.send()
              currentFile = {
                  boardId: this.currentBinder.id,
                  uuid: uid,
                  client_uuid: uid,
                  uploader: uploader,
                  file: payload.file,
                  name: fileName,
                  url: payload.url,
                  percentage: 0,
                  isMock: true,
                  isContentLibrary: !!payload.file.folderId,
                  remoteFileDetail: payload.file.remoteFileDetail,
                  status: UploadStatus.PENDING
              }
          }
          this.updateBoardFiles({
              files: [currentFile]
          })
      },
      async deleteFile (file) {
          const SPath = file.SPath
          if (SPath) {
              await this.directServiceController.deleteFiles([SPath])
          }
      },
      async deleteFileWithFileId (fileId) {
          this.updateBoardFiles({files: [{
              client_uuid: fileId,
              is_deleted: true
          }]})
      },
      subscribeBoardFiles (callback) {
          this.fileControllerInstance?.subscribeFolder('', (files)=>{
              if (callback) {
                  return callback(files)
              }
              this.updateBoardFiles(files)
          }, (err)=>{
              console.error(err)
          })
      },
      unsubscribeBoardFiles () {
          if (this.boardFiles.length) {
              this.boardFiles.forEach(file=>(file as any).xhr && (file as any).xhr.abort())
          }
          this.fileControllerInstance?.unsubscribeFolder()
      },
      getBoardFoldersFiles () {
          return this.fileControllerInstance.getBoardFoldersFiles('')
      },
      copyFiles ({fromBoardId, files, toBoardId, toFolderPath, newFilesName, isFromBinder}) {
          return this.directServiceController.copyFiles(fromBoardId, files, toBoardId, toFolderPath, newFilesName, isFromBinder)
      },
      uploadRemoteFile (payload) {
        const {binderId, url, name, token, toFolderPath, contentType, id} = payload
        return this.directServiceController.uploadRemoteFile(binderId, url, name, token, toFolderPath, contentType, id)
      },
      addTempFiles (files) {
          const privilegesStore = useStore('privileges')
          const maxFileSize = privilegesStore.maxFileSize.value
          const allowedFileTypes = privilegesStore.allowedFileTypes.value
          const hasWebsdkConfig = maxFileSize || allowedFileTypes
          let allFilesName = []
          if (this.boardFiles.length) {
              allFilesName = this.boardFiles.map(f => f.name)
          }
          const commitFiles = files.map((file)=>{
              if (file.isDDRFile) {
                  return file
              } else {
                  const uid = file.uid || FunctionUtil.uuid()
                  let fileName = file.name
                  let f = file.base64?file.file:file //copy and paste can not use native File object name
                  if (file.remoteFileDetail) {
                      f = null
                  }
                  fileName = getAvaliableName(fileName, allFilesName)
                  allFilesName.push(fileName)
                  let errorFile
                  if (hasWebsdkConfig) {
                      errorFile = getErrorFile(maxFileSize, allowedFileTypes, uid, file)
                  }
                  if (errorFile) {
                      return errorFile
                  } else {
                      return {
                          uuid: uid,
                          client_uuid: uid,
                          file: f,
                          name: fileName,
                          percentage: file.percentage || 100,
                          isMock: true,
                          base64: file.base64,
                          context: file.context,
                          isContentLibrary: !!file.folderId,
                          remoteFileDetail: file.remoteFileDetail,
                          status: UploadStatus.UPLOADED,
                          url: file.url,
                          thumbnailUrl: file.thumbnailUrl,
                          webLinkUrl: file.webLinkUrl,
                          fileType: file.fileType
                      }
                  }
              }
          })
          this.updateBoardFiles({
              files: commitFiles
          })
      },
      getAvailableFilesName ({names, folder}) {
          return this.directServiceController.getFilesFromFolder(folder).then(board=>{
              let pageGroup = board.page_groups || []
              if (folder) {
                  const object = FileFormatter.getFolderInfo(board, folder)
                  pageGroup = object.folder.files || []
              }
              const files = pageGroup.map(pageGroup=>pageGroup.name)
              return names.map(name=>{
                  return getAvaliableName(name, files)
              })
          })
      },
      setExistingAttachments (attachments) {
          if (attachments?.length) {
            let nowTs = Date.now()
            attachments.forEach(attachment => {
              if (attachment.isDDRFile) {
                // clientUuidKey to differentiate mulitple same DDR files
                attachment.clientUuidKey = `${attachment.clientUuid}-${nowTs++}`
              }
            })
          }
          this.existingAttachments = attachments
      },
      setDeletedExistingFiles (file) {
        if (file === 'reset') {
          this.deletedExistingFiles = []
        } else {
          this.deletedExistingFiles.push(file)
        }
      },
      _generateUploaderFactory (uid, file) {
          const callback = file.callback
          return UploadController.uploaderFactory(uid, file.url, file.file, (info) => {
              let fileType = ''
              const infoFile = info.file as any
              const fileNameArr = infoFile.name.split('.')
              if (infoFile.fileType) {
                  fileType = infoFile.fileType
              } else if (fileNameArr.length > 1) {
                  fileType = fileNameArr.pop()
              }
              const file = {
                  uuid: info.id,
                  client_uuid: info.id,
                  isMock: true,
                  xhr: info.xhr,
                  percentage: info.percentage,
                  name: infoFile.name,
                  fileType: fileType,
                  isContentLibrary: !!infoFile.folderId,
                  remoteFileDetail: infoFile.remoteFileDetail,
                  ...info
              }
              if (callback && (file.response || file.error)) {
                  callback(file)
              }
              if (info.error && (info.error as any).code === MxConsts.ErrorCode.InvalidRequest) {
                  //try again has conflict client_uuid due to async issue
                  return
              }
              if (UploadStatus.ERROR === info.status && !info.error) {
                  //trigger from uploadController onError callback
                  //in onLoad error, error will have value from transformServerError
                  (file as any).error = {
                      code: MxConsts.ErrorCode.UnKnownError
                  }
              }
              this.updateBoardFiles({
                  files: [file]
              })
          }, file.options || {})
      },
      updateBoardFiles (payload) {
          payload.files && payload.files.forEach((file)=>{
              if (file.error && file.isRemote) {
                  if (file.error.detail) {
                      file.error = UploadController.transformServerUploadError(file.error.detail)
                  } else {
                      file.error = {
                          code: MxConsts.ErrorCode.UnKnownError
                      }
                  }
              }
              const index = _findIndex(this.boardFiles, item => {
                  if ((item as any).isDDRFile) {
                      // clientUuidKey of DDR file is using to identify multiple same DDR file references
                      if (file.clientUuidKey) {
                          // add attachment
                          return (item as any).clientUuidKey && (item as any).clientUuidKey === file.clientUuidKey
                      } else {
                          // delete attachment
                          return (item as any).clientUuidKey && (item as any).clientUuidKey === file.client_uuid
                      }
                  } else {
                      if (file.clientUuid) {
                          return (item as any).clientUuid === file.clientUuid
                      } else {
                          return item.client_uuid && item.client_uuid === file.client_uuid
                      }
                  }
              })
              if (file.is_deleted) {
                  if (index > -1) {
                      this.boardFiles.splice(index, 1)
                      this.deletedFiles.push(file)
                  }
              } else if (index > -1) {
                  let f = file
                  const stateBoardFile = this.boardFiles[index]
                  
                  if (stateBoardFile.SPath && file.isMock) {
                      return
                  }
  
                  f.boardId = this.currentBinder.id
                  if (f.response) {
                      const responseBoard = f.response?.object?.board || {}
                      if (responseBoard.page_groups) {
                        const pageGroup = responseBoard.page_groups[0]
                        f.SPath = `page_groups[sequence=${pageGroup.sequence}]`
                      }
                  }
                  if (stateBoardFile.name !== file.name) {
                      f = {
                          ...file,
                          name: this.boardFiles[index].name
                      }
                  }
                  this.boardFiles.splice(index, 1, f);
              } else {
                  if (this.boardFiles.length && (this.boardFiles[0] as any).context === 'signature') {
                      this.boardFiles.splice(0, 1, file);
                  } else {
                      if (this.deletedFiles.findIndex(f => f.client_uuid === file.client_uuid) < 0) {
                          this.boardFiles.push(file);
                      }
                  }
              }
          });
      },
      _mergeSingleBoardFile (boardFile) {
          //boardFile is from transformFiles in fileController
          const index = _findIndex(this.boardFiles, item => {
              return item.client_uuid === boardFile.client_uuid
          })
          if (index > -1) {
              const mergedFile = {
                  ...this.boardFiles[index],
                  ...boardFile
              }
              if (!mergedFile.name) {
                  mergedFile.name = this.boardFiles[index].name
              }
              this.boardFiles.splice(index, 1, mergedFile);
          }
      },
    }
  }


export default function useAttachmentsStore () {
    const uuid = FunctionUtil.uuid()
    const store = defineStore('attachmentsStore'+uuid, AttachmentsStore)
    return store()
}

export const AttachmentsStoreKey = Symbol('AttachmentsStore')