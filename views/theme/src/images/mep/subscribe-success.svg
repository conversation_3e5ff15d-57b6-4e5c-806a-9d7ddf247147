<?xml version="1.0" encoding="UTF-8"?>
<svg width="302px" height="227px" viewBox="0 0 302 227" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 64 (93537) - https://sketch.com -->
    <title>Illustration</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0.5" y="0.2" width="165.798" height="116.883" rx="5.4"></rect>
        <filter x="-13.3%" y="-18.0%" width="126.5%" height="138.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.06 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter3"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter3" result="shadowBlurOuter3"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter3" result="shadowMatrixOuter3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter3"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-3" x="0.8990652" y="0.9" width="112.707" height="159.867" rx="5.4"></rect>
        <filter x="-19.5%" y="-13.1%" width="139.0%" height="128.1%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.06 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter3"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter3" result="shadowBlurOuter3"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter3" result="shadowMatrixOuter3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter3"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-5" x="0.61722" y="0.02583" width="191.16675" height="134.77113" rx="5.4"></rect>
        <filter x="-11.5%" y="-15.6%" width="123.0%" height="133.4%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.06 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter3"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter3" result="shadowBlurOuter3"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter3" result="shadowMatrixOuter3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter3"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="6.820745%" y1="93.1775861%" x2="93.1780471%" y2="6.82028403%" id="linearGradient-7">
            <stop stop-color="#5291FF" offset="0%"></stop>
            <stop stop-color="#2DB6F4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Update-Subscription" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="(3.11)-Subscription-Success" transform="translate(-569.000000, -121.000000)">
            <rect id="Container" fill="#FFFFFF" x="268" y="0" width="904" height="1024"></rect>
            <g id="Illustration" transform="translate(580.000000, 133.000000)">
                <g id="Right-Card" transform="translate(102.600000, 0.000000)" fill-rule="nonzero">
                    <g id="Card" transform="translate(88.528060, 65.990880) rotate(-4.540000) translate(-88.528060, -65.990880) translate(5.028060, 6.990880)">
                        <g id="path-3-link">
                            <g id="path-3">
                                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                <use fill="#FFFFFF" xlink:href="#path-1"></use>
                            </g>
                        </g>
                    </g>
                    <rect id="Subtitle" fill="#F0F2F5" transform="translate(81.474541, 64.388944) rotate(-4.540000) translate(-81.474541, -64.388944) " x="13.9250413" y="60.7889436" width="135.099" height="7.2" rx="3.6"></rect>
                    <rect id="Subtitle" fill="#F0F2F5" transform="translate(79.532473, 45.459208) rotate(-4.540000) translate(-79.532473, -45.459208) " x="11.9829732" y="41.8592079" width="135.099" height="7.2" rx="3.6"></rect>
                    <rect id="Subtitle" fill="#F0F2F5" transform="translate(84.116778, 83.502439) rotate(-4.573921) translate(-84.116778, -83.502439) " x="16.351465" y="79.8909373" width="135.530627" height="7.22300325" rx="3.6"></rect>
                </g>
                <g id="Left-Card" transform="translate(0.000000, 75.500000)" fill-rule="nonzero">
                    <g id="Card" transform="translate(84.900474, 64.059428) rotate(-85.550000) translate(-84.900474, -64.059428) translate(27.900474, -16.440572)">
                        <g id="path-5-link">
                            <g id="path-5">
                                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                                <use fill="#FFFFFF" xlink:href="#path-3"></use>
                            </g>
                        </g>
                    </g>
                    <rect id="Subtitle" fill="#F0F2F5" transform="translate(83.903500, 72.913578) rotate(4.450000) translate(-83.903500, -72.913578) " x="22.6" y="69.3135776" width="122.607" height="7.2" rx="3.6"></rect>
                    <rect id="Subtitle" fill="#F0F2F5" transform="translate(80.215990, 48.948846) rotate(4.450000) translate(-80.215990, -48.948846) " x="24.4519897" y="41.7488462" width="111.528" height="14.4" rx="7.2"></rect>
                </g>
                <g id="Warning" transform="translate(40.000000, 30.000000)">
                    <g id="Group" transform="translate(0.600000, 0.600000)">
                        <g id="Card" fill-rule="nonzero">
                            <g id="path-7-link">
                                <g id="path-7">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                                    <use fill="#FFFFFF" xlink:href="#path-5"></use>
                                </g>
                            </g>
                        </g>
                        <rect id="Rectangle" fill="url(#linearGradient-7)" x="59" y="37" width="71" height="71" rx="35.5"></rect>
                        <path d="M110.86743,67.165315 L92.9646622,84.900011 C92.8961028,84.9679296 92.826814,85.0365707 92.74075,85.1044893 C92.6889657,85.1724079 92.6379108,85.2063672 92.5861266,85.2576674 C92.3279346,85.4961049 92.070472,85.6666239 91.8297846,85.8371429 C91.8130094,85.8544838 91.7955049,85.8371429 91.7955049,85.8711022 C91.5548174,86.0242802 91.2966255,86.143499 91.0559381,86.2121401 C90.8152506,86.314018 90.5745632,86.3653182 90.3338758,86.3826591 C90.2310366,86.4 90.0931884,86.4 89.9728447,86.4 C89.3018373,86.3660407 88.665839,86.1615624 88.0984002,85.735265 C88.0466159,85.7013057 87.9780565,85.6673464 87.9437768,85.6160462 C87.7716488,85.5307867 87.6513051,85.411568 87.5141862,85.2757308 C87.4456267,85.2417715 87.3763379,85.173853 87.325283,85.1052118 L78.9318562,76.8075851 C77.8830425,75.7512344 77.3666586,74.763525 77.4016677,73.8430115 C77.4359474,72.9058797 77.8487628,72.0713482 78.6401139,71.2873945 C79.431465,70.5034407 80.3081507,70.0778658 81.2541252,70.0265656 C82.1825951,69.9926063 83.1803539,70.5034407 84.2466721,71.5597913 L90.0421335,77.3010785 L105.570848,61.9182438 C106.619662,60.8618932 107.617421,60.3676772 108.563395,60.4016364 C109.491865,60.4189774 110.351776,60.8445523 111.143127,61.628506 C111.934478,62.4124598 112.347293,63.2636096 112.398348,64.1841231 C112.432628,65.1378733 111.933748,66.1263053 110.86743,67.165315 Z" id="Path" fill="#FFFFFF" fill-rule="nonzero"></path>
                        <circle id="Window" fill="#DB4646" fill-rule="nonzero" opacity="0.5" cx="16.2" cy="15.75" r="4.05"></circle>
                        <circle id="Window" fill="#FF9F04" fill-rule="nonzero" opacity="0.5" cx="31.509" cy="15.75" r="4.05"></circle>
                        <circle id="Window" fill="#5392FF" fill-rule="nonzero" cx="46.818" cy="15.75" r="4.05"></circle>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>