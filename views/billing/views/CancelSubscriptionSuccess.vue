<template>
  <AfterActionLayout
    :display-image="displayImage"
    :btn-text="$t('dismiss')"
    @submit="dismiss"
  >
  <span slot="title">{{$t('your_plan_has_been_cancelled')}}</span>
    <span slot="subTitle">{{$t('we_are_sad_to_see_you_go')}}</span>
    <div
      slot="footer"
      class="contact-us"
    >
      {{$t('Question')}}?
      <a
        class="mx-clickable"
        @click="contactSales"
      >{{$t('Contact_us')}}</a>
    </div>
  </AfterActionLayout>
</template>

<script>
import AfterActionLayout from '../layout/AfterActionLayout'
import * as mepPic from '@views/theme/src/images/mep'
import { mapGetters, mapMutations, mapActions } from 'vuex'

export default {
  name: 'CancelSubscriptionSuccess',
  components: {
    AfterActionLayout
  },
  data() {
    return {
      displayImage: mepPic.subscriptionCancel2
    }
  },
  computed: {
    ...mapGetters('billing', ['viewTypes']),
    ...mapGetters('group', ['groupBasicInfo']),
  },
  methods: {
    ...mapMutations('billing', ['updateView']),
    dismiss() {
      if(this.groupBasicInfo.cancellation_request_time)
        this.$router.push({name: "plansandbilling"})
      else
        this.updateView(this.viewTypes.SUBSCRIPTION_CANCELLED)
    },
    contactSales() {
      this.updateView(this.viewTypes.CONTACT_SALES)
    }
  }
}
</script>

<style scoped>
.contact-us {
  width: 376px;
  text-align: center;
  margin: 25px auto;
}
</style>

