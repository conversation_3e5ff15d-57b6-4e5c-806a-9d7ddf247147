import {TemplateViewModel, TemplateViewModelArray} from "@controller/contentLibrary";
import {CallbackWithName, TemplateAPICallback, TemplatePlugin} from "./common";
import {Defines, MxBoard} from 'isdk'
import { ObjectUtils } from '@commonUtils';
import {
    boardToTemplateViewModel,milestoneToTemplateViewModel
} from '@controller/contentLibrary/src/transformContentLibrary';
import {Board, MxCallback} from 'isdk/src/api/defines';
import { WorkflowTemplateController } from '@newController/index'


const MilestoneAPIs = (): TemplatePlugin =>{
    return {
        subscriber:null,
        delete (mxBoard: MxBoard, items: TemplateViewModelArray, callback){
            return Promise.all(items.map(item =>{
                console.log(item)
                const seq = item.id as number
                WorkflowTemplateController.deleteMilestoneTemplate(mxBoard.id, seq).then(res=>{
                    callback(item)
                    return res;
                })
            }))
        },
        rename (mxBoard: MxBoard,item: TemplateViewModel,name: string,callback: CallbackWithName): Promise<Defines.Board>{
            const seq = item.id as number
            const workflow = {
                sequence: seq,
                name: name
            }
            return mxBoard.updateWorkflow(workflow).then(res =>{
                callback(item, name)
                return res
            })
        },
        duplicate (mxBoard: MxBoard, item: TemplateViewModel,name: string,callback: Function){
            const seq = item.id as number
            const templateInfo = {
                name
            }
             return WorkflowTemplateController.duplicateMilestoneTemplate(mxBoard.id,seq,templateInfo).then(res=>{
                console.log(res)
                let model = boardToTemplateViewModel(mxBoard.board)
                let items = milestoneToTemplateViewModel(mxBoard.board, model)
                let addedMilestoneIndex = items.length - 1
                callback(items[addedMilestoneIndex])
                return items[addedMilestoneIndex]
            })
        },
        move (mxBoard: MxBoard, items: TemplateViewModelArray,toBoardId: string,callback: Function){
            return Promise.all(items.map(item =>{
                const seq = item.id as number
                return WorkflowTemplateController.moveMilestoneTemplate(mxBoard.id,seq,toBoardId).then(res =>{
                    callback(item)
                })
            }))
        },
        async subscribe(mxBoard:MxBoard, callback:TemplateAPICallback){
            // this.subscriber =  mxBoard.subscribeTransactions((transaction) =>{
            //     let model = boardToTemplateViewModel(mxBoard.basicInfo)
            //     callback(transactionToTemplateViewModel({transactions: transaction}, model))
            // })

        },
        unsubscribe (){
            if(this.subscriber){
                this.subscriber.unsubscribe()
                this.subscriber = null
            }
        },
        async list(mxBoard: MxBoard, callback: TemplateAPICallback, _model: TemplateViewModel): Promise<TemplateViewModelArray>{
            const subscribeCallBack = (board)=>{
                let model = boardToTemplateViewModel(mxBoard.board)
                let items = milestoneToTemplateViewModel(board, model, mxBoard)
                callback(items, model.id as string)
            }
            const [board, milestonSubScribe] = await WorkflowTemplateController.listMilestoneTemplatesRaw(mxBoard.id,subscribeCallBack)
            let model = boardToTemplateViewModel(mxBoard.board)
            let items = milestoneToTemplateViewModel(board, model)
            this.subscriber = milestonSubScribe
            callback(items, model.id as string)
            return items;
        },

        async readThread (mxBoard: MxBoard, item: TemplateViewModel) {
            const workflowSeq = item.id as number
            const folderId = item.folderId
            const [model] = await WorkflowTemplateController.loadMilestoneTemplate(folderId,workflowSeq)
            return model
        },
        // waiting todo 
        async subscribeThread (mxBoard: MxBoard, item: TemplateViewModel, callback:MxCallback<Board>) {
                // let trans = mxBoard.getTransactionBaseObject(item.id as number)
                // return mxBoard.subscribeThread(trans, callback)
            },
        async updateFile (mxBoard: MxBoard, item: TemplateViewModel, milestone, callback: Function) {
            const workflow = {
                sequence:  item.id as number,
                description: milestone.description
            }
            return mxBoard.updateWorkflow(workflow).then(res =>{
                callback(item, milestone)
                return res
            })
        },

        }
}
export default MilestoneAPIs
