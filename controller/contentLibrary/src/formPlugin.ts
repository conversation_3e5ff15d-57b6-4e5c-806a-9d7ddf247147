import qs from 'qs'
import {Defines, MxAjax, MxBoard, MxISDK} from 'isdk'
import {
    CallbackWithName,
    TemplateAPICallback,
    TemplatePlugin,
    TemplateViewModel,
    TemplateViewModelArray
} from '@controller/contentLibrary'
import {Board} from 'isdk/src/proto/generated/Board'
import {UploadController, UploadStatus} from '@controller/uploader/src/uploadController'
import {FileController} from '@controller/files/src/fileController'
import {FunctionUtil, ObjectUtils} from '@commonUtils'
import {BoardAccessType, BoardTransaction, DueTimeFrameType, MxCallback} from 'isdk/src/api/defines'
import {ThreadUtils} from '@controller/utils/thread'
import {boardToTemplateViewModel, transactionToTemplateViewModel} from './transformContentLibrary'
import {CFormModel} from '@controller/defines/CFormModel';
import {makeTransactionResourceUrl} from '@controller/contentLibrary/src/form';
import {FlowBuilderType, initializeBuilderStore} from '@views/stores/flowBuilder'
import {FormModel, FormPage} from '@model/form/defines/serverDataStructure';
import {FormElementType} from '@model/form/defines/shared';
import { upgradeFormData} from '@model/form/data/upgradeFormData';

type FormInfo = {
    id?: number
    title: string
    sub_title: string
    folderId: string
    copyToFolderId?: string
    expireDate?: number
    dueInTimeframe?: DueTimeFrameType
    excludeWeekends?: boolean
    template_name?: string
    template_description?: string
    /**
      if value is true, then the form data is come from segments
     */
    isFromSegments?: boolean;
    assigneeCount?:number;
    hasCustomFolder?: boolean;
    customFolderName?: string;
    signer?: string;
}

const requireValueTypes = ['SingleLineText', 'MultiLineText', 'EmailAddress', 'Number', 'DropdownList', 'SingleSelection']
const requireDisplayValueTypes = ['Date', 'PhoneNumber', 'MultiSelection']


export function transactionToFormModel(boardId, transaction: Defines.BoardTransaction): CFormModel{
    const model: CFormModel = {
        creator: '',
        boardId,
        pages: [],
        title: transaction.title,
        transactionSequence: transaction.sequence,
        dueDate: transaction.expiration_date ,
        description: transaction.sub_title,
        templateName:transaction.template_name,
        templateDescription : transaction.template_description,
        isFromSegments: false,
        version: ''
    }

    try{
        const cardObj = JSON.parse(transaction.card)
        upgradeFormData(cardObj)
        model.version = cardObj.version
        model.pages = cardObj.data.pages
        model.pages.forEach(page => {
            page.fields.forEach(field => {
                if(field.type === 'FileUpload') {
                    if(field.value && !Array.isArray(field.value)) {
                        field.value = [field.value]
                    }
                }
            })
        })
        model.creator = cardObj.creator
    }catch (e){
        console.warn('Form parse error', transaction.card)
    }


    return model;
}

export function formModelToSegments(model: CFormModel): Defines.TransactionElement[]{
    return model.pages.map(page => {
        return {
            sequence: page.sequence,
            string_value: JSON.stringify(page)
        }
    })
}
export function formModelToCardJSONString(model:CFormModel){
    const hasField = model.pages.find(page => page.fields.length > 0)
    // When fields value is UNDEFINED, need to set it as '' to trigger workflow variable resolving process
    model.pages.forEach(page => {
        page.fields.forEach(field => {
            if (requireValueTypes.includes(field.type) && field.value === undefined) {
                field.value = ''
            }
            if (requireDisplayValueTypes.includes(field.type) && field.displayValue === undefined) {
                // need empty displayvalue to trigger workflow variable resolving process 
                field.displayValue = ''
            }
            if (field.type === 'Currency' && field.value.amount === undefined) {
                field.value.amount = ''
            }
        })
    })
    return JSON.stringify({
        type: 'form',
        creator: model.creator,
        isEmptyForm: !hasField,
        submitState: model.submitState,
        version: model.version,
        data: {
            pages: model.pages
        }
    })
}
export function formModelToTransactionCard(model: CFormModel){
    return formModelToCardJSONString(model)
}
export function formModelToTransactionProps(model: CFormModel){
    if(model.isFromSegments){
        return {
            segments: formModelToSegments(model)
        }
    }else {
        return {
            card:formModelToCardJSONString(model)
        }
    }
}
const imageField = ['Heading','Image']
export interface FormImageResource {
    url: string;
    client_uuid: string;
    name: string;
}
export function getFormImageResources(model: CFormModel): FormImageResource[]{
    const resources = [];
    (model.pages ||[]).forEach(page =>{
        page.fields.filter(field => imageField.includes(field.type)).forEach(field=>{
            const imageUUID = field.fieldSpecific?.imageUUID
            if(imageUUID){
                resources.push({
                    url: makeTransactionResourceUrl(model.boardId,model.transactionSequence,imageUUID),
                    client_uuid:imageUUID,
                    name: field.fieldSpecific?.imageName
                })
            }
        })
    })
    return resources
}

export function segmentsToFormModel(transaction: Defines.BoardTransaction): CFormModel {
    const model: CFormModel = {
        creator: '',
        pages: [],
        title: transaction.title,
        isFromSegments: !transaction.card && !!transaction.segments?.length
    }
    if(transaction.is_template){
        model.templateName = transaction.template_name
        model.templateDescription = transaction.template_description
    }
    transaction.segments.filter(item => !item.is_deleted).forEach(item => {
        const page = JSON.parse(item.string_value)
        if(!model.creator) {
            model.creator = page.creator
        }
        page.sequence = item.sequence
        model.pages.push(page)
    })
    return model
}
export function createFormTemplate(formInfo: FormInfo, form: FormModel |null, is_template = true, boardInfo = {}): Promise<any> {
    const { title, template_name, template_description, folderId, expireDate, dueInTimeframe, excludeWeekends, sub_title } = formInfo
    const currentUser = MxISDK.getCurrentUser()
    let mxBoard
    const actions = [
        {
            id: 'Button1',
            text: 'Fill Form',
            force_read: true,
            payload: 'this is payload field',
            type: 'ACTION_TYPE_FILL_FORM',
            feed_msg: 'Fill Form',
            style: 'branding'
        }
    ]
    if(!form){
        form = {type: FormElementType.Form,isEmptyForm: true,data: {pages:[]}}
    }
    const transaction = {
        title: title,
        sub_title: sub_title,
        template_name: template_name,
        template_description: template_description,
        card: JSON.stringify(form),
        type: Defines.TransactionType.TRANSACTION_TYPE_FORM_REQUEST,
        is_template,
        status: Defines.TransactionStatus.TRANSACTION_STATUS_ACTIVE,
        expiration_date: expireDate,
        due_in_timeframe: dueInTimeframe,
        exclude_weekends: excludeWeekends,
        has_custom_folder: formInfo.hasCustomFolder || false,
        custom_folder_name: formInfo.customFolderName || '',
    } as BoardTransaction
    if (formInfo.signer) {
        transaction.steps = [{
            'order_number': '100',
            'id': 'FillForm',
            'action_style': Defines.TransactionActionStyle.ACTION_STYLE_BUTTON,
            'actions': JSON.stringify(actions),
            'status': Defines.TransactionStepStatus.STEP_STATUS_INITIAL,
            'assignee': {
                user: {
                    id: formInfo.signer
                }
            }
        }]
    }
    if (folderId) {
        mxBoard = currentUser.getInstantBoard(folderId)
        return mxBoard.createTransaction(transaction)
    } else {
        return currentUser.createTempBoard(boardInfo).then((board: Board) => {
            mxBoard = currentUser.getCacheBoard(board.id)
            return mxBoard.createTransaction(transaction)
        })
    }
}
const imageFields = ['Heading', 'Image', 'Signature']
export function updateImageResourceUrl(pages:FormPage[],newBoardId, transactionSequence ){
    pages.forEach((page:any) => {
        (page.fields || []).forEach((field) => {
            if(imageFields.includes(field.type) && field.image) {
                field.image = `/board/${newBoardId}/transaction/${transactionSequence}/${field.fieldSpecific.imageUUID}`
            }
        })
    })
    return pages
}


export function updateFormCard(model:CFormModel) {
    return new Promise(async (resolve, reject) => {
        try {
            const mxBoard = MxISDK.getCurrentUser().getInstantBoard(model.boardId)
            const transaction = {
                title: model.title,
                sub_title: model.description,
                template_name: model.templateName,
                template_description: model.templateDescription,
                expiration_date: model.dueDate,
                ...formModelToTransactionProps(model)
            }
            let board =  await mxBoard.updateTransaction(model.transactionSequence as number, transaction)
            const viewToken = await mxBoard.createViewToken(BoardAccessType.BOARD_OWNER)
            board = await mxBoard.loadFullBoard()
            resolve({board: board, model: model, viewToken: viewToken.view_tokens[0].token})

        } catch (e) {
            reject(e)
        }
    })
}
interface UpdateFormOption {
    appendSteps: boolean;
}
export function updateFormTemplate(model: FormInfo, form: FormModel, option: UpdateFormOption = {appendSteps: false}): Promise<any> {
    return new Promise(async (resolve, reject) => {
        try {
            const mxUser = MxISDK.getCurrentUser()
            const mxBoard = mxUser.getInstantBoard(model.folderId)
            const transactionData: Defines.BoardTransaction = {
                title: model.title,
                sub_title: model.sub_title,
                template_name: model.template_name,
                template_description: model.template_description,
                expiration_date: model.expireDate,
                due_in_timeframe: model.dueInTimeframe,
                exclude_weekends: model.excludeWeekends,
                card: JSON.stringify(form),
                has_custom_folder: model.hasCustomFolder || false,
                custom_folder_name: model.customFolderName || '',
            }
            if(option && option.appendSteps){
                const actions = [
                    {
                        id: 'Button1',
                        text: 'Fill Form',
                        payload: 'this is payload field',
                        type: 'ACTION_TYPE_FILL_FORM',
                        feed_msg: 'Fill Form',
                        style: 'branding'
                    }
                ]
                transactionData.steps = [{
                    'order_number': '100',
                    'id': 'Approve',
                    'action_style': Defines.TransactionActionStyle.ACTION_STYLE_BUTTON,
                    'actions': JSON.stringify(actions),
                    'status':  Defines.TransactionStepStatus.STEP_STATUS_INITIAL,
                    'assignee': {
                        user: {
                            id: mxUser.id
                        }
                    }
                }]
            }


            const _createOrUpdateWorkflow = async (board) => {
                // create or update workflow
                const actionbuilderStore = initializeBuilderStore(FlowBuilderType.ACTION_TEMPLATE)
                await actionbuilderStore.createOrUpdateAmWorkflow(board)
            }

           let board =  await mxBoard.updateTransaction(model.id as number, transactionData)
            if (model.copyToFolderId) {
                 board = await mxBoard.copyTransactions([model.id], model.copyToFolderId)
                const transactionSequence = board.transactions[0].sequence
                form.data.pages = updateImageResourceUrl(form.data.pages,board.id, transactionSequence)
                const newMxBoard = MxISDK.getCurrentUser().getInstantBoard(model.copyToFolderId)
                 await newMxBoard.updateTransaction(transactionSequence, {
                    card: JSON.stringify(form),
                })

                await _createOrUpdateWorkflow(board)

                await mxBoard.deleteTransaction(model.id)
                resolve(board)
            } else {
                await _createOrUpdateWorkflow(board)
                resolve(board)
            }
        } catch (e) {
            reject(e)
        }
    })
}

export function getNoteContent(boardId: string, pageSeq: string | number) {
    const contextPath = MxISDK.getContextPath()
    let url = `${contextPath}/board/${boardId}/${pageSeq}/vector?${Math.random()}`
    if (contextPath && (url.indexOf(contextPath) === 0)) {
        url = url.substring(contextPath.length)
    }
    return MxAjax.get(url, {
        contentType: 'html'
    })
}

export function createAndFillForm(boardId: string, name: string, content: any, userId?: string, isDraft = false) {
    if (!userId) {
        userId = MxISDK.getCurrentUser().id
    }
    const mxBoard = MxISDK.getCurrentUser().getInstantBoard(boardId)
    const actions = [
        {
            id: 'Button1',
            text: 'Fill Form',
            payload: 'this is payload field',
            type: 'ACTION_TYPE_FILL_FORM',
            feed_msg: 'Fill Form',
            style: 'branding'
        }
    ]
    const transaction = {
        title: name,
        card: JSON.stringify({
            type: 'form',
            data: content
        }),
        type: Defines.TransactionType.TRANSACTION_TYPE_FORM_REQUEST,
        is_template: true,
        status: isDraft ? Defines.TransactionStatus.TRANSACTION_STATUS_ACTIVE : Defines.TransactionStatus.TRANSACTION_STATUS_COMPLETED,
        steps: [{
            'order_number': '100',
            'id': 'Approve',
            'action_style': Defines.TransactionActionStyle.ACTION_STYLE_BUTTON,
            'actions': JSON.stringify(actions),
            'status': isDraft ? Defines.TransactionStepStatus.STEP_STATUS_PENDING : Defines.TransactionStepStatus.STEP_STATUS_COMPLETED,
            'assignee': {
                user: {
                    id: userId
                }
            }
        }]
    } as BoardTransaction
    return mxBoard.createTransaction(transaction)
}

export function getFormContent(boardId: string, pageSeq: string | number) {
    return getNoteContent(boardId, pageSeq).then(data => {
        return JSON.parse(data)
    })
}

export function fillForm(boardId: string, pageSeq: string, fileId: string, formData: any) {
    const params = qs.stringify({
        name: 'form1.html',
        type: 'vector',
        id: boardId,
        destfile: fileId,
        seq: pageSeq
    })
    return MxAjax
        .post(`/board/upload?${params}`, JSON.stringify(formData), {
            contentType: 'text/html'
        })
}

export function getUploaderFactory(boardId, file) {
    return new Promise((resolve, reject) => {
        const fileInstance = new FileController(boardId)
        const url = fileInstance.makeUploadUrl(file.name, '')
        const uid = FunctionUtil.uuid()
        const uploader = UploadController.uploaderFactory(uid, url, file, (info) => {
            if (info.status === UploadStatus.UPLOADED) {
                //we need to set pageGroup before allFilesResolved is set
                resolve(ObjectUtils.getByPath(info.response, 'object.board.feeds.0'))
            }
        }, {})
        uploader.send()
    })
}
export function uploadFormResourceFromURLs(toBoardId: string, transactionSequence: number, resources: FormImageResource[]){
    let mxBoard = MxISDK.getCurrentUser().getInstantBoard(toBoardId)
    return Promise.all(resources.map(({url, client_uuid,name}) =>{
        return mxBoard.uploadTransactionResourceFromUrl(transactionSequence,url,name, client_uuid)
    }))
}

const FormAPIs = (): TemplatePlugin => {
    return {
        subscriber: null,
        delete(mxBoard: MxBoard, items: TemplateViewModelArray, callback) {
            return Promise.all(items.map(item => {
                const seq = item.id as number
                return mxBoard.deleteTransaction(seq).then(res => {
                    callback(item)
                    return res
                })
            }))
        },
        rename(mxBoard: MxBoard, item: TemplateViewModel, name: string, callback: CallbackWithName): Promise<Defines.Board> {
            const seq = item.id as number
            return mxBoard.updateTransaction(seq, { template_name: name }).then(res => {
                callback(item, name)
                return res
            })
        },
        duplicate(mxBoard: MxBoard, item: TemplateViewModel, name: string, callback: Function) {
            const seq = item.id as number
            return mxBoard.copyTransactions([seq], mxBoard.id).then(board => {
                const sequence = ObjectUtils.getByPath(board, 'transactions.0.sequence')
                const model = boardToTemplateViewModel(mxBoard.basicInfo)
                const modes = transactionToTemplateViewModel(board, model)
                console.debug()
                return mxBoard.updateTransaction(sequence, { template_name: name }).then(res => {
                    const mode = modes[0]
                    mode.name = name
                    callback(mode)
                    return mode
                })
            })
        },
        move(mxBoard: MxBoard, items: TemplateViewModelArray, toBoardId: string, callback: Function) {
            return Promise.all(items.map(item => {
                const seq = item.id as number
                return mxBoard.copyTransactions([seq], toBoardId, false, true).then(res => {
                    return mxBoard.deleteTransaction(seq).then(res => {
                        callback(item)
                    })
                })
            }))
        },
        async subscribe(mxBoard: MxBoard, callback: TemplateAPICallback) {
            this.subscriber = mxBoard.subscribeTransactions((transaction) => {
                const model = boardToTemplateViewModel(mxBoard.basicInfo)
                callback(transactionToTemplateViewModel({ transactions: transaction }, model))
            })

        },
        unsubscribe() {
            if (this.subscriber) {
                this.subscriber.unsubscribe()
                this.subscriber = null
            }
        },
        async list(mxBoard: MxBoard, callback: TemplateAPICallback): Promise<TemplateViewModelArray> {
            return mxBoard.listTransactions().then(board => {
                const model = boardToTemplateViewModel(mxBoard.basicInfo) as TemplateViewModel
                const items = transactionToTemplateViewModel(board, model)
                callback(items, model.id as string)
                return items
            })
        },
        async updateFile(mxBoard: MxBoard, item: TemplateViewModel, transaction: BoardTransaction, callback: Function) {
            return mxBoard.updateTransaction(item.id as number, transaction).then(() => {
                callback(item, transaction)
            })
        },
        async readThread(mxBoard: MxBoard, item: TemplateViewModel) {
            const trans = mxBoard.getTransactionBaseObject(item.id as number)
            return mxBoard.readThread(trans).then((board) => {
                const [transaction] = board.transactions
                return ThreadUtils.computeTransactionBaseObject(transaction, board, MxISDK.getCurrentUser().user)
            })
        },
        async subscribeThread(mxBoard: MxBoard, item: TemplateViewModel, callback: MxCallback<Board>) {
            const trans = mxBoard.getTransactionBaseObject(item.id as number)
            return mxBoard.subscribeThread(trans, callback)
        }

    }
}
export default FormAPIs
