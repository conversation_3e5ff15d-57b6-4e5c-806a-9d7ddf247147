import { <PERSON>Logger } from '@controller/common/decorators'
import { MxISDK } from 'isdk'
import { MepxSDK } from 'mepxSDK'
import utils from "@views/common/utils/utils"
import DEFAULTGROUPBINDERIMAGE from "@views/theme/src/images/dashboard/conversation_group.png"
let advancedDashboardReportsController: AdvancedDashboardReportsController
import { ObjectUtils } from '@commonUtils'

@ClassLogger()
export class AdvancedDashboardReportsController {
  private currentRequest: Promise<any[]> | void = null
  private constructor() { }
  static getInstance(): AdvancedDashboardReportsController {
    if (!advancedDashboardReportsController) {
      advancedDashboardReportsController = new AdvancedDashboardReportsController()
    }
    return advancedDashboardReportsController
  }

  destroyDashboardReports() {
    advancedDashboardReportsController = null
  }

  async fetchWorkspaceConfig(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchWorkspaceConfig(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  async updateWorkspaceConfig(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.updateWorkspaceConfig(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  async updateConfigOrder(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.updateConfigOrder(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  fetchWorkspaces(fullPayload): Promise<any> {
    interface NewObject {
      workspace_id: string
      workspace_name: string
      workspace_type: string
      flow_progress_percentage: string
      processed_workspace_image: string
      processed_group_binder_image: string
      is_file_request: boolean
      is_external: boolean
      is_external_workspace: boolean
      is_service_request: boolean
      flow_type: string
      step_flow_type: string
      thumbnail_sequence: number
      total_steps: number
      processed_steps: number
      processed_total_steps: string,
      open_steps: number
      open_actions_count: number
      open_actions_list: any[]
      processed_step_type_name: string
      current_action_icon: string
      current_step_type: string
      current_step_sub_type: string
      current_step_title: string
      current_step_status: string
      owned_by: string
      assignees: string[]
      file_request_uploaders: any[]
      file_request_reviewers: any[]
      file_request_uploader_type: string
      no_of_assignees: number
      assignee_type: string
      flow_template_id: string
      flow_template_name: string
      workspace_status: string
      workspace_status_background_color: string
      processed_workspace_status: string
      processed_waiting_to_text: string
      created_on: string
      formatted_created_at_date: string
      last_updated: string
      workspace_due_date: string
      formatted_last_updated_at: string
      completed_at: string
      formatted_completed_at: string
      duration_in_seconds: number
      formatted_duration: string
      owner_id: string
      team_user_id?: string
    }

    const payload = ObjectUtils.cloneDeep(fullPayload)

    if (payload.filter_config && payload.filter_config.quick_params && payload.filter_config.quick_params.length) {
      payload.filter_config.quick_params = payload.filter_config.quick_params.sort((a, b) => a.name.localeCompare(b.name)).filter(item => {
        return item.string_value
      })
    }
    if (payload.filter_config && payload.filter_config.advanced_params && payload.filter_config.advanced_params.line_items && payload.filter_config.advanced_params.line_items.length) {
      payload.filter_config.advanced_params.line_items.sort((a, b) => a.filter.localeCompare(b.filter))
    }
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchWorkspaces(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request

      request
        .then((res: any) => {
          const response_data = {
            total_records: 0,
            total_records_by_kanban_column: {},
            list: []
          }

          const formattedArray = []

          if (res.workspaces && res.workspaces.length) {

            res.workspaces.forEach((item) => {
              item.action_flow_type_name = processActionFlowType(item?.assignees).toUpperCase()
              const details = processAssigneeDetails(item);
              const new_object: NewObject = {
                workspace_id: item?.binder_id,
                workspace_name: item?.binder_name,
                workspace_type: item?.binder_type,
                flow_progress_percentage: calculatePercentage(item),
                processed_workspace_image: processWorkspaceImage(item),
                processed_group_binder_image: processGroupBinderImage(item),
                direct_binder_image: processImage(item),
                is_file_request: isFileRequest(item),
                is_external: item?.is_external,
                is_external_workspace: isExternalWorkspace(item),
                flow_type: item?.flow_type,
                step_flow_type: item?.step_flow_type,
                thumbnail_sequence: item?.thumbnail_sequence,
                total_steps: item?.total_steps,
                processed_steps: item?.processed_steps,
                open_steps: item?.open_steps ? item.open_steps.length : 0,
                open_actions_count: item?.open_actions ? item.open_actions.length : 0,
                is_service_request: item?.is_service_req,
                current_step_type: item?.current_step_type,
                current_step_sub_type: item?.obj_sub_type,
                current_step_title: item?.binder_obj_title,
                current_step_status: item?.current_step_status,
                owned_by: item?.owned_by,
                assignees: item?.assignees,
                file_request_uploaders: details.file_request_uploaders,
                file_request_reviewers: details.file_request_reviewers,
                file_request_uploader_type: details.file_request_uploader_type,
                no_of_assignees: item.assignees?.length || 0,
                assignee_type: item?.assignee_type,
                flow_template_id: item?.flow_tmpl_binder_id,
                flow_template_name: item?.flow_tmpl_name,
                workspace_status: item?.workspace_status,
                workspace_status_background_color: statusBackgroundColor(item?.workspace_status),
                created_on: item?.started_on,
                formatted_created_at_date: formatCreatedAtDate(item?.started_on, payload.current_user_timezone),
                last_updated: item?.last_updated,
                workspace_due_date: utcToReadable(item?.due_date),
                formatted_completed_at: formatCreatedAtDate(item?.ended_on, payload.current_user_timezone),
                duration_in_seconds: item?.duration_in_seconds,
                owner_id: item?.owner_id,
                ...item
              }
              if (item?.team_user_id) {
                new_object.team_user_id = item.team_user_id
              }

              formattedArray.push(new_object)
            })

            if (formattedArray.length) {
              response_data.list = formattedArray
            }
          }

          if (res.total_records) {
            response_data.total_records = res.total_records
          }

          if (res.total_records_by_kanban_column) {
            response_data.total_records_by_kanban_column = res.total_records_by_kanban_column
          }

          resolve(response_data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  async fetchActionConfig(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchActionConfig(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  async updateActionConfig(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.updateActionConfig(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  fetchActions(fullPayload): Promise<any> {
    interface NewObject {
      action_name: string
      action_type: string
      action_sub_type: string
      action_obj_type: string
      current_action_icon: string
      processed_step_type_name: string,
      workspace_name: string
      workspace_id: string
      workspace_type: string
      workspace_object_sequence: number
      workspace_object_step_sequence: number
      workspace_status: string
      workspace_created_at: number
      workspace_due_date: number
      is_file_request: boolean,
      is_external: boolean
      is_external_workspace: boolean
      is_service_request: boolean
      action_status: string
      action_status_background_color: string
      processed_action_status: string,
      step_status: string
      assignee: string
      assignee_user_id: string
      assignee_type: string
      assignees: any[]
      file_request_uploaders: any[]
      file_request_reviewers: any[]
      file_request_uploader_type: string
      action_flow_type: string
      action_flow_type_name: string
      due_date: string
      formatted_due_date: string
      processed_waiting_to_text: string,
      creator: string
      creator_user_id: string
      duration_in_seconds: number
      formatted_duration: string,
      created_at: string
      updated_at: string
      formatted_last_updated_at: string
      completed_at: string
      formatted_completed_at: string
      custom_data: any
      owner_id: string
      team_user_id?: string
    }

  const payload = ObjectUtils.cloneDeep(fullPayload)
    if (payload.filter_config && payload.filter_config.quick_params && payload.filter_config.quick_params.length) {
      payload.filter_config.quick_params = payload.filter_config.quick_params.sort((a, b) => a.name.localeCompare(b.name)).filter(item => {
        return item.string_value
      })
    }
    if (payload.filter_config && payload.filter_config.advanced_params && payload.filter_config.advanced_params.line_items && payload.filter_config.advanced_params.line_items.length) {
      payload.filter_config.advanced_params.line_items.sort((a, b) => a.filter.localeCompare(b.filter))
    }
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchActions(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      request
        .then((res: any) => {

          const response_data = {
            total_records: 0,
            total_records_by_kanban_column: {},
            list: []
          }

          const formattedArray = []

          if (res.actions && res.actions.length) {
            res.actions.forEach((item) => {
              const details = processAssigneeDetails(item);
              item.action_flow_type_name = processActionFlowType(item?.assignees).toUpperCase()
              const new_object: NewObject = {
                action_name: item?.binder_obj_title,
                action_type: item?.obj_type_name,
                action_sub_type: item?.obj_sub_type,
                action_obj_type: item?.obj_type_id,
                workspace_name: item?.binder_name,
                workspace_id: item?.binder_id,
                workspace_type: item?.binder_type,
                workspace_object_sequence: item?.binder_obj_seq,
                workspace_object_step_sequence: item?.binder_obj_step_seq,
                workspace_status: item?.workspace_status,
                workspace_created_at: item?.workspace_created_at,
                workspace_due_date: item?.workspace_due_date,
                is_file_request: isFileRequest(item),
                is_external: item?.is_external,
                is_external_workspace: isExternalWorkspace(item),
                is_service_request: item?.is_service_req,
                action_status: item?.action_status,
                action_status_background_color: statusBackgroundColor(item?.action_status),
                step_status: item?.step_status,
                assignee: item?.assignee,
                assignee_user_id: item?.assignee_user_id,
                assignee_type: item?.assignee_type,
                assignees: item?.assignees,
                file_request_uploaders: details.file_request_uploaders,
                file_request_reviewers: details.file_request_reviewers,
                file_request_uploader_type: details.file_request_uploader_type,
                action_flow_type: item?.action_flow_type,
                action_flow_type_name: item?.action_flow_type_name,
                due_date: item?.due_date,
                formatted_due_date: utcToReadable(item?.due_date),
                creator: item?.creator,
                creator_user_id: item?.creator_user_id,
                duration_in_seconds: item?.duration_in_seconds,
                created_at: item?.created_at,
                updated_at: item?.updated_at,
                formatted_completed_at: formatCreatedAtDate(item?.completed_at, payload.current_user_timezone),
                custom_data: item?.custom_data,
                owner_id: item.owner_id,
                ...item
              }
              if (item?.team_user_id) {
                new_object.team_user_id = item.team_user_id
              }
              formattedArray.push(new_object)
            })

            if (formattedArray.length) {
              response_data.list = formattedArray
            }
          }

          if (res.total_records) {
            response_data.total_records = res.total_records
          }

          if (res.total_records_by_kanban_column) {
            response_data.total_records_by_kanban_column = res.total_records_by_kanban_column
          }

          resolve(response_data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  exportWorkspaceData(payload) {
    if (payload.filter_config.applied_filter_type === 'advanced') {
      delete payload.filter_config.quick_params
    } else {
      delete payload.filter_config.advanced_params
      payload.filter_config.quick_params = payload.filter_config.quick_params.filter(item => item.string_value)
    }
    return MepxSDK.exportWorkspaceData(payload)
  }

  exportActionData(payload) {
    if (payload.filter_config.applied_filter_type === 'advanced') {
      delete payload.filter_config.quick_params
    } else {
      delete payload.filter_config.advanced_params
      payload.filter_config.quick_params = payload.filter_config.quick_params.filter(item => item.string_value)
    }
    return MepxSDK.exportActionData(payload)
  }

  async fetchTemplates(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchTemplates(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  async fetchMilestones(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchMilestones(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  async fetchTemplateSteps(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchTemplateSteps(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  async fetchUsers(payload): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = MepxSDK.fetchWorkspaceUsers(payload)
      if (this.currentRequest) {
        MxISDK.abortRequest(this.currentRequest)
        this.currentRequest = null
      }
      this.currentRequest = request
      return request
        .then((res: any) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
}

function utcToReadable(datetime) {
  const utc_milliseconds = Number(datetime);
  if (!isNaN(utc_milliseconds) && utc_milliseconds > 0) {
    const date = new Date(utc_milliseconds);
    return date.toLocaleDateString("en-US", { month: "short", day: "2-digit", year: "numeric" });
  }
  return "-";
}

function processWorkspaceImage(item) {
  return item?.thumbnail_sequence ? `/board/${item.binder_id}/${item.thumbnail_sequence}` : '';
}

function processGroupBinderImage(item) {
  return item?.thumbnail_sequence
    ? `/board/${item.workspace_id}/${item.thumbnail_sequence}`
    : DEFAULTGROUPBINDERIMAGE;
}

function isExternalWorkspace(item) {
  const assignees = item?.assignees || [];
  if (assignees.length) {
    return assignees.some(({ assignee_type }) =>
      ["user_type_local", "group_type_client_team"].includes(assignee_type?.toLowerCase())
    );
  }
  return false;
}

function isFileRequest(item) {
  const actionType = item?.current_step_type || item?.obj_type_name || '';
  return actionType.toLowerCase() === 'file request';
}

function formatCreatedAtDate(datetime, current_user_timezone) {
  if (datetime) {
    return utils.formatDisplayedTime(datetime, {
      now: Date.now(),
      displayCompactDate: true,
      timezone: current_user_timezone,
    })
  }
  else {
    return '-'
  }
}

function calculatePercentage(item) {
  return Math.round(
    (parseInt(item.processed_steps) / parseInt(item.total_steps)) * 100
  )
}

function statusBackgroundColor(status) {
  if (status) {
    status = status.toUpperCase()
    if (status == "OPEN") {
      return "status-blue"
    } else if (status == "COMPLETED") {
      return "status-green"
    } else if (status == "DUE_TODAY") {
      return "status-orange"
    } else if (status == "DUE_TOMORROW") {
      return "status-orange"
    } else if (status == "DUE_IN_7_DAYS") {
      return "status-orange"
    } else if (status == "OVERDUE") {
      return "status-red"
    } else if (status == "CANCELLED") {
      return "status-grey"
    }
    else {
      return 'status-blue'
    }
  }
  else {
    return 'status-blue'
  }
}

function processImage(item) {
  return "/user/" + item.thumbnail_sequence
}

function processAssigneeDetails(item) {
  const step_type = item.current_step_type || item.obj_type_name || ''

  const details = {
    file_request_uploaders: [],
    file_request_reviewers: [],
    file_request_uploader_type: ''
  }

  if (item.assignees && item.assignees.length) {
    let currentStepOrder = null;
    let stepOrderNumber = 0;

    item.assignees.sort((a, b) => a.step_order - b.step_order);
    item.assignees.forEach((assignee) => {
      assignee.step_status = assignee?.status
      if (assignee.step_order !== currentStepOrder) {
        currentStepOrder = assignee.step_order;
        stepOrderNumber++;
      }
      assignee.step_order_number = stepOrderNumber;
    });

    // File Request
    if (step_type.toLowerCase() === 'file request') {
      const maxStepOrder = item.assignees[item.assignees.length - 1].step_order
      const uploaders = item.assignees.filter((d) => d.step_order !== maxStepOrder)
      const reviewers = item.assignees.filter((d) => d.step_order === maxStepOrder)

      details.file_request_uploader_type = processActionFlowType(uploaders)

      let prevObj = null
      const updateStatus = (array) => {
        const allSameStepOrderAndOpen = array.every(item => item.step_order === array[0].step_order && item.status.toLowerCase() === "open");
        let inProgressFound = false
        return array.map((item, index) => {
          if (allSameStepOrderAndOpen) {
            item.status = 'in_progress'
          } else {
            if (item.status?.toLowerCase() === 'in_progress') {
              item.status = item.isReviewer ? 'waiting_to_review' : 'waiting_to_upload'
            } else if (item.status?.toLowerCase() === 'open' && !inProgressFound) {
              item.status = 'in_progress'
              inProgressFound = true
            } else if (item.status?.toLowerCase() === 'open' && inProgressFound) {
              item.status = 'waiting_to_upload'
            }
            if (
              prevObj &&
              prevObj.step_order === item.step_order &&
              !['waiting_to_upload', 'waiting_to_review', 'completed', 'skipped'].includes(
                item?.status.toLowerCase()
              )
            ) {
              item.status = 'in_progress'
            }
            prevObj = item
          }
          return item
        })
      }
      const updateStatusReviewer = (array, isAllCompleted) => {
        return array.map((item, index) => {
          if (item.status?.toLowerCase() === 'open') {
            if (isAllCompleted) {
              item.status = 'in_progress'
            } else {
              item.status = 'waiting_to_review'
            }
          }
          return item
        })
      }

      const updatedUploaded = updateStatus(uploaders)
      details.file_request_uploaders = updatedUploaded
      const isAllCompleted = updatedUploaded.every(
        (item) => item.status?.toLowerCase() === 'completed'
      )
      details.file_request_reviewers = updateStatusReviewer(reviewers, isAllCompleted)
    }
    else {
      if (processActionFlowType(item?.assignees)?.toUpperCase() === 'SEQUENCIAL') {
        let sequenceNumber = 0
        item?.assignees.forEach((assignee) => {
          assignee.step_status = assignee.status?.toLowerCase()
          if ((assignee?.step_status?.toLowerCase() == 'open') && sequenceNumber == 0) {
            assignee.step_status = 'in_progress'
            assignee.status = 'in_progress'
            sequenceNumber = Number(assignee.step_order_number)
          } else if (sequenceNumber == assignee?.step_order_number && assignee?.step_status.toLowerCase() == 'open') {
            assignee.step_status = 'in_progress'
            assignee.status = 'in_progress'
          } else if (assignee?.status?.toLowerCase() == 'open') {
            assignee.step_status = 'waiting_to_review'
            assignee.status = 'waiting_to_review'
          }
        })
      }
      else if (processActionFlowType(item?.assignees)?.toUpperCase() === 'PARALLEL') {
        item?.assignees.forEach((assignee) => {
          assignee.step_status = assignee.status?.toLowerCase()
          if (assignee?.status?.toLowerCase() == 'open') {
            assignee.step_status = 'in_progress'
            assignee.status = 'in_progress'
          }
          else if (assignee?.step_status?.toLowerCase() == 'in_progress') {
            assignee.step_status = 'waiting_to_review'
            assignee.status = 'waiting_to_review'
          }
          else {
            assignee.step_status = assignee?.status?.toLowerCase()
            assignee.status = assignee.step_status
          }
        })
      }
    }
  }
  return details
}

const processActionFlowType = (assignees: any[]): string => {
  if (assignees && assignees.length) {
    const allStepOrdersSame = assignees.every(item => Number(item.step_order) === Number(assignees[0].step_order));
    if (allStepOrdersSame) {
      return 'PARALLEL';
    } else {
      return 'SEQUENCIAL';
    }
  }
  return '';
};