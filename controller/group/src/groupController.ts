import {Defines, MxAjax, MxGroup, MxISDK} from 'isdk'
import JsZ<PERSON> from 'jszip'
import {
  ClientResponseCode,
  GroupSubscriptionStatus,
  GroupUser,
  GroupUserRoleType,
  GroupUserStatus,
  MxGroupManagementFilter,
  UserIdentity,
  UserTag as GroupTag,
  UserType,
  UserGroup,
  GroupType,
  GroupAccessType, UserRelationStatus, MxGroupMemberFilter,
  BoardAccessType,
  GroupIntegration
} from 'isdk/src/api/defines'
import {UserFormatter} from '@controller/utils/user'
import {CGroupUser} from '@controller/defines/CGroupUser'
import {CUserValidateState} from '@controller/defines/CUserValidateState'
import {CBaseGroup} from '@controller/defines/CBaseGroup'
import {CQuickLink} from '@controller/defines/CQuickLink'
import {BrowserUtils, FunctionUtil, ObjectUtils} from '@controller/utils'
import {transformError} from '@controller/common/decorators'
import {OnlineStatus} from '@controller/defines/EOnlineStatus'
import {CAcdChannelInfo,CSrChannelInfo} from '@controller/defines/CAcdChannelInfo';
import {websdkController} from '@controller/websdk/WebsdkController'
import { CTeam } from '@controller/defines/CTeam.d'
import { drawOrgSquareLogo } from '@controller/utils/brand'
import { saveBlobAsFile } from '@commonUtils/blob'
import { mxWebLogIndexedDB } from '@commonUtils/mxWeLogIndexedDB'
import { MxConsts } from '@commonUtils/consts'
import {PrivilegesController} from '@controller/privileges/src/privilegesController';
import cloneDeep from 'lodash/cloneDeep';

let groupController: GroupController

export class GroupController {
  private currentGroup: MxGroup
  private currentRequest: Promise<Defines.Group> | void = null
  private groupBasicInfoSubscriber: Defines.MxSubscription
  private privilegesResolveCallback: Function
  private teamInfoResolveCallback: Function
  private quickLinkSubscriber: Defines.MxSubscription
  private currentGetMemberRequest: Promise<Defines.Group> | void = null
  private _tags: object
  private _isM0APP: boolean

  private constructor () {
    this.currentGroup = MxISDK.getCurrentOrg()
  }

  get brandName () {
    return this.currentGroup.tags['OrgConfig_BrandName']
  }

  static getContextPath () {
    return MxISDK.getContextPath()
  }

  @transformError()
  static getUserBasicInfoByQRToken (token: string): Promise<CGroupUser> {
    return MxISDK.decodeQRToken(token).then((group: Defines.Group) => {
      const user = GroupController.transformGroupUser(group.members, group.id)[0]
      if (user.avatar) {
        user.avatar = `${user.avatar}?t=${token}`
      }
      return user
    })
  }

  static formatQuickLinks (data: string, filterWebOnly: boolean): CQuickLink[] {
    //filterWebOnly: web client portal and internal portal will filter out platform is android or ios
    //admin will need all the data
    let arr = JSON.parse(data) as any[]
    let result = [] as CQuickLink[]
    if (filterWebOnly) {
      arr = arr.filter((link) => {
        //old data might have no platform field
        return !link.platform || link.platform.indexOf('web') > -1
      })
    }
    if (arr && arr.length) {
      arr.forEach(link => {
        let quickLink = {} as CQuickLink
        quickLink.name = link.name || ''
        quickLink.description = link.description || ''
        quickLink.icon = link.icon || ''
        quickLink.identity = link.identity || ''
        quickLink.order_number = link.order_number || 0
        quickLink.url = link.url || ''
        quickLink.iframe_code = link.iframe_code || ''
        quickLink.tiled_icon = link.tiled_icon || ''
        quickLink.open_in_native_browser = link.open_in_native_browser
        quickLink.roles = link.roles
        result.push(quickLink)
      })
    }
    return result
  }

  get tags () {
    return this._tags || {}
  }

  get isM0APP () {
    return this._isM0APP
  }

  static transformOrgTags (tags: Array<GroupTag>): object {
    let result = {
      'Show_App_Review': true,
      'Show_Send_Feedback': true,
      'Show_Todo': false,
      'enable_e-sign': true,
      'SHOW_MEET_LINK': true,
      'START_WITH_RECORDING': false,
      'Enable_Recording_Meetings': true,
      'Start_Meet_From_Binder': true,
      'Show_Binder_Options': true,
      'Show_Binder_Name': true,
      'Show_Binder_Email_Address': true,
      'Show_New_Binder': true,
      'Show_Meet_Now': true,
      'Show_Schedule_Meet': true,
      'Show_Meet_Share_Screen': true,
      'Enable_Call': true,
      'Enable_Attendee_Annotate_In_Meet': true,
      'Enable_Attendee_Chat_In_Meet': true,
      'Show_Meet_Audio': true,
      'Show_Meet_Video': true,
      'Show_Meet_Remote_Control': true,
      'Enable_Meet_Telephony': true,
      'Allow_Join_Before_Host': true,
      'Disable_Auto_Join_Meet_Audio': true,
      'Show_Presence_Indicator': true,
      'Allow_CopyMove_AnotherBinder': true,
      'Show_Share': true,
      'Enable_VCF': true,
      'Show_Deleted_User_Name': true,
      'Enable_Secure_Token_Url': true,
      'Enable_Welcome_Message': true,
      'Enable_Presence_Status': true,
      'Enable_Calendar': true,
      'Show_Help_Support': true,
      'Hide_Terms_Policy': false,
       OrgConfig_Enable_Customize_Link: false,
      'Enable_Share_File_Warning': false,
      'enable_copy_to': true,
      'Enable_Conversation_Email_Address_for_Client': true,
      'Enable_Client_Notification_Settings': true,
      "Show_Previews_for_Links": true,
      "Enable_Password_Show_Button": true,
      "Enable_Remember_Me": true,
      "Allow_Edit_Internal_Personal_Info": true,
      'Enable_Approval': true,
      'Enable_Acknowledgement': true,
      'Enable_Form': true,
       'Enable_PDF_Form': true,
      'Enable_DocuSign': false,
      'Enable_Launch_Web_App': false,
      'Enable_File_Request': true,
      'Enable_User_Behavior_Logging': true,
      Enable_HostPresenterChat_In_Meet: true,
      'Hide_Action_Entries': false,
      'Enable_Flow_Gallery': true,
      Enable_Weblink_Upload: true,
      enable_magic_link: true,
      'hide_team_in_dashboard': false,
      Disable_Create_Collaborator_Conversation: false,
      Enable_Jumio: false,
      Enable_Other_Integration_Apps: false,
      Enable_Workspace_Link: false,
      Enable_Meet_Request: true,
      Show_Emoji_Btn: true,
      Show_User_Personal_Identification_Info: true,
      Hide_Logout_Button_For_Clients: false,
      Show_Flow_Welcome_Page: true,
      enable_ai: false,
    }

    const stringTypeTagNames = [
      'B_Branding_Color',
      'B_Incomming_Msg_BgColor',
      'B_Outgoing_Msg_BgColor',
      'B_Branding_Foreground_Color',
      'B_Branding_Background_Color',
      'OrgConfig_BrandName',
      'Main_White_Square_Logo',
      'Main_Color_Rectangle_Logo',
      'Main_White_Rectangle_Logo',
      'Main_Color_Square_Logo',
      'Main_Color_Rectangle_Logo_Left',
      'Email_Header_Logo',
      'Moxtra_Logo_Url',
      'OrgConfig_DownloadLink_iOS',
      'OrgConfig_DownloadLink_Android',
      'OrgConfig_DownloadLink_Mac',
      'OrgConfig_DownloadLink_Windows',
      'White_Logo_Moxtra_Powerby',
      'Note_Watermark_Image',
      'B_Branding_Pre_Login_Background_Image',
      'Sms_Prefix',
      'B_Term_Set_Client',
      'B_Term_Set_Internal',
      'Maximum_Allowed_Acd_Channels',
      'API_CUS_TAC',
      'API_Org_Policy_URL',
      'Quick_Links_Client',
      'Quick_Links_Internal',
      'SAML_Logout_Url',
      'OrgConfig_Customize_Link',
      'OrgConfig_Customize_Link_Label',
      'OrgConfig_DownloadLink_Windows',
      'OrgConfig_DownloadLink_Mac',
      'Client_Portal_Direct_Conversation_Section_title',
      'B_Term_Set_Workspace',
      'terms_group_name',
      'Session_Service_Type',
    ]

    const integerTypeTagNames = [
        'Session_Timeout_Interval',
        'Web_Session_Timeout_Interval',
        'Web_Session_Timeout_Client',
        'Session_Timeout_Client',  //mobile only
        'binder_view_token_timeout',
        'Group_workspace_required_client_users'
    ]
    tags.forEach((tag: GroupTag) => {
      let tagValue: any
      if (integerTypeTagNames.indexOf(tag.name) > -1) {
        tagValue = parseInt(tag.string_value)
      } else if (stringTypeTagNames.indexOf(tag.name) > -1) {
        if (/^\/group\/\w+\/resource\//gi.test(tag.string_value)) {
          tagValue = MxISDK.getContextPath() + tag.string_value
        } else {
          tagValue = tag.string_value
        }
      } else if (tag.string_value === '1' || tag.string_value === 'true') {
        tagValue = true
      } else if (tag.string_value === '0' || tag.string_value === 'false') {
        tagValue = false
      } else {
        tagValue = tag.string_value
      }
      result[tag.name] = tagValue
    })
    return result
  }

  getQuickLinks (isClient, filterWebOnly, resolve: Function): void {
    if (!this.quickLinkSubscriber) {
      let tags = this.currentGroup.tags
      resolve(this.parseQuickLinks(tags, filterWebOnly, isClient))
      this.quickLinkSubscriber = this.currentGroup.subscribeBasicInfo(group => {
        const tags = this.currentGroup.tags
        resolve(this.parseQuickLinks(tags, filterWebOnly, isClient))
      })
    }
  }

  private parseQuickLinks (tags, filterWebOnly, isClient) {
    let quickLinks = isClient ? tags['Quick_Links_Client'] : tags['Quick_Links_Internal']
    if (quickLinks) {
      return GroupController.formatQuickLinks(quickLinks, filterWebOnly)
    } else {
      return []
    }
  }

  static transformGroupUser(
      members: Array<GroupUser>, id: string,
      includeAll: boolean = false, includeMe: boolean = true,
      userIsClientTeamManager: boolean = false) {
    let membersList: Array<CGroupUser> = []
    members.forEach((member: GroupUser) => {
      let result: CGroupUser = Object.assign({}, member) as CGroupUser
      Object.assign(result, UserFormatter.transformToBaseUser(member.user))
      result.avatar = GroupController.getGroupUserAvatar(member, id, userIsClientTeamManager)
      result.uniqueKey = 'G' + result.sequence
      result.onlineStatus = result.user.online ? OnlineStatus.Online : OnlineStatus.Offline
      result.title = result.isInternalUser ? result.title : result.email
      result.isPendingRelation = !result.isInternalUser && member.status === Defines.GroupUserStatus.GROUP_INVITED
      result.relationCount = result.user && result.user.relations_total ? result.user.relations_total : 0
      let currentUser = MxISDK.getCurrentUser()
      let isMe = false
      if (currentUser) {
        isMe = currentUser.id === result.id
        let relation = currentUser.getUserRelationByUserId(result.id)
        if (relation && relation.status === UserRelationStatus.RELATION_PENDING) {
          result.isPendingRelation = true
        }
      }
      result.type = member.type
      result.roleSequence = member.role
      if (includeAll) {
        membersList.push(result)
      } else if (!member.is_deleted && !ObjectUtils.getByPath(member, 'user.disabled')) {
        if (includeMe || (!includeMe && !isMe)) {
          membersList.push(result)
        }
      }
    })
    return membersList
  }

  static getGroupUserAvatar (groupUser: GroupUser, id: string, userIsClientTeamManager: boolean = false) {
    let user = groupUser.user
    if (user) {
      let picture = user.picture4x || user.picture2x || user.picture
      if (picture) {
        return `${MxISDK.getContextPath()}/group/${id}/${userIsClientTeamManager ? 'manager' : 'member'}/${groupUser.sequence}/${picture}`
      } else {
        return UserFormatter.getInitialAvatar(user.first_name || '', user.last_name || '')
      }
    }
    return ''
  }

  @transformError()
  getGroupMember (user: UserIdentity): Promise<CGroupUser> {
    return this.currentGroup.readMember(user).then(group => {
      const members = group.members
      if (members.length) {
        return GroupController.transformGroupUser(members, this.currentGroup.id)[0]
      }
      return null
    })
  }

  @transformError()
  readGroupMember (user: UserIdentity) {
    return this.currentGroup.readMembers([user])
  }

  loadGroupMembers (startSequence = 0, {
    includeInternalUser = true,
    includeRelationUser = true,
    includeSuggestedUser = true,
    includeMyRelationUserOnly = false,
    involveAll = false,
    involveMe = false,
    outputClientTeam = true
  }) {
    const PAGE_SIZE = 200
    let disableCreateCollaboratorConversation = this.tags['Disable_Create_Collaborator_Conversation']
    const gmFilter = {
      includeRelationUser,
      includeSuggestedUser: includeSuggestedUser && !disableCreateCollaboratorConversation,
      isInternal: includeInternalUser,
      isLocal: includeMyRelationUserOnly,
      outputClientTeam
    }
    return this.currentGroup.searchMembers(startSequence, PAGE_SIZE, this.handleSearchAllUserLogic(gmFilter)).then((group: Defines.Group) => {
      const members = (group.members || []) as Array<GroupUser>
      return {
        members: GroupController.transformGroupUser(members, this.currentGroup.id, involveAll, involveMe),
        hasNextPage: members.length >= PAGE_SIZE
      }
    })
  }

  getMyClientTotalCount(startSequence = 0, {
    includeInternalUser = false,
    includeRelationUser = true,
    includeSuggestedUser = true,
    includeMyRelationUserOnly = true,
    outputClientTeam = true
  }) {
    const PAGE_SIZE = 1
    let disableCreateCollaboratorConversation = this.tags['Disable_Create_Collaborator_Conversation']
    let gmFilter = {
      includeRelationUser,
      includeSuggestedUser: includeSuggestedUser && !disableCreateCollaboratorConversation,
      isInternal: includeInternalUser,
      isLocal: includeMyRelationUserOnly,
      outputClientTeam,
      outputCount:true
    }
    return this.currentGroup.searchMembers(startSequence, PAGE_SIZE, gmFilter).then((group: Defines.Group) => {
      const totalCount = (group.total_members || 0) as number
      return totalCount
    })
  }


  fetchGroupMembers (startSequence = 0, searchKey: string, {
    includeInternalUser = true,
    includeRelationUser = true,
    includeSuggestedUser = true,
    includeMyRelationUserOnly = false,
    onlyGroupNormalUser = false, // API level to read normal group member
    involveAll = false, // function level
    involveMe = false,
    outputClientTeam = true
  }): Promise<CGroupUser[]> {
    const PAGE_SIZE = 200
    const disableCreateCollaboratorConversation = this.tags['Disable_Create_Collaborator_Conversation']
    const gmFilter = {
      includeRelationUser,
      includeSuggestedUser: includeSuggestedUser && !disableCreateCollaboratorConversation,
      isInternal: includeInternalUser,
      isNormal: onlyGroupNormalUser,
      isLocal: includeMyRelationUserOnly,
      searchKey,
      outputClientTeam
    }
    const request = this.currentGroup.searchMembers(startSequence, PAGE_SIZE, this.handleSearchAllUserLogic(gmFilter))
    if (this.currentRequest) {
      MxISDK.abortRequest(this.currentRequest)
      this.currentRequest = null
    }
    this.currentRequest = request
    return request.then((group: Defines.Group) => {
      const members = group.members || [] as Array<GroupUser>
      let transformedMembers = GroupController.transformGroupUser(members, this.currentGroup.id, involveAll, involveMe)
      Object.defineProperty(transformedMembers, 'noNextPage', {
        value: !members.length || (members.length < 200)
      })
      return transformedMembers
    })
  }

  filterGroupMembers (startSequence = 0,size = 200, filter?: MxGroupMemberFilter, includeAll = false, includeMe = true){
    const request = this.currentGroup.searchMembers(startSequence, size, this.handleSearchAllUserLogic(filter))
    if (this.currentRequest) {
      console.warn('GroupController filterGroupMembers abort the currentRequest=',this.currentRequest);
      MxISDK.abortRequest(this.currentRequest)
      this.currentRequest = null
    }

    this.currentRequest = request
    return request.then((group: Defines.Group) => {
      const members = group.members || [] as Array<GroupUser>
      let transformedMembers = GroupController.transformGroupUser(members, this.currentGroup.id, includeAll, includeMe)
      Object.defineProperty(transformedMembers, 'noNextPage', {
        value: !members.length || (members.length < 200)
      })
      return transformedMembers
    })
  }

  searchAuditableUsers (searchKey: string, startSequence = 0, pageSize = 200, isForAudit = false, includeDeletedUser = false) {
    const request = this.currentGroup.searchMembers(startSequence, pageSize, {
      searchKey: searchKey,
      isForAudit,
      includeDeletedUser
    })
    this.currentRequest = request
    return request.then((group: Defines.Group) => {
      let members = group.members || [] as Array<GroupUser>
      return GroupController.transformGroupUser(members, this.currentGroup.id, true)
    })
  }

  getUsersOnlineStatus (contacts: Array<Defines.User>) {
    const currentUser = MxISDK.getCurrentUser()
    const identities = contacts.map((u) => {
      return {id: u.id}
    })
    if (identities.length > 1000) {
      return FunctionUtil.divideLargeRequest(currentUser.queryUserPresence.bind(currentUser), identities).then(userStatusArr => {
        let presenceArr = []
        if (userStatusArr && userStatusArr.length) {
          userStatusArr.forEach(status => {
            presenceArr = presenceArr.concat(status)
          })
        }
        return presenceArr.map((user: Defines.User) => {
          return {...user, status: UserFormatter.getUserStatus(user)}
        })
      })
    } else {
      return currentUser.queryUserPresence(identities).then((userStatus: Defines.UserPresence[]) => {
        return (userStatus || []).map((user: Defines.User) => {
          return {...user, status: UserFormatter.getUserStatus(user)}
        })
      })
    }
  }

  private handleSearchAllUserLogic(filter: MxGroupMemberFilter): MxGroupMemberFilter {
    const newFilter = cloneDeep(filter)
    if(newFilter.includeRelationUser && newFilter.includeSuggestedUser) {
      const privileges = PrivilegesController.getInstance().getPrivileges
      //Note: today, we will follow the logic as
      // if can_access_business_directory is false, whatever the can_view_all_clients is true or false
      // we should treat it as false, that means can_view_all_clients is based on the can_access_business_directory
      if(privileges.canViewAllClients && privileges.canAccessBusinessDirectory) {
        newFilter.includeRelationUser = false
        newFilter.includeSuggestedUser = false
        if(newFilter.isInternal) {
          newFilter.isInternal = false
        }
      }
    }
    return newFilter
  }

  static transformTeamInfo (teams: UserGroup[]) {
    const currentUser = MxISDK.getCurrentUser().basicInfo
    const currentUserTeams = currentUser.groups || []
    const userTeamMapper = {}
    for (let userTeam of currentUserTeams) {
      if (!userTeam.is_deleted) {
        const userTeamGroup = userTeam.group
        userTeamMapper[userTeamGroup.id] = {
          isManager: userTeam.type === GroupAccessType.GROUP_ADMIN_ACCESS
        }
      }
    }

    const routintConfig = MxISDK.getCurrentOrg().group.routing_config || {}
    const srChannels = routintConfig.sr_channels || []
    const srChannelTeamIds = []
    srChannels.forEach(channel => {
      if (channel.teams && channel.teams.length > 0) {
        const srTeam = channel.teams[0]
        if (!srTeam.is_deleted) {
          srChannelTeamIds.push(srTeam.group.id)
        }
      }
    })

    let acdChannels = routintConfig.acd_channels || []
    const acdChannelTeamIds = []
    acdChannels.forEach(channel => {
      if (channel.teams && channel.teams.length > 0) {
        const acdTeam = channel.teams[0]
        if (!acdTeam.is_deleted) {
          acdChannelTeamIds.push(acdTeam.group.id)
        }
      }
    })

    const __teams__: CTeam[] = []

    for (let team of teams) {
      if (!team.is_deleted && srChannelTeamIds.indexOf(team.group.id) === -1 && acdChannelTeamIds.indexOf(team.group.id) === -1) {
        const teamGroup = team.group
        if (teamGroup.type === GroupType.GROUP_TYPE_TEAM || teamGroup.type === GroupType.GROUP_TYPE_CLIENT_TEAM) {
          const __team__: CTeam = {
            id: teamGroup.id,
            name: teamGroup.name,
            sequence: team.sequence,
            memberCounts: teamGroup.total_members || 0,
            description: teamGroup.description,
            revision: team.revision,
            isClientTeam: teamGroup.type === GroupType.GROUP_TYPE_CLIENT_TEAM,
            type: teamGroup.type,
            isTeam: true
          }
          if (teamGroup.picture) {
            __team__.avatar = `/group/${teamGroup.id}/resource/${teamGroup.picture}`
          }
          if (userTeamMapper[teamGroup.id]) {
            __team__.isMember = true
            __team__.isManager = userTeamMapper[teamGroup.id].isManager
          } else {
            __team__.isMember = false
          }
          __teams__.push(__team__)
        }
      }
    }

    return __teams__.sort((t1, t2) => {
      const t1Name = (t1.name || '').toLowerCase()
      const t2Name = (t2.name || '').toLowerCase()
      if (t1Name > t2Name) {
        return 1
      } else {
        return -1
      }
    })
  }

  @transformError()
  readTeamInfo (teamId: string): Promise<CTeam> {
    return this.currentGroup.readTeam(teamId)
      .then(group => {
        return GroupController.transformTeamInfo([{group}])[0]
      })
  }

  getTeamManagers (teamId: string) {
    return this.currentGroup.readTeam(teamId).then(group => {
      let teamManagers = []
      if (group.managers) {
        for (let manager of group.managers) {
          if (!manager.is_deleted) {
            const memberUser = manager.user
            const contextPath = MxISDK.getContextPath()
            let userAvatar = ''
            let picture = memberUser.picture4x || memberUser.picture2x || memberUser.picture
            if (picture) {
              userAvatar = `${contextPath}/group/${teamId}/manager/${manager.sequence}/${picture}`
            } else {
              userAvatar = UserFormatter.getInitialAvatar(memberUser.first_name || '', memberUser.last_name || '')
            }
            const currentUser = MxISDK.getCurrentUser().basicInfo
            teamManagers.push({
              avatar: userAvatar,
              isDisabled: memberUser.disabled,
              phoneNumber: memberUser.phone_number,
              isMe: currentUser.id === memberUser.id,
              name: UserFormatter.getUserName(memberUser) || '',
              title: UserFormatter.getUserSubTitle(memberUser) || '-',
              ...ObjectUtils.pick(memberUser, ['id', 'first_name', 'email', 'display_email', 'display_phone_number', 'display_id', 'unique_id'])
            })
          }
        }
      }
      return teamManagers
    })
  }

  readTeamMembersByPagination (teamId: string, filter?: MxGroupMemberFilter) {
    return this.currentGroup.readTeamMembers(teamId, '', filter)
      .then(group => {
        const members = group?.members ? group.members : []
        const currentUser = MxISDK.getCurrentUser().basicInfo
        const meList = []
        const managerList = []
        const memberList = []
        for (let member of members) {
          if (!member.is_deleted) {
            const memberUser = member.user
            const contextPath = MxISDK.getContextPath()
            let userAvatar = ''
            let picture = memberUser.picture4x || memberUser.picture2x || memberUser.picture
            if (picture) {
              userAvatar = `${contextPath}/group/${teamId}/member/${member.sequence}/${picture}`
            } else {
              userAvatar = UserFormatter.getInitialAvatar(memberUser.first_name || '', memberUser.last_name || '')
            }
            let isOrgMember = false
            if (memberUser.groups) {
              isOrgMember = memberUser.groups[0].status === GroupUserStatus.GROUP_MEMBER
            }
            const parsedMember = {
              isMe: currentUser.id === memberUser.id,
              isManager: member.type === GroupAccessType.GROUP_ADMIN_ACCESS,
              isMember: member.status = GroupUserStatus.GROUP_MEMBER,
              isClient: memberUser.type === UserType.USER_TYPE_LOCAL,
              id: memberUser.id,
              sequence: member.sequence,
              name: UserFormatter.getUserName(memberUser) || '',
              avatar: userAvatar,
              title: UserFormatter.getUserSubTitle(memberUser) || '-',
              phoneNumber: memberUser.phone_number,
              isDisabled: memberUser.disabled,
              isOrgMember: isOrgMember,
              originalTitle: memberUser.title,
              ...ObjectUtils.pick(memberUser, ['first_name', 'email', 'display_email', 'display_phone_number', 'display_id', 'unique_id','email_verified','type']),
            }
            if (parsedMember.isMe) {
              meList.push(parsedMember)
            } else if (parsedMember.isManager) {
              managerList.push(parsedMember)
            } else {
              memberList.push(parsedMember)
            }
          }
        }
        const sortFn = (userA, userB) => {
          const userAName = userA.name.toLowerCase()
          const userBName = userB.name.toLowerCase()
          if (userAName > userBName) {
            return 1
          } else {
            return -1
          }
        }
        const sortedManageList = managerList.length > 1 ? managerList.sort(sortFn) : managerList
        const sortedMemberList = memberList.length > 1 ? memberList.sort(sortFn) : memberList
        return [...meList, ...sortedManageList, ...sortedMemberList]
      })
  }

  get getBasicInfo (): CBaseGroup {
    return GroupController.transformGroupBasicInfo(this.currentGroup.basicInfo, this.brandName)
  }

  static getOrgInfo (contextPath?: string) {
    return new Promise((resolve, reject) => {
      if (contextPath) {
        const reqPath = location.origin.replace(/https:\/\//gi, '') + contextPath
        const serviceUrl = `https://${reqPath}`
        const socketUrl = `wss://${reqPath}`
        MxISDK.initialize({contextPath, serviceUrl, socketUrl})
      }
      MxISDK.readOrgInfo()
        .then((group: MxGroup) => {
          let isOrgInvalid = false, isOrgUnavaialbel = false
          const basicInfo = group.basicInfo
          if (basicInfo.status === GroupSubscriptionStatus.GROUP_EXPIRED_SUBSCRIPTION){
            isOrgInvalid = !basicInfo.group_caps.enable_new_frame
            isOrgUnavaialbel = true
          } else if (basicInfo.status === GroupSubscriptionStatus.GROUP_DISABLED_SUBSCRIPTION || basicInfo.status === GroupSubscriptionStatus.GROUP_CANCELED_SUBSCRIPTION) {
            isOrgUnavaialbel = true
            isOrgInvalid = true
          }
          if (isOrgInvalid) {
            reject({
              isOrgInvalid: true,
              isOrgUnavaialbel
            })
          } else {
            resolve(GroupController.transformGroupBasicInfo(basicInfo, group.tags['OrgConfig_BrandName']))
          }
        })
        .catch(err => {
          if (err.code === ClientResponseCode.RESPONSE_ERROR_NOT_FOUND || err.code === ClientResponseCode.RESPONSE_ERROR_INVALID_REQUEST) {
            reject({
              isOrgInvalid: true
            })
          }
        })
    })
  }

  static transformRoutingChannels(config:Defines.RoutingConfig, id: string):CAcdChannelInfo[] {
    let contextPath = MxISDK.getContextPath()
    let transformedChannels = []
    if(config && config.acd_channels) {
      transformedChannels = config.acd_channels.filter(channel => !channel.is_deleted).map(channel => {
        const channlPic = channel.picture
        return {
          channel_id: channel.sequence,
          picture: channlPic ? `${contextPath}/group/${id}/channel/${channel.sequence}/picture?revision=${channlPic}` : '',
          name: channel.name,
          order_number: channel.order_number,
          bot_user: ObjectUtils.getByPath(channel, 'user.user.id')
        } as CAcdChannelInfo
      })

      transformedChannels = transformedChannels.sort((c1, c2) => parseInt(c1.order_number) - parseInt(c2.order_number))
    }
    return transformedChannels as CAcdChannelInfo[]
  }

  private static transformGroupBasicInfo (group: Defines.Group, brandName: string): CBaseGroup {
    let groupInfo = {} as CBaseGroup
    groupInfo.id = group.id
    groupInfo.name = brandName || group.name
    groupInfo.group_name = group.name // for inbox binder
    groupInfo.isOrg = true
    groupInfo.alias = group.alias
    groupInfo.plan_code = group.plan_code
    groupInfo.plan_quantity = group.plan_quantity
    groupInfo.plan_code_local = group.plan_code_local
    groupInfo.board_properties = group.board_properties
    groupInfo.plan_quantity_local = group.plan_quantity_local
    groupInfo.commitment_end_time = group.commitment_end_time
    groupInfo.cancellation_request_time = group.cancellation_request_time
    groupInfo.timezone = group.timezone
    groupInfo.tac = group.tac
    groupInfo.group_caps = group.group_caps
    groupInfo.group_settings = group.group_settings
    groupInfo.isFreemium = false
    groupInfo.created_time = group.created_time
    groupInfo.updated_time = group.updated_time
    groupInfo.status = group.status
    groupInfo.routing_config = group.routing_config
    groupInfo.tags = GroupController.transformOrgTags(group.tags || [])
    groupInfo.totalChargableMembers = group.total_members - (group.total_local_members || 0)
    groupInfo.originalTags = (group.tags || []).filter(tag => {
      return !tag.is_deleted
    }) // complete tags info
    groupInfo.acdChannels = GroupController.transformRoutingChannels(group.routing_config, group.id)

    const sr_channels = ObjectUtils.getByPath(group, 'routing_config.sr_channels')
    if(sr_channels && sr_channels.length){
      groupInfo.srChannels = sr_channels.sort((s1:CSrChannelInfo,s2:CSrChannelInfo)=>{ return  s1.sequence  - s2.sequence})
    }else{
      groupInfo.srChannels = []
    }

    const template_messages = ObjectUtils.getByPath(group, 'routing_config.template_messages')
    if(template_messages && template_messages.length){
      groupInfo.templateMessages = template_messages.filter(msg=>{return !msg.is_deleted}).sort((s1,s2)=>{ return  s1.name  - s2.name})
    }else{
      groupInfo.templateMessages = []
    }

    const template_messages_sr = ObjectUtils.getByPath(group, 'routing_config.template_messages_sr')
    if(template_messages_sr && template_messages_sr.length){
      groupInfo.templateMessagesSR = template_messages_sr.filter(msg=>{return !msg.is_deleted}).sort((s1,s2)=>{ return  s1.name  - s2.name})
    }else{
      groupInfo.templateMessagesSR = []
    }

    groupInfo.isSRChannelsInit = sr_channels && sr_channels.length > 0

    groupInfo.integrations = (group.integrations || []).map(int => {
      // MVB-20529, use group logo as inbox bot avatar. MxISDK.getCurrentOrg() is not ready in this case
      if (int.webapp && int.webapp.type === 'WEBAPP_TYPE_INBOX_BOT') {
        let thumbnail
        let groupLogo = groupInfo.tags['Main_Color_Square_Logo']
        if (groupLogo) {
          thumbnail = MxISDK.getContextPath() ? MxISDK.getContextPath() + groupLogo : groupLogo
        } else {
          thumbnail = drawOrgSquareLogo({name: groupInfo.name, brandingColor: groupInfo.tags['B_Branding_Color']})
        }
        return {...int, thumbnail}
      } else {
        return int
      }
    })
    groupInfo.supportEmail = ObjectUtils.getByPath(group, 'support.user.email', '')
    groupInfo.clientSupportEmail = ObjectUtils.getByPath(group, 'client_support.user.email', '')
    if (group.customer_id) {
      groupInfo.customer_id = group.customer_id
    }
    if (group.trial_start_time) {
      groupInfo.trial_start_time = group.trial_start_time
    }
    if (group.trial_end_time) {
      groupInfo.trial_end_time = group.trial_end_time
    }

    switch (group.status) {
      case GroupSubscriptionStatus.GROUP_CANCELED_SUBSCRIPTION:
        groupInfo.isCancelled = true
        break
      case GroupSubscriptionStatus.GROUP_DISABLED_SUBSCRIPTION:
        groupInfo.isDisabled = true
        break
      case GroupSubscriptionStatus.GROUP_EXPIRED_SUBSCRIPTION:
        groupInfo.isPaymentExpired = true
        break
      case GroupSubscriptionStatus.GROUP_NORMAL_SUBSCRIPTION:
        groupInfo.isNormal = true
        break
      case GroupSubscriptionStatus.GROUP_PAST_DUE_SUBSCRIPTION:
        groupInfo.isPassedDue = true
        break
      case GroupSubscriptionStatus.GROUP_TRIAL_SUBSCRIPTION:
        groupInfo.isTrial = true
        break
    }

    if (group.partner) {
        groupInfo.partnerId = group.partner.partner.id
    }

    if (group.invitation_tokens) {
      let user = ObjectUtils.getByPath(group.invitation_tokens, '0.creator.user')
      Object.assign(user, UserFormatter.transformToBaseUser(user))
      groupInfo.invitation_tokens = group.invitation_tokens
    }

    const square_logo = groupInfo.tags['Main_Color_Square_Logo']
    if (square_logo) {
      groupInfo.squareLogo = MxISDK.getContextPath() ? MxISDK.getContextPath() + square_logo : square_logo
    } else {
      groupInfo.squareLogo = drawOrgSquareLogo({name: groupInfo.name, brandingColor: groupInfo.tags['B_Branding_Color']})
    }

    if(group.shared_content_library_group_id) {
      groupInfo.sharedContentLibraryGroupId = group.shared_content_library_group_id
    }

    return groupInfo
  }


  static getInstance () {
    if (!groupController) {
      groupController = new GroupController()
    }
    if (!groupController.currentGroup || groupController.currentGroup.isAnonymous) {
      //todo: need to remove before v9.2.1 ER, add log to track issue
      console.trace('in anonymous case')
      groupController.currentGroup = MxISDK.getCurrentOrg()
    }
    return groupController
  }

  isMemberExist (userIdentity: { email?: string, phone_number?: string, first_name?: string, last_name?: string}, canAccessBusinessDirectory = true): Promise<CUserValidateState> {
    let {email, phone_number, first_name, last_name} = userIdentity
    return new Promise((resolve, reject) => this.currentGroup.readMember({email, phone_number})
      .then((group: Defines.Group) => {
        const groupUser = ObjectUtils.getByPath(group, 'members.0.user') || {}
        const currentUserImpl = MxISDK.getCurrentUser()
        if (groupUser.type) {
          const userId = groupUser.id
          const userStatus = ObjectUtils.getByPath(group, 'members.0.status')
          if (groupUser.type === UserType.USER_TYPE_LOCAL) {
            const hasUserRelation = currentUserImpl.getUserRelationByUserId(userId)
            if (hasUserRelation && !hasUserRelation.is_deleted) {
              let users = GroupController.transformGroupUser(group.members, group.id)
              reject({
                isMyClient: true,
                isMemberExist: true,
                isDisabledClient: groupUser.disabled,
                user:users[0],
                isPendingUser: userStatus === Defines.GroupUserStatus.GROUP_INVITED
              })
            } else {
              last_name = last_name || '';
              let gUserLastName = groupUser.last_name || ''
              let hasSameName = groupUser.first_name === first_name && last_name === gUserLastName
              let users = GroupController.transformGroupUser(group.members, group.id)
              const groupMemberRoles = ObjectUtils.getByPath(group, 'members.0.roles')
              reject({
                isDisabledClient: groupUser.disabled,
                isMemberExist: true,
                hasSameName,
                user:users[0],
                groupMemberRoles,
                isPendingUser: userStatus === Defines.GroupUserStatus.GROUP_INVITED
              })
            }
          } else if (groupUser.type === UserType.USER_TYPE_NORMAL) {
            const result = {
              isMemberExist: true,
              isInternalUser: true,
              isMyCollaborator: true,
              isDisabled: groupUser.disabled,
              isPendingUser: userStatus === Defines.GroupUserStatus.GROUP_INVITED
            }
            if (!canAccessBusinessDirectory) {
              let isMyCollaborator = false
              const allCollaborators = currentUserImpl.basicInfo.collaborators || []
              for (let collaborator of allCollaborators) {
                if (!collaborator.is_deleted && UserFormatter.isSameUser(userIdentity, collaborator.user)) {
                  isMyCollaborator = true
                  break
                }
              }
              result.isMyCollaborator = isMyCollaborator
            }
            reject(result)
          }
        } else {
          resolve({
            isMemberExist: true
          })
        }
      })
      .catch((err) => {
        if (err) {
          if (err.code === ClientResponseCode.RESPONSE_ERROR_NOT_FOUND) {
            resolve({
              isMemberExist: false
            })
          } else if (err.code === ClientResponseCode.RESPONSE_ERROR_FAILED) {
            reject({
              isUnknownError: true
            })
          }
        } else {
          reject({
            isUnknownError: true
          })
        }
      }))
  }
  getDuplicatedClientUserIdentity (userIdentity: UserIdentity): Promise<UserIdentity> {
    return new Promise((resolve, reject)=>{
      this.currentGroup.readMember(userIdentity).then(group=>{
        const groupUser = group?.members?.[0].user
        if (groupUser.type === UserType.USER_TYPE_LOCAL) {
          reject({isMemberExist: true})
        } else {
          resolve({id: groupUser.id})
        }
      }).catch(()=>{
        resolve(userIdentity)
      })
    })
  }
  validateGroupUserState (userIdentity: {email?: string; phoneNumber?: string; firstName?: string; lastName?: string}): Promise<CUserValidateState> {
    const {email, phoneNumber, firstName, lastName} = userIdentity
    return new Promise((resolve, reject) => this.currentGroup.readMember({email, phone_number: phoneNumber})
      .then((group: Defines.Group) => {
        const groupUser = ObjectUtils.getByPath(group, 'members.0.user') || {}
        const currentUserImpl = MxISDK.getCurrentUser()
        if (groupUser.type) {
          const result: CUserValidateState = {isMemberExist: true}
          const userId = groupUser.id
          const userStatus = ObjectUtils.getByPath(group, 'members.0.status')

          const users = GroupController.transformGroupUser(group.members, group.id)
          result.user = users[0]
          result.isPendingUser = userStatus === Defines.GroupUserStatus.GROUP_INVITED
          result.isDisabled = groupUser.disabled

          if (groupUser.type === UserType.USER_TYPE_LOCAL) {
            result.isClient = true
            result.isInternal = false

            const hasUserRelation = currentUserImpl.getUserRelationByUserId(userId)
            if (hasUserRelation && !hasUserRelation.is_deleted) {
              result.isMyClient = true
            } else {
              const first_name = firstName || ''
              const last_name = lastName || ''
              const gUserLastName = groupUser.last_name || ''
              const hasSameName = groupUser.first_name === first_name && last_name === gUserLastName
              result.hasSameName = hasSameName
            }
          } else if (groupUser.type === UserType.USER_TYPE_NORMAL) {
            result.isClient = false
            result.isInternal = true
          }
          resolve(result)
        } else {
          resolve({isMemberExist: true})
        }
      }).catch((err) => {
        if (err?.code === ClientResponseCode.RESPONSE_ERROR_NOT_FOUND) {
          resolve({isMemberExist: false})
        } else {
          reject({isUnknownError: true})
        }
      }))
  }


  get groupCaps () {
    return this.currentGroup.basicInfo.group_caps
  }

  get groupSettings () {
    return this.currentGroup && this.currentGroup.group && this.currentGroup.group.group_settings
  }

  get groupRoles () {
    return this.currentGroup.roles
  }

  subscribeGroupBasicInfo (resolve: Function): void {
    if (!this.groupBasicInfoSubscriber) {
      let group = GroupController.transformGroupBasicInfo(this.currentGroup.basicInfo, this.brandName)
      this._tags = group.tags
      resolve(group)
      if (this.privilegesResolveCallback) {
        this.privilegesResolveCallback(group)
      }
      if (this.teamInfoResolveCallback) {
        const teams = GroupController.transformTeamInfo(this.currentGroup.basicInfo.teams || [])
        this.teamInfoResolveCallback({
          updatedTeams: teams
        })
      }
      this.groupBasicInfoSubscriber = this.currentGroup.subscribeBasicInfo((group: Defines.Group) => {
        let formattedGroup = GroupController.transformGroupBasicInfo(group, this.brandName)
        this._tags = formattedGroup.tags
        resolve(formattedGroup)
        if (this.privilegesResolveCallback) {
          this.privilegesResolveCallback(formattedGroup)
        }
        // if (this.teamInfoResolveCallback) {
        //   const teams = GroupController.transformTeamInfo(this.currentGroup.basicInfo.teams)
        //   this.teamInfoResolveCallback(teams)
        // }
        if (group.routing_config && websdkController) {
          websdkController.handleGroupACDUpdate(group.routing_config)
        }
      })

      this.currentGroup.subscribeTeams(teams => {
        const updatedTeams = []
        const deletedTeams = []
        for (let team of teams) {
          if (!team.is_deleted) {
            updatedTeams.push(team)
          } else {
            deletedTeams.push(team)
          }
        }
        this.teamInfoResolveCallback({
          updatedTeams: GroupController.transformTeamInfo(updatedTeams),
          deletedTeams: deletedTeams
        })
      })

      // When add a new acd channel or sr channel
      // Should mark the acd team or sr team as deleted
      // Or will display this team in contact team list
      const groupCaps = this.currentGroup.basicInfo.group_caps
      if (groupCaps.enable_acd || groupCaps.enable_channel_subscription) {
        this.currentGroup.subscribeRoutingConfig((config) => {
          const acdChannels = config.acd_channels
          const srChannels = config.sr_channels

          const channel = acdChannels ? acdChannels[0] : (srChannels ? srChannels[0] : null)

          if (channel && !channel.is_deleted && channel.teams && channel.teams.length > 0) {
            this.teamInfoResolveCallback({
              updatedTeams: [],
              deletedTeams: [channel.teams[0]]
            })
          }
        })
      }
    }
  }

  subscribeGroupACLInfo (resolve: Function): void {
    this.privilegesResolveCallback = resolve
    resolve()
  }

  subscribeGroupTeams (resolve: Function): void {
    this.teamInfoResolveCallback = resolve
  }

  getRole (sequence: number, userType: String): Defines.GroupUserRole {
    if (!sequence) {
      return this.currentGroup.roles.find((role: Defines.GroupUserRole) => {
        if (role.is_default) {
          if (userType === UserType.USER_TYPE_LOCAL) {
            return role.type === GroupUserRoleType.ROLE_TYPE_LOCAL
          } else if(userType === UserType.USER_TYPE_NORMAL){
            return role.type === GroupUserRoleType.ROLE_TYPE_NORMAL
          } else {
            return role.type === GroupUserRoleType.ROLE_TYPE_GUEST
          }
        } else {
          return false
        }
      })
    } else {
      return this.currentGroup.roles.find((role: Defines.GroupUserRole) => {
        return role.sequence === sequence
      })
    }
  }

  getUserByEmail (email: string): Promise<CGroupUser> {
    return this.currentGroup.readMember({email: email}).then((group: Defines.Group) => {
      const members = (group.members || []) as Array<GroupUser>
      return GroupController.transformGroupUser(members, this.currentGroup.id).find(member => member.email && member.email.toLowerCase() === email)
    })
  }

  @transformError()
  resendInviteEmail (email: String): Promise<boolean> {
    return this.currentGroup.resendInviteEmail({email: email} as UserIdentity).then(_ => true).catch(_ => false)
  }

  @transformError()
  updateAllPlatformBranding (brandingTag: Object) {
    let brandTag: Defines.BoardTag = {}
    Object.values(brandingTag).forEach(tag => {
      brandTag[tag.name] = tag.value
    })
    return this.currentGroup.createOrUpdateTags(brandTag)
  }

  @transformError()
  updateOnlineBillingBranding (brandingTag: Object, brandLogoTag: Array<Object>) {
    return Promise.all(this.uploadOnlineBillingLogo(brandingTag, brandLogoTag)).then(() => {
      let brandTag: Defines.BoardTag = {}
      Object.values(brandingTag).forEach(tag => {
        brandTag[tag.name] = tag.value
      })
      return this.currentGroup.createOrUpdateTags(brandTag)
    })
  }

  private uploadOnlineBillingLogo (brandingTag: Object, brandLogoTag: Array<Object>): Promise<any>[] {
    const uploadUrl = `${MxISDK.getContextPath()}/group/${this.currentGroup.id}/upload`
    let requests = [] as Promise<any>[]
    brandLogoTag.forEach(brandLogo => {
      requests.push(MxAjax.post(uploadUrl, BrowserUtils.base64ToBlob(brandLogo['value']), {
        contentType: brandLogo['fileType']
      }).then((response) => {
        const sequence = ObjectUtils.getByPath(response, 'object.group.resources.0.sequence')
        const groupId = ObjectUtils.getByPath(response, 'object.group.id')
        let link = ''
        if (sequence) {
          if (groupId) {
            link = `${MxISDK.getContextPath()}/group/${groupId}/resource/${sequence}`
          } else {
            link = `${MxISDK.getContextPath()}/group/resource/${sequence}`
          }
        }
        brandingTag[brandLogo['tag']].value = link
      }))
    })
    return requests
  }

  @transformError()
  updateGroup (group: Defines.Group): Promise<Defines.Group> {
    return this.currentGroup.updateGroup(group)
  }

  @transformError()
  generateQRToken (user: Defines.User) {
    return MxISDK.getCurrentUser().createQRToken(user)
  }

  destroy () {
    groupController = null
  }

  @transformError()
  static readWebApp (id: string) {
    return new Promise((resolve, reject) => {
      if(MxISDK.getCurrentUser()){
        MxISDK.getCurrentUser().readWebApp(id).then(data => {
          if (data.is_universal && groupController) {
            groupController._isM0APP = true
          }
          resolve(data)
        }).catch(reject)
      } else {
        MxISDK.getAnonymousUser().readWebApp(id).then(data => {
          if (data.is_universal && groupController) {
            groupController._isM0APP = true
          }
          resolve(data)
        }).catch(reject)
      }
    })
  }

  @transformError()
  updateMemberEmailPhoneNum (id: string, email: string, phoneNumber: string, emailOff: boolean = true, smsOff: boolean = true) {
    return this.currentGroup.updateMemberEmailPhoneNum(id, {email: email, phone_number: phoneNumber, emailOff, smsOff})
  }

  @transformError()
  inviteMember(user: GroupUser | GroupUser[], updateExisting?: boolean, suppressEmailSms?: boolean) {
    return new Promise((resolve, reject) => {
      this.currentGroup.inviteMember(user, updateExisting, suppressEmailSms).then(group => {
        let members = GroupController.transformGroupUser(group.members, this.currentGroup.id)
        resolve(members[0])
      }).catch(reject)
    })
  }

  readManagementInternalUsers (fromTime: number, toTime: number, start: number = 0, size: number = 50) {
    let filter = {
      fromTime: fromTime,
      toTime: toTime,
      start: start,
      size: size
    } as MxGroupManagementFilter
    return this.currentGroup.readManagementInternalUsers(filter)
  }

  readManagementClientDetail (userIdentity: {
    email?: string,
    id?: string,
    phoneNumber: string
  }, fromTime: number, toTime: number) {
    let filter = {
      fromTime: fromTime,
      toTime: toTime
    } as MxGroupManagementFilter
    return this.currentGroup.readManagementClientDetail(filter, userIdentity).then(res => {
      let group = res.object.group, peerData = res.report.user_engagement
      return {
        members: group.members.map((member) => {
          let activity = peerData.find(p => p.peer_user.id === member.user.id)
          return {
            name: UserFormatter.getUserName(member.user),
            ...member,
            teams: member.user.groups,
            docSharedCount: activity.doc_shared_count,
            esignCount: activity.esign_count,
            meetCount: activity.meet_count,
            msgCount: activity.msg_count,
            peerDocShareCount: activity.peer_doc_shared_count,
            peerEsignCount: activity.peer_esign_count,
            peerMsgCount: activity.peer_msg_count,
            peerTodoCount: activity.peer_todo_count,
            todoCount: activity.todo_count,
            totalCount: activity.todo_count
              + activity.peer_todo_count
              + activity.peer_msg_count
              + activity.esign_count
              + activity.meet_count
              + activity.msg_count
              + activity.peer_esign_count
              + activity.peer_doc_shared_count
              + activity.doc_shared_count
          }
        })
      }
    })
  }

  readManagementInternalUserDetail (userIdentity: {
    email?: string,
    id?: string,
    phoneNumber: string
  }, fromTime: number, toTime: number) {
    let filter = {
      fromTime: fromTime,
      toTime: toTime
    } as MxGroupManagementFilter
    return this.currentGroup.readManagementInternalUserDetail(filter, userIdentity)
  }

  readManagementTeamDetail (teamId: string, fromTime: number, toTime: number) {
    return this.currentGroup.readManagementTeamDetail({fromTime: fromTime, toTime: toTime}, teamId)
  }

  readManagementShareUserBoards (clientIdentity: {
    email?: string,
    id?: string,
    phoneNumber: string
  }, internalIdentity: {
    email?: string,
    id?: string,
    phoneNumber: string
  }, fromTime: number, toTime: number) {
    return this.currentGroup.readManagementShareUserBoards({
      fromTime: fromTime,
      toTime: toTime
    }, clientIdentity, internalIdentity).then(res => {
      let activity = res.report.user_engagement[0]
      return {
        boards: UserFormatter.transformUserBoards(res.object.user.boards || []),
        docSharedCount: activity.doc_shared_count || 0,
        esignCount: activity.esign_count || 0,
        meetCount: activity.meet_count || 0,
        msgCount: activity.msg_count || 0,
        peerDocShareCount: activity.peer_doc_shared_count || 0,
        peerEsignCount: activity.peer_esign_count || 0,
        peerMsgCount: activity.peer_msg_count || 0,
        peerTodoCount: activity.peer_todo_count || 0,
        todoCount: activity.todo_count || 0
      }
    })
  }

  readManagementClients (fromTime: number, toTime: number, clientFilter: MxGroupManagementFilter, start: number = 0, size: number = 50) {
    let filter = {
      fromTime: fromTime,
      toTime: toTime,
      pageNumber: start,
      size: size,
      clientFilter: clientFilter
    } as MxGroupManagementFilter
    return this.currentGroup.readManagementClients(filter).then((res) => {
      if (!res.object || !res.object.group.total_members) {
        return {
          members: [],
          totalMembers: 0
        }
      }
      let group = res.object.group
 	  return {
        members: GroupController.transformGroupUser(group.members, group.id).map(groupMember => {
          if (!res.report || !res.report.user_activity_summary) {
            return {...groupMember}
          }
          let activitySummary = res.report.user_activity_summary.find(item => item.user.id === groupMember.id) || {}
          return {
            ...groupMember,
            activeRelationTotal: activitySummary.active_relation_total || 0,
            docSharedCount: activitySummary.doc_shared_count || 0,
            esignCount: activitySummary.esign_count || 0,
            meetCount: activitySummary.meet_count || 0,
            msgCount: activitySummary.msg_count || 0,
            todoCount: activitySummary.todo_count || 0,
            totalCount: (activitySummary.msg_count || 0)
              + (activitySummary.meet_count || 0)
              + (activitySummary.esign_count || 0)
              + (activitySummary.doc_shared_count || 0)
              + (activitySummary.todo_count || 0)
          }
        }),
        totalMembers: res.object.group.total_members
      }
    })
  }

  filterActiveGroupMembers (userIds: UserIdentity[]) {
    let cleanUserIds = userIds.map(user => {
      // MVB-11514, invalid phone_number will cause 400 error when do GROUP_REQUEST_READ_MEMBERS
      // if email exists, just request with email directly
      if (user.email && user.phone_number) {
        delete user.phone_number
      }
      return user
    })
    return MxISDK.getCurrentOrg().readMembers(cleanUserIds).then(group => {
      return group.members && group.members.filter(member => member.status === GroupUserStatus.GROUP_MEMBER).map(member => {
        const isAdmin = member.type === GroupAccessType.GROUP_ADMIN_ACCESS
        let {id, unique_id, email, phone_number, type} = member.user
        return {id, unique_id, email, phone_number, type, isAdmin}
      })
    })
  }

  queryGroupMembers (users: UserIdentity[]) {
    const tempUsers = users.map(user => {
      // MVB-11514, invalid phone_number will cause 400 error when do GROUP_REQUEST_READ_MEMBERS
      // if email exists, just request with email directly
      if (user.email && user.phone_number) {
        delete user.phone_number
      }
      return user
    })
    return MxISDK.getCurrentOrg().readMembers(tempUsers).then(group => {
      const members = group.members || [] as Array<GroupUser>
      const transformedMembers = GroupController.transformGroupUser(members, this.currentGroup.id, true, true)

      return transformedMembers
    })
  }

  createOrUpdateInvitationToken () {
    return new Promise((resolve, reject) => {
      this.currentGroup.createOrUpdateInvitationToken().then((group) => {
        let creator = ObjectUtils.getByPath(group, 'invitation_tokens.0.creator.user')
        Object.assign(creator, UserFormatter.transformToBaseUser(creator))
        resolve(group)
      }).catch(reject)
    })
  }

  @transformError()
  readOfficeHour () {
    return this.currentGroup.readOfficeHour()
  }

  checkSmsServiceStatus () {
    return this.currentGroup.readGroupCapability()
      .then(response => {
        if (response) {
          return response.has_sms_config
        } else {
          return false
        }
      })
      .catch(err => {
        throw err
      })
  }

  refreshMeetingPassword () {
    return this.currentGroup.refreshDefaultMeetPassword()
  }

  // Save client logs as local file
  downloadClientLog(): void {
    const fileName = this.getClientLogZipFileName()
    this.getClientLogZipFileContent().then(content => {
      saveBlobAsFile(fileName, content)
    })
  }

  // Attach client logs to email
  async getLocalLog(
      maxZipSize: number = 23 * 1024 * 1024 /* 23 MB, unit: byte */,
      maxTextSize: number = 200 * 1024 * 1024 /* 200 MB, unit: byte */): Promise<any> {
    const fileName = this.getClientLogZipFileName()
    return new Promise(resolve => {
      this.getClientLogZipFileContent(maxTextSize).then(content => {
        if (content.size > maxZipSize) {
          resolve(null)
        } else {
          const attachments = new Map()
          attachments.set(fileName, content)
          resolve(attachments)
        }
      })
    })
  }

  async getClientLogZipFileContent(maxTextSize: number = 0): Promise<Blob> {
    const groupName = this.currentGroup && this.currentGroup.basicInfo.name
    const dateStr = new Date().toISOString().split('T')[0].replace(/\-/g, '')

    const meetLogFileName = `${groupName}clientlog${dateStr}.txt`
    const webLogFileBaseName = `${groupName}webapplog${dateStr}`

    const meetLogContent = await MxISDK.getLocalLog()

    let webLogChunks = await mxWebLogIndexedDB.retrieveChunkedLogs()

    // Limit size of web log in email attachment
    if (webLogChunks.length > 0 && maxTextSize) {
      const meetLogSize = meetLogContent.length
      const webLogMaxSize = maxTextSize - meetLogSize

      if (webLogMaxSize > 0) {
        let webLogChunkCount = Math.floor(webLogMaxSize / MxConsts.WebLogIndexedDBParams.CHUNK_SIZE)
        const remainder = webLogMaxSize % MxConsts.WebLogIndexedDBParams.CHUNK_SIZE

        if (remainder > 0 && remainder >= webLogChunks.slice(-1)[0].length) {
          webLogChunkCount += 1
        }

        if (webLogChunkCount > 0) {
          webLogChunks = webLogChunks.splice(-webLogChunkCount)
        }
      }
    }

    const zip = new JsZip()
    zip.file(meetLogFileName, meetLogContent)

    if (webLogChunks.length <= 1) {
      zip.file(`${webLogFileBaseName}.txt`, webLogChunks[0] || '')
    } else {
      for (let i = 0; i < webLogChunks.length; i++) {
        const fileName = `${webLogFileBaseName}_${i + 1}.txt`
        zip.file(fileName, webLogChunks[i])
      }
    }

    return zip.generateAsync({
      type: "blob",
      compression: "DEFLATE",
      compressionOptions: {
          level: 9
      }
    })
  }

  getClientLogZipFileName(): string {
    const groupName = this.currentGroup && this.currentGroup.basicInfo.name
    const dateStr = new Date().toISOString().split('T')[0].replace(/\-/g, '')
    return `${groupName} logs ${dateStr}.zip`
  }

  addTeamMembers (teamId: string, users: UserIdentity[]) {
    return this.currentGroup.addTeamMembers(teamId, users, GroupAccessType.GROUP_MEMBER_ACCESS)
  }

  removeTeamMember (teamId: string, user: UserIdentity) {
    return this.currentGroup.removeTeamMember(teamId, user)
  }

  addTeamManagers (teamId: string, users: UserIdentity[]) {
    return this.currentGroup.addTeamManagers(teamId, users)
  }

  removeTeamManager (teamId: string, user: UserIdentity) {
    return this.currentGroup.removeTeamManager(teamId, user)
  }

  // Add user to binder directly if user is not in binder by Org Admin
  addGroupBoardMember(boardId: string, users: UserIdentity[], accessType?: BoardAccessType) {
    const currentUser = MxISDK.getCurrentUser()
    return currentUser.readBoard(boardId).then(board => {
      const boardUserIds = board.users.filter(u => !u.is_deleted).map(u => u.user.id)
      const usersNotInBoard = users.filter(u => !boardUserIds.find(id => id === u.id))
      if (usersNotInBoard.length > 0) {
        return this.currentGroup.addGroupBoardMember(boardId, usersNotInBoard, accessType)
      } else {
        return Promise.resolve(board)
      }
    }).catch(error => {
      return Promise.reject(error)
    })
  }

  readGroupMembers(users:UserIdentity[]){
    return MxISDK.getCurrentOrg().readMembers(users).then(group => {
      const transformedMembers = group.members && GroupController.transformGroupUser(group.members, group.id, true)
      return transformedMembers
    })
  }

  async createOrUpdateIntegration(integrationSeq: number, integration: GroupIntegration) {
    let response

    try {
      if (integrationSeq) {
        response = await this.currentGroup.updateIntegration(integrationSeq, integration)
      } else {
        response = await this.currentGroup.createIntegration(integration)
      }
    } catch(error) {
      throw new Error(error)
    }

    return response
  }

  async deleteIntegration(integrationSeq: number) {
    let response

    try {
      response = await this.currentGroup.deleteIntegration(integrationSeq)
    } catch(error) {
      throw new Error(error)
    }

    return response
  }

  async verifyIntegration(integrationSeq: number) {
    let response

    try {
      response = await this.currentGroup.verifyIntegration(integrationSeq)
    } catch(error) {
      throw new Error(error)
    }

    return response
  }

  @transformError()
  unregisterGroup(verifyCode: string){
    return MxISDK.unregisterGroup(verifyCode)
  }

  getInternalUserTeams (user: UserIdentity) {
    return this.currentGroup.getInternalUserTeams(user).then((teams: UserGroup[]) => {
      return GroupController.transformTeamInfo(teams)
    })
  }

  updateMemberDistributionList (userId: string, roles: number[]) {
    return this.currentGroup.updateMemberDistributionList(userId, roles)
  }
}
