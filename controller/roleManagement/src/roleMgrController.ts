import { MxISDK, MxGroup, Defines } from "isdk"
import { transformError, ClassLogger } from "@controller/common/decorators"
import { GroupUserRole, UserIdentity, GroupRoleCategory } from "isdk/src/api/defines"
import { UserFormatter } from "@controller/utils/user"

import {CGroupRole} from "@controller/defines/admin/CGroupRole"


let roleMgrController: RoleMgrController


@ClassLogger()
export class RoleMgrController {
    private currentGroup: MxGroup

    private constructor() {
        this.currentGroup = MxISDK.getCurrentOrg()
    }

    static getInstance(): RoleMgrController {
        if (!roleMgrController) {
            roleMgrController = new RoleMgrController()
        }
        return roleMgrController
    }

    subscribeGroupRole(resolve: Function) {
      this.currentGroup.subscribeBasicInfo((group: Defines.Group) => {
        let roles = group.roles.filter(role => !role.is_deleted)
        resolve(roles.map(role => {
            return this.transformGroupRole(role);
        }))
      })
    }

    private transformGroupRole(role: GroupUserRole): CGroupRole {
        role.users_total = role.users_total || 0
        role.name = role.name || ''
        role.category = role.category || GroupRoleCategory.ROLE_CATEGORY_NONE
        return {
            ...role,
            isInternal: role.type === 'ROLE_TYPE_NORMAL'
        }
    }

    getGroupRoles():CGroupRole[] {
        return this.currentGroup.basicInfo.roles.filter((role)=>!role.is_deleted).map((role)=>{
            return this.transformGroupRole(role);
        })
    }
    
    @transformError()
    createRole(role:GroupUserRole) {
        return this.currentGroup.createRole(role)
    }

    @transformError()
    updateRole(roleSequence: number, role:GroupUserRole) {
        return this.currentGroup.updateRole(roleSequence, role)
    }

    @transformError()
    updateMemberRole(memberSequence: number, roleSequence: number) {
        return this.currentGroup.updateMemberRole(memberSequence, roleSequence)
    }

    @transformError()
    deleteRole(roleSequence:number) {
        return this.currentGroup.deleteRole(roleSequence)
    }

    @transformError()
    batchUpdateMembersRole(users: UserIdentity[], roleSequence: number){
        return this.currentGroup.batchUpdateMembersRole(users, roleSequence)
    }

    @transformError()
    batchUpdateMembersRoles(rolesMap: Map<number, number[]>){
        return this.currentGroup.batchUpdateMembersRoles(rolesMap).then(response => {
            return UserFormatter.transformToCGroupMemberList(response.members)
        })
    }

    destroy() {

    }
}
