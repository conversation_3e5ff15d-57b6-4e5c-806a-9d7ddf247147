import { Defines, MxGroup, MxISDK } from 'isdk'
import {
  Group,
  GroupAccessType,
  GroupUser,
  UserIdentity,
  UserGroup,
  GroupType,
  MxGroupMemberFilter,
  GroupUserSetting
} from 'isdk/src/api/defines'
import { CTeam } from '@controller/defines/admin/CTeam'
import { transformError } from '@controller/common/decorators/transformError'
import { ClassLogger } from '@controller/common/decorators'
import { AdminController } from '@controller/admin/src/adminController'
import { ObjectUtils } from '@controller/utils'

let teamInstance: TeamController
let mxGroup: MxGroup

@ClassLogger()
export class TeamController {
  private teamSubscriber: Defines.MxSubscription

  private constructor() {
    mxGroup = MxISDK.getCurrentOrg()
  }

  static getInstance(): TeamController {
    if (!teamInstance) {
      teamInstance = new TeamController()
    }
    return teamInstance
  }

  subscribeTeam(resolve: Function, type?: string): void {
    if (!this.teamSubscriber) {
      let teams = this.filterTeams(type)
      this.teamSubscriber = mxGroup.subscribeTeams(() => {
        teams = this.filterTeams(type)
        resolve(teams.map((team) => {
          return this.transformToCTeam(team)
        }))
      })
      mxGroup.subscribeBasicInfo(() => {
        teams = this.filterTeams(type)
        resolve(teams.map((team) => {
          return this.transformToCTeam(team)
        }))
      })
      resolve(teams.map((team) => {
        return this.transformToCTeam(team)
      }))
    }
  }

  private filterTeams(type?: string) {
    let acd_teams_sequences = [], sr_teams_sequences = []
    mxGroup.group.routing_config && mxGroup.group.routing_config.acd_channels && mxGroup.group.routing_config.acd_channels.forEach(i => { i.teams && acd_teams_sequences.push(i.teams[0].sequence) })
    mxGroup.group.routing_config && mxGroup.group.routing_config.sr_channels && mxGroup.group.routing_config.sr_channels.forEach(i => { i.teams && sr_teams_sequences.push(i.teams[0].sequence) })
    let excludeTeamsSequences = acd_teams_sequences.concat(sr_teams_sequences)
    const teams = (mxGroup.group.teams || []).filter(team => {
      const isValid = !team.is_deleted && excludeTeamsSequences.indexOf(team.sequence) === -1
      if (type) {
        return isValid && team.group?.type === type
      }
      return isValid && team.group?.type === 'GROUP_TYPE_TEAM'
    })
    return teams
  }

  unsubscribeTeam() {
    if (this.teamSubscriber) {
      this.teamSubscriber.unsubscribe()
    }
  }

  private transformToCTeam(team: UserGroup): CTeam {
    const cteam = {
      name: team.group.name,
      sequence: team.sequence,
      description: team.group.description,
      createdTime: team.created_time,
      updatedTime: team.updated_time,
      total_members: team.group.total_members ? team.group.total_members : 0,
      total_managers: team.group.total_managers ? team.group.total_managers : 0,
      id: team.group.id,
      picture: team.group.picture
    } as CTeam
    return cteam
  }

  getTeam(teamId: string) {
    let teams = mxGroup.group.teams
    if (teams) {
      return teams.find(
        t => t.group.id === teamId
      )
    }
    return null
  }

  @transformError()
  readTeam(teamId: string) {
    return mxGroup.readTeam(teamId).then((group) => {
      return this.transformToCTeam({group})
    })
  }

  @transformError()
  createTeam(name: string, description: string, type?: GroupType, managersSetting?: GroupUserSetting) {
    return mxGroup.createTeam(name, {description, type, managersSetting}).then((group: Defines.Group) => {
      let team = ObjectUtils.getByPath(group, 'teams.0') || {}
      return this.transformToCTeam(team)
    })
  }

  @transformError()
  addTeamMembers(teamId: string, users: UserIdentity[], isManager: boolean) {
    let accessType: GroupAccessType = GroupAccessType.GROUP_MEMBER_ACCESS
    if (isManager) {
      accessType = GroupAccessType.GROUP_ADMIN_ACCESS
    }
    return mxGroup.addTeamMembers(teamId, users, accessType).then((group: Group) => {
      const members = group.members || [] as Array<GroupUser>
      return AdminController.transformGroupUser(members, true, teamId)
    })
  }

  @transformError()
  addTeamManagers(teamId: string, users: UserIdentity[]) {
    return mxGroup.addTeamManagers(teamId, users).then((group: Group) => {
      const members = group.managers || [] as Array<GroupUser>
      return AdminController.transformGroupUser(members, true, teamId, false, 'type', true)
    })
  }

  @transformError()
  readTeamMembers(teamId: string, searchKey: string, filter?: MxGroupMemberFilter) {
    return mxGroup.readTeamMembers(teamId, searchKey, filter).then((group: Group) => {
      const members = group?.members || [] as Array<GroupUser>
      return AdminController.transformGroupUser(members, true, teamId)
    })
  }

  @transformError()
  readClientTeamManagers(teamId: string) {
    return mxGroup.readTeam(teamId).then(group => {
      const managers = (group?.managers || [] as Array<GroupUser>).filter(user => !user?.is_deleted)
      return AdminController.transformGroupUser(managers, true, teamId, false, 'type', true)
    })
  }
  updateTeamMemberAccessType(teamId: string, user: UserIdentity, isManager: boolean) {
    let accessType: GroupAccessType = GroupAccessType.GROUP_MEMBER_ACCESS
    if (isManager) {
      accessType = GroupAccessType.GROUP_ADMIN_ACCESS
    }
    return mxGroup.updateTeamMemberAccessType(teamId, user, accessType).then((group: Group) => {
      return AdminController.transformGroupUser(group.members, true, teamId)
    })
  }

  @transformError()
  removeTeamMember(teamId: string, user: UserIdentity): Promise<boolean> {
    return mxGroup.removeTeamMember(teamId, user).then(_ => true).catch(_ => false)
  }

  @transformError()
  removeTeamManager(teamId: string, user: UserIdentity): Promise<boolean> {
    return mxGroup.removeTeamManager(teamId, user).then(_ => true).catch(_ => false)
  }

  @transformError()
  updateTeam(team: CTeam): Promise<boolean> {
    return mxGroup.updateTeam(team.id, team.name, team.description).then(_ => true).catch(_ => false)
  }

  @transformError()
  deleteTeam(teamId: string): Promise<boolean> {
    return mxGroup.deleteTeam(teamId).then(_ => true).catch(_ => false)
  }


  destroy() {
    this.unsubscribeTeam()
    teamInstance = null
  }
}
