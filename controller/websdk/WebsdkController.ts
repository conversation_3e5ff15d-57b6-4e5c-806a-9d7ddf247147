/**
 * Created by colin on 2019-10-22
 */
import { IframeRemoteCall, isString } from "@commonUtils/IframeRemoteCall";
import { MxISDK, Defines, MxBoard } from "isdk";
import { MxLogger } from "@commonUtils/MxLogger";
import { CBoardUser } from "@controller/defines/CBoardUser";
import { CBoard } from "@controller/defines/CBoard";
import { BoardFormatter } from "@controller/utils/board";
import cloneDeep from "lodash/cloneDeep";
import _uniq from "lodash/uniqBy";
import some from "lodash/some";
import {ClientResponseCode, MxBoardOption, SocialType, UserActivityLog} from "isdk/src/api/defines";
import { CBaseUser } from "@controller/defines/CBaseUser";
import WebsdkError from "./models/WebsdkError";
import { TimeUtils, BrowserUtils, MxConsts} from "@commonUtils";
import { MEPLiveChatStatus} from "./models/MepChat";
import { MepMeet, MepParticipant } from "./models/MepMeet";
import { BoardRoutingStatus } from "isdk/src/proto/generated/BoardRoutingStatus";
import { MepChatType } from "./models/MepChat";
import { ClientRequestType } from "isdk/src/proto/generated/ClientRequestType";
import { ClientRequest } from "isdk/src/proto/generated/ClientRequest";
import { MEPAction } from './models/MepAction'

let logger = MxLogger.create('websdkController')
function connectToWebsdk(callback) {
  const pangFn = (event) => {
    if (event.data) {
      let requestMessage
      try {
        if (!isString(event.data)) {
          return;
        }

        if (event.data == 'websdk-pang') {
          logger.debug('get websdk-pang')
          window.removeEventListener('message', pangFn)
          callback(event.source)
        }

      } catch (e) {

      }
    }
  }
  window.addEventListener('message', pangFn);
  window.postMessage('websdk-ping', '*');
  logger.debug('send websdk-ping')
}

interface SalesforceTriggerInfo {
  salesProcessName?: string;
  stageName?: string;
  isPublished?: boolean;
}

export class WebsdkController {

  activetimer: any
  irc: IframeRemoteCall
  isWebsdk: boolean
  accessToken: string
  params: string
  isSalesforceEmbedApp: boolean
  private _onReady: Function
  private isReady: boolean = false
  private acdChannels: object
  private currOfficeStatus: string
  private canAddUserCheckCB: Function
  private visibilityChangeCb: Function
  private lastTriggeredActiveTS: number
  callback = {

  }
  config = {
    hideCalendarTab: false,
    hideContactsTab: false,
    hideSettingTab: false,
    hideTimelineCategory: false,
    hideInactiveRelationChat: false,
    showInlineSearch: false,
    disableDownload: false,
    disableChangeChatName: false,
    disableEmoji: false,
    disableUserPresence: false,
    showOOOOnly: false,
    disableEditChatMessage: false,
    hideChatNotificationsSetting: false,
    disableChangePassword: false,
    disableLogout: false,
    disableWhiteboard: false,
    hideChatHeader: false,
    hideHeader: false,
    disableUserProfile: false,
    disableForwardMessage: false,
    disableCopyPaste: false,
    disableLeaveChat: false,
    //internal option
    hideNavBar: false,
    hideTimeline: false,
    fontFileLink: '',
    actionColor: '',
    allowedFileTypes: null,
    maxFileSize: 0,
    disableRingerUI: false,
    disableMakeOwner: false,
    cliModuleView: false,
    hideBrandingHeader: false,
    canAddUserInChat: null,
    hideChatView: false,
    hideTimelineHeader: false,
    view_name: null,
    enablePrint: false,
    useLegacyChatHeaderTabs: false,
    enableCookie: true,
    hideBinderSetting: false,
    hideFileTab: false,
    hideActionTab: false,
    contentEditInterval: null,
    disableMeetChat: false,
    disableMeetScreenShare: false,
    disableMeetFileShare: false,
    disableMeetCoBrowse: false,
    enableMeetInviteParticipants: false,
    disableRotatePage: false,
    disableRenameFile: false,
    disableArchiveChat: false,
    enableTimelineSummary: true,
    showTimelineSummaryOnly: false
  }
  get fontFileLink() {
    return this.config.fontFileLink
  }
  get isHideInactiveRelationChat() {
    return this.config.hideInactiveRelationChat
  }
  constructor() {
    this.isWebsdk = false;
    if (BrowserUtils.isInIframe) {
      this.initIPC(window, window.parent)
    } else {
      connectToWebsdk((win) => {
        this.initIPC(window, win)
      })
    }
  }

  mepWinVisibilityChange (callback) {
    this.visibilityChangeCb = callback
  }

  initWithoutIframe (config: any){
    this.setConfig(config)
    this.isWebsdk = true;
    this.isReady = true;
    this._onReady && this._onReady()
    //this.initAPI()
  }

  initIPC (win: Window, parent: Window) {
    this.irc = new IframeRemoteCall(win, parent);
    this.irc.registerCall('websdkConfig', (settings: any) => {
      const { accessToken, config, callbacks, connectId, params, origin } = settings
      if (origin) {
        this.irc.setPostOrigin(origin)
      }
      this.setConfig(config)
      // NOTE: for start/join meet use cases, callbacks may update with following websdkConfig call
      // which means other params like accessToken will not bring up at that time
      if (callbacks) {
        Object.keys(callbacks).forEach((key) => {
          this.callback[key] = true;
        })
      }
      this.isWebsdk = true;
      if (params) {
        this.params = params;
        this.isSalesforceEmbedApp = params.isSalesforceEmbedApp
        if (params.view_name) {
          this.config.view_name = params.view_name
          document.body.className += ` ${params.view_name}`
        }
      }
      if (connectId) {
        this.irc.connectId = connectId;
      }
      if (accessToken) {
        this.accessToken = accessToken;
        MxISDK.initialize({useCookies: this.config.enableCookie})
        MxISDK.loginWithAccessToken(accessToken).then((user) => {
          this.sendUserLoggedInAuditLog()
          this.isReady = true;
          this._onReady && this._onReady()
          this.initAPI()
          // update user timezone to local, language to local
          const newProfile: Defines.User = {}
          const matchedLocalTimezone = TimeUtils.tryMatchTimezone(null)
          if (matchedLocalTimezone) {
            newProfile.timezone = matchedLocalTimezone
          }
          if (!user.basicInfo?.language) {
            newProfile.language = BrowserUtils.getLocalLanguage()
          }
          if (Object.keys(newProfile).length) {
            /* TODO START: MV-18866 - Temp code for data migration in v10.0, expected to remove it in v10.3 */
            const notificationLevelTag = user.tags?.['default_notification_settings']
            if(notificationLevelTag) {
              if(!user.user.board_notification_level) {
                let boardNotificationLevel: Defines.NotificationLevel
                switch (notificationLevelTag.string_value) {
                  case '0':
                    boardNotificationLevel = Defines.NotificationLevel.NOTIFICATION_LEVEL_ALL
                    break
                  case '10':
                    boardNotificationLevel = Defines.NotificationLevel.NOTIFICATION_LEVEL_RELATED
                    break
                  case '20':
                    boardNotificationLevel = Defines.NotificationLevel.NOTIFICATION_LEVEL_NOTHING
                    break
                }
                MxISDK.getCurrentUser().deleteTag('default_notification_settings')

                if(boardNotificationLevel) {
                  newProfile.board_notification_level = boardNotificationLevel
                }
              }
            }
            /* TODO END */
            MxISDK.getCurrentUser().updateProfile(newProfile);
          }
        }).catch(error => {
          if (error.code === ClientResponseCode.RESPONSE_ERROR_INVALID_TOKEN) {
            this.trigger('error', {name: MxConsts.MepSDKErrorType.INVALID_ACCESS_TOKEN, message: 'Invalid access_token, failed to continue the process.'})
          } else {
            this.trigger('error', {name: MxConsts.MepSDKErrorType.LOGIN_FAILED, message: 'Failed to verify with access_token, unable to continue the process.'})
          }
        })
      } else {
        this._onReady && this._onReady()
      }
      if (this.hasCallback('onWillSendMessage')) {
        this.setupOnWillSendMessageCb()
      }
      return
    })
    this.irc.registerCall('mepWinVisibility', (visible: boolean) => {
      this.visibilityChangeCb(visible)
    })
    this.irc.registerCall('closeWSConnection', () => {
      MxISDK.closeWSConnection()
    })
  }

  setConfig (config: any) {
    if (config) {
      Object.keys(config).forEach((key) => {
        this.config[key] = config[key];
      })

      // MepSDKCfg will be used in meeting tab
      localStorage.setItem('mx:MepSDKCfg', JSON.stringify({
        disableUserPresence: this.config['disableUserPresence'],
        showOOOOnly: this.config['showOOOOnly'],
      }))
    }
  }

  initAPI() {
    const currUser = MxISDK.getCurrentUser();
    this.irc.registerAsyncCall('getTags', (chatId: string) => {
      return currUser.loadBoard(chatId).then((mxBoard: MxBoard) => {
        return mxBoard.tags
      })
    })
    this.irc.registerAsyncCall('setTagWithName', ({ chatId, name, value }) => {
      return currUser.loadBoard(chatId).then((mxBoard: MxBoard) => {
        return mxBoard.createOrUpdateTag(name, value)
      })
    })
    this.irc.registerAsyncCall('getMembers', (chatId: string) => {
      return currUser.loadBoard(chatId).then((mxBoard: MxBoard) => {
        let binderObj: CBoard = BoardFormatter.transformBoardBasicInfo(mxBoard.basicInfo)
        let actualBinderUsers = []
        if (binderObj.boardUsers) {
          let removedUsers = []
          const isInternalUser = currUser.basicInfo.type !== Defines.UserType.USER_TYPE_LOCAL;

          const validUsers = []
          let hasValidSocialClient = false
          let lastValidSocialClient = null
          for (let binderUser of binderObj.boardUsers) {
            if (binderObj.isSocial && binderUser.isClient) {
              if (!binderUser.is_deleted) {
                hasValidSocialClient = true
                validUsers.push(binderUser)
              } else {
                lastValidSocialClient = binderUser
              }
            } else if (binderUser.is_deleted) {
              removedUsers.push(binderUser)
            } else if (!binderUser.isBot) {
              validUsers.push(binderUser)
            }
          }

          if (binderObj.isSocial && !hasValidSocialClient && lastValidSocialClient) {
            validUsers.push(lastValidSocialClient)
          }

          actualBinderUsers = cloneDeep(validUsers)
          if (!isInternalUser && removedUsers.length > 0) {
            removedUsers = _uniq(removedUsers, 'id')
            removedUsers.forEach(removed => {
              let isThisUserBack = some(actualBinderUsers, normal => normal.primaryKey === removed.primaryKey)
              if (!isThisUserBack && removed.isOwner) {
                actualBinderUsers.push(removed)
              }
            })
          }
        }
        return actualBinderUsers.map((boardUser: CBoardUser) => {
          return {
            uniqueId: boardUser.unique_id,
            firstname: boardUser.first_name,
            lastname: boardUser.last_name,
            email: boardUser.email,
            title: boardUser.title
          }
        })
        //return BinderController.getMemberList(mxBoard)
      })
    })

    const isClient = currUser.basicInfo && currUser.basicInfo.type === 'USER_TYPE_LOCAL'
    if (this.hasCallback('liveChatUpdated') && isClient) {
      this.acdChannels = {}
      const mxGroup = MxISDK.getCurrentOrg()
      const routing_config = mxGroup.routingConfig
      if (routing_config?.acd_channels) {
        for (let i = 0; i < routing_config.acd_channels.length; i++) {
          const channel = routing_config.acd_channels[i]
          if (channel.is_deleted) {
            continue
          }
          this.acdChannels[channel.sequence] = channel.name
        }

        mxGroup.readOfficeHour().then(board => {
          this.currOfficeStatus = board.routing_status
        })
      }

      setInterval(() => {
        this.handleGroupACDOfficeHour()
      }, 10 * 1000)
    }

    if (this.hasCallback('checkCanAddUser')) {
      this.irc.registerAsyncCall('onCanAddUserChecked', (canAdd: boolean) => {
        if (this.canAddUserCheckCB) {
          this.canAddUserCheckCB(canAdd)
        }
      })
    }

    if (this.config.maxFileSize > 0) {
      const userCapMaxFileSize = currUser?.cap?.client_max_body_size || 0
      if (userCapMaxFileSize > 0 && this.config.maxFileSize > userCapMaxFileSize) {
        console.warn('warning: max upload file size configuration from org admin is less than sdk configuration, sdk configuration will be deprecated')
      }
    }
  }

  handleGroupACDUpdate(routingCfg: Defines.RoutingConfig) {
    if (this.acdChannels) {
      const nameChangedACDs = []
        // handle acd channel name changes
      for (let i = 0; i < routingCfg.acd_channels.length; i++) {
        const channel = routingCfg.acd_channels[i]
        if (channel.is_deleted) {
          continue
        }
        if (this.acdChannels[channel.sequence] === undefined) {
          this.acdChannels[channel.sequence] = channel.name

        } else if (this.acdChannels[channel.sequence] !== channel.name) {
          this.acdChannels[channel.sequence] = channel.name
          if (MxISDK.getCurrentUser().boards) {
            const acdBoard = MxISDK.getCurrentUser().boards.find(ub => ub.board.is_acd && ub.board.routing_channel === channel.sequence)
            if (acdBoard && acdBoard.board) {
              status = acdBoard.board.routing_status
            }
          }
          nameChangedACDs.push({name: channel.name, status})
        }
      }
      if (nameChangedACDs.length > 0) {
        this.triggerLiveChatUpdated(nameChangedACDs)
      }
    }
  }

  handleGroupACDOfficeHour () {
    if (this.acdChannels) {
      const mxGroup = MxISDK.getCurrentOrg()
      mxGroup.readOfficeHour().then(board => {
        const officeStatusChanged = this.currOfficeStatus !== board.routing_status
        this.currOfficeStatus = board.routing_status
        const officeHourChangedACDs = []
        if (officeStatusChanged) {
          const acd_channels = mxGroup.routingConfig.acd_channels
          for (const channel of acd_channels) {
            if (!channel.is_deleted) {
              const userBoards = MxISDK.getCurrentUser().boards
              if (userBoards && userBoards.length) {
                const tgtUserBoard = userBoards.find(ub => ub.board && ub.board.routing_channel === channel.sequence)
                const tgtChannelBoard = tgtUserBoard && tgtUserBoard.board
                if (!tgtChannelBoard || (tgtChannelBoard && tgtChannelBoard.routing_status !== BoardRoutingStatus.ROUTING_STATUS_IN_PROGRESS)) {
                  officeHourChangedACDs.push({
                    name: channel.name,
                    status: this.currOfficeStatus
                  })
                }
              }
            }
          }
        }
        if (officeHourChangedACDs.length > 0) {
          this.triggerLiveChatUpdated(officeHourChangedACDs)
        }
      })
    }
  }

  canAddUserInChat(cboard, callback) {
    const chat: any = {
      chatId: cboard.id,
      chatName: cboard.name,
      isActive: !cboard.isNoAction,
      unreadCount: cboard.feed_unread_count,
      chatType: MepChatType.GroupChat
    }
    if (cboard.isconversation) {
      chat.chatType = MepChatType.PrivateChat
    } else if (cboard.is_relation) {
      chat.chatType = MepChatType.RelationChat
    } else if (cboard.social_type) {
      if (cboard.social_type === 'SOCIAL_TYPE_WECHAT') {
        chat.chatType = MepChatType.WeChat
      } else if (cboard.social_type === 'SOCIAL_TYPE_WHATSAPP') {
        chat.chatType = MepChatType.WhatsApp
      } else if (cboard.social_type === 'SOCIAL_TYPE_LINE') {
        chat.chatType = MepChatType.Line
      }
    } else if (cboard.workflows) {
      chat.chatType = MepChatType.Flow
    }
    if (cboard.boardUsers) {
      chat.users = []
      for (const buser of cboard.boardUsers) {
        const {id, unique_id: uniqueId, first_name: firstName, last_name: lastName, email, phone_number: phoneNumber, title, avatar} = buser
        if (!buser.is_deleted) {
          if (buser.isMySelf) {
            chat.owner = {id, uniqueId, firstName, lastName, email, phoneNumber, title, avatar}
          } else {
            chat.users.push({id, uniqueId, firstName, lastName, email, phoneNumber, title, avatar})
          }
        }
      }
    }
    this.canAddUserCheckCB = callback
    this.trigger('checkCanAddUser', chat)
  }

  setupOnWillSendMessageCb () {
    const onWillSendMessageCB = (request: ClientRequest) => {
      return new Promise<boolean>((resolve, reject) => {
        let content
        const board = request.object.board || {}
        if ([ClientRequestType.BOARD_REQUEST_CREATE_COMMENT, ClientRequestType.BOARD_REQUEST_UPDATE_COMMENT,
          ClientRequestType.BOARD_REQUEST_TRANSACTION_COMMENT_CREATE, ClientRequestType.BOARD_REQUEST_TRANSACTION_COMMENT_UPDATE,
          ClientRequestType.FILE_FLOW_REQUEST_COMMENT_CREATE, ClientRequestType.FILE_FLOW_REQUEST_COMMENT_UPDATE,
          ClientRequestType.SIGN_FLOW_REQUEST_COMMENT_CREATE, ClientRequestType.SIGN_FLOW_REQUEST_COMMENT_UPDATE,
          ClientRequestType.SESSION_FLOW_REQUEST_COMMENT_CREATE, ClientRequestType.SESSION_FLOW_REQUEST_COMMENT_UPDATE].includes(request.type)) {
          content = board.comments?.[0].text
        } else if ([ClientRequestType.BOARD_REQUEST_CREATE_PAGE_COMMENT, ClientRequestType.BOARD_REQUEST_UPDATE_PAGE_COMMENT,
          ClientRequestType.BOARD_REQUEST_CREATE_PAGE_POSITION_COMMENT, ClientRequestType.BOARD_REQUEST_UPDATE_PAGE_POSITION_COMMENT].includes(request.type)) {
          content = board.pages?.[0].comments?.[0].text
        } else if ([ClientRequestType.BOARD_REQUEST_CREATE_TODO_COMMENT, ClientRequestType.BOARD_REQUEST_UPDATE_TODO_COMMENT].includes(request.type)) {
          content = board.todos?.[0].comments?.[0].text
        }
        if (content) {
          const payload = {content}
          this.irc.call('onWillSendMessage', payload, {nonTimeoutCtrl: true}).then((state: boolean) => {
            resolve(state)
          }).catch(err => {
            resolve(false)
          })
        } else {
          resolve(true)
        }
      })
    }
    MxISDK.setRequestInterceptor(onWillSendMessageCB)
  }

  send(method: string, a?: any, b?: any, c?: any, d?: any, e?: any, f?: any): void {
    if (this.irc) {
      this.irc.call(method, a, b, c, d, e, f).catch(e => {
        //ignore the not define error
      });
    }
  }

  trigger(name: string, a?: any, b?: any, c?: any, d?: any, e?: any, f?: any): void {
    this.send('event.' + name, a, b, c, d, e, f)
  }

  /**
   * return true if user has set a callback
   * @param name
   */
  hasCallback(name): boolean {
    return this.callback['callback_' + name]
  }

  /**
   * trigger addMember event
   * @param boardId
   */
  triggerAddMemberEvent(boardId: string): void {
    this.trigger('addMember', boardId);
  }

  triggerRemoveMemberEvent(param) {
    let user: CBoardUser = param.user;
    this.trigger('removeMember',
      param.binderId, {
      firstname: user.first_name,
      lastname: user.last_name,
      uniqueId: user.unique_id,
      email: user.email,
      title: user.title
    });
  }
  /**
   * support the last actibe timestamp callback, register a async callback function
   * @param fn
   */
  supportLatestActiveTime(fn: Function): void {
    this.irc.registerCall('getLastActiveTimestamp', fn);
  }

  /**
   * trigger error
   * @param type
   */
  triggerError(error: WebsdkError): void {
    this.trigger('error', {name: error.name, message: error.message || error.name})
  }

  /**
   * trigger user logout event
   */
  triggerClientLogoutEvent(): void {
    const name = 'clientLogout'
    this.trigger(name)
  }

  triggerClientAccessBlockedEvent(): void {
    const name = 'clientAccessBlocked'
    this.trigger(name)
  }

  /**
   * trigger unread count change event
   * @param count
   */
  triggerUnreadCountChangeEvent(count): void {
    this.trigger('unreadCountChange', count)
  }

  /**
   * trigger transaction button click event
   * @param info
   */
  triggerTransactionButtonClickEvent(info: {
    payload: string,
    stepId: string,
    chatId: string,
    transactionId: string,
    buttonId: string
  }): void {
    this.trigger('transactionButtonClick', info)
  }

  /**
   * trigger timeline rendered event
   */
  triggerTimelineRendered(): void {
    const name = 'timelineRendered'
    if (this.hasCallback(name)) {
      this.trigger(name)
    }
  }

  /**
   * trigger tapNewChat event
   */
  triggerTapNewChatEvent(): void {
    this.trigger('tapNewChat')
  }

  getMepChatInterface(mxBoard: CBoard) {
    const chat = {
      chatId: mxBoard.id,
      chatName: mxBoard.name,
      isWhatsChannel: mxBoard.isWhatsappChannel,
      isWeChatChannel: mxBoard.isWechatChannel,
      isLineChannel: mxBoard.isLineChannel,
      isActive: !mxBoard.isNoAction,
      chatType: MepChatType.Normal
    }
    if ((mxBoard.workflows && mxBoard.workflows.length) || mxBoard.isWorkflow) {
      chat.chatType = MepChatType.Flow
    } else if (mxBoard.isconversation) {
      chat.chatType = MepChatType.PrivateChat
    } else if (mxBoard.is_relation) {
      chat.chatType = MepChatType.RelationChat
    } else if (mxBoard.social_type) {
      if (mxBoard.social_type === 'SOCIAL_TYPE_WECHAT') {
        chat.chatType = MepChatType.WeChat
      } else if (mxBoard.social_type === 'SOCIAL_TYPE_WHATSAPP') {
        chat.chatType = MepChatType.WhatsApp
      } else if (mxBoard.social_type === 'SOCIAL_TYPE_LINE') {
        chat.chatType = MepChatType.Line
      }
    } else if (!mxBoard.is_acd) {
      chat.chatType = MepChatType.GroupChat
    }
    return chat
  }

  /**
   * trigger switch binder event
   * @param info
   */
  triggerSwitchBinderEvent(mxBoard: CBoard): void {
    this.trigger('switchBinder', this.getMepChatInterface(mxBoard))
  }

  /**
   * trigger notification event
   * @param info
   */
  triggerNotificationEvent(info: {
    message: string,
    boardName: string,
    icon: string,
    sequence: number,
    boardId: string
  }): void {
    this.trigger('notification', info)
  }

  /**
   * trigger last active data change event
   * @param time
   */
  triggerLastActiveDateChange(time: number): void {
    const name = 'lastActiveDateChange'
    if (this.hasCallback(name)) {
      const triggerCB = () => {
        this.activetimer = null
        this.lastTriggeredActiveTS = Date.now()
        this.trigger(name, time)
      }
      if (this.activetimer) {
        const triggerInteval = Date.now() - this.lastTriggeredActiveTS
        if (triggerInteval < 5000) {
          clearTimeout(this.activetimer)
          this.activetimer = setTimeout(triggerCB, 5000)
        }
      } else {
        this.activetimer = setTimeout(triggerCB, 5000)
      }
    }
  }
  ready(callback) {
    this._onReady = callback
    if (this.isReady) {
      callback()
    }
  }

  triggerTapChatCallButtonEvent(mxBoard: CBoard) {
    this.trigger('tapChatCallButton', this.getMepChatInterface(mxBoard))
  }

  triggerTapCallEvent(info: {board: CBoard, user: CBaseUser}) {
    let chat = info.board && this.getMepChatInterface(info.board)
    this.trigger('tapCall', chat, info.user)
  }

  triggerTapMeetNowEvent(param) {
    this.trigger('tapMeetNow', param)
  }

  triggerTapScheduleMeetEvent(param) {
    this.trigger('tapScheduleMeet', param)
  }

  triggerMeetEndEvent({meetId, isMissedCall}) {
    this.trigger('meetEnd', meetId, isMissedCall)
  }

  triggerTapMeetInvite(meetId) {
    this.trigger('tapMeetInvite', meetId)
  }

  triggerHostChangedEvent(param: {meet: MepMeet, participant: MepParticipant}) {
    this.trigger('hostChanged', param.meet, param.participant)
  }

  triggerMeetReadyEvent(meetId) {
    this.trigger('meetReady', {meetId})
  }

  triggerJoinMeetEvent(meet) {
    let {session_key, topic} = meet
    this.trigger('tapJoinMeet', {meetId: session_key, topic: topic})
  }

  triggerViewMeetEvent(meet) {
    let {session_key, topic} = meet
    this.trigger('tapViewMeet', {meetId: session_key, topic: topic})
  }

  triggerEditMeetEvent(meet) {
    let {session_key, topic} = meet
    this.trigger('tapEditMeet', {meetId: session_key, topic: topic})
  }

  triggerResendInvitationEvent(chatType, relationID) {
    let mepChatWrapper:any = {}
    switch (chatType) {
      case SocialType.SOCIAL_TYPE_LINE:
        mepChatWrapper.isLineChannel = true
        break
      case SocialType.SOCIAL_TYPE_WECHAT:
        mepChatWrapper.isWeChatChannel = true
        break
      case SocialType.SOCIAL_TYPE_WHATSAPP:
        mepChatWrapper.isWhatsChannel = true
        break
    }
    this.trigger('resendInvitation', mepChatWrapper, relationID)
  }

  triggerTapInviteInLiveMeet(meet) {
    let {session_key, topic} = meet
    this.trigger('tapInviteInLiveMeet', {meetId: session_key, topic: topic})
  }

  triggerTapLogConversation(payload: {orgId: string, chatId: string}) {
    this.trigger('tapLogConversation', payload)
  }

  triggerLiveChatUpdated(payload) {
    const chats = []
    payload.forEach(chat => {
      chats.push({name: chat.name, status: MEPLiveChatStatus[chat.status]})
    })
    this.trigger('liveChatUpdated', chats)
  }

  triggerOnPreview(){
    this.trigger('onPreview')
  }

  triggerOnPreviewExit(){
    this.trigger('onPreviewExit')
  }

  triggerMeetLastActiveTimeChange(time) {
    this.trigger('onMeetLastActiveTimeChange', time)
  }

  triggerBinderClosed(binderObj: CBoard) {
    this.trigger('onBinderClosed', this.getMepChatInterface(binderObj))
  }

  triggerOnUseFlowTemplate(template: object) {
    this.trigger('onUseFlowTemplate', template)
  }

  triggerWillSubmitEsign (payload) {
    return this.irc.call('onWillSubmitEsign', payload, {nonTimeoutCtrl: true})
  }
  triggerWillSearchCRMUser (payload) {
    return this.irc.call('tapSearchCRMUser', payload, {nonTimeoutCtrl: true})
  }
  triggerWillGetCRMTriggers (stageName) {
    return this.irc.call('getCRMTriggers', {stageName}, {nonTimeoutCtrl: true})
  }
  triggerWillGetSalesProcessStages (salesProcessId) {
    return this.irc.call('getCRMTriggers', {salesProcessId}, {nonTimeoutCtrl: true})
  }

  triggerOpenHelpCenter (isOpen) {
    return this.trigger('openHelpCenter', {isOpen})
  }

  triggerLeaveMessageForLiveChat (payload) {
    // payload: {binderId: string, todoId: string}
    this.trigger('onLeaveMessageForLiveChat', payload)
  }

  triggerOnPreviewOpened () {
    this.trigger('onPreviewOpened')
  }

  triggerOnPreviewClosed () {
    this.trigger('onPreviewClosed')
  }

  triggerOnACDBoardCreated (boardInfo) {
    this.trigger('onACDBoardCreated', boardInfo)
  }

  triggerPrintBinder(url) {
    this.trigger('onPrintBinder', url)
  }

  triggerOnChatMessageSent (payload) {
    this.trigger('onChatMessageSent', payload)
  }

  triggerOnSalesforceTemplateCreated (payload) {
    this.trigger('onSalesforceTemplateCreated', payload)
  }

  triggerOnSalesforceTriggerConfigured (payload) {
    this.trigger('onSalesforceTriggerConfigured', payload)
  }
  triggerOnSalesforceConfigurationCompleted () {
    return this.irc.call('onSalesforceConfigurationCompleted', {}, {nonTimeoutCtrl: true})
  }

  triggerOnRemovingSalesforceTrigger (payload) {
    this.trigger('onRemovingSalesforceTrigger', payload)
  }

  triggerOnMeetAccepted (meet: MepMeet) {
    this.trigger('onMeetAccepted', meet)
  }

  triggerOnMeetDeclined (meet: MepMeet) {
    this.trigger('onMeetDeclined', meet)
  }

  triggerTapOpenWorkspace(info: {id: string}) {
    this.trigger('tapOpenWorkspaceButton', info)
  }

  triggerOnPreviewWorkspaceClosed () {
    this.trigger('onPreviewWorkspaceClosed')
  }

  triggerOnActionButtonTapped(info: MEPAction) {
    this.trigger('onActionButtonTapped', info)
  }

  getBoardBasicInfo (boardId: string, option?: MxBoardOption) {
    let userBoard
    const currentUser = MxISDK.getCurrentUser()
    const userBoards = currentUser.boards
    if (userBoards && userBoards.length) {
      userBoard = userBoards.find(item => item.board?.id === boardId)
    }
    if (userBoard) {
      return userBoard.board
    } else {
      // for admin
      return currentUser.readBoard(boardId).then((board) => {
        return board
      }).catch((err) => {
        // for manager
        if (option?.userId) {
          return currentUser.readBoardBasicInfo(boardId, option).then(board => {
            return board
          }).catch((err) => {
            if (err.code === MxConsts.MepSDKErrorType.RESPONSE_ERROR_PERMISSION) {
              this.triggerError({
                name: MxConsts.MepSDKErrorType.RESPONSE_ERROR_PERMISSION,
                message: 'Access denied, please provide correct teamMemberId'
              })
            }
          })
        } else if (err.detailCode === 'ERROR_INVALID_BOARD_ID') {
          return {
            id: boardId,
            users: [{
              user: {
                id: currentUser.id
              }
            }]
          }
        } else {
          this.triggerError({
            name: MxConsts.MepSDKErrorType.RESPONSE_ERROR_PERMISSION,
            message: 'Access denied, please provide teamMemberId'
          })
          return null
        }
      })
    }
  }

  markSalesforceTemplate (boardId: string, info: SalesforceTriggerInfo) {
    const object: Record<string, string> = {}
    if (info.salesProcessName) {
      object['API_sales_process_name'] = info.salesProcessName
    }
    if (info.stageName) {
      object['API_stage_name'] = info.stageName
    }
    object['API_is_published'] = info.isPublished?'true':'false'
    return MxISDK.getCurrentUser().loadBoard(boardId).then(mxBoard=>{
      return mxBoard.createOrUpdateTags(object).then(()=>{
        MxISDK.getCurrentUser().unloadBoard(boardId)
        return Promise.resolve()
      })
    })
  }

  sendUserLoggedInAuditLog () {
    const mxUserBasicInfo = MxISDK.getCurrentUser()?.basicInfo
    const userActivity: UserActivityLog = {
      actor_org_id: MxISDK.getCurrentOrg() ? MxISDK.getCurrentOrg().id : '',
      actor_email: mxUserBasicInfo ? mxUserBasicInfo.email : '',
      actor_id: mxUserBasicInfo ? mxUserBasicInfo.id : '',
      activities: [{
        action_type_id: 'USER_LOGGED_IN',
        action_group_id: 'USER_SESSIONS',
        platform: 'Web',
        client_version: MxISDK.getISDKConfig().clientVersion
      }]
    }
    MxISDK.getCurrentUser().postAuditLog(userActivity)
  }
}
const websdkController = new WebsdkController();

export { WebsdkError, websdkController }
