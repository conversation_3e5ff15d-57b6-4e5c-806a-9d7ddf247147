import moment from 'moment-timezone'
import RRule, {RRuleSet} from 'rrule'

import {Defines, MxISDK} from 'isdk'
import {ObjectUtils} from "@commonUtils/object"
import {BrowserUtils} from '@commonUtils/browser'
import {TimeUtils} from "@commonUtils/time"
import {ActionObject} from 'isdk/src/proto/generated/ActionObject'
import {BoardUserStatus, CBoardTeam, CBoardUser} from '@controller/defines/CBoardUser'
import {SessionStatus} from 'isdk/src/proto/generated/SessionStatus'
import {BoardFormatter} from './board'
import {CMeetObject} from '@controller/defines/meet/CMeetObject'
import {CMeetRecording} from '@controller/defines/meet/CMeetRecording'
import {BoardSession} from 'isdk/src/proto/generated/BoardSession'
import {BoardResourceType} from 'isdk/src/proto/generated/BoardResourceType'
import {RSVPStatus} from 'isdk/src/proto/generated/RSVPStatus'
import {CalendarController} from '@controller/calendar/src/calendarController'
import {UserController} from '@controller/user/src/userController'
import {GroupController} from '@controller/group/src/groupController'
import {UserFormatter} from './user'
import {MxConsts} from '@commonUtils/consts'
import {BinderController} from '@controller/binder/src/binderController'
import {BoardType} from "isdk/src/proto/generated/BoardType";


export class MeetUtils {

	static getTopic(board: Defines.Board): string {
		let session = ObjectUtils.getByPath(board, 'sessions.0.session')
		return ObjectUtils.getByPath(session, 'topic', ObjectUtils.getByPath(board, 'name', ''))
	}

	static getHost(cboardUsers: CBoardUser[]): CBoardUser {
		let host: CBoardUser = null
		if (!cboardUsers) {
			return host
		}
		for (let i = 0; i < cboardUsers.length; i++) {
			if (cboardUsers[i].isOwner && !cboardUsers[i].is_deleted) {
				host = cboardUsers[i]
				break
			}
		}
		return host
	}

	static getDuration(session: ActionObject) {
		let start = session.start_time
		let end = session.end_time
		if (!start && !end) {
			start = session.scheduled_start_time
			end = session.scheduled_end_time
		}
		if (end >= start) {
			return end - start
		} else {
			return 0
		}

	}

	static getDisplayRange (session:ActionObject) {
		let start_time, end_time
		//recurring meeting scheduled_start_time and scheduled_end_time is already mocked
		if (session.scheduled_start_time) {
			if (session.session_status === SessionStatus.SESSION_ENDED) {
				start_time = session.start_time
				end_time = session.end_time
			} else {
				start_time = session.scheduled_start_time
				end_time = session.scheduled_end_time || session.scheduled_start_time + 60 * 60 * 1000
			}
		} else {
			start_time = session.start_time
			if (session.session_status === SessionStatus.SESSION_STARTED) {
				end_time = session.start_time + 60 * 60 * 1000
			} else {
				end_time = session.end_time
			}
		}
		return {start_time, end_time}
	}

	static getFormattedDuration(session: ActionObject, timezone: string, language: string, opts?: {ignoreStartDate?: boolean}) {
		const {start_time, end_time} = MeetUtils.getDisplayRange(session)
		const startM = TimeUtils.getTimezonedTime(start_time, timezone)
		const endM = TimeUtils.getTimezonedTime(end_time, timezone)
		let startTimeStr
		if (!opts) {
			startTimeStr = TimeUtils.formatShortDate(start_time, timezone) + ' ' + TimeUtils.formatShortTime(start_time, timezone, language)
		} else if (opts.ignoreStartDate) {
			startTimeStr = TimeUtils.formatShortTime(start_time, timezone, language)
		}

		let endTimeStr = TimeUtils.formatShortTime(end_time, timezone, language)
		if (!startM.isSame(endM, 'day')) {
			if (startM.isSame(endM, 'year')) {
				endTimeStr = TimeUtils.formatShortDate(end_time, timezone, true) + ' ' + endTimeStr
			} else {
				endTimeStr = TimeUtils.formatShortDate(end_time, timezone) + ' ' + endTimeStr
			}
		}

		return startTimeStr + ' - ' + endTimeStr
	}

	static isHost(me: Defines.User, users: CBoardUser[]) {
		let host: CBoardUser = MeetUtils.getHost(users)
		return host && host.id === me.id
	}

	static isInvited(users: CBoardUser[]) {
		let self:CBoardUser = users.find(user => user.isMySelf)
		return self && self.status === BoardUserStatus.BOARD_INVITED
	}

	static isPast(session: ActionObject, passedMeetTime?: {start_time: number, end_time: number}) {
		let scheduled_end_time = session.scheduled_end_time
		let past = false
		if (session.rrule) {
			/* In two cases, we need to consider a recurring meet as past:
			1. The meet is started once (server will create a new instance to replace this)
			2. The meet is not started and the schedule end time is in the past
			*/
			scheduled_end_time = (passedMeetTime && passedMeetTime.end_time) || session.scheduled_end_time
		}
		if (session.session_status === SessionStatus.SESSION_STARTED) {
			past = false
		} else if (session.session_status === SessionStatus.SESSION_SCHEDULED) {
			past = Date.now() > (scheduled_end_time + 8 * 60 * 60 * 1000)
		} else if (session.session_status === SessionStatus.SESSION_ENDED) {
			past = true
		}
		return past
	}

	static isStartable(me: Defines.User, session: ActionObject, cboardUsers: CBoardUser[], passedMeetTime?: {start_time: number, end_time: number}) {
		return (
			MeetUtils.isHost(me, cboardUsers) &&
			!MeetUtils.isPast(session, passedMeetTime) &&
			!MeetUtils.isStarted(session)
		)
	}

	// NOTE: meet restart logic currently only applied in Calendar view and Freemium Binder "Meetings" tab
	static isRestartable(session, isHost) {
		if (!isHost) {
			return false
		}

		let {session_key, session_status, rrule, start_time, end_time, original_board_id, scheduled_start_time, scheduled_end_time} = session
		if (session_status !== SessionStatus.SESSION_ENDED || !scheduled_end_time || Date.now() > scheduled_end_time) {
			return false
		}

		let isRestartable = true, inRestartableCtx = false, monthMeets = []

		const calendarCtrl = CalendarController.getInstance()
		const groupCtrl = GroupController.getInstance()
		if (calendarCtrl.monthMeets?.length) {
			// for normal case in Calendar view
			inRestartableCtx = true
			monthMeets = calendarCtrl.monthMeets
		} else if (groupCtrl.getBasicInfo?.isFreemium && original_board_id) {
			const binderCtrl = BinderController.getInstance(original_board_id)
			if (binderCtrl.monthMeets?.length) {
				inRestartableCtx = true
				monthMeets = binderCtrl.monthMeets
			}
		}
		if (!inRestartableCtx) {
			return false
		}

		if (rrule) {
			if (monthMeets) {
				// if current session has meet instance scheduled on today, convert its scheduled_end_time to today
				for (let i = 0; i < monthMeets.length; i++) {
					const meet = monthMeets[i]
					const msession = ObjectUtils.getByPath(meet, 'board.sessions.0.session', {})
					if (msession.session_key === session_key && msession.session_status === SessionStatus.SESSION_ENDED) {
						const isToday = moment().isSame(msession.scheduled_end_time, 'day')
						if (isToday) {
							scheduled_start_time = msession.scheduled_start_time
							scheduled_end_time = msession.scheduled_end_time
							break
						}
					}
				}
			}

			if (end_time < scheduled_start_time) {
				// recurring meet was started and ended before its scheduled_start_time
				return false
			}
		}

		const userCtrl = UserController.getInstance()
		if (userCtrl) {
			// check if has same session_key meeting started
			for (let i = 0; i < userCtrl.activeMeets.length; i++) {
				const asession = ObjectUtils.getByPath(userCtrl.activeMeets[i], 'sessions.0.session', {})
				if (asession.session_status === SessionStatus.SESSION_STARTED && asession.session_key === session_key) {
					isRestartable = false
					break
				}
			}
		}

		if (isRestartable && monthMeets) {
			// check if has other same session_key meetings, and current one is the recent one
			for (let i = 0; i < monthMeets.length; i++) {
				const msession = ObjectUtils.getByPath(monthMeets[i], 'board.sessions.0.session', {})
				if (msession.session_key === session_key) {
					if (msession.session_status === SessionStatus.SESSION_STARTED) {
						isRestartable = false
						break
					} else if (msession.session_status === SessionStatus.SESSION_ENDED && (start_time < msession.start_time)) {
						isRestartable = false
						break
					}
				}
			}
		}
		return isRestartable
	}

	static isJoinable(session: ActionObject, passedMeetTime?: {start_time: number, end_time: number}) {
		let scheduled_start_time = (session.rrule && passedMeetTime && passedMeetTime.start_time) || session.scheduled_start_time
		if (MeetUtils.isStarted(session)) {
			return true
		} else if (
			session.session_status === SessionStatus.SESSION_SCHEDULED &&
			session.milliseconds_allowed_to_join_before_start &&
			scheduled_start_time - Date.now() <=
			session.milliseconds_allowed_to_join_before_start &&
			!MeetUtils.isPast(session, passedMeetTime)
		) {
			return true
		} else {
			return false
		}
	}

	static isDeletable(me: Defines.User, session: ActionObject, cboardUsers: CBoardUser[]) {
		return MeetUtils.isHost(me, cboardUsers) && !MeetUtils.isStarted(session)
	}

	static getMeetTabName() {
		// v2.9.0, fix issue on Windows Agent, if set <a> target to others, agent can ONLY catch the link once, so need set it to browser default '_blank'
		return window.location.host + '.meet.tab'
	}

	static isStarted(session: ActionObject) {
		return session.session_status === SessionStatus.SESSION_STARTED
	}

	static isExternalMeet(session: ActionObject) {
		const ExtCalType = Defines.ExtCalType
		return session.ext_cal_type === ExtCalType.CAL_TYPE_OUTLOOK || session.ext_cal_type === ExtCalType.CAL_TYPE_GOOGLE
	}

	static isScheduled(session: ActionObject) {
		return session.session_status === SessionStatus.SESSION_SCHEDULED
	}

	static isInstant(session: ActionObject) {
		return !(session.scheduled_start_time || session.scheduled_end_time)
	}

	static isEnded(session: ActionObject) {
		return session.session_status === SessionStatus.SESSION_ENDED
	}

	// Note: we have similar method in /controller/websdk/utils/WebsdkUtils.ts, any changes should apply to WebsdkUtils.ts as well.
	static getRSVPStates(board: Defines.Board, schedule_start_time) {
		const userRSVPStatus = {}
		if (board.user_rsvps) {
			for (let i = 0; i < board.user_rsvps.length; i++) {
				const ursvp = board.user_rsvps[i]
				if (ursvp.is_deleted) {
					continue
				}

				const actorId = ObjectUtils.getByPath(ursvp, 'actor.user.id')
				const rsvpStat = BoardFormatter.getUserRsvpStatus(actorId, board, schedule_start_time)
				userRSVPStatus[actorId] = rsvpStat
			}
		}
		return userRSVPStatus
	}

	static getRecordingUrl(session: ActionObject) {
		if (session && session.recording) {
		  return `${MxISDK.getContextPath()}/board/${session.board_id}/${session.recording}`
		} else {
			return ''
		}
	}

	static getMeetLink (session: ActionObject) {
		const groupCtrl = GroupController.getInstance()
		if (groupCtrl && groupCtrl.isM0APP) {
			return `${BrowserUtils.getOrigin()}${MxISDK.getContextPath()}/universal/meet?action=join&sessioncode=${session.session_key}&universal=true`
		} else {
			return `${BrowserUtils.getOrigin()}${MxISDK.getContextPath()}/meet?action=join&sessioncode=${session.session_key}`
		}
	}

	static getMeetPassword (session: ActionObject) {
		let password
		const groupCtrl = GroupController.getInstance()
		if (groupCtrl.groupSettings) {
			// if groupCtrl.getBasicInfo is null, then it is anonymous user access case
			const settings = groupCtrl.groupSettings
			if (!settings.enable_private_meet) {
				const {is_private, password_protected, session_password} = session
				if (settings.enable_meet_password) {
					password = session_password || settings.meeting_default_password
				} else {
					if (!is_private && password_protected) {
						password = session_password
					}
				}
			}
		}
		return password
	}

	static getMeetSecurity (board: Defines.Board, session: ActionObject) {
		let security
		const groupCtrl = GroupController.getInstance()
		if (groupCtrl.groupSettings) {
			const settings = groupCtrl.groupSettings
			if (settings.enable_private_meet) {
				security = MxConsts.MeetSecurityType.PRIVATE
			} else if (settings.enable_meet_password) {
				security = MxConsts.MeetSecurityType.PASSWORD
			} else {
				const {is_private, password_protected} = session
				if (is_private) {
					security = MxConsts.MeetSecurityType.PRIVATE
				} else if (password_protected) {
					security = MxConsts.MeetSecurityType.PASSWORD
				} else if (board.enable_waiting_room) {
					security = board.waiting_room_audience ?? MxConsts.WaitingRoomAudience.WAITING_ROOM_AUDIENCE_GUEST
				} else {
					security = MxConsts.MeetSecurityType.STANDARD
				}
			}
		} else {
			// for anonymous user
			if (board.enable_waiting_room) {
				security = board.waiting_room_audience ?? MxConsts.WaitingRoomAudience.WAITING_ROOM_AUDIENCE_GUEST
			}
		}
		return security
	}

	/**
	 * transform to CMeetObject
	 * @param board
	 * @param passedMeetTime - required by recurrent meet, should pass meetTime from calendar timeline view
	 * NOTE: This method is invoked by meet anonymous join, that means currentUser may not exist
	 */
	static transformMeetObject(board: Defines.Board, passedMeetTime?: {start_time: number, end_time: number}): CMeetObject {
		const mxUser = MxISDK.getCurrentUser()
		const loginUser = mxUser && mxUser.user
		const users = (board.users || []).map(buser => BoardFormatter.transformBoardUser(buser, board.id))

		const boardSession: BoardSession = ObjectUtils.getByPath(board, 'sessions.0')
		let session: ActionObject = boardSession.session

		const timezone = loginUser && loginUser.timezone
		const language = loginUser && loginUser.language
		// if session is recurrent session, use start_time/end_time passed from Calendar Timeline
		let sessionModelForDuration = session
		if (session.rrule && passedMeetTime) {
			//in calendar, we use passedMeetTime for recurring meeting.
			sessionModelForDuration = {
				scheduled_start_time: passedMeetTime.start_time || session.scheduled_start_time,
				scheduled_end_time: passedMeetTime.end_time || session.scheduled_end_time,
				start_time: session.start_time,
				end_time: session.end_time,
				session_status: session.session_status
			}
		}
		let {start_time, end_time} = MeetUtils.getDisplayRange(sessionModelForDuration)
		let dateArr = TimeUtils.formatShortDate(start_time, timezone, true).split(' ')
		let meetObject: CMeetObject = {
			session: session,
			sequence: boardSession.sequence,
			topic: MeetUtils.getTopic(board),
			board_id: board.id,
			isDraft: board.istemp,
			session_key: session.session_key,
			password: MeetUtils.getMeetPassword(session),
			hasPassword: session.password_protected,
			agenda: session.agenda,
			auto_recording: session.auto_recording,
			isRecurrent: !!session.rrule,
			duration: end_time - start_time,
			dateInfo: {
				month: dateArr[0].toUpperCase(),
				date: dateArr[1]
			},
			startDate: TimeUtils.formatShortDate(start_time, timezone),
			durationInfo: MeetUtils.getFormattedDuration(sessionModelForDuration, timezone, language, {ignoreStartDate: true}),
			formattedDurationInfo: MeetUtils.getFormattedDuration(sessionModelForDuration, timezone, language),
			meetLink: MeetUtils.getMeetLink(session),
			host: MeetUtils.getHost(users),
			members: users.filter(user => !user.is_deleted), // ignore deleted users
			isStarted: MeetUtils.isStarted(session),
			isInstant: MeetUtils.isInstant(session),
			start_time: session.start_time,
			end_time: session.end_time,
			isEnded: MeetUtils.isEnded(session),
			created_time: board.created_time,
			all_video_on: false,
			isExternalMeet: MeetUtils.isExternalMeet(session),
			host_video_on: false,
			participant_video_on: false,
			security: MeetUtils.getMeetSecurity(board, session),
			tags: board.tags,
			original_board_id: session.original_board_id,
			waitingUsers: board.waiting_users,
		}

		if (board.teams && board.teams.length) {
			meetObject.teams = []
			for (let team of board.teams) {
				if (!team.is_deleted) {
					const { id, name, total_members, description,type,picture } = team.group
					meetObject.teams.push({
						id,
						name,
						memberCounts: total_members || 0,
						description: description,
						sequence: team.sequence,
						type,
						picture
					} as CBoardTeam)
				}
			}
		}
		if (board.workflows) {
			meetObject.workflow = BoardFormatter.transformBoardWorkflow(board.workflows[0], board)
		}

		meetObject.reminder_interval = session.reminder_interval === undefined ? -1 : session.reminder_interval
		if (loginUser && board.reminders) {
			const reminder = board.reminders.find(reminder => ObjectUtils.getByPath(reminder, 'creator.user.id') === loginUser.id)
			if (reminder) {
				meetObject.reminder_interval = reminder.reminder_interval
			}
		}

		meetObject.scheduled_start_time = session.scheduled_start_time
		meetObject.scheduled_end_time = session.scheduled_end_time
		if (session.rrule && passedMeetTime) {
			if (passedMeetTime.start_time) {
				meetObject.scheduled_start_time = passedMeetTime.start_time
			}
			if (passedMeetTime.end_time) {
				meetObject.scheduled_end_time = passedMeetTime.end_time
			}
		}

		if (session.rrule && session.exdate) {
			let formattedStartTime = moment(meetObject.scheduled_start_time).tz(session.timezone).format('YYYYMMDDTHHmmss')
			if (session.exdate.indexOf(formattedStartTime) > -1) {
				meetObject.isExceptionMeet = true
			}
		}

		meetObject.isPast = MeetUtils.isPast(session, passedMeetTime)
		meetObject.isHost = loginUser ? MeetUtils.isHost(loginUser, users) : false
		meetObject.isRestartable = MeetUtils.isRestartable(session, meetObject.isHost)

		if (meetObject.isPast && !meetObject.isRestartable) {
			if (session.user_roster) {
				meetObject.members = MeetUtils.getPastMeetingMembers(meetObject.members, session)
			}
			if (session.session_status === SessionStatus.SESSION_ENDED) {
				meetObject.showRosterStates = true
			}
		}

		meetObject.rsvpStates = MeetUtils.getRSVPStates(board, meetObject.scheduled_start_time)
		if (loginUser) {
			if (meetObject.isHost) {
				meetObject.isAccepted = true
				meetObject.isDeclined = false
			} else if (Object.keys(meetObject.rsvpStates).length > 0) {
				const myRSVPStat = meetObject.rsvpStates[loginUser.id]
				meetObject.isAccepted = myRSVPStat === RSVPStatus.RSVP_ACCEPTED
				meetObject.isDeclined = myRSVPStat === RSVPStatus.RSVP_DECLINED
			}
			if (!meetObject.isExceptionMeet) {
				meetObject.isStartable = MeetUtils.isStartable(loginUser, session, users, passedMeetTime)
				if  (meetObject.isHost) {
					meetObject.isEditable = !meetObject.isPast && !meetObject.isStarted
				} else {
					if (session.rrule) {
						meetObject.isEditable = false
					} else {
						meetObject.isEditable = !meetObject.isPast && !meetObject.isStarted && meetObject.isAccepted
					}
				}
			}

			if (meetObject.members && meetObject.members.findIndex(member => member.id === loginUser.id) < 0) {
				meetObject.removedMe = true
			}

			const isScheduled = session.session_status === SessionStatus.SESSION_SCHEDULED
			if (meetObject.isHost && isScheduled && !meetObject.isPast && meetObject.members && meetObject.members.length > 1) {
				meetObject.indicateAllDeclined = true
				for (const member of meetObject.members) {
					if (!member.isMySelf && meetObject.rsvpStates[member.id] !== RSVPStatus.RSVP_DECLINED) {
						meetObject.indicateAllDeclined = false
						break
					}
				}
			}
		}

		if (!meetObject.isDeclined) {
			meetObject.isJoinable = !meetObject.isExceptionMeet && MeetUtils.isJoinable(session, passedMeetTime)
		}

		if (board.resources) {
			meetObject.recordings = MeetUtils.getMeetRecordings(board)
		}

		if (meetObject.isStartable || meetObject.isJoinable) {
			meetObject.tabName = MeetUtils.getMeetTabName()
		}

		if (board.tags && board.tags.length) {
			for (const btag of board.tags) {
				if (btag.name === 'API_all_video_on' && btag.string_value === '1') {
					meetObject.all_video_on = true
				} else if (btag.name === 'API_host_video_on' && btag.string_value === '1') {
					meetObject.host_video_on = true
				} else if (btag.name === 'API_participant_video_on' && btag.string_value === '1') {
					meetObject.participant_video_on = true
				} else if (btag.name === 'API_mute_participants_upon_entry' && btag.string_value === '1') {
					meetObject.mute_upon_entry = true
				} else if (btag.name === 'API_originalSessionKey') {
					meetObject.originalSessionKey = btag.string_value
				}
			}
		}

		const {isRecurrent, originalSessionKey, session_key} = meetObject
		if (isRecurrent && (!originalSessionKey || originalSessionKey === session_key)) {
			meetObject.isOriginRecurrent = true
		}
		meetObject.parentBoardId = session.parent_board_id
		meetObject.isWorkflowStarted = board.type === BoardType.BOARD_TYPE_WORKFLOW

		return meetObject
	}

	// Generally, there is only one recording in meeting (find out the video which session.recording referenced)
	static getMeetRecording (board: Defines.Board) {
		let meetRecording: CMeetRecording
		const session = board.sessions[0].session
		if (session && session.recording) {
			if (board.resources) {
				let video, chat
				for (let i = 0; i < board.resources.length; i++) {
					if (video && chat) {
						break
					} else {
						const resource = board.resources[i]
						if (resource.type === BoardResourceType.BOARD_RESOURCE_SESSION_AS_VIDEO && resource.sequence === session.recording) {
							video = resource
						} else if (resource.type === BoardResourceType.BOARD_RESOURCE_SESSION_MEET_CHAT && resource.original_resource_sequence === session.recording) {
							chat = resource
						}
					}
				}
				if (video) {
					meetRecording = {
						name: video.name,
						sequence: video.sequence
					}
					if (chat) {
						meetRecording.chat = chat
						const download_name = video.name.replace(/\.\w+$/, '.zip')
						const mxBoard = MxISDK.getCurrentUser().getInstantBoard(board.id)
						meetRecording.download_url = mxBoard.makeDownloadZipResourceUrl([video.sequence, chat.sequence], download_name)
					} else {
						meetRecording.download_url = `${MxISDK.getContextPath()}/board/${board.id}/${video.sequence}?d=`
					}
				}
			}
		}
		return meetRecording
	}

	static getPastMeetingMembers (boardUsers: Array<CBoardUser>, session: ActionObject) {
		let members = [...boardUsers]
		let anonymousMembers = []
		for (let roster of session.user_roster) {
			const {id: rid, name: rname, email: remail, phone_number: rphoneNumber, unique_id: runiqueId} = roster.user
			const idx = boardUsers.findIndex(member => UserFormatter.isSameUser(member as Defines.User, roster.user))
			if (idx > -1) {
				members[idx].hasJoinedMeet = true
			} else {
				const rmember = {
					id: rid,
					email: remail,
					phone_number: rphoneNumber,
					unique_id: runiqueId,
					name: rid ? UserFormatter.getUserName(roster.user) : rname,
					nonInvitedUser: true,
					hasJoinedMeet: true,
					sequence: roster.sequence
				} as CBoardUser
				if ((rid && anonymousMembers.findIndex(auser => auser.id === rid) === -1) || !rid) {
					anonymousMembers.push(rmember)
				}
			}
		}
		members = members.sort((a, b) => {
			const aname = a.name || ''
			const bname = b.name || ''
			return aname.toLowerCase().localeCompare(bname.toLowerCase())
		})
		anonymousMembers = anonymousMembers.sort((a, b) => {
			const aname = a.name || ''
			const bname = b.name || ''
			return aname.toLowerCase().localeCompare(bname.toLowerCase())
		})
		return members.concat(anonymousMembers)
	}


	// For meeting detail view. Previously, one meeting may have serveral recordings
	static getMeetRecordings (board: Defines.Board) {
		let recordings
		if (board && board.resources) {
			recordings = []
			const videos = {}, chats = {}
			let vtt= null
			let summary
			const session = board.sessions?.[0]?.session
			const curentVttSequence = session?.transcription_vtt
			board.resources.forEach(res => {
				if (res.type === BoardResourceType.BOARD_RESOURCE_SESSION_AS_VIDEO) {
					videos[res.sequence] = {
						sequence: res.sequence,
						name: res.name
					}
				} else if (res.type === BoardResourceType.BOARD_RESOURCE_SESSION_MEET_CHAT) {
					chats[res.original_resource_sequence] = {
						sequence: res.sequence,
						name: res.name
					}
				} else if (res.type === BoardResourceType.BOARD_RESOURCE_SESSION_TRANSCRIPTION_VTT && res.sequence === curentVttSequence) {
					vtt = {
						sequence: res.sequence,
						name: res.name
					}
				} else if (res.type === BoardResourceType.BOARD_RESOURCE_SESSION_SUMMARY && res.sequence === session?.meet_summary) {
					summary = {
						sequence: res.sequence,
						name: res.name
					}
				}
				
			})
			for (const key in videos) {
				const video = videos[key]
				const chat = chats[video.sequence]
				
				
				const meetRecording: CMeetRecording = {
					name: video.name,
					sequence: video.sequence
				}
				if (chat || vtt || summary) {
					meetRecording.chat = chat
					meetRecording.vtt = vtt
					meetRecording.summary = summary
					const download_name = video.name.replace(/\.\w+$/, '.zip')
					const mxBoard = MxISDK.getCurrentUser().getInstantBoard(board.id)
					meetRecording.download_url = mxBoard.makeDownloadZipResourceUrl([video.sequence, ...[chat?.sequence, vtt?.sequence, summary?.sequence].filter(Boolean)], download_name)

				} else {
					meetRecording.download_url = `${MxISDK.getContextPath()}/board/${board.id}/${video.sequence}?d=`
				}
				recordings.push(meetRecording)
			}
		}
		return recordings
	}

	static getMeetRecordingUrl (boardSession:any) {
	  let session = boardSession.session || {}
	  if (session && session.recording) {
		return `${MxISDK.getContextPath()}/board/${session.board_id}/${session.recording}`
	  }
	  return ''
	}

	static computeSessionInfo (boardSession:any, board:any) {
	  let session = boardSession.session
	  if (session) {
		boardSession.recording = MeetUtils.getMeetRecordingUrl(boardSession)
	  }
	  return boardSession
	}

	static transformCliUpcomingMeet (meetBoard, timezone) {
		const currUser = MxISDK.getCurrentUser()
		const language = currUser && currUser.user.language
		const board = ObjectUtils.getByPath(meetBoard, 'board')
		const session = ObjectUtils.getByPath(board, 'sessions.0.session')
		const users = board.users
		const s_time = session.scheduled_start_time
		const e_time = session.scheduled_end_time

		const dateArr = TimeUtils.formatShortDate(s_time, timezone, true).split(' ')
		// const timeFormat = TimeUtils.getHourMechanism()
		const meeting = {
			// recurring meeting has no meetBoard.client_uuid
			client_uuid: meetBoard.client_uuid || board.client_uuid,
			board_id: board.id,
			session_key: session.session_key,
			topic: session.topic || board.name,
			dateInfo: {
				month: dateArr[0].toUpperCase(),
				date: dateArr[1]
			},
			durationInfo: MeetUtils.getFormattedDuration(session, timezone, language, {ignoreStartDate: true}),
			start_time: s_time,
			end_time: e_time,
			members: users.filter(user => !user.is_deleted),
			isDeclined: false,
			indicateAllDeclined: false,
			isAccepted: false
		}
		if (meeting.members && meeting.members.length > 1) {
			meeting.indicateAllDeclined = true
		}

		if (board.user_rsvps) {
			const rsvpStates = MeetUtils.getRSVPStates(board, session.scheduled_start_time)
			if (Object.keys(rsvpStates).length > 0 && rsvpStates[currUser.id]) {
				meeting.isDeclined = rsvpStates[currUser.id] === RSVPStatus.RSVP_DECLINED
				meeting.isAccepted = rsvpStates[currUser.id] === RSVPStatus.RSVP_ACCEPTED
			}
			if (meeting.indicateAllDeclined) {
				for (const member of meeting.members) {
					const userId = member.user.id
					if (userId !== currUser.id && rsvpStates[userId] !== RSVPStatus.RSVP_DECLINED) {
						meeting.indicateAllDeclined = false
						break
					}
				}
			}
		} else {
			meeting.indicateAllDeclined = false
		}
		return meeting
	}

  static getRRuleSetOfRecurrentMeet(
    session: any /* session object of meeting board in original server response */
  ): RRuleSet {
    // process datetime based on UTC
    const { dtstart, rrule, exdate, timezone } = session
    const dtstartUtc = moment.tz(dtstart, timezone).tz('UTC')
    const rruleSet = new RRuleSet()

    try {
      const rule = RRule.fromString(
        `DTSTART;TZID=UTC:${dtstartUtc.format('YYYYMMDDTHHmmss')};\nRRULE:${rrule}`)
      rruleSet.rrule(rule)

      if (exdate) {
        for (let dt of exdate.split(',')) {
          if (dt.startsWith('EXDATE:')) {
            dt = dt.substring(7)
          }
          rruleSet.exdate(moment.tz(dt, timezone).tz('UTC').toDate())
        }
      }
    } catch(error) {
      throw new Error(error)
    }
    return rruleSet
  }

  static getFirstOccurrenceDateOfRecurrentMeet(
    session: any /* session object of meeting board in original server response */
  ): Date {
    const rruleSet = this.getRRuleSetOfRecurrentMeet(session)
    return rruleSet.all(function (date, i) {
      return i < 1
    })[0]
  }

  static getLastOccurrenceTimestampOfRecurrentMeet(
    session: any /* session object of meeting board in original server response */
  ): number /* milliseconds */ {
    const { rrule } = session

    // return Date of now + 100 years if never end repeat
    if (rrule.indexOf('UNTIL=') === -1 && rrule.indexOf('count=') === -1) {
      return moment().add(100, 'y').unix() * 1000
    }

    const rruleSet = this.getRRuleSetOfRecurrentMeet(session)
    const lastOccurrenceDate = rruleSet.all().splice(-1)[0]
    if (lastOccurrenceDate) {
      return lastOccurrenceDate.getTime()
    } else {
      // return 0 if all occurrences are exception meetings
      return 0
    }
  }

  static getOccurrenceDatesOfRecurrentMeet(
    session: any, /* session object of meeting board in original server response */
    afterDate: Date = moment().subtract(1, 'y').toDate(),
    beforeDate: Date = moment().add(1, 'y').toDate(),
  ): Array<Date> {
    const rruleSet = this.getRRuleSetOfRecurrentMeet(session)
    return rruleSet.between(afterDate, beforeDate)
  }
}
