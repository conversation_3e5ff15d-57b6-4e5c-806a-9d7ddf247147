import {Defines, MxBoard, MxISD<PERSON>, MxUser} from 'isdk'
import {CUserBoard} from '../../defines/CUserBoard'
import {UserFormatter} from '@controller/utils/user'
import {CGroupUser} from '@controller/defines/CGroupUser'
import {FunctionUtil, ObjectUtils} from '@controller/utils'
import {find1on1chat} from '@controller/user/src/util/find1on1chat'
import {User} from 'isdk/src/proto/generated/User'
import {UserTag} from 'isdk/src/proto/generated/UserTag'
import {transformError} from '@controller/common/decorators/transformError'
import {ClassLogger} from '@controller/common/decorators'
import {ArrayUtils} from '@commonUtils/array'
import {CBaseUser} from '@controller/defines/CBaseUser'
import {WorkspaceNotificationSettings} from '@controller/defines/WorkspaceNotificationSettings'
import {BoardUserStatus} from 'isdk/src/proto/generated/BoardUserStatus'
import {
  ActionNotificationSetting,
  AppStatistics,
  Board,
  BoardAccessType,
  BoardComment,
  BoardMemberNotificationSetting,
  BoardNotificationSetting,
  BoardType,
  BoardUserAOSM,
  ClientParam,
  ClientResponseCode,
  Group,
  GroupType,
  MxRegisterUserOption,
  MxSearchDataSourceFilter,
  RSVPStatus,
  SessionStatus,
  SocialType,
  UserActivityLog,
  UserBoard,
  UserIdentity,
  UserMentionMe,
  UserType
} from 'isdk/src/api/defines'
import {CUserRelation} from '@controller/defines/CUserRelation'
import {CContactUser} from '@controller/defines/CContactUser'
import {BoardFormatter} from '@controller/utils/board'
import {ObjectFeed} from 'isdk/src/proto/generated/ObjectFeed'
import {BoardRoutingStatus} from 'isdk/src/proto/generated/BoardRoutingStatus'
import {MxConsts} from '@commonUtils/consts'
import {CUserCap} from '@controller/defines/CUserCap'
import {GroupController} from '@controller/group/src/groupController'
import {CCustomError} from '@controller/common/CCustomError'
import {MxDuplicateBoardOption, MxPaginationUserBoards} from 'isdk/src/api/mxUser'
import {RSVPReply} from 'isdk/src/proto/generated/RSVPReply'
import {websdkController} from '@controller/websdk/WebsdkController'
import {MepMeet} from '@controller/websdk/models/MepMeet'

let userController: UserController

@ClassLogger()
export class UserController {
  private userBoardSubscriber: Defines.MxSubscription
  private userRelationSubscriber: Defines.MxSubscription
  private userNotificationSubscriber: Defines.MxSubscription
  private userBasicInfoSubscriber: Defines.MxSubscription
  private userResourceSubscriber: Defines.MxSubscription
  private userContactsSubscriber: Defines.MxSubscription
  private userPresenceSubscriber: Defines.MxSubscription
  private userActionItemsSubscriber: Defines.MxSubscription
  private readonly currentUser: MxUser
  private readonly userCap: CUserCap
  private meetResolveCallback: Function
  private suggestedContactResolveCallback: Function
  private userBoardCallback: Function
  private userBoardArchiveCallback: Function
  private groupCtrl: GroupController
  private searchUserBoardRequest = null
  private searchBoardRequest = null
  private searchUserBoardByMemberNameReq = null
  private searchUserBoardByBoardNameReq = null

  private userBoardMapper = {}
  private inboxBoardMapper = {}
  private pendingAcdBoard = null
  private recentClosedAcd = {}
  private sortedUserBoardList = []
  private sortedPendingMeetList = []
  private sortedActiveMeetList = [] // meetings whihc is in progress or scheduled_start_time met
  private sortedRemindMeetList = []
  private mentionPaginationSequence = 0
  private declinedMeetList=[]
  private expiredMeetList=[]
  private upcomingMeetList=[]

  private readMeetsST = null

  private userBoardCompareFn = (a, b) => {
    const weightDiff = a.weight - b.weight
    if (weightDiff === 0) {
      if (a.timestamp === b.timestamp) {
        return a.boardTimestamp - b.boardTimestamp > 0
      } else {
        return a.timestamp - b.timestamp > 0
      }
    } else {
      return weightDiff > 0
    }
  }

  private pendingMeetCompareFn = (a, b) => a.created_time - b.created_time > 0
  private activeMeetCompareFn = (a, b) => {
    const session_a = ObjectUtils.getByPath(a, 'sessions.0.session', {})
    let a_start_time = session_a.start_time || session_a.scheduled_start_time
    const session_b = ObjectUtils.getByPath(b, 'sessions.0.session', {})
    let b_start_time = session_b.start_time || session_b.scheduled_start_time
    if (a_start_time === b_start_time) {
      a_start_time = a.created_time
      b_start_time = b.created_time
    }
    return a_start_time - b_start_time > 0
  }

  private remindMeetCompareFn = (a, b) => {
    if (a.reminder_time === b.reminder_time) {
      return a.created_time - b.created_time > 0
    } else {
      return a.reminder_time - b.reminder_time > 0
    }
  }

  private formatUserPresence = (presences) => {
    let presenceObj = {}
    for (let presence of presences) {
      if (ObjectUtils.getByPath(presence, 'out_of_office.backup')) {
        let backup = presence.out_of_office.backup
        presence.out_of_office.backup = {
          ...backup,
          name: UserFormatter.getUserName(backup)
        }
      }
      presenceObj[presence.id] = {
        ...presence,
        id: presence.id,
        status: UserFormatter.getUserStatus(presence)
      }
    }
    return presenceObj
  }

  protected constructor() {
    if (!userController) {
      this.currentUser = MxISDK.getCurrentUser()
      this.userCap = this.currentUser.cap
      this.groupCtrl = GroupController.getInstance()
    }
  }

  static getInstance(): UserController {
    if (!userController) {
      userController = new UserController()
    }
    return userController
  }

  get basicInfo() {
    return UserFormatter.transformUserInfo(this.currentUser.basicInfo, this.userCap, {
      hasPassword: this.currentUser.hasPassword,
      timelineBottomFeedTimestamp: this.currentUser.timelineBottomFeedTime
    })
  }

  get group() {
    const groups = ObjectUtils.getByPath(this.currentUser, 'user.groups')
    if (groups) {
      return groups.find((group) => {
        return group.group && group.group.type !== Defines.GroupType.GROUP_TYPE_TEAM
      })
    } else {
      return {}
    }
  }

  get role() {
    if (this.group) {
      return this.group.role
    } else {
      return null
    }
  }

  get activeMeets() {
    return this.sortedActiveMeetList
  }

  getUserRelation(id: string): Defines.UserRelation {
    return this.currentUser.getUserRelationByUserId(id)
  }

  getUserContact(id: string): Defines.UserContact {
    return this.currentUser.getUserContactByUserId(id)
  }

  getSharedBinder(userIds: string[], teamId?: string, filterP2P?: boolean, filterLatest?: boolean): Promise<Array<CUserBoard>> {
    const users = []
    const teams = []
    let start_time = undefined
    let end_time = undefined
    if (userIds && userIds.length) {
      userIds.forEach((uid) => {
        users.push({
          id: uid
        })
      })
    }
    if (teamId) {
      teams.push(teamId)
    }
    if (filterLatest) {
      const now = Date.now();
      start_time = now-1;
      start_time = new Date(new Date(now).setMonth(new Date(now).getMonth() - 6)).valueOf(); 
      end_time = now.valueOf() + 24 * 60 * 60 * 1000;
    }  
    return this.currentUser.queryShareBoards(users, teams, filterP2P, start_time, end_time).then((user: Defines.User) => {
      if (user.boards) {
        const userBoards = user.boards.filter((ub) => !ub.is_deleted)
        return UserFormatter.transformUserBoards(userBoards)
      } else {
        return []
      }
    })
  }

  subscribeUserBasicInfo(resolve: Function): void {
    if (!this.userBasicInfoSubscriber) {
      resolve(
        UserFormatter.transformUserInfo(this.currentUser.basicInfo, this.userCap, {
          hasPassword: this.currentUser.hasPassword,
          timelineBottomFeedTimestamp: this.currentUser.timelineBottomFeedTime
        })
      )
      this.userBasicInfoSubscriber = this.currentUser.subscribeUserBasicInfo(
        (user: Defines.User) => {
          resolve(
            UserFormatter.transformUserInfo(user, this.userCap, {
              hasPassword: this.currentUser.hasPassword,
              timelineBottomFeedTimestamp: this.currentUser.timelineBottomFeedTime
            })
          )
        }
      )
    }
  }

  subscribeUserResources(resolve: Function): void {
    const userResources = this.currentUser?.user?.resources ?? []
    resolve(UserFormatter.transformUserResources(userResources))
    
    if (!this.userResourceSubscriber) {
      this.userResourceSubscriber = this.currentUser.subscribeUserResources(() => {
        const resources = this.currentUser?.user?.resources ?? []
        resolve(UserFormatter.transformUserResources(resources))
      })
    }
  }

  getAccessToken(): Promise<string> {
    return this.currentUser.getAccessToken().then((token: string) => {
      return token
    })
  }

  subscribeUserRelation(resolve: Function): void {
    if (!this.userRelationSubscriber) {
      const result = UserFormatter.transformUserRelations(this.currentUser.relations, false)
      this.processUserBoardsWhenContactsChange(result)
      resolve(result)
      this.userRelationSubscriber = this.currentUser.subscribeUserRelations(
        (relations: Array<Defines.UserRelation>) => {
          const result = UserFormatter.transformUserRelations(relations, false)
          this.processUserBoardsWhenContactsChange(result, true)
          resolve(result)
        }
      )
    }
  }

  async processUserNotifications(
    notifications: Array<Defines.UserNotification>,
    ignoreDeleted: boolean
  ) {
    const remoteUserBoardIds = []
    let remoteUserBoards = []

    notifications.forEach((notification) => {
      const object = ObjectUtils.getByPath(notification, 'payload.request.object', {})
      if (object.board) {
        const boardId = object.board.id
        if (
          !this.currentUser.getUserBoardByBoardId(boardId) &&
          !remoteUserBoardIds.includes(boardId)
        ) {
          remoteUserBoardIds.push(boardId)
        }
      }
    })

    if (remoteUserBoardIds.length) {
      try {
        const { boards } = await this.currentUser.readUserBoards(remoteUserBoardIds)
        remoteUserBoards = boards
      } catch (e) {
        remoteUserBoards = []
      }
    }

    return UserFormatter.transformUserNotifications(notifications, ignoreDeleted, remoteUserBoards)
  }

  subscribeUserNotifications(resolve: Function): void {
    if (!this.userNotificationSubscriber) {
      this.processUserNotifications(this.currentUser.notifications, true).then((result) => {
        resolve(result)
      })
      this.userNotificationSubscriber = this.currentUser.subscribeUserNotification2(
        (notifications: Array<Defines.UserNotification>) => {
          this.processUserNotifications(notifications, false).then((result) => {
            resolve(result)
          })
        }
      )
    }
  }

  dismissUserNotifications({ dismissAll = false, sequences = [] }) {
    if (dismissAll) {
      return this.currentUser.removeNotifications(null, Date.now())
    } else {
      return this.currentUser.removeNotifications(sequences)
    }
  }

  updateUserNotificationsAccessTime() {
    return this.currentUser.updateProfile({ notification_accessed_time: Date.now() })
  }

  private getWelcomeMessage(message?: string) {
    // MV-5751
    // If org enable welcome message
    // For RM: Use invitation message in user tag
    // For Client: Use default welcome message defined in local lang file
    let welcomeMessage = message || ''
    const groupBasicInfo = MxISDK.getCurrentOrg().basicInfo
    if (groupBasicInfo) {
      const tags = groupBasicInfo.tags
      let enableWelcomeMessage = true
      for (let tag of tags) {
        if (
          !tag.is_deleted &&
          tag.name === 'Enable_Welcome_Message' &&
          (tag.string_value === '0' || tag.string_value === 'false')
        ) {
          enableWelcomeMessage = false
        }
      }

      if (!enableWelcomeMessage) {
        welcomeMessage = ''
      } else if (!welcomeMessage) {
        const currentUser = MxISDK.getCurrentUser()
        const userType = currentUser.basicInfo.type
        if (userType === UserType.USER_TYPE_NORMAL) {
          const userTags = currentUser.tags
          if (userTags['invitation_message']) {
            welcomeMessage = userTags['invitation_message']
          }
        }
      }
    }

    return welcomeMessage
  }

  @transformError()
  confirmRelation(
    relationSequence: number,
    noFeed: boolean = false,
    message?: string,
    resendEmail?: boolean,
    resendSms?: boolean
  ): Promise<Object> {
    const welcomeMessage = this.getWelcomeMessage(message)
    return this.currentUser
      .confirmRelation(relationSequence, welcomeMessage, resendEmail, resendSms, noFeed)
      .then((user: Defines.User) => {
        return {
          binderId: ObjectUtils.getByPath(user, 'boards.0.board.id'),
          userId: ObjectUtils.getByPath(user, 'relations.0.user.id')
        }
      })
  }

  @transformError()
  resendInvitationByRM(
    relationSequence: number,
    viaEmail: boolean = true,
    viaPhoneNumber: boolean,
    noRelationBoard: boolean = false
  ): Promise<object> {
    return this.currentUser
      .confirmRelation(relationSequence, '', viaEmail, viaPhoneNumber, true, noRelationBoard)
      .then((user: Defines.User) => {
        const responseBoard = ObjectUtils.getByPath(user, 'boards.0.board')
        if (responseBoard) {
          const binderId = responseBoard.id
          const binderUsers = responseBoard.users
          const clientUser = binderUsers.find((binderUser) => binderUser.invited_time)
          const result = {
            binderId,
            invitedTime: 0
          }
          if (clientUser) {
            result.invitedTime = clientUser.invited_time
          }
          return result
        } else {
          return {}
        }
      })
  }

  @transformError()
  createRelation(user: User, noFeed: boolean = false, message?: string): Promise<object> {
    return this.currentUser.createRelation(user, noFeed).then((user: Defines.User) => {
      const boardId = ObjectUtils.getByPath(user, 'boards.0.board.id')
      let resp: any = {
        relation: {
          sequence: ObjectUtils.getByPath(user, 'relations.0.sequence')
        }
      }
      return resp
    })
  }

  @transformError()
  createSocialConnection(type: SocialType, user: User, isResend?: boolean) {
    return this.currentUser
      .createSocialConnection(type, user, isResend)
      .then((user: Defines.User) => {
        // return ObjectUtils.getByPath(user, 'boards.0.board.id')
        return user
      })
  }

  @transformError()
  reactivateSocialConnection(
    binderId: string,
    social: {
      isWeChat?: boolean
      isWhatsapp?: boolean
      isLine?: boolean
    },
    reason?:string
  ) {
    let socialType
    if (social.isWeChat) {
      socialType = SocialType.SOCIAL_TYPE_WECHAT
    } else if (social.isWhatsapp) {
      socialType = SocialType.SOCIAL_TYPE_WHATSAPP
    } else if (social.isLine) {
      socialType = SocialType.SOCIAL_TYPE_LINE
    } else {
      socialType = SocialType.SOCIAL_TYPE_INVALID
    }
    return this.currentUser.reactivateSocialConnection(socialType, binderId,reason)
  }

  processUserBoardsWhenContactsChange(users: CBaseUser[], isFromRelation: boolean = false) {
    if (this.userBoardCallback) {
      let userIdArr = users.map((u) => u.id).join('|')
      let changedBoards = []
      let userBoards = this.currentUser.boards
      if (userBoards && userBoards.length) {
        userBoards.forEach((userBoard) => {
          if (ObjectUtils.getByPath(userBoard, 'board.id')) {
            const index = userBoard.board.users.findIndex(
              (user) => userIdArr.indexOf(ObjectUtils.getByPath(user, 'user.id')) >= 0
            )
            if (index >= 0) {
              changedBoards.push(userBoard)
            }
          }
        })
        this.userBoardCallback(
          this.processUserBoardChange(
            ArrayUtils.mergeArray([], changedBoards, { clone: true }),
            true,
            isFromRelation
          )
        )
      }
    }
  }

  // load all meetings in coming 4 months
  reloadComingMeets(force) {
    return new Promise((resolve, reject) => {
      const now = Date.now()
      const start_time = now - 24 * 3600 * 1000
      const currentDate = new Date(start_time)
      const end_time = currentDate.setMonth(currentDate.getMonth() + 4).valueOf()
      if (!force && this.readMeetsST && start_time - this.readMeetsST < 2 * 3600 * 1000) {
        resolve({
          remindMeets: [...this.sortedRemindMeetList],
          activeMeets: [...this.sortedActiveMeetList],
          pendingMeets: [...this.sortedPendingMeetList],
          declinedMeets: [...this.declinedMeetList],
          expiredMeets: [...this.expiredMeetList],
          upcomingMeets: [...this.upcomingMeetList]
        })
      } else {
        // Note: origin user-board array for freemium org home->calendar meetings count display, haven't been transfromed as remind/active/pending meetings
        const upcomingMeets = []
        const expiredMeets=[]
        this.readMeetsST = start_time
        this.currentUser
          .queryMeets(this.readMeetsST, end_time)
          .then((user: Defines.User) => {
            if (user && user.boards) {
              const uboards = []
              const meetMapper = {}
              for (let i = 0; i < user.boards.length; i++) {
                const uboard = user.boards[i]
                const sequence = uboard.sequence || uboard.original_sequence
                const { scheduled_end_time, session_status } = ObjectUtils.getByPath(
                  uboard,
                  'board.sessions.0.session'
                )
                let ignoreInTimeline = false
                if (session_status === SessionStatus.SESSION_SCHEDULED) {
                  ignoreInTimeline = now > scheduled_end_time
                  if (scheduled_end_time > now && upcomingMeets.length < 5) {
                    // Note: only maintain top 5 upcoming meetings for Home/Calendar section
                    upcomingMeets.push(uboard)
                  }else if(scheduled_end_time < now && now< scheduled_end_time + 8 * 3600 * 1000){
                    expiredMeets.push(uboard)
                  }
                } else if (session_status !== SessionStatus.SESSION_STARTED) {
                  ignoreInTimeline = true
                }
                if (!ignoreInTimeline && !meetMapper[sequence]) {
                  if (uboard.original_sequence) {
                    // Only take the first meeting instance of recurring meetings
                    uboard.sequence = sequence
                  }
                  uboards.push(uboard)
                  meetMapper[sequence] = true
                }
              }
              // wipe existing meets list
              this.wipeTimelineMeets()
              this.processUserBoardChange(ArrayUtils.mergeArray([], uboards, { clone: true }), true)
              this.expiredMeetList=expiredMeets
            }
            this.upcomingMeetList = upcomingMeets
            resolve({
              remindMeets: [...this.sortedRemindMeetList],
              activeMeets: [...this.sortedActiveMeetList],
              pendingMeets: [...this.sortedPendingMeetList],
              declinedMeets: this.declinedMeetList,
              expiredMeets,
              upcomingMeets
            })
          })
          .catch(reject)
      }
    })
  }

  private wipeTimelineMeets() {
    if (this.sortedRemindMeetList && this.sortedRemindMeetList.length) {
      let i = this.sortedRemindMeetList.length
      while (i--) {
        const remindMeet = this.sortedRemindMeetList[i]
        this.modifyCategoryList(remindMeet, 'sortedRemindMeetList', this.remindMeetCompareFn, true)
      }
    }
    if (this.sortedActiveMeetList && this.sortedActiveMeetList.length) {
      let i = this.sortedActiveMeetList.length
      while (i--) {
        const activeMeet = this.sortedActiveMeetList[i]
        this.modifyCategoryList(activeMeet, 'sortedActiveMeetList', this.activeMeetCompareFn, true)
      }
    }
    if (this.sortedPendingMeetList && this.sortedPendingMeetList.length) {
      let i = this.sortedPendingMeetList.length
      while (i--) {
        const pendingMeet = this.sortedPendingMeetList[i]
        this.modifyCategoryList(
          pendingMeet,
          'sortedPendingMeetList',
          this.pendingMeetCompareFn,
          true
        )
      }
    }
  }

  syncTimelineMeets(meets) {
    const {
      startedRemindMeets,
      expiredActiveMeets,
      expiredPendingMeets,
      dismissedRemindMeets
    } = meets
    if (startedRemindMeets && startedRemindMeets.length) {
      startedRemindMeets.forEach((meet) => {
        // 1. remove meets from remindMeets
        this.modifyCategoryList(meet, 'sortedRemindMeetList', this.remindMeetCompareFn, true)
        // 2. add meets to activeMeets
        this.modifyCategoryList(meet, 'sortedActiveMeetList', this.activeMeetCompareFn)
      })
    }
    if (expiredActiveMeets && expiredActiveMeets.length) {
      expiredActiveMeets.forEach((meet) => {
        this.modifyCategoryList(meet, 'sortedActiveMeetList', this.activeMeetCompareFn, true)
      })
    }
    if (expiredPendingMeets && expiredPendingMeets.length) {
      expiredPendingMeets.forEach((meet) => {
        this.modifyCategoryList(meet, 'sortedPendingMeetList', this.pendingMeetCompareFn, true)
      })
    }
    if (dismissedRemindMeets && dismissedRemindMeets.length) {
      dismissedRemindMeets.forEach((meet) => {
        this.modifyCategoryList(meet, 'sortedRemindMeetList', this.remindMeetCompareFn, true)
      })
    }
    return {
      remindMeets: [...this.sortedRemindMeetList],
      activeMeets: [...this.sortedActiveMeetList],
      pendingMeets: [...this.sortedPendingMeetList]
    }
  }

  subscribeUserBoards(resolve: Function): void {
    this.userBoardCallback = resolve
    if (!this.userBoardSubscriber) {
      if (this.currentUser && this.currentUser.boards) {
        this.userBoardCallback(
          this.processUserBoardChange(
            ArrayUtils.mergeArray([], this.currentUser.boards, { clone: true }),
            true
          )
        )
        if (this.meetResolveCallback) {
          this.meetResolveCallback(
            ArrayUtils.mergeArray([], this.currentUser.boards, { clone: true })
          )
        }
      }
      this.userBoardSubscriber = this.currentUser.subscribeUserBoards(
        (userBoards: Array<Defines.UserBoard>) => {
          const filteredUserBoards = userBoards.filter((ub) => {
            if (ub.board) {
              const lastFeedTimeStamp = ObjectUtils.getByPath(ub, 'board.feeds.0.timestamp')
              if (lastFeedTimeStamp) {
                return lastFeedTimeStamp >= this.currentUser.timelineBottomFeedTime
              } else {
                return true
              }
            }
            return true
          })
          this.userBoardCallback(
            this.processUserBoardChange(
              ArrayUtils.mergeArray([], filteredUserBoards, { clone: true })
            )
          )
          if (this.meetResolveCallback) {
            this.meetResolveCallback(ArrayUtils.mergeArray([], userBoards, { clone: true }))
          }
          if (this.userBoardArchiveCallback) {
            this.userBoardArchiveCallback(this.processUserBoardChangeForArchive(userBoards))
          }
        }
      )
    }
  }

  processUserBoardChangeForArchive(userBoards: UserBoard[]) {
    const updatedWorkspaces: CUserBoard[] = []
    for (const userBoard of userBoards) {
      const { islive, is_acd, is_service_request, is_content_library } = userBoard?.board || {}
      if (islive || is_acd || is_service_request || is_content_library) {
        continue
      } else {
        updatedWorkspaces.push(UserFormatter.transformUserBoard(userBoard))
      }
    }
    return updatedWorkspaces
  }

  get mentionmes() {
    return this.currentUser.mentionmes
  }

  subscribeMentionMes(callback) {
    this.currentUser.subscribeUserMentionMes(callback)
  }

  processUserBoardChange(
    ubs: Array<UserBoard>,
    isInit: boolean = false,
    isFromRelation: boolean = false
  ) {
    const currentUser = MxISDK.getCurrentUser()
    const isUserBoardMapperEmpty = Object.keys(this.userBoardMapper).length === 0
    const currIsInternal = currentUser.basicInfo.type === UserType.USER_TYPE_NORMAL
    const inboxBinderList = []
    const declinedMeetsList = []
    let isPendingAcdUB = false
    let forceReloadMeets = false

    for (let ub of ubs) {
      // if board is acd, and the channel already removed, then ignore it
      if (!currIsInternal && ub && ub.board && ub.board.is_acd) {
        const channelId = ub.board.routing_channel
        const orgActiveChannels = this.groupCtrl.getBasicInfo.acdChannels
        if (
          !orgActiveChannels ||
          !orgActiveChannels.find((channel) => channel.channel_id === channelId)
        ) {
          continue
        }
      }

      const isScheduleFlowBoard =
        ObjectUtils.getByPath(ub, 'board.type') === BoardType.BOARD_TYPE_SCHEDULE
      if (isScheduleFlowBoard) {
        continue
      }

      let sequence = ub.sequence
      const existingUBObj = this.userBoardMapper[sequence]
      let existingUbRevision = 0
      if (existingUBObj) {
        existingUbRevision = this[existingUBObj.category][existingUBObj.index]?.revision || 0
      }
      const isMeet = ObjectUtils.getByPath(ub, 'board.islive')
      if (ub.revision <= existingUbRevision && !isFromRelation && !isMeet) {
        continue
      }
      const transformedUb = UserFormatter.transformUserBoard(ub, isInit)
      if (Object.keys(transformedUb).length > 0) {
        const now = Date.now()
        const session = ObjectUtils.getByPath(transformedUb, 'sessions.0.session', {})
        let isDismissed = false
        let isDeclined = false
        let isAccepted = false
        let hostJoined = false
        let reminderReached = false
        let needAction = false
        let isInMeeting = false
        if (transformedUb.islive) {
          if (
            !transformedUb.myRSVPStatus &&
            transformedUb.status === BoardUserStatus.BOARD_MEMBER
          ) {
            // lagecy scheduled meetings, already accepted
            isAccepted = true
          }
          if (transformedUb.myRSVPStatus) {
            if (transformedUb.myRSVPStatus === RSVPStatus.RSVP_DECLINED) {
              isDeclined = true
              declinedMeetsList.push(transformedUb)
            } else if (transformedUb.myRSVPStatus === RSVPStatus.RSVP_ACCEPTED) {
              isAccepted = true
            } else if (transformedUb.myRSVPStatus === RSVPStatus.RSVP_NEEDS_ACTION) {
              needAction = true
            }
          } else if (!isAccepted) {
            // Once meet was accepted, declined, or dismissed (dismiss in callmodal with 'RSVP_TENTATIVE')
            needAction = true
          }
          if (transformedUb.dismissed_time) {
            // MVB-18339 For active recurring meetings, dismiss only apply to THIS meeting
            const { rrule, start_time, scheduled_start_time } = session
            if (rrule) {
              const compare_time =
                start_time && start_time < scheduled_start_time ? start_time : scheduled_start_time
              if (transformedUb.dismissed_time > compare_time) {
                isDismissed = true
              }
            } else {
              isDismissed = true
            }
          }
          reminderReached =
            isAccepted && transformedUb.reminder_time > -1 && transformedUb.reminder_time < now
          hostJoined = session.user_roster && session.user_roster.length > 0
          if (session.session_status === SessionStatus.SESSION_STARTED) {
            const inMeetings = localStorage.getItem('isInMeeting')
            if (inMeetings) {
              const inMeetingObj = JSON.parse(inMeetings)[session.session_key]
              // this meeting is started/joined in another tab of current browser
              isInMeeting = inMeetingObj && now - inMeetingObj.timestamp < 3 * 1000
            }
          }
        }

        if (
          transformedUb.islive ||
          ['sortedPendingMeetList', 'sortedActiveMeetList', 'sortedRemindMeetList'].includes(
            existingUBObj?.category
          )
        ) {
          // reload upcoming meets for Home page
          forceReloadMeets = true
        }

        if (currentUser.basicInfo.type !== UserType.USER_TYPE_LOCAL) {
          const routingStatus = transformedUb.routing_status
          if (transformedUb.is_acd) {
            if (
              [
                BoardRoutingStatus.ROUTING_STATUS_OPEN,
                BoardRoutingStatus.ROUTING_STATUS_OPEN_NO_TIMEOUT,
                BoardRoutingStatus.ROUTING_STATUS_IN_PROGRESS
              ].indexOf(routingStatus) > -1
            ) {
              if (
                routingStatus === BoardRoutingStatus.ROUTING_STATUS_OPEN ||
                routingStatus === BoardRoutingStatus.ROUTING_STATUS_OPEN_NO_TIMEOUT
              ) {
                if (!transformedUb.is_deleted) {
                  isPendingAcdUB = true
                  this.pendingAcdBoard = transformedUb
                }
              } else if (this.pendingAcdBoard && this.pendingAcdBoard.id === transformedUb.id) {
                this.pendingAcdBoard = null
              }
            }
          }
          if (
            this.pendingAcdBoard &&
            transformedUb.is_deleted &&
            transformedUb.sequence === this.pendingAcdBoard.sequence
          ) {
            this.pendingAcdBoard = null
          }
        }
        if (existingUBObj) {
          if (existingUBObj.category === 'sortedUserBoardList') {
            const existingUb = this.sortedUserBoardList[existingUBObj.index]
            if (transformedUb.is_deleted) {
              if (existingUb && existingUb.is_acd) {
                // TODO should not show toast message for guests (only for primary agent)
                const boardUsers = existingUb.boardUsers
                const currentUserId = currentUser.basicInfo.id
                // ACD binder is closed, show toast message
                if (
                  boardUsers.findIndex(
                    (user) => user.isPrimaryAgent && user.id === currentUserId
                  ) >= 0
                ) {
                  let customerName
                  let acd = boardUsers.filter((user) => user.isOwner)
                  if (acd && acd.length) {
                    customerName = acd[0].name || acd[0].displayName
                  }
                  this.recentClosedAcd = {
                    name: customerName,
                    id: existingUb.id
                  }
                }
              }
            }
            this.modifyCategoryList(
              transformedUb,
              'sortedUserBoardList',
              this.userBoardCompareFn,
              transformedUb.is_deleted,
              isUserBoardMapperEmpty
            )
          } else if (existingUBObj.category === 'sortedPendingMeetList') {
            if (transformedUb.is_deleted) {
              // meet was deleted
              this.modifyCategoryList(
                transformedUb,
                'sortedPendingMeetList',
                this.pendingMeetCompareFn,
                true,
                isUserBoardMapperEmpty
              )
            } else {
              if (session.session_status === SessionStatus.SESSION_SCHEDULED) {
                if (!needAction) {
                  // meet was taken action by attendee
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedPendingMeetList',
                    this.pendingMeetCompareFn,
                    true,
                    isUserBoardMapperEmpty
                  )

                  if (isAccepted) {
                    if (session.scheduled_start_time > now) {
                      this.modifyCategoryList(
                        transformedUb,
                        'sortedRemindMeetList',
                        this.remindMeetCompareFn,
                        false,
                        isUserBoardMapperEmpty
                      )
                    } else {
                      if (session.rrule) {
                        // recurring meeting which was scheduled earlier than now
                        forceReloadMeets = true
                      } else {
                        this.modifyCategoryList(
                          transformedUb,
                          'sortedActiveMeetList',
                          this.activeMeetCompareFn,
                          false,
                          isUserBoardMapperEmpty
                        )
                      }
                    }
                  }
                } else {
                  // Note: recurring meetings read from Server through "queryMeets" has no sequence
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedPendingMeetList',
                    this.pendingMeetCompareFn,
                    false,
                    isUserBoardMapperEmpty
                  )
                }
              } else {
                if (session.session_status === SessionStatus.SESSION_STARTED) {
                  if (hostJoined || reminderReached || (!hostJoined && isInMeeting)) {
                    // meet was started by host, or host joined, or JBH by me
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedPendingMeetList',
                      this.pendingMeetCompareFn,
                      true,
                      isUserBoardMapperEmpty
                    )
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedActiveMeetList',
                      this.activeMeetCompareFn,
                      false,
                      isUserBoardMapperEmpty
                    )
                  } else {
                    // meet was started by JBH, then click accept/decline button
                    if (isDeclined || isAccepted) {
                      this.modifyCategoryList(
                        transformedUb,
                        'sortedPendingMeetList',
                        this.pendingMeetCompareFn,
                        true,
                        isUserBoardMapperEmpty
                      )
                      if (isAccepted && transformedUb.reminder_time > -1) {
                        this.modifyCategoryList(
                          transformedUb,
                          'sortedRemindMeetList',
                          this.remindMeetCompareFn,
                          false,
                          isUserBoardMapperEmpty
                        )
                      }
                    }
                  }
                }
              }
            }
          } else if (existingUBObj.category === 'sortedActiveMeetList') {
            if (
              transformedUb.is_deleted ||
              session.session_status === SessionStatus.SESSION_ENDED ||
              isDismissed ||
              isDeclined
            ) {
              // meet deleted, ended, dimissed or declined
              this.modifyCategoryList(
                transformedUb,
                'sortedActiveMeetList',
                this.activeMeetCompareFn,
                true,
                isUserBoardMapperEmpty
              )
            } else {
              if (session.session_status === SessionStatus.SESSION_SCHEDULED) {
                // meet not started, but scheduled_start_time reached
                if (needAction) {
                  // remove from active, add it to pending
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedActiveMeetList',
                    this.activeMeetCompareFn,
                    true,
                    isUserBoardMapperEmpty
                  )
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedPendingMeetList',
                    this.pendingMeetCompareFn,
                    false,
                    isUserBoardMapperEmpty
                  )
                } else {
                  if (session.scheduled_start_time > now) {
                    // meet not started, scheduled_start_time updated to later
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedActiveMeetList',
                      this.activeMeetCompareFn,
                      true,
                      isUserBoardMapperEmpty
                    )
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedRemindMeetList',
                      this.remindMeetCompareFn,
                      false,
                      isUserBoardMapperEmpty
                    )
                  } else if (transformedUb.reminder_time === -1) {
                    // meet not started, and reminder set to no-reminder
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedActiveMeetList',
                      this.activeMeetCompareFn,
                      true,
                      isUserBoardMapperEmpty
                    )
                  } else if (session.rrule) {
                    // is recurring meet, has updated
                    forceReloadMeets = true
                  }
                }
              } else {
                if (
                  session.session_status === SessionStatus.SESSION_STARTED &&
                  !(hostJoined || reminderReached)
                ) {
                  // meet started by JBH(host not joined), and reminder time not exits or not reached
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedActiveMeetList',
                    this.activeMeetCompareFn,
                    true,
                    isUserBoardMapperEmpty
                  )
                } else {
                  // meet status not changed, but detail info may changed
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedActiveMeetList',
                    this.activeMeetCompareFn,
                    false,
                    isUserBoardMapperEmpty
                  )
                }
              }
            }
          } else if (existingUBObj.category === 'sortedRemindMeetList') {
            if (session.session_status === SessionStatus.SESSION_SCHEDULED && needAction) {
              // remove from remind, add it to pending
              this.modifyCategoryList(
                transformedUb,
                'sortedRemindMeetList',
                this.remindMeetCompareFn,
                true,
                isUserBoardMapperEmpty
              )
              this.modifyCategoryList(
                transformedUb,
                'sortedPendingMeetList',
                this.pendingMeetCompareFn,
                false,
                isUserBoardMapperEmpty
              )
            } else {
              if (transformedUb.is_deleted || transformedUb.reminder_time === -1 || isDeclined) {
                // dismiss current remind meet
                this.modifyCategoryList(
                  transformedUb,
                  'sortedRemindMeetList',
                  this.remindMeetCompareFn,
                  true,
                  isUserBoardMapperEmpty
                )
              } else {
                if (
                  session.session_status === SessionStatus.SESSION_STARTED &&
                  (hostJoined || reminderReached || (!hostJoined && isInMeeting))
                ) {
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedRemindMeetList',
                    this.remindMeetCompareFn,
                    true,
                    isUserBoardMapperEmpty
                  )
                  this.modifyCategoryList(
                    transformedUb,
                    'sortedActiveMeetList',
                    this.activeMeetCompareFn,
                    false,
                    isUserBoardMapperEmpty
                  )
                } else {
                  const { scheduled_start_time, rrule } = session
                  if (rrule && scheduled_start_time < now) {
                    // recurring meeting which have not started before
                    // the data from subscription always bring with the past scheduled_start_time and scheduled_end_time
                    forceReloadMeets = true
                  } else {
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedRemindMeetList',
                      this.remindMeetCompareFn,
                      false,
                      isUserBoardMapperEmpty
                    )
                  }
                }
              }
            }
          }
        } else {
          if (!transformedUb.is_deleted) {
            if (transformedUb.islive) {
              if (!isDismissed) {
                if (session.session_status === SessionStatus.SESSION_STARTED) {
                  if (
                    !isDeclined &&
                    (hostJoined || reminderReached || (!hostJoined && isInMeeting))
                  ) {
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedActiveMeetList',
                      this.activeMeetCompareFn,
                      false,
                      isUserBoardMapperEmpty
                    )
                  } else if (needAction && now < session.scheduled_end_time) {
                    // meeting started by JBH, has taken no action, and scheduled_end_time not reached
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedPendingMeetList',
                      this.pendingMeetCompareFn,
                      false,
                      isUserBoardMapperEmpty
                    )
                  } else if (
                    isAccepted &&
                    transformedUb.reminder_time > -1 &&
                    session.scheduled_start_time > now
                  ) {
                    // reminder state - JBH by someone but host not joined and reminder time not reached
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedRemindMeetList',
                      this.remindMeetCompareFn,
                      false,
                      isUserBoardMapperEmpty
                    )
                  }
                } else if (session.session_status === SessionStatus.SESSION_SCHEDULED) {
                  if (needAction && session.scheduled_end_time > now) {
                    this.modifyCategoryList(
                      transformedUb,
                      'sortedPendingMeetList',
                      this.pendingMeetCompareFn,
                      false,
                      isUserBoardMapperEmpty
                    )
                  } else if (transformedUb.reminder_time > -1 && isAccepted) {
                    if (session.scheduled_start_time > now) {
                      this.modifyCategoryList(
                        transformedUb,
                        'sortedRemindMeetList',
                        this.remindMeetCompareFn,
                        false,
                        isUserBoardMapperEmpty
                      )
                    } else if (
                      now > session.scheduled_start_time &&
                      now < session.scheduled_end_time
                    ) {
                      this.modifyCategoryList(
                        transformedUb,
                        'sortedActiveMeetList',
                        this.activeMeetCompareFn,
                        false,
                        isUserBoardMapperEmpty
                      )
                    } else if (session.rrule) {
                      forceReloadMeets = true
                    }
                  }
                }
              }
            } else if (!isPendingAcdUB) {
              this.modifyCategoryList(
                transformedUb,
                'sortedUserBoardList',
                this.userBoardCompareFn,
                false,
                isUserBoardMapperEmpty
              )
            }
          }
        }
      }
    }

    if (isUserBoardMapperEmpty) {
      this.buildUserBoardMapper('sortedUserBoardList', 0, this.sortedUserBoardList.length - 1)
      this.buildUserBoardMapper('sortedPendingMeetList', 0, this.sortedPendingMeetList.length - 1)
      this.buildUserBoardMapper('sortedActiveMeetList', 0, this.sortedActiveMeetList.length - 1)
      this.buildUserBoardMapper('sortedRemindMeetList', 0, this.sortedRemindMeetList.length - 1)
    }

    this.checkDuplicateUserBoards('sortedUserBoardList')

    const result: any = {}
    result.userBoards = [...this.sortedUserBoardList]
    result.pendingMeets = [...this.sortedPendingMeetList]

    if (Object.keys(this.inboxBoardMapper).length > 0) {
      for (let key in this.inboxBoardMapper) {
        const index = this.inboxBoardMapper[key]
        const ub = this.sortedUserBoardList[index]
        inboxBinderList.push({
          id: ub.id,
          index: index
        })
      }
    }
    this.declinedMeetList=declinedMeetsList
    result.inboxBinderList = [...inboxBinderList]
    result.activeMeets = [...this.sortedActiveMeetList]
    result.remindMeets = [...this.sortedRemindMeetList]
    result.pendingAcdBoard = this.pendingAcdBoard
    result.recentClosedAcd = this.recentClosedAcd
    result.forceReloadMeets = forceReloadMeets

    return result
  }

  modifyCategoryList(
    ub: CUserBoard,
    category: string,
    compareFn: Function,
    isDeleted?: boolean,
    isInit?: boolean
  ) {
    let needReflow = false
    let index = -1
    let sequence = ub.sequence
    const existingUBObj = this.userBoardMapper[sequence]
    if (existingUBObj && existingUBObj.category === category) {
      index = existingUBObj.index
    }
    if (isDeleted) {
      needReflow = true
      this[category].splice(index, 1)
      delete this.userBoardMapper[sequence]
      if (category === 'sortedUserBoardList' && this.inboxBoardMapper[sequence] > -1) {
        delete this.inboxBoardMapper[sequence]
      }
      if (!isInit) {
        this.buildUserBoardMapper(category, index, this[category].length - 1)
      }
    } else {
      if (index >= 0) {
        if (category === 'sortedUserBoardList') {
          this[category].splice(index, 1)
        } else {
          this[category].splice(index, 1, ub)
          return false
        }
      }
      let insertBeforeIndex = -1
      const sortedList = this[category]
      for (let i = 0; i < sortedList.length; i++) {
        const existingUb = sortedList[i]
        if (compareFn(ub, existingUb)) {
          insertBeforeIndex = i
          break
        }
      }
      if (insertBeforeIndex < 0) {
        needReflow = true
        this[category].push(ub)
        insertBeforeIndex = this[category].length - 1
        this.userBoardMapper[sequence] = {
          category: category,
          index: insertBeforeIndex
        }
      } else {
        needReflow = index !== insertBeforeIndex
        this[category] = [
          ...sortedList.slice(0, insertBeforeIndex),
          ub,
          ...sortedList.slice(insertBeforeIndex)
        ]
        if (needReflow && !isInit) {
          if (index >= 0) {
            this.buildUserBoardMapper(
              category,
              Math.min(index, insertBeforeIndex),
              Math.max(index, insertBeforeIndex)
            )
          } else {
            this.buildUserBoardMapper(category, insertBeforeIndex, this[category].length - 1)
          }
        }
      }
      if (ub.is_inbox) {
        this.inboxBoardMapper[sequence] = insertBeforeIndex
      }
    }
    return needReflow
  }

  buildUserBoardMapper(category: string, start: number, end: number) {
    if (start <= end) {
      for (let i = start; i <= end; i++) {
        const id = this[category][i].id
        const sequence = this[category][i].sequence
        this.userBoardMapper[sequence] = {
          category: category,
          index: i,
          id
        }
        if (category === 'sortedUserBoardList' && this.inboxBoardMapper.hasOwnProperty(sequence)) {
          this.inboxBoardMapper[sequence] = i
        }
      }
    }
  }

  checkDuplicateUserBoards(category: string) {
    const uniqueUserBoards = []
    const checkedUserBoardSequenceMapper = {}
    for (let ub of this[category]) {
      if (!checkedUserBoardSequenceMapper[ub.sequence]) {
        uniqueUserBoards.push(ub)
        checkedUserBoardSequenceMapper[ub.sequence] = true
      }
    }

    if (uniqueUserBoards.length < this[category].length) {
      const missedOrigUserBoards = []
      Object.keys(this.userBoardMapper).forEach((sequence) => {
        const simpleUserBoardInfo = this.userBoardMapper[sequence]
        if (simpleUserBoardInfo.category === category) {
          // safe to use getUserBoardByBoardId here since the userBoardMapper is built from the same userBoardList
          const localUserBoard = MxISDK.getCurrentUser().getUserBoardByBoardId(
            simpleUserBoardInfo.id
          )
          if (!checkedUserBoardSequenceMapper[sequence] && localUserBoard) {
            missedOrigUserBoards.push(localUserBoard)
          }
          delete this.userBoardMapper[sequence]
        }
      })

      this[category] = uniqueUserBoards
      this.buildUserBoardMapper(category, 0, this[category].length - 1)
      const missedTransformedUserBoards = UserFormatter.transformUserBoards(missedOrigUserBoards)
      missedTransformedUserBoards.forEach((ub) => {
        this.modifyCategoryList(ub, category, this.userBoardCompareFn)
      })
      this.buildUserBoardMapper(category, 0, this[category].length - 1)
    }
  }

  getLocalUserBoard(sequence: number) {
    const existingUBObj = this.userBoardMapper[sequence]
    if (existingUBObj) {
      return this[existingUBObj.category][existingUBObj.index]
    } else {
      return null
    }
  }

  @transformError()
  async pageQueryTimelineBoards(pageSize: number = 100) {
    const { user, nextPageStart } = await this.currentUser.readTimelineUserBoards(
      this.currentUser.timelineBottomFeedTime,
      pageSize
    )
    const pageUserBoards = user?.boards ?? []
    const validUserBoards = pageUserBoards.filter(
      (ub) =>
        ub.board &&
        !ub.board.islive &&
        !ub.board.is_service_request &&
        !ub.board.is_channel_subscription &&
        !ub.board.is_app_subscription
    )
    const { userBoards } = this.processUserBoardChange(validUserBoards, false)
    return {
      userBoards,
      hasNextPage: nextPageStart !== 0
    }
  }

  searchUserBoard(searchKey: string, boardId: string) {
    return this.currentUser.searchBoard(searchKey, 0, 21, boardId)
  }

  async findConversationWithUser(id: string): Promise<CUserBoard> {
    const currentUser = this.currentUser
    const userBoards = currentUser.boards

    const userBoard = userBoards.find((ub) => {
      if (!ub.board) {
        return false
      }
      const boardInfo = ub.board
      if (boardInfo.is_relation || boardInfo.isconversation) {
        let validMemberCount = 0
        let myBoardUser
        let targetBoardUser

        for (let boardUser of boardInfo.users) {
          if (!boardUser.is_deleted) {
            validMemberCount++
          }

          if (boardUser.is_deleted) {
            continue
          }

          if (!myBoardUser && boardUser.user.id === currentUser.id) {
            myBoardUser = boardUser
          }

          if (!targetBoardUser && boardUser.user.id === id) {
            targetBoardUser = boardUser
          }
        }

        // If target user is not found, then no direct conversation
        if (!targetBoardUser) {
          return false
        }

        // If target user found and member count is 2, then it must be a direct conversation
        if (validMemberCount === 2) {
          return true
        }

        // If member count is larger than 2, and current user is board owner, and target user is internal user
        // Then means target user must be a guest user, in this case will filter this conversation out.
        if (
          myBoardUser.type === BoardAccessType.BOARD_OWNER &&
          UserFormatter.isInternalUser(targetBoardUser.user.type)
        ) {
          return false
        }

        // If member count is larger than 2, and current user is not board user, and current user is internal user
        // Then means current user is a guest user, in this case will filter this conversation out.
        if (
          myBoardUser.type !== BoardAccessType.BOARD_OWNER &&
          UserFormatter.isInternalUser(myBoardUser.user.type)
        ) {
          return false
        }

        return true
      } else {
        return false
      }
    })
    if (userBoard) {
      return UserFormatter.transformUserBoards([userBoard])[0]
    } else if (currentUser.timelineBottomFeedTime > 0 && id) {
      const [remoteUserBoard] = await this.batchQueryPrivateConversations([id])
      return remoteUserBoard
    } else {
      return null
    }
  }

  unsubscribeUserMeets(): void {
    this.meetResolveCallback = null
  }

  subscribeUserMeets(resolve: Function): void {
    this.meetResolveCallback = resolve
  }

  subscribeForUserBoardArchive(callback: Function): void {
    this.userBoardArchiveCallback = callback
  }

  unsubscribeForUserBoardArchive(): void {
    this.userBoardArchiveCallback = null
  }

  subscribeContacts(resolve: Function): void {
    if (!this.userContactsSubscriber) {
      const formattedContacts = UserFormatter.transformContacts(this.currentUser.contacts, false)
      resolve(formattedContacts)
      this.processUserBoardsWhenContactsChange(formattedContacts)
      if (this.suggestedContactResolveCallback) {
        this.suggestedContactResolveCallback(this.findSuggestedContacts(formattedContacts, false))
      }
      this.userContactsSubscriber = this.currentUser.subscribeUserContacts(
        async (userContacts: Array<Defines.UserContact>) => {
          const formattedContacts = UserFormatter.transformContacts(userContacts, true)
          resolve(formattedContacts)
          this.processUserBoardsWhenContactsChange(formattedContacts)
          if (this.suggestedContactResolveCallback) {
            this.suggestedContactResolveCallback(
              this.findSuggestedContacts(formattedContacts, true)
            )
          }
        }
      )
    }
  }

  findSuggestedContacts(contacts: CContactUser[], isFromSubscribe: boolean) {
    let suggestedContacts = []
    for (let contact of contacts) {
      if (!contact.isInternalUser) {
        if (!this.currentUser.getUserRelationByUserId(contact.id)) {
          suggestedContacts.push(contact)
        } else if (isFromSubscribe) {
          suggestedContacts.push({
            ...contact,
            is_deleted: true
          })
        }
      }
    }
    return suggestedContacts
  }

  subscribeSuggestContacts(resolve: Function) {
    this.suggestedContactResolveCallback = resolve
  }

  subscribeUserPresence(resolve: Function) {
    if (this.userPresenceSubscriber) {
      this.userPresenceSubscriber.unsubscribe()
    }
    this.userPresenceSubscriber = this.currentUser.subscribeUserPresences(
      (presences: Defines.UserPresence[]) => {
        let presenceObj = {}
        for (let presence of presences) {
          if (ObjectUtils.getByPath(presence, 'out_of_office.backup')) {
            let backup = presence.out_of_office.backup
            presence.out_of_office.backup = {
              ...backup,
              name: UserFormatter.getUserName(backup)
            }
          }
          presenceObj[presence.id] = {
            ...presence,
            id: presence.id,
            status: UserFormatter.getUserStatus(presence)
          }
        }
        resolve(presenceObj)
      }
    )
  }

  startChat(userId: string): Promise<Defines.Board> {
    return new Promise((resolve, reject) => {
      const create = () => {
        this.currentUser
          .createBoard({
            isconversation: true,
            use_member_avatar_as_cover: true
          })
          .then((board: Defines.Board) => {
            this.currentUser
              .loadBoard(board.id)
              .then((mxBoard: MxBoard) => {
                mxBoard
                  .inviteMember([
                    {
                      id: userId
                    }
                  ])
                  .then((res) => {
                    this.currentUser.unloadBoard(board.id)
                    resolve(board)
                  })
              })
              .catch(reject)
          })
          .catch(reject)
      }
      this.currentUser
        .queryShareBoards([{ id: userId }])
        .then((user: Defines.User) => {
          let boards: Array<Defines.UserBoard> = ObjectUtils.getByPath(user, 'boards', [])
          let userBoard: Defines.UserBoard = find1on1chat(boards, userId)
          if (userBoard) {
            if (userBoard.status === BoardUserStatus.BOARD_INVITED) {
              this.currentUser
                .joinBoard(ObjectUtils.getByPath(userBoard, 'board.id'))
                .then(() => {
                  resolve(userBoard.board)
                })
                .catch(reject)
            } else {
              resolve(userBoard.board)
            }
          } else {
            create()
          }
        })
        .catch((e) => {
          create()
        })
    })
  }

  static createRelationWithQRToken(token: string) {
    return MxISDK.getCurrentUser()
      .createRelationWithQRToken(token, true)
      .then((user) => {
        return user
      })
  }

  async createGroupConversation(
    name: string,
    members: Defines.UserIdentity[],
    teams?: Group[],
    syncInvite?: boolean
  ) {
    const mxUser = MxISDK.getCurrentUser()
    try {
      const board = await mxUser.createTempBoard({
        name,
        use_member_avatar_as_cover: true
      })
      const boardId = board.id
      await mxUser.convertTempBoardToNormalBoard(boardId)
      if ((members && members.length > 0) || (teams && teams.length)) {
        if (syncInvite) {
          await mxUser.getInstantBoard(boardId).inviteMember(members, {}, teams)
        } else {
          mxUser.getInstantBoard(boardId).inviteMember(members, {}, teams)
        }
      }
      return board
    } catch (e) {
      throw e
    }
  }

  createMockConversation(name: string, member: CGroupUser) {
    const userId = member.id
    const self = this

    function checkExistingBinder() {
      return new Promise((resolve, reject) => {
        const currentUser = MxISDK.getCurrentUser()
        currentUser
          .queryShareBoards([{ id: userId }], null, true)
          .then((user: Defines.User) => {
            let boards: Array<Defines.UserBoard> = ObjectUtils.getByPath(user, 'boards', [])
            let preferRelationChat = false
            if (currentUser.basicInfo) {
              let isInternal = currentUser.basicInfo.type === UserType.USER_TYPE_NORMAL
              if (
                (isInternal && !member.isInternalUser) ||
                (!isInternal && member.isInternalUser)
              ) {
                // An internal/client user click "Chat" on client/internal user contact defail view, prefer to open relation binder
                preferRelationChat = true
              }
            }
            let userBoard: Defines.UserBoard = find1on1chat(boards, userId, preferRelationChat)
            if (userBoard) {
              if (userBoard.status === BoardUserStatus.BOARD_INVITED) {
                const boardId = ObjectUtils.getByPath(userBoard, 'board.id')
                currentUser
                  .joinBoard(boardId)
                  .then(() => {
                    resolve(userBoard.board)
                  })
                  .catch(reject)
              } else {
                resolve(userBoard.board)
              }
            } else {
              reject()
            }
          })
          .catch((e) => {
            reject()
          })
      })
    }

    const createMockBoard = () => {
      let isConversation = true
      if (!member.isInternalUser) {
        let idx = this.currentUser.relations.findIndex(
          (relation) => ObjectUtils.getByPath(relation, 'user.id') === member.id
        )
        const isHideClientDashboard = this.groupCtrl.groupCaps['hide_client_dashboard']
        if (idx > -1 && !isHideClientDashboard) {
          isConversation = false
        }
      }
      const mockBoardParam: Defines.MxMockBoardParam = {
        name: name.trim(),
        peerUser: {
          ...member,
          id: userId,
          name: name,
          title: member.title,
          avatar: member.avatar,
          isJoined: false
        } as User,
        isExternal: !member.isInternalUser,
        isConversation: isConversation,
        isRelation: !isConversation,
        suppressInviteFeed: true,
        checkFunc: checkExistingBinder
      }
      return MxISDK.getCurrentUser().createMockBoard(mockBoardParam)
    }
    return new Promise((resolve, reject) => {
      checkExistingBinder()
        .then((board) => {
          resolve(board)
        })
        .catch(() => {
          resolve(createMockBoard())
        })
    })
  }

  updateUserBoardAccessTime(boardId: string): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      this.currentUser
        .updateUserBoardAccessTime(boardId)
        .then(() => resolve(true))
        .catch((err) => reject(err))
    })
  }

  updateUserBoardArchiveStatusForAll(isArchive: boolean, boardId: string, forAllInternals?: boolean): Promise<void> {
    return this.currentUser.archivedBoardForAll(boardId, isArchive, forAllInternals)
  }

  async updateUserBoardArchiveStatus(
    isArchive: boolean,
    userBoardIdentities: Array<{
      id: string
      sequence: number
    }>
  ) {
    let count = 0
    let index = 0
    const requestPayloads: UserBoard[][] = []

    const needMoreInfoUserBoardIds = []
    userBoardIdentities.forEach(({ id, sequence }) => {
      const ubSequence = sequence || this.currentUser.getUserBoardByBoardId(id).sequence
      if (!ubSequence) {
        needMoreInfoUserBoardIds.push(id)
      }
    })

    const moreUserBoardSeqMap = {}
    if (needMoreInfoUserBoardIds.length) {
      const { boards } = await this.currentUser.readUserBoards(needMoreInfoUserBoardIds)
      boards.forEach((board) => {
        moreUserBoardSeqMap[board.board.id] = board.sequence
      })
    }

    userBoardIdentities.forEach(async ({ id, sequence }) => {
      const ubSequence =
        sequence || this.currentUser.getUserBoardByBoardId(id).sequence || moreUserBoardSeqMap[id]
      const payload: UserBoard = {
        sequence: ubSequence,
        is_archive: isArchive
      }
      if (isArchive) {
        payload.accessed_time = 0
        payload.is_favorite = false
      }

      if (!requestPayloads[index]) {
        requestPayloads[index] = []
      }
      requestPayloads[index].push(payload)
      count++
      if (count % 400 === 0) {
        index++
      }
    })

    const userBoards = await Promise.all(
      requestPayloads.map((payload) => this.currentUser.updateUserBoards(payload))
    )

    let flatBoards = []
    userBoards.forEach((user) => {
      flatBoards = [...flatBoards, ...user.boards]
    })

    return flatBoards
  }

  dismissActiveMeet(boardId: string): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      this.currentUser
        .updateUserBoard(boardId, { dismissed_time: 0 })
        .then(() => resolve(true))
        .catch(reject)
    })
  }

  acceptBoard(boardId: string, password: string, noFeed: boolean = true): Promise<boolean> {
    const mxUesr = this.currentUser
    return new Promise<boolean>((resolve, reject) => {
      mxUesr
        .joinBoard(boardId, noFeed, '', password)
        .then(() => {
          mxUesr
            .loadBoard(boardId)
            .then((mxBoard) => {
              mxUesr.unloadBoard(boardId)
            })
            .catch((_) => mxUesr.unloadBoard(boardId))
          resolve(true)
        })
        .catch((err) => reject(err))
    })
  }

  declineBoard(boardId: string): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      this.currentUser
        .leaveBoard(boardId)
        .then(() => resolve(true))
        .catch((err) => reject(err))
    })
  }

  updatePendingInvitationRemind(time: number): Promise<boolean> {
    if (!time) {
      // saved value should be second timestemp
      time = parseInt((Date.now() / 1000).toString())
    }
    const TAG_NAME = 'pending_invitation_remind_timestamp'
    let tags = this.currentUser.user.tags
    let oldSequence
    if (tags && tags.length >= 1) {
      tags.forEach((tag) => {
        if (tag.name == TAG_NAME) {
          oldSequence = tag.sequence
        }
      })
    }

    const usr = {} as User
    const tag: UserTag = {
      name: TAG_NAME,
      string_value: time.toString()
    }
    if (oldSequence) {
      tag.sequence = oldSequence
    }

    usr.tags = [tag]
    return new Promise<boolean>((resolve, reject) => {
      this.currentUser
        .updateProfile(usr)
        .then(() => {
          resolve(true)
        })
        .catch((err) => reject(err.message))
    })
  }

  @transformError()
  static createUserWithQRToken(
    token: string,
    user: User,
    isInternalUser?: boolean
  ): Promise<MxUser> {
    return MxISDK.registerWithQRToken(user, token, true, isInternalUser)
  }

  static initAnnoymousUser(user: {
    id: string
    firstName: string
    lastName: string
    accessToken: string
  }) {
    if (MxISDK.isAnonymousContext()) {
      MxISDK.getAnonymousUser().setUserOption({
        accessToken: user.accessToken
      })
      MxISDK.setAnonymousContext({
        id: user.id,
        first_name: user.firstName,
        last_name: user.lastName
      })
    }
  }

  destroy() {
    this.userBoardSubscriber?.unsubscribe()
    this.userRelationSubscriber?.unsubscribe()
    this.userNotificationSubscriber?.unsubscribe()
    this.userBasicInfoSubscriber?.unsubscribe()
    this.userContactsSubscriber?.unsubscribe()
    this.userPresenceSubscriber?.unsubscribe()
    this.userResourceSubscriber?.unsubscribe()
    this.userNotificationSubscriber = null
    this.userBasicInfoSubscriber = null
    this.userResourceSubscriber = null
    this.userRelationSubscriber = null
    this.userBoardSubscriber = null
    this.userContactsSubscriber = null
    this.userPresenceSubscriber = null
    userController = null
  }

  getQrToken(): Promise<string> {
    const tokens =
      (this.currentUser.basicInfo.qr_tokens || []).filter((t) => t.is_deleted !== true) || []
    const currentToken = ObjectUtils.getByPath(tokens, '0.token', '')

    if (currentToken) {
      return Promise.resolve(currentToken)
    } else {
      return this.currentUser.createQRToken().then((user: Defines.User) => {
        return ObjectUtils.getByPath(user, 'qr_tokens.0.token', '')
      })
    }
  }

  resendVerificationEmail(clientUserId: string): Promise<void> {
    return this.currentUser.resendVerificationEmail(clientUserId)
  }

  @transformError()
  postAuditLog(
    actionGroupId: string,
    actionTypeId: string,
    payload: any,
    actionBoardId?: string,
    board?: Board
  ): Promise<void> {
    payload = payload || {}
    let mxBoard: Board = board || {
      id: actionBoardId || payload.board_id,
      tags: [
        {
          name: 'ACTIVITY_DETAIL',
          string_value: JSON.stringify(payload)
        }
      ]
    }
    let userActivity: UserActivityLog = {
      actor_org_id: MxISDK.getCurrentOrg() ? MxISDK.getCurrentOrg().id : '',
      actor_email: this.currentUser ? this.currentUser.basicInfo.email : '',
      actor_id: this.currentUser ? this.currentUser.id : '',
      activities: [
        {
          action_type_id: actionTypeId,
          action_group_id: actionGroupId,
          platform: 'Web',
          client_version: MxISDK.getISDKConfig().clientVersion,
          detail: {
            object: {
              board: mxBoard
            }
          }
        }
      ]
    }

    return this.currentUser.postAuditLog(userActivity)
  }

  @transformError()
  createJWT(payload: any, app_lang: string): Promise<string> {
    if (!payload) {
      payload = {}
    }
    payload = { ...payload, org_id: this.group.group.id }
    const currUser = this.currentUser.user
    if (currUser.unique_id) {
      payload.unique_id = currUser.unique_id
    } else if (currUser.email) {
      payload.email = currUser.email
    } else if (currUser.phone_number) {
      payload.phone_number = currUser.phone_number
    }
    if (currUser.display_id) {
      payload.display_id = currUser.display_id
    }
    payload.app_language = app_lang
    return this.currentUser.createJWT(JSON.stringify(payload))
  }

  @transformError()
  getRelationWithQRToken(qrToken: string): Promise<CUserRelation> {
    let self = this
    return new Promise((resolve, reject) => {
      MxISDK.decodeQRToken(qrToken)
        .then((group) => {
          let guser = group.members[0]
          let relation = self.currentUser.relations.find(
            (relation) => relation.user.id === guser.user.id
          )
          if (relation) {
            resolve(UserFormatter.transformUserRelations([relation])[0])
          } else {
            resolve(null)
          }
        })
        .catch((_) => reject)
    })
  }

  @transformError()
  createPrivateConversation(userId: string): Promise<String> {
    return new Promise((resolve, reject) => {
      let mxUser = MxISDK.getCurrentUser()
      mxUser
        .queryShareBoards([{ id: userId }])
        .then((board) => {
          resolve(board.id)
        })
        .catch((err) => {
          mxUser
            .createBoard({
              isconversation: true,
              use_member_avatar_as_cover: true
            })
            .then((board: Defines.Board) => {
              mxUser
                .loadBoard(board.id)
                .then((mxBoard: MxBoard) => {
                  mxBoard
                    .inviteMember([
                      {
                        id: userId
                      }
                    ])
                    .then((res) => {
                      mxUser.unloadBoard(board.id)
                      resolve(board.id)
                    })
                })
                .catch(reject)
            })
            .catch(reject)
        })
    })
  }

  duplicateConversation(boardId: string, name: string, users: UserIdentity[], teams?: Group[], opts?: MxDuplicateBoardOption) {
    return new Promise((resolve, reject) => {
      let mxUser = MxISDK.getCurrentUser()
      mxUser.duplicateBoard(boardId, name, users, teams, opts)
        .then((board) => {
          resolve(board.id)
        })
        .catch(reject)
    })
  }

  async batchQueryPrivateConversations(userIds: string[]): Promise<CUserBoard[]> {
    let privateConversations = []
    try {
      const userResp = await this.currentUser.queryShareBoards(
        userIds.map((id) => ({ id })),
        [],
        true
      )
      privateConversations = userResp.boards || []
    } catch (e) {
      privateConversations = []
    }
    return UserFormatter.transformUserBoards(
      privateConversations.filter((board) => !board.is_deleted)
    )
  }

  async readUserBoards(
    boardIds: string[],
    keepOriginal?: boolean
  ): Promise<CUserBoard[] | UserBoard[]> {
    const { boards } = await this.currentUser.readUserBoards(boardIds)
    return keepOriginal ? boards : UserFormatter.transformUserBoards(boards)
  }

  async isUserBoardExist(boardId: string): Promise<boolean> {
    const localUserBoard = MxISDK.getCurrentUser().getUserBoardByBoardId(boardId)
    if (localUserBoard) {
      return true
    }

    try {
      const { boards } = await MxISDK.getCurrentUser().readUserBoards([boardId])
      return boards.length > 0
    } catch (e) {
      return false
    }
  }

  @transformError()
  searchArchivedBoardsByMemberName(
    searchKey: string,
    startIndex: number = 0,
    pageSize: number = 20,
    needTotalCount: boolean = false
  ) {
    return this.searchUserBoards({
      searchKey,
      startIndex,
      pageSize,
      needTotalCount,
      searchType: 'MEMBER_NAME',
      searchDataSourceType: MxSearchDataSourceFilter.ARCHIVED_BOARDS
    })
  }

  @transformError()
  searchNormalBoardsByMemberName(searchKey: string, startIndex: number = 0, pageSize: number = 20) {
    return this.searchUserBoards({
      searchKey,
      startIndex,
      pageSize,
      needTotalCount: true,
      searchType: 'MEMBER_NAME',
      searchDataSourceType: MxSearchDataSourceFilter.TIMELINE_BOARDS
    })
  }

  @transformError()
  searchNormalBoardsByBoardName(searchKey: string, startIndex: number, pageSize: number) {
    return this.searchUserBoards({
      searchKey,
      startIndex,
      pageSize,
      needTotalCount: true,
      searchType: 'BOARD_NAME',
      searchDataSourceType: MxSearchDataSourceFilter.TIMELINE_BOARDS
    })
  }

  @transformError()
  searchArchivedBoardsByBoardName(searchKey: string, startIndex: number, pageSize: number) {
    return this.searchUserBoards({
      searchKey,
      startIndex,
      pageSize,
      needTotalCount: false,
      searchType: 'BOARD_NAME',
      searchDataSourceType: MxSearchDataSourceFilter.ARCHIVED_BOARDS
    })
  }

  @transformError()
  searchUserBoards(
    options: {
      searchKey: string
      startIndex: number
      pageSize: number
      needTotalCount: boolean
      searchType: string // 'MEMBER_NAME' or 'BOARD_NAME'
      searchDataSourceType?: MxSearchDataSourceFilter
    } = {
      searchKey: '',
      startIndex: 0,
      pageSize: 20,
      searchType: 'MEMBER_NAME',
      needTotalCount: false
    }
  ): Promise<CUserBoard[]> {
    const {
      searchKey,
      startIndex,
      pageSize,
      needTotalCount,
      searchType,
      searchDataSourceType
    } = options
    let searchUserBoardReq =
      searchType === 'MEMBER_NAME'
        ? this.searchUserBoardByMemberNameReq
        : this.searchUserBoardByBoardNameReq
    if (searchUserBoardReq) {
      MxISDK.abortRequest(searchUserBoardReq)
      searchUserBoardReq = null
    }

    if (searchType === 'MEMBER_NAME') {
      searchUserBoardReq = this.currentUser.searchUserBoardsByMember(
        searchKey,
        startIndex,
        pageSize,
        searchDataSourceType
      )
    } else {
      searchUserBoardReq = this.currentUser.searchUserBoardsByName(
        searchKey,
        startIndex,
        pageSize,
        searchDataSourceType
      )
    }

    return searchUserBoardReq.then((user: Defines.User) => {
      let userBoards = user && user.boards
      if (userBoards) {
        const filteredUserBoards = userBoards.filter(UserFormatter.isValidTimelineBoard)
        if (needTotalCount) {
          return {
            totalCount: userBoards.length,
            boards: UserFormatter.transformUserBoards(filteredUserBoards),
            originalUserBoards: userBoards
          }
        } else {
          return UserFormatter.transformUserBoards(filteredUserBoards)
        }
      }
      return needTotalCount
        ? {
            totalCount: 0,
            boards: [],
            originalUserBoards: []
          }
        : []
    })
  }

  @transformError()
  readArchivedWorkspaces(
    pageStart: number,
    pageSize = 100
  ): Promise<{ nextPageStart: number; workspaces: CUserBoard[] }> {
    const loadArchivedWs = this.currentUser.readArchivedUserBoards(pageStart, pageSize)
    return loadArchivedWs.then((data: MxPaginationUserBoards) => {
      const userBoards = data?.user?.boards
      if (userBoards) {
        return {
          nextPageStart: data.nextPageStart,
          workspaces: UserFormatter.transformUserBoards(userBoards)
        }
      }
      return {
        nextPageStart: 0,
        workspaces: []
      }
    })
  }

  handleDueDateChange(newItems: any[], oldItems: any[]) {
    let map = {}
    if (oldItems) {
      // for bug MVB-22433
      oldItems.forEach((item) => {
        map[item.binderId + item.sequence] = item.due_time
      })
    }
    let d = new Date()
    let start = new Date(d.getFullYear(), d.getMonth(), d.getDate()).getTime()
    let end = start + 1000 * 60 * 60 * 24
    let updates = []
    newItems.forEach((item) => {
      if (
        map[item.binderId + item.sequence] &&
        map[item.binderId + item.sequence] !== item.due_time
      ) {
        if (item.due_time >= start && item.due_time <= end) {
          updates.push({
            sequence: item.sequence,
            enabled_time: item.updated_time
          })
        }
      }
    })
    if (updates.length) {
      this.updateActionItemEnableTime(updates)
    }
  }

  subscribeActionItems(cb: Function) {
    if (!this.userActionItemsSubscriber) {
      const actionItems = this.currentUser.actionItems
      const ret = UserFormatter.actionItemsPreprocess(actionItems)
      if (ret.syncEnableTimeItems.length) {
        this.updateActionItemEnableTime(ret.syncEnableTimeItems)
      }
      cb(ret.actionItems)
      this.userActionItemsSubscriber = this.currentUser.subscribeActionItems((userBoards) => {
        const preproRet = UserFormatter.actionItemsPreprocess(userBoards)
        if (preproRet.syncEnableTimeItems.length) {
          this.updateActionItemEnableTime(preproRet.syncEnableTimeItems)
        }
        cb(preproRet.actionItems)
      })
    }
  }

  updateActionItemsAccessTime() {
    return this.currentUser.updateActionItemAccessTime()
  }

  updateActionItemEnableTime(userBoards: Defines.UserBoard[]) {
    return this.currentUser.updateActionItemEnableTime(userBoards)
  }

  isValidActionItem(
    payload: { binderId: string; sequence: number; type: string },
    activeOnly?: boolean
  ): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const mxUser = this.currentUser
      const { binderId, sequence, type } = payload
      mxUser
        .loadBoard(binderId)
        .then((mxBoard) => {
          let baseObj = null
          if (type === MxConsts.NotificationType.TODO) {
            baseObj = mxBoard.getTodoBaseObject(sequence)
          } else if (type === MxConsts.NotificationType.SIGNATURE) {
            baseObj = mxBoard.getSignatureBaseObject(sequence)
          } else if (type === MxConsts.NotificationType.TRANSACTION) {
            baseObj = mxBoard.getTransactionBaseObject(sequence)
          }
          mxBoard
            .readThread(baseObj)
            .then((thread) => {
              mxUser.unloadBoard(binderId)
              if (type === MxConsts.NotificationType.TODO && thread.todos) {
                const todo = ObjectUtils.getByPath(thread, 'todos.0', {})
                if (activeOnly && todo.is_deleted) {
                  resolve(false)
                } else {
                  resolve(true)
                }
              } else if (type === MxConsts.NotificationType.SIGNATURE && thread.signatures) {
                const signature = ObjectUtils.getByPath(thread, 'signatures.0', {})
                if (activeOnly && signature.is_deleted) {
                  resolve(false)
                } else {
                  resolve(true)
                }
              } else if (type === MxConsts.NotificationType.TRANSACTION && thread.transactions) {
                const transaction = ObjectUtils.getByPath(thread, 'transactions.0', {})
                if (activeOnly && transaction.is_deleted) {
                  resolve(false)
                } else {
                  resolve(true)
                }
              } else {
                resolve(false)
              }
            })
            .catch(() => {
              mxUser.unloadBoard(binderId)
              resolve(false)
            })
        })
        .catch((err) => {
          if (err.code === ClientResponseCode.RESPONSE_ERROR_PERMISSION) {
            resolve(false)
          } else if (err.code === ClientResponseCode.RESPONSE_ERROR_NOT_FOUND) {
            resolve(false)
          } else {
            reject(err)
          }
        })
    })
  }

  isValidNotification(payload: {
    binderId: string
    sequence: number
    feedSequence: number
    todoSequence: number
    signatureSequence: number
    transactionSequence: number
    relationSequence: number
    teamId: string
    subType?: string
    type?: string
    rootFolderBoardId?: string
  }): Promise<boolean> {
    if (payload.binderId) {
      if (payload.subType && payload.type === 'BTOM') {
        // ownership transfer use cases
        let binderId = payload.binderId
        if (payload.subType === MxConsts.NotificationType.FLOW_TEMPLATE_OWNER_TRANSFER && payload.rootFolderBoardId) {
          binderId = payload.rootFolderBoardId
        }
        return this.isAccessibleBoard(binderId, payload.type)
      } else if (payload.type === 'MTOM' || payload.type === 'MBM') {
        // meeting ownership transfer use cases
        return this.isMemberOfBoard(payload.binderId)
      } else if (['BORMM', 'BIRMM', 'BARM'].includes(payload.type)) {
        // 'BORMM', 'BIRMM' member have not joined use case
        // 'BARM' workspace auto-archive/archive-for-all use case
        return this.isAccessibleBoard(payload.binderId)
      } else if (payload.type === 'BORMM' || payload.type === 'BIRMM') {
        // member have not joined use case
        return this.isAccessibleBoard(payload.binderId)
      } else if (payload.subType === MxConsts.NotificationType.WORKSPACE_DUE) {
        return this.isAccessibleBoard(payload.binderId)
      } else {
        // todo, esign, transaction notification
        let new_payload: any = {
          binderId: payload.binderId
        }
        if (payload.todoSequence) {
          new_payload.type = MxConsts.NotificationType.TODO
          new_payload.sequence = payload.todoSequence
        } else if (payload.signatureSequence) {
          new_payload.type = MxConsts.NotificationType.SIGNATURE
          new_payload.sequence = payload.signatureSequence
        } else if (payload.transactionSequence) {
          new_payload.type = MxConsts.NotificationType.TRANSACTION
          new_payload.sequence = payload.transactionSequence
        }
        return this.isValidActionItem(new_payload, true)
      }
    } else {
      // contact, team notification, the contact is a relation client user
      return new Promise((resolve, reject) => {
        const { relationSequence, teamId } = payload
        if (relationSequence) {
          const relations = ObjectUtils.getByPath(this.currentUser, 'user.relations', [])
          const relation = relations.find(
            (urelation) => urelation && urelation.sequence === relationSequence
          )
          if (relation && !relation.is_deleted) {
            resolve(true)
          } else {
            resolve(false)
          }
        } else if (teamId) {
          const groups = ObjectUtils.getByPath(this.currentUser, 'user.groups', [])
          const ugroup = groups.find(
            (ugroup) => ObjectUtils.getByPath(ugroup, 'group.id') === teamId
          )
          if (ugroup && !ugroup.is_deleted) {
            resolve(true)
          } else {
            resolve(false)
          }
        }
      })
    }
  }

  isAccessibleBoard(boardId: string, type?: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.currentUser
        .readBoard(boardId)
        .then((board) => {
          if (type === 'BTOM' && board.users && !board.access_control_board_id) {
            const boardUser = board.users.find(item => !item.is_deleted && (item.user?.id === this.currentUser.id || item.group))
            if (!boardUser) {
              resolve(false)
              return
            }
          }
          resolve(true)
        })
        .catch((err) => {
          if (err.code === ClientResponseCode.RESPONSE_ERROR_PERMISSION) {
            resolve(false)
          } else if (err.code === ClientResponseCode.RESPONSE_ERROR_NOT_FOUND) {
            resolve(false)
          } else {
            reject(err)
          }
        })
    })
  }

  isMemberOfBoard(boardId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.currentUser
        .readBoard(boardId)
        .then((board) => {
          if (
            !board.users.find((user) => !user.is_deleted && user.user.id === this.currentUser.id)
          ) {
            resolve(false)
          }

          resolve(true)
        })
        .catch((err) => {
          if (err.code === ClientResponseCode.RESPONSE_ERROR_PERMISSION) {
            resolve(false)
          } else if (err.code === ClientResponseCode.RESPONSE_ERROR_NOT_FOUND) {
            resolve(false)
          } else {
            reject(err)
          }
        })
    })
  }

  transformMentionMe(mention: UserMentionMe, teams: Array<string>) {
    let board = mention.board
    let todoSequence
    let comment: BoardComment
    let comments: BoardComment[]
    let cacheBoard = this.currentUser.getUserBoardByBoardId(mention.board.id)
    if (board.is_deleted) {
      return null
    }
    if (mention.is_deleted) {
      // to let client side delete
      return mention
    }
    if (board.comments) {
      comments = board.comments
    } else if (board.pages) {
      comments = ObjectUtils.getByPath(board.pages, '0.comments')
    } else if (board.todos) {
      comments = ObjectUtils.getByPath(board.todos, '0.comments')
      todoSequence = ObjectUtils.getByPath(board.todos, '0.sequence')
    }
    if (comments) {
      if (comments.length === 2) {
        comment = comments[1]
      } else {
        comment = comments[0]
      }
    }
    let feed: ObjectFeed = board.feeds[0]
    let actor = BoardFormatter.transformBoardActor({user:feed.actor}, board)
    let binderName = BoardFormatter.getBoardName(cacheBoard ? cacheBoard.board : board, true)
    let { text, rich_text, created_time } = comment
    let maxMentionTime = Math.max(comment.created_time, mention.created_time)
    let result = {
      text: rich_text || text,
      createdTime: created_time,
      actor,
      binderName,
      binderId: board.id,
      feedSequence: feed.sequence,
      sequence: mention.sequence,
      board: mention.board,
      maxMentionTime,
      feedTime: feed.created_time,
      todoSequence
    }
    let { first_name, last_name } = this.currentUser.basicInfo
    let commentText = result.text.toLowerCase()
    let name
    if (last_name) {
      name = '@' + first_name.toLowerCase() + ' ' + last_name.toLowerCase()
    } else {
      name = '@' + first_name.toLowerCase()
    }
    if (board && (board.is_transaction || board.islive)) {
      return false
    } else {
      const strings = [name.toLowerCase(), ...teams]
      let contain = false
      for (const str of strings) {
        if (commentText.indexOf(str.toLowerCase()) > -1) {
          contain = true
          break
        }
      }
      if (contain) {
        return result
      } else {
        return false
      }
    }
  }

  getMentionList() {
    let result = []
    const teams = this.currentUser.basicInfo.groups
      .filter((g) => {
        return g.group && g.group.type === GroupType.GROUP_TYPE_TEAM
      })
      .map((g) => g.group.name.toLowerCase())
    for (let i = this.currentUser.mentionmes.length - 1; i >= 0; i--) {
      if (
        !this.mentionPaginationSequence ||
        this.currentUser.mentionmes[i].sequence < this.mentionPaginationSequence
      ) {
        let mention = this.transformMentionMe(this.currentUser.mentionmes[i], teams)
        if (mention) {
          result.push(mention)
          if (result.length === 200) {
            break
          }
        }
      }
    }
    if (result.length) {
      this.mentionPaginationSequence = result[result.length - 1].sequence
    }
    return result.sort((a, b) => b.feedTime - a.feedTime)
  }

  resetMentionPaginationSequence() {
    this.mentionPaginationSequence = 0
  }

  updateUserTag(tag: UserTag) {
    return this.currentUser.updateProfile({ tags: [tag] })
  }

  @transformError()
  loadBinderCover(boardId: string) {
    return this.currentUser.readBoardBasicInfo(boardId).then((board: Board) => {
      return BoardFormatter.transformBoardBasicInfo(board)
    })
  }

  @transformError()
  updateClientProfile(clientId: string, user: User) {
    return this.currentUser.updateClientProfile(clientId, user)
  }

  @transformError()
  updateTooltipViewTime(accessedTime: number) {
    return this.currentUser.createOrUpdateTag('tooltip_view_time', accessedTime)
  }

  @transformError()
  updateFirstWorkspaceId(id: string) {
    return this.currentUser.createOrUpdateTag('First_Workspace_ID', id)
  }
  @transformError()
  createOrUpdateTag(tagName: string, stringValue: string) {
    return this.currentUser.createOrUpdateTag(tagName, stringValue)
  }

  @transformError()
  deleteTag(key: string) {
    return this.currentUser.deleteTag(key)
  }

  // @transformError()
  removeMentionmes(mentionSequence: number[], beforeTime?: number) {
    let mxUser = MxISDK.getCurrentUser()
    mentionSequence = mentionSequence || []
    return mxUser.removeMentionmes(mentionSequence, beforeTime)
  }

  updateMeetReminder(boardId, interval) {
    return this.currentUser.updateMeetReminder(boardId, interval)
  }

  updateBoardRSVPReply(boardId: string, rsvpReply: RSVPReply) {
    return new Promise((resolve, reject) => {
      this.currentUser.updateRSVPStatus(boardId, rsvpReply).then(data => {
        const hasSDKAcceptMeetCb = websdkController.hasCallback('onMeetAccepted') && rsvpReply.partstat === RSVPStatus.RSVP_ACCEPTED
        const hasSDKDeclineMeetCb = websdkController.hasCallback('onMeetDeclined') && rsvpReply.partstat === RSVPStatus.RSVP_DECLINED
        if (hasSDKAcceptMeetCb || hasSDKDeclineMeetCb) {
          this.currentUser.readUserBoards([boardId]).then(user => {
            const {session_key, topic} = ObjectUtils.getByPath(user, 'boards.0.board.sessions.0.session')
            const mepMeet: MepMeet = {
              meetId: session_key,
              topic
            }
            if (hasSDKAcceptMeetCb) {
              websdkController.triggerOnMeetAccepted(mepMeet)
            }
            if (hasSDKDeclineMeetCb) {
              websdkController.triggerOnMeetDeclined(mepMeet)
            }
          })
        }
        resolve(data)
      }).catch(reject)
    })
  }

  readBoardField(boardId: string, fieldName: string, sequenceArray: Array<number>): Promise<Board> {
    return this.currentUser.getInstantBoard(boardId).readField(fieldName, sequenceArray)
  }

  @transformError()
  queryUserPresence(users: UserIdentity[]) {
    if (users.length > 1000) {
      return FunctionUtil.divideLargeRequest(
        this.currentUser.queryUserPresence.bind(this.currentUser),
        users
      ).then(([presences]) => {
        return this.formatUserPresence(presences)
      })
    } else {
      return this.currentUser.queryUserPresence(users).then((presences) => {
        return this.formatUserPresence(presences)
      })
    }
  }

  updateAppStatistics(statistics: AppStatistics, orgId?: string) {
    return this.currentUser.updateAppStatistics(statistics, orgId)
  }

  async getRelationOr1on1ChatIdWithUserId(userId: string) {
    const userBoards = this.currentUser.boards
    const ub = find1on1chat(userBoards, userId, true)
    if (ub) {
      return ObjectUtils.getByPath(ub, 'board.id')
    } else if (this.currentUser.timelineBottomFeedTime > 0 && userId) {
      const [remoteUserBoard] = await this.batchQueryPrivateConversations([userId])
      return remoteUserBoard?.id
    } else {
      return null
    }
  }

  getUserInfo(id) {
    return this.groupCtrl.getGroupMember({ id })
  }

  updateEmail(email: string, verifyCode: string) {
    return this.currentUser.updateEmail(email, verifyCode)
  }

  updatePhoneNumber(phoneNumber: string, verifyCode: string) {
    return this.currentUser.updatePhoneNumber(phoneNumber, verifyCode)
  }

  readAllContactsPresence(filterOnline: boolean, filterClient: boolean) {
    return this.currentUser
      .readAllContactsPresence(filterOnline, filterClient)
      .then((presences) => {
        return this.formatUserPresence(presences)
      })
  }

  getCurrentACDCacheBoard(boardId: string) {
    let board = this.currentUser.getCacheBoard(boardId)
    if (board) {
      return BoardFormatter.getAnonymousAcdBoardUser(board.board)
    }
  }

  leaveAcdMessageAsTodo(
    name: string,
    description: string,
    userName: string,
    email: string,
    phoneNumber?: string
  ) {
    return this.currentUser.createTempBoard({}).then((board) => {
      let binder = this.currentUser.getInstantBoard(board.id)
      const todoPayload = {
        name,
        note: ''
      }
      todoPayload.note += `Client Name: ${userName} \n`
      if (this.currentUser.basicInfo.unique_id) {
        todoPayload.note += `User ID: ${this.currentUser.basicInfo.unique_id} \n`
      }
      todoPayload.note += `Email: ${email} \n`
      if (phoneNumber) {
        todoPayload.note += `Phone: ${phoneNumber} \n`
      }
      todoPayload.note += '\n'
      todoPayload.note += description
      return binder.createTodo({ name }).then((board) => {
        // createTodo does not guarantee order for create and updateTodoBasic
        return binder.updateTodo(board.todos[0].sequence, todoPayload)
      })
    })
  }

  copyTodoToRoutingServer(
    tempBoardId: string,
    todoSeq: number,
    channelSeq: number,
    actor?: User
  ): Promise<Board> {
    return this.currentUser.leaveRoutingMessage(tempBoardId, todoSeq, channelSeq, actor)
  }

  updateBoardUserAOSM(boardId: string, rsvpState: string) {
    const mxUser = MxISDK.getCurrentUser()
    const buAOSM: BoardUserAOSM = {
      timestamp: Date.now(),
      reply: RSVPStatus[rsvpState]
    }
    return mxUser.updateUserAOSM(boardId, buAOSM)
  }

  readBoard(boardId: string, filters?: string[], params?: ClientParam[]) {
    return MxISDK.getCurrentUser().readBoard(boardId, filters, params)
  }
  updateBoardActiveStatus(boardId: string, isActive: boolean) {
    return MxISDK.getCurrentUser().updateBoardActiveStatus(boardId, isActive)
  }

  @transformError()
  static registerFreemiumUser(user: User, option: MxRegisterUserOption): Promise<MxUser> {
    return MxISDK.registerFreemiumUser(user, option)
  }

  async createTempBoardWithViewToken() {
    const currentUser = this.currentUser
    const board = await currentUser.createTempBoard({})
    const { view_tokens } = await currentUser
      .getInstantBoard(board.id)
      .createViewToken(BoardAccessType.BOARD_OWNER)
    const viewToken = view_tokens[0].token
    return {
      board,
      viewToken
    }
  }

  updateDefaultWorkspaceNotificationSetting(setting: WorkspaceNotificationSettings) {
    const mxUser = MxISDK.getCurrentUser()
    let board_notification_level: Defines.NotificationLevel
    switch(setting.notificationLevel) {
      case '0':
        board_notification_level = Defines.NotificationLevel.NOTIFICATION_LEVEL_ALL
        break
      case '10':
        board_notification_level = Defines.NotificationLevel.NOTIFICATION_LEVEL_RELATED
        break
      case '20':
        board_notification_level = Defines.NotificationLevel.NOTIFICATION_LEVEL_NOTHING
        break
    }
    const { memberNotificationSetting, actionNotificationSetting, boardNotificationSetting } = setting
    let changedMemberNoti, changedActionNoti, changedBoardNoti
    if (memberNotificationSetting) {
      changedMemberNoti = {
        not_join_within: memberNotificationSetting.notJoinWithin,
        not_join_within_to_owner: memberNotificationSetting.notJoinWithinToOwner
      } as BoardMemberNotificationSetting
    }
    if (actionNotificationSetting) {
      changedActionNoti = {
        on_due: actionNotificationSetting.onDue,
        before_due: actionNotificationSetting.beforeDue,
        after_due: actionNotificationSetting.afterDue,
        after_due_repeat: actionNotificationSetting.afterDueRepeat
      } as ActionNotificationSetting
    }
    if (boardNotificationSetting) {
      changedBoardNoti = {
        on_due: boardNotificationSetting.onDue,
        before_due: boardNotificationSetting.beforeDue,
        after_due: boardNotificationSetting.afterDue,
        after_due_repeat: boardNotificationSetting.afterDueRepeat
      } as BoardNotificationSetting
    }

    if (board_notification_level || changedMemberNoti || changedActionNoti || changedBoardNoti) {
      return mxUser.updateProfile({
        board_notification_level,
        board_member_notification_settings: changedMemberNoti,
        action_notification_settings: changedActionNoti,
        board_notification_settings: changedBoardNoti
      })
    } else {
      return Promise.resolve()
    }
  }

  getUserBoard(boardId: string): Defines.UserBoard {
    const mxUser = MxISDK.getCurrentUser()
    return mxUser.getUserBoardByBoardId(boardId)
  }
  async updateWorkspacePinnedStatus(boardId: string, isPinned: boolean) {
    const mxUser = MxISDK.getCurrentUser()
    const limitMax = 5
    const count = mxUser.boards.reduce((total, userBoard) => {
      if (!userBoard.is_archive && !userBoard.is_deleted && userBoard.is_favorite) {
        total++
      }
      return total
    }, 0)
    if (isPinned && count >= limitMax) {
      return Promise.reject(
        new CCustomError('', {
          limitMax: limitMax
        })
      )
    }
    const { boards } = await mxUser.readUserBoards([boardId])
    const userBoard = boards[0]
    return mxUser.updateUserBoards([
      {
        sequence: userBoard.sequence,
        is_favorite: isPinned
      }
    ])
  }

  deleteResources (resourceSeqs: number[]): Promise<boolean> {
    return this.currentUser
      .deleteUserResource(resourceSeqs)
      .then(() => true)
      .catch(() => false)
  }

  createClientGroup(name: string, description: string, clients: UserIdentity[]) {
    const mxGroup = MxISDK.getCurrentOrg()
    return mxGroup.createTeam(name, {description, type: GroupType.GROUP_TYPE_CLIENT_TEAM, useCreatorAsOwner: true}).then((group) => {
      const teamId = ObjectUtils.getByPath(group,'teams.0.group.id')
      return mxGroup.addTeamMembers(teamId, clients)
    })
  }
  editClientGroup (id: string, name: string, description: string) {
    const mxGroup = MxISDK.getCurrentOrg()
    return mxGroup.updateTeam(id, name, description)
  }
}
