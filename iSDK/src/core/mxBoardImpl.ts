import { BoardWorkflow } from './../proto/generated/BoardWorkflow';
import { WorkflowVar } from './../proto/generated/WorkflowVar';
import { BoardUserRSVP } from './../proto/generated/BoardUserRSVP';
import { BoardUserActivity } from './../proto/generated/BoardUserActivity';
import { BoardReferenceLink } from './../proto/generated/BoardReferenceLink';
import { ObjectFeed } from "../proto/generated/ObjectFeed";
import { BoardSignature } from "../proto/generated/BoardSignature";
import { BoardTransaction } from "../proto/generated/BoardTransaction";
import { BoardSignee } from "../proto/generated/BoardSignee";
import { BoardPage } from "../proto/generated/BoardPage";
import { Board } from "../proto/generated/Board";
import { BoardAccessType } from "../proto/generated/BoardAccessType";
import { BoardComment } from "../proto/generated/BoardComment";
import { BoardTodo } from "../proto/generated/BoardTodo";
import { ClientResponse } from '../proto/generated/ClientResponse';
import { NotificationLevel } from '../proto/generated/NotificationLevel';
import { BoardSession } from '../proto/generated/BoardSession';
import { User } from '../proto/generated/User';
import { Group } from "../proto/generated/Group";
import { BoardTag } from '../proto/generated/BoardTag';
import { BoardEditorType } from '../proto/generated/BoardEditorType';
import { BoardPin } from '../proto/generated/BoardPin';
import { BoardPageGroup } from '../proto/generated/BoardPageGroup';
import { SignatureStyle } from '../proto/generated/SignatureStyle';
import { BoardSignatureStatus } from '../proto/generated/BoardSignatureStatus';
import { ActionUserRoster } from '../proto/generated/ActionUserRoster';
import {BoardUser} from "../proto/generated/BoardUser";
import {BoardType} from "../proto/generated/BoardType";
import {CacheObject} from "../proto/generated/CacheObject";
import { BoardDataReference } from '../proto/generated/BoardDataReference';
import { BoardFolderType } from '../proto/generated/BoardFolderType';
import { mergeBoardCacheObject } from '../proto';
import { getAllTags, getTagByKey } from "../data/cache/common";
import { parseSPath, getByPath, mxLogger, cloneObject } from "../util";
import { Ajax } from '../network/ajax';
import { MxErr } from './error';
import { MxSubscription, MxCallback, MxSPath, MxBaseObject, UserIdentity, MxPageElementType, MxInviteMemberOption, ITransactionUpdateOption, ISignatureUpdateOption, ITransferActionParam, ICreateTransactionAttachmentParam, IResourceInfo, TransactionStep, MxCopyFileOption, MxCreateCommentOption, TransactionStatus } from "../api/defines";
import { BoardCache } from "../data/cache/boardCache";
import { MxBoardSubscriber } from "../data/subscribe/boardSubscriber";
import { MxSignatureElement, MxBaseObjectType, MxBoardOption } from './../api/defines';
import { MxBoard } from "../api/mxBoard";
import { getContextPath } from './config';
import * as bboard from '../biz/board';
import * as bcomment from '../biz/comment';
import * as bfile from '../biz/file';
import * as btodo from '../biz/todo';
import * as btag from '../biz/tag';
import * as bthread from '../biz/thread';
import * as bsignature from '../biz/signature';
import * as btransaction from '../biz/transaction';
import * as bpin from '../biz/pin';
import * as bworkflow from '../biz/workflow';
import * as bmeet from '../biz/meet';
import * as brelation from '../biz/relation';
import { BoardActor } from '../proto/generated/BoardActor';
import { BoardProperty } from '../proto/generated/BoardProperty';

export class MxBoardImpl implements MxBoard {
    protected _fullBoard: Board;
    protected _contentLibraryBoard: Board;
    protected _boardCache: BoardCache;
    protected _boardSubscriber: MxBoardSubscriber;
    protected _boardId: string;
    protected _isSubscribed: boolean;
    protected _isActionsLoaded: boolean;
    protected _isInstant: boolean;
    protected _isNoAction: boolean;
    protected _option: MxBoardOption;
    protected _viewModels: Map<string, Object>;

    private _pendingPrimiseIds: string[];

    constructor(boardCache: BoardCache) {
        this._boardId = boardCache.board.id;
        this._fullBoard = null;
        this._contentLibraryBoard = null;
        this._boardCache = boardCache;
        this._boardSubscriber = new MxBoardSubscriber(this._boardCache);
        this._pendingPrimiseIds = [];
        this._isSubscribed = false;
        this._isInstant = false;
        this._viewModels = new Map()
    }

    updateBoardCache(board : Board) {
        this._boardId = board.id;
        this._boardCache = new BoardCache(board);
        this._boardSubscriber = new MxBoardSubscriber(this._boardCache);
        this._pendingPrimiseIds = [];
        this._isSubscribed = false;
    }


    get id(): string {
        return this._boardId;
    }

    get isMocked(): boolean {
        return false;
    }

    get isInstant(): boolean {
        return this._isInstant;
    }

    get isNoAction(): boolean {
        return this._isNoAction;
    }

    get isSubscribed(): boolean {
        return this._isSubscribed;
    }

    get board(): Board {
        return this._boardCache.board;
    }

    get fullBoard(): Board {
        return this._fullBoard;
    }

    get contentLibraryBoard(): Board {
        return this._contentLibraryBoard
    }

    get basicInfo(): Board {
        return this.board;
    }

    get tags(): Object {
        return getAllTags(this.board.tags);
    }

    get reference_links(): BoardReferenceLink[] {
        return this._boardCache.cacheReferenceLinks();
    }

    get waiting_users(): ActionUserRoster[] {
        return this._boardCache.board.waiting_users || [];
    }

    get sessions(): BoardSession[] {
        return (this._boardCache.board?.sessions || []).filter(s => !s.is_deleted);
    }

    get option(): MxBoardOption {
        return this._option;
    }

    get viewModels(): Map<string, Object> {
        return this._viewModels
    }

    setBoardOption(option: MxBoardOption) {
        this._option = option;
    }

    setIsNoAction(isNoAction: boolean) {
        this._isNoAction = isNoAction;
    }

    markAsInstantBoard(): void {
        this._isInstant = true;
    }

    onBoardUpdated(board: Board): void {
        if (this._fullBoard) {
            mergeBoardCacheObject(this._fullBoard, board);
        }

        if (this._contentLibraryBoard) {
            mergeBoardCacheObject(this._contentLibraryBoard, board);
        }

        this._boardCache.onObjectUpdate(board);
        this._boardSubscriber.onObjectUpdate(board);
    }

    sync(): Promise<Board> {
        return Promise.resolve(this.board);
    }

    subscribeBoard(): void {
        if (!this._isSubscribed && !this.isMocked && !this.isInstant) {
            this._isSubscribed = true;
            bboard.subscribeBoard(this.id, this.basicInfo.revision).then(()=> {
                mxLogger.info('subscribe board:' + this.id);
            }).catch(e => {
                this._isSubscribed = false;
                mxLogger.warn('subscribe board failed:' + e);
            });
        }
    }

    subscribeBasicInfo(cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeBasicInfo(cb);
    }

    subscribeFullBoard(cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeFullBoard(cb);
    }

    subscribeMeetDetail(cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeMeetDetail(cb);
    }

    subscribeFeeds(cb: MxCallback<ObjectFeed[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeFeeds(cb);
    }

    subscribeRawFeeds(cb: MxCallback<ObjectFeed[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeRawFeeds(cb);
    }

    subscribeFolder(parentFolderPath: string, cb: MxCallback<Board>): MxSubscription {
        if (!parentFolderPath) parentFolderPath = ''
        this.subscribeBoard();
        return this._boardSubscriber.subscribeFolder(parentFolderPath, cb);
    }

    subscribeTodos(cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeTodos(cb);
    }

    subscribeSessions(cb: MxCallback<BoardSession[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeSessions(cb);
    }

    subscribeSignatures(cb: MxCallback<BoardSignature[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeSignatures(cb);
    }

    subscribeTransactions(cb: MxCallback<BoardTransaction[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeTransactions(cb);
    }

    subscribeThread(baseObj: MxBaseObject, cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeThread(baseObj, cb);
    }

    subscribeFile(filePath: MxSPath, cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeFile(filePath, cb);
    }

    subscribePins(cb: MxCallback<BoardPin[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribePins(cb);
    }

    subscribeTags(cb: MxCallback<BoardTag[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeTags(cb);
    }

    subscribeRSVP(cb: MxCallback<BoardUserRSVP[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeRSVP(cb);
    }

    subscribeWaitingUsers(cb: MxCallback<ActionUserRoster[]>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeWaitingUsers(cb);
    }

    getFeedBaseObject(feedSeq: number|ObjectFeed): MxBaseObject {
        return this._boardCache.getFeedBaseObject(feedSeq);
    }

    getCommentBaseObject(commentSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.COMMENT, commentSeq);
    }

    getTodoBaseObject(todoSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.TODO, todoSeq);
    }

    getSignatureBaseObject(signatureSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.SIGNATURE, signatureSeq);
    }

    getTransactionBaseObject(transactionSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.TRANSACTION, transactionSeq);
    }

    getMeetBaseObject(sessionSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.MEET, sessionSeq);
    }

    getPageBaseObject(pageSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.PAGE, pageSeq);
    }

    getPositionCommentBaseObject(pageSeq: number, commentSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.POSITION_COMMENT, commentSeq, pageSeq);
    }

    getFileBaseObject(filePath: MxSPath): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.FILE, 0, 0, filePath);
    }

    getWorkflowBaseObject(workflowSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.WORKFLOW, workflowSeq);
    }

    getWorkflowStepBaseObject(workflowSeq: number, stepSeq: number): MxBaseObject {
        return this._boardCache.getBaseObject(MxBaseObjectType.WORKFLOW_STEP, stepSeq, workflowSeq);
    }
    
    loadFullBoard(option?: MxBoardOption): Promise<Board> {
        if (this._fullBoard) return Promise.resolve(this._fullBoard);

        return bboard.readFullBoard(this.id, option).then(response => {
            this._fullBoard = getByPath(response, 'object.board', null);
            return this._fullBoard;
        });
    }

    loadContentLibraryBoard(): Promise<Board> {
        if (this._contentLibraryBoard) return Promise.resolve(this._contentLibraryBoard);

        return bboard.readContentLibraryBoard(this.id).then(response => {
            this._contentLibraryBoard = getByPath(response, 'object.board', null);
            return this._contentLibraryBoard;
        });
    }

    readCover(): Promise<Board> {
        return bboard.readCover(this.id).then(response => {
            return getByPath(response, 'object.board', null);
        });
    }

    readFeeds(startSequence?: number, beforeSize?: number, afterSize?: number): Promise<Board> {
        return this.wrapPendingPromise(bboard.readFeedsByPagination(this.id, startSequence, beforeSize, afterSize)).then((response: ClientResponse) => {
            let feeds: ObjectFeed[] = getByPath(response, 'object.board.feeds', []);
            let signatures: BoardSignature[] = getByPath(response, 'object.board.signatures');
            this._boardCache.addMainStreamFeeds(feeds);
            let board: Board =  {id: this.id, feeds: feeds};
            if (signatures) {
                this._boardCache.addFeedCacheSignatures(signatures);
            }
            return board;
        });
    }

    readThread(baseObject: MxBaseObject): Promise<Board> {
        if (this.isSubscribed && this._boardCache.cacheThread(baseObject, true)) {
            let board = this._boardCache.cacheThread(baseObject);
            return Promise.resolve(board);
        }

        return this.wrapPendingPromise(bthread.readThread(this.id, baseObject)).then(response => {
            let threadBoard: Board = getByPath(response, 'object.board', null);
            if (threadBoard) {
                this._boardCache.addCacheThread(baseObject, threadBoard);
            }
            return threadBoard;
        });
    }

    listAllFolders(parentFolder?: MxSPath): Promise<Board> {
        return bfile.listAllFolders(this.id, parentFolder).then(response => {
            return getByPath(response, 'object.board', null);
        });
    }

    listFolder(parentFolder: MxSPath, noCache?: boolean, includeFirstSubFolderFile: boolean=true): Promise<Board> {
        if (!parentFolder) parentFolder = ''
        if (!noCache && this.isSubscribed && this._boardCache.cacheFolder(parentFolder, true)) {
            let board = this._boardCache.cacheFolder(parentFolder);
            return Promise.resolve(board);
        }

        let oldP = bfile.listFolder(this.id, parentFolder, includeFirstSubFolderFile);
        let newP = new Promise((resolve, reject) => {
            oldP.then((response: ClientResponse) => {
                let folderBoard: Board = getByPath(response, 'object.board', null);
                if (folderBoard) {
                    this._boardCache.addCacheFolder(parentFolder, folderBoard);
                }
                resolve(folderBoard);
            }).catch(e => reject(e));
        });

        newP['id'] = oldP['id'];
        this._pendingPrimiseIds.push(newP['id']);
        return newP;
    }

    listActions(): Promise<Board> {
        if (this.isSubscribed && this._isActionsLoaded) {
            return Promise.resolve({
                id: this.id,
                signatures: this._boardCache.cacheSignatures(),
                transactions: this._boardCache.cacheTransactions(),
                todos: this._boardCache.cacheTodos(),
                reference_links: this._boardCache.cacheReferenceLinks(),
            });
        }

        const fileds = ["/board/transactions", "/board/signatures", "/board/todos", "/board/reference_links", "/board/sessions"]
        return this.wrapPendingPromise(bboard.readBoard(this.id, fileds)).then(response => {
            this._isActionsLoaded = true
            let todos: BoardTodo[] = getByPath(response, 'object.board.todos', []);
            this._boardCache.addCacheTodos(todos);

            let signatures: BoardSignature[] = getByPath(response, 'object.board.signatures', []);
            this._boardCache.addCacheSignatures(signatures);

            let transactions: BoardTransaction[] = getByPath(response, 'object.board.transactions', []);
            this._boardCache.addCacheTransactions(transactions);

            let links: BoardReferenceLink[] = getByPath(response, 'object.board.reference_links', []);
            this._boardCache.addCacheReferenceLinks(links);

            let sessions: BoardSession[] = getByPath(response, 'object.board.sessions', []);
            this._boardCache.setSessions(sessions);

            const board = getByPath(response, 'object.board', {});
            board.id = this.id
            return board
        });
    }

    listTodos(): Promise<Board> {
        if (this.isSubscribed && this._boardCache.cacheTodos()) {
            return Promise.resolve({
                id: this.id,
                todos: this._boardCache.cacheTodos(),
                reference_links: this._boardCache.cacheReferenceLinks()
            });
        }

        return this.wrapPendingPromise(btodo.listTodos(this.id)).then(response => {
            let todos: BoardTodo[] = getByPath(response, 'object.board.todos', []);
            let links: BoardReferenceLink[] = getByPath(response, 'object.board.reference_links', []);
            this._boardCache.addCacheTodos(todos);
            this._boardCache.addCacheReferenceLinks(links);
            return {
                id: this.id,
                todos: todos,
                reference_links: links,
            };
        });
    }

    listSignatures(): Promise<Board> {
        if (this.isSubscribed && this._boardCache.cacheSignatures()) {
            return Promise.resolve({
                id: this.id,
                signatures: this._boardCache.cacheSignatures()
            });
        }

        return this.wrapPendingPromise(bsignature.listSignatures(this.id)).then(response => {
            let signatures: BoardSignature[] = getByPath(response, 'object.board.signatures', []);
            this._boardCache.addCacheSignatures(signatures);
            const workflows: BoardWorkflow[] = getByPath(response, 'object.board.workflows', []);
            this._boardCache.addCacheWorkflows(workflows);
            return {id: this.id, signatures: signatures, workflows};
        });
    }


    listTransactions(): Promise<Board> {
        if (this.isSubscribed && this._boardCache.cacheTransactions()) {
            return Promise.resolve({
                id: this.id,
                transactions: this._boardCache.cacheTransactions(),
                workflows: this._boardCache.cacheWorkflows()
            });
        }

        if (!this.isInstant && !this._boardCache.hasTransactions()) {
            this._boardCache.addCacheTransactions([]);
            return Promise.resolve({id: this.id, transactions: []});
        }

        return this.wrapPendingPromise(btransaction.listTransactions(this.id)).then(response => {
            const transactions: BoardTransaction[] = getByPath(response, 'object.board.transactions', []);
            const workflows: BoardWorkflow[] = getByPath(response, 'object.board.workflows', []);
            this._boardCache.addCacheTransactions(transactions);
            this._boardCache.addCacheWorkflows(workflows);
            return {id: this.id, transactions: transactions, workflows};
        });
    }

    listWorkflows(): Promise<Board> {
        // if (this.isSubscribed && this._boardCache.cacheWorkflows()) {
        //     return Promise.resolve({
        //         id: this.id,
        //         users: this.basicInfo.users,
        //         workflows: this._boardCache.cacheWorkflows()
        //     });
        // }
        // TODO: waiting for Sheng check the code.
        if (this.isSubscribed && (this._boardCache.cacheWorkflows() || this._boardCache.board?.workflows)) {
            return Promise.resolve({
                id: this.id,
                users: this.basicInfo.users,
                workflows: this._boardCache.board?.workflows || this._boardCache.cacheWorkflows()
            });
        }

        return this.wrapPendingPromise(this.readFields(['/board/workflows', '/board/users'])).then(board => {
            const workflows = board.workflows || []
            this._boardCache.addCacheWorkflows(workflows);
            return {
                id: this.id,
                users: board.users,                
                workflows
            };
        });
    }


    readFields(fileds: string[]): Promise<Board> {
        if (fileds.indexOf('/board/transactions') > -1 
            && fileds.indexOf('/board/signatures') > -1 
            && fileds.indexOf('/board/todos') > -1) {
            return this.listActions()
        }

        return this.wrapPendingPromise(bboard.readBoard(this.id, fileds)).then(response => {
            if (fileds.indexOf('/board/todos') > -1) {
                let todos: BoardTodo[] = getByPath(response, 'object.board.todos', []);
                this._boardCache.addCacheTodos(todos);
            }

            if (fileds.indexOf('/board/signatures') > -1) {
                let signatures: BoardSignature[] = getByPath(response, 'object.board.signatures', []);
                this._boardCache.addCacheSignatures(signatures);
            }

            if (fileds.indexOf('/board/transactions') > -1) {
                let transactions: BoardTransaction[] = getByPath(response, 'object.board.transactions', []);
                this._boardCache.addCacheTransactions(transactions);
            }

            if (fileds.indexOf('/board/reference_links') > -1) {
                let links: BoardReferenceLink[] = getByPath(response, 'object.board.reference_links', []);
                this._boardCache.addCacheReferenceLinks(links);
            }

            if (fileds.indexOf('/board/sessions') > -1) {
                let sessions: BoardSession[] = getByPath(response, 'object.board.sessions', []);
                this._boardCache.setSessions(sessions);
            }

            return getByPath(response, 'object.board');;
        });
    }


    readFileDetail(filePath: MxSPath): Promise<Board> {
        if (this.isSubscribed && this._boardCache.cacheFile(filePath, true)) {
            return Promise.resolve(this._boardCache.cacheFile(filePath));
        }

        return this.wrapPendingPromise(bfile.readFile(this.id, filePath)).then(response => {
            let fileBoard: Board = getByPath(response, 'object.board', null);
            if (fileBoard) {
                this._boardCache.addCacheFile(filePath, fileBoard);
            }
            return fileBoard;
        })
    }

    readFileByReferenceLink(refSeq: number): Promise<Board> {
        return bfile.readFileByReferenceLink(this.id, refSeq).then(response => {
            return getByPath(response, 'object.board', null)
        })
    }

    readPageDetail(pageSequence: number): Promise<Board> {
        return this.wrapPendingPromise(bfile.readPage(this.id, pageSequence)).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    getCacheSignature(signatureSequence: number): BoardSignature {
        let matchSignatures: BoardSignature[] = this._boardCache.cacheSignatures([signatureSequence]) || [];
        if (matchSignatures.length === 0) {
            matchSignatures = this._boardCache.cacheFeedSignatures([signatureSequence]) || [];
        }
        return matchSignatures.length > 0 ? matchSignatures[0] : null;
    }

    readSignatureDetail(signatureSequence: number): Promise<Board> {
        let spath: MxSPath = `signatures[sequence=${signatureSequence}]`;
        if (this.isSubscribed && this._boardCache.cacheFile(spath, true)) {
            return Promise.resolve(this._boardCache.cacheFile(spath));
        }

        return this.wrapPendingPromise(bsignature.readSignature(this.id, signatureSequence)).then(response => {
            let fileBoard: Board = getByPath(response, 'object.board', null);
            if (fileBoard) {
                this._boardCache.addCacheFile(spath, fileBoard);
            }
            return fileBoard;
        })
    }

    readTransactionDetail(transactionSequence: number): Promise<Board> {
        return this.wrapPendingPromise(btransaction.readTransaction(this.id, transactionSequence)).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    readOnGoingSignatures(): Promise<Board> {
        if (!this._boardCache.board.total_signatures) {
            return Promise.resolve({id: this.id});
        }
        return this.wrapPendingPromise(bsignature.readOnGoingSignatures(this.id)).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    readOnGoingTransactions(): Promise<Board> {
        return this.wrapPendingPromise(btransaction.readOnGoingTransactions(this.id)).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    readOnGoingDelegateFeeds(): Promise<Board> {
        return this.wrapPendingPromise(bboard.readOnGoingDelegateFeeds(this.id)).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    readUserActivities(): Promise<Board> {
        if (this.isSubscribed && this._boardCache.cacheUserActivities()) {
            return Promise.resolve({
                id: this.id,
                user_activities: this._boardCache.cacheUserActivities(),
            });
        }

        return this.wrapPendingPromise(bboard.readUserActivities(this.id)).then((response: ClientResponse) => {
            let activities: BoardUserActivity[] = getByPath(response, 'object.board.user_activities', []);
            this._boardCache.addCacheUserActivities(activities);
            return getByPath(response, 'object.board');
        });
    }

    readField(fieldName: string, seqs: number[]): Promise<Board> {
        return bboard.readBoardByField(this.id, fieldName, seqs).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    readBoardResourceBySequence(seqs: number[]): Promise<Board> {
        const cacheResources = this._boardCache.cacheResources(seqs)
        if (this.isSubscribed && cacheResources?.length === seqs?.length) {
            return Promise.resolve({
                id: this.id,
                resources: cacheResources,
            });
        }

        return this.readField('resources', seqs).then(board => {
            this._boardCache.addCacheResources(board.resources)
            return board
        });
    }

    updateBasicInfo(board: Board, suppressFeed?: boolean): Promise<Board> {
        return bboard.updateBoard(this.id, board, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    inviteMember(users: UserIdentity[], option?: MxInviteMemberOption, teams?: Group[]): Promise<Board> {
        return bboard.inviteUser(this.id, users, null, teams, option).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    inviteGroup(group: Group, option?: MxInviteMemberOption): Promise<Board> {
        return bboard.inviteUser(this.id, [], group, [], option).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    inviteGroups(groups: Group[], option?: MxInviteMemberOption): Promise<Board> {
        return bboard.inviteGroups(this.id, groups, option).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    removeMember(seqs: number | number[], suppressFeed?: boolean): Promise<Board> {
        return bboard.removeUser(this.id, seqs, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    removeTeam(teamSeqs: number[], suppressFeed?: boolean): Promise<Board> {
        return bboard.removeTeam(this.id, teamSeqs, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    removeMemberAndTeam(userSeqs: number[], teams: BoardUser[], suppressFeed?: boolean): Promise<Board> {
        return bboard.removeUserAndTeam(this.id, userSeqs, teams, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    setOwner(boardUserSequence: number): Promise<Board> {
        return bboard.setOwner(this.id, boardUserSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    setBoardType(type: BoardType): Promise<Board> {
        return bboard.setBoardType(this.id, type).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateMemberAccessType(seqs: number | number[], type: BoardAccessType): Promise<Board> {
        return bboard.updateUserAccessType(this.id, seqs, type).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateTypeIndication(): Promise<Board> {
        return bboard.updateTypeIndication(this.id).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateBoardProperties(properties: BoardProperty[]): Promise<Board> {
        return bboard.updateBoardProperties(this.id, properties).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });

    }

    createComment(comment: BoardComment, opt?: MxCreateCommentOption): Promise<Board> {
        return bcomment.createComment(this.id, comment, opt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateComment(commentSequence: number, comment: BoardComment, isUpdateUrlPreview?: boolean): Promise<Board> {
        return bcomment.updateComment(this.id, commentSequence, comment, isUpdateUrlPreview).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteComment(commentSequence: number, noFeed?: boolean, isDeleteUrlPreview?: boolean): Promise<Board> {
        return bcomment.deleteComment(this.id, commentSequence, noFeed, isDeleteUrlPreview).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createThreadComment(obj: MxBaseObject, comment: BoardComment): Promise<Board> {
        return bthread.createThreadComment(this.id, obj, comment).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateThreadComment(obj: MxBaseObject, commentSequence: number, comment: BoardComment): Promise<Board> {
        return bthread.updateThreadComment(this.id, obj, commentSequence, comment).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteThreadComment(obj: MxBaseObject, commentSequence: number): Promise<Board> {
        return bthread.deleteThreadComment(this.id, obj, commentSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createPositionComment(pageSequence: number, comment: BoardComment): Promise<Board> {
        return bcomment.createPositionComment(this.id, pageSequence, comment).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updatePositionComment(pageSequence: number, commentSequence: number, comment: BoardComment): Promise<Board> {
        return bcomment.updatePositionComment(this.id, pageSequence, commentSequence, comment).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deletePositionComment(pageSequence: number, commentSequence: number): Promise<Board> {
        return bcomment.deletePositionComment(this.id, pageSequence, commentSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createFolder(name: string, parentFolderPath?: MxSPath, folderType?: BoardFolderType): Promise<Board> {
        return bfile.createFolder(this.id, name, parentFolderPath, folderType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteFolder(folderPath: MxSPath): Promise<Board> {
        return bfile.deleteFolder(this.id, folderPath).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    renameFolder(folderPath: MxSPath, name: string): Promise<Board> {
        return bfile.renameFolder(this.id, folderPath, name).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createWhiteboard(name: string, width: number, height: number, folderPath?: MxSPath, fileUUID?: string): Promise<Board> {
        return bfile.createWhiteboard(this.id, name, width, height, folderPath, fileUUID).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }


    createWebLinkPage(url: string, fileUUID?: string, name = ''): Promise<Board> {
        return bfile.createWebLink(this.id, url, fileUUID, name).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    copyResource(fromBoardId: string, resourceSeq: number, toBoardId: string, toFolderPath: MxSPath, newFileName?: string, suppressFeed?: boolean|null): Promise<Board> {
        return bfile.copyResource(fromBoardId, resourceSeq, toBoardId, toFolderPath, newFileName, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    copyFiles(fromBoardId: string, files: string[], toBoardId: string, toFolderPath: MxSPath, newFileNames?: string[], suppressFeed?: boolean|null, opt?: MxCopyFileOption): Promise<Board> {
        return bfile.copyFiles(fromBoardId, files, toBoardId, toFolderPath, newFileNames, suppressFeed, opt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    moveFiles(files: MxSPath[], toFolderPath: MxSPath, newFileNames?: string[]): Promise<Board> {
        return bfile.moveFiles(this.id, files, toFolderPath, newFileNames).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    uploadFromRemoteUrl(url: string, name: string, authToken: string, toFolderPath?: MxSPath, contentType?: string, id?:string, suppressFeed?: boolean): Promise<Board> {
        return bfile.uploadFromRemoteUrl(this.id, url, name, authToken, toFolderPath, contentType, id, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    uploadResourceFromRemoteUrl(url: string, name: string, resType: string): Promise<Board> {
        return bfile.uploadResourceFromRemoteUrl(this.id, url, name, resType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    getBoxAccessToken(payload: string): Promise<string> {
        return bfile.getBoxAccessToken(this.id, payload).then((response: Object) => {
            if (response && response.hasOwnProperty('access_token')) {
                return response['access_token'];
            }else {
                return Promise.reject(MxErr.ServerError());
            }
        });
    }

    renameFile(filePath: MxSPath, name: string): Promise<Board> {
        return bfile.renameFile(this.id, filePath, name).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateFile(filePath: MxSPath, file: BoardPageGroup): Promise<Board> {
        return bfile.updateFile(this.id, filePath, file).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }


    deleteFiles(files: MxSPath[], noFeed?: boolean): Promise<Board> {
        return bfile.deleteFiles(this.id, files, noFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    increaseUsedCount(filePath: MxSPath[]): Promise<Board> {
        return bfile.increaseUsedCount(this.id, filePath).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updatePage(pageSequence: number, param: BoardPage): Promise<Board> {
        return bfile.updatePage(this.id, pageSequence, param).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    lockPage(pageSequence: number): Promise<Board> {
        return bfile.lockPage(this.id, pageSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    unlockPage(pageSequence: number): Promise<Board> {
        return bfile.unlockPage(this.id, pageSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updatePageEditorType(pageSequence: number, editorType: BoardEditorType): Promise<Board> {
        return bfile.updatePageEditorType(this.id, pageSequence, editorType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createPageElement(pageSequence: number, content: string, elementUuid?: string, elementType?: MxPageElementType): Promise<Board> {
        return bfile.createPageElement(this.id, pageSequence, content, elementUuid, elementType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updatePageElement(pageSequence: number, elementUuid: string, content: string): Promise<Board> {
        return bfile.updatePageElement(this.id, pageSequence, elementUuid, content).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deletePageElement(pageSequence: number, elementUuid: string): Promise<Board> {
        return bfile.deletePageElement(this.id, pageSequence, elementUuid).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    getReferenceLinkBySequence(referenceLinkSeq: number): Board {
        let board : Board = {
            id: this.id,
            reference_links: this._boardCache.cacheReferenceLinks([referenceLinkSeq])
        }
        return board;
    }

    createSignature(filePath: MxSPath, newFileName?: string, isTemplate?: boolean): Promise<Board> {
        return bsignature.createSignature(this.id, filePath, newFileName, isTemplate).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createBlankSignature(signature: BoardSignature): Promise<Board> {
        return bsignature.createBlankSignature(this.id, signature).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateSignature(signatureSequence: number, param: BoardSignature, opt?: ISignatureUpdateOption): Promise<Board> {
        return bsignature.updateSignature(this.id, signatureSequence, param, opt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteSignature(signatureSequence: number | number[], noFeed?:boolean): Promise<Board> {
        return bsignature.deleteSignature(this.id, signatureSequence, noFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    addSignatureSignee(signatureSequence: number, signees: BoardActor| BoardActor[]): Promise<Board> {
        return bsignature.addSignatureSignee(this.id, signatureSequence, signees).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateSignatureSignee(signatureSequence: number, signeeSequence: number, signee: BoardSignee, isUpdateOthers?: boolean): Promise<Board> {
        return bsignature.updateSignatureSignee(this.id, signatureSequence, signeeSequence, signee, isUpdateOthers).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateSignatureSignees(signatureSequence:number, signees: BoardSignee[], suppressFeed?: boolean): Promise<Board> {
        return bsignature.updateSignatureSignees(this.id, signatureSequence, signees, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteSignatureSignee(signatureSequence: number, signeeSequence: number | number[]): Promise<Board> {
        return bsignature.deleteSignatureSignee(this.id, signatureSequence, signeeSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    assignElementToSignee(signatureSequence: number, signeeSequence: number, elements: number[]): Promise<Board> {
        return bsignature.assignElementToSignee(this.id, signatureSequence, signeeSequence, elements).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    startSignature(signatureSequence: number): Promise<Board> {
        return bsignature.startSignature(this.id, signatureSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    submitSignature(signatureSequence: number, elements: MxSignatureElement[], keepStatusUnchanged?: boolean, jwt?: string): Promise<Board> {
        return bsignature.submitSignature(this.id, signatureSequence, elements, keepStatusUnchanged, jwt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    declineSignature(signatureSequence: number, message: string, jwt?: string): Promise<Board> {
        return bsignature.declineSignature(this.id, signatureSequence, message, jwt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createSignatureElement(signatureSequence: number, pageSequence: number, content: string, elementType: MxPageElementType, client_uuid?: string): Promise<Board> {
        return bsignature.createSignatureElement(this.id, signatureSequence, pageSequence, content, elementType, client_uuid).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    batchCreateSignatureElements(signatureSequence: number, pages: BoardPage[], suppressFeed?: boolean): Promise<Board> {
        return bsignature.batchCreateSignatureElements(this.id, signatureSequence, pages, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateSignatureElement(signatureSequence: number, pageSequence: number, client_uuid: string, content: string, ddrs?: BoardDataReference[], readOnly?: boolean): Promise<Board> {
        return bsignature.updateSignatureElement(this.id, signatureSequence, pageSequence, client_uuid, content, ddrs, readOnly).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    batchUpdateSignatureElements(signatureSequence: number, pages: BoardPage[], suppressFeed?: boolean): Promise<Board> {
        return bsignature.batchUpdateSignatureElements(this.id, signatureSequence, pages, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteSignatureElement(signatureSequence: number, pageSequence: number, client_uuid: string): Promise<Board> {
        return bsignature.deleteSignatureElement(this.id, signatureSequence, pageSequence, client_uuid).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteSignatureElements(signatureSequence: number, pages: BoardPage[]): Promise<Board> {
        return bsignature.deleteSignatureElements(this.id, signatureSequence, pages).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    uploadSignatureResourceToPage(signatureSequence: number, pageSequence: number, resourceUrl: string, resourceName: string): Promise<Board> {
        return bsignature.uploadSignatureResourceToPage(this.id, signatureSequence, pageSequence, resourceName, resourceUrl).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    copySignature(fromBoardId: string, signatureSeq: number, toBoardId: string, keepCreator?: boolean, copyWithTotalUsedCount?: boolean, isTemplate?: boolean, status?: BoardSignatureStatus, signature?: BoardSignature, copyWithWorkflow?: boolean): Promise<Board> {
        return bsignature.copySignature(fromBoardId, signatureSeq, toBoardId, keepCreator, copyWithTotalUsedCount, isTemplate, status, signature, copyWithWorkflow).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    reopenSignature(signatureSequence:number, generateReopenEvent?: boolean): Promise<Board> {
        return bsignature.reopenSignature(this.id, signatureSequence, generateReopenEvent).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    resetSignatureStatus(signatureSequence:number): Promise<Board> {
        return bsignature.resetSignatureStatus(this.id, signatureSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateSignatureViewTime(signatureSequence: number): Promise<Board> {
        return bsignature.updateSignatureViewTime(this.id, signatureSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    addSignatureAttachment(signatureSequence: number, file: MxSPath, isReply?: boolean, suppressFeed?: boolean): Promise<Board> {
        return bsignature.addSignatureAttachment(this.id, signatureSequence, file, isReply, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    removeSignatureAttachment(signatureSequence: number, referenceSequence: number, suppressFeed?: boolean): Promise<Board> {
        return bsignature.removeSignatureAttachment(this.id, signatureSequence, referenceSequence, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    replaceSignature(signatureSequence: number, fromBoardId: string, fromSignatureSequence: number, suppressFeed?: boolean): Promise<Board> {
        return bsignature.replaceSignature(this.id, signatureSequence, fromBoardId, fromSignatureSequence, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    replaceSignaturePagesFromFile(signatureSequence: number, fromBoardId: string, fromFilePath: MxSPath, suppressFeed?: boolean): Promise<Board> {
        return bsignature.replaceSignaturePagesFromFile(this.id, signatureSequence, fromBoardId, fromFilePath, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createTodo(todo: BoardTodo): Promise<Board> {
        return btodo.createTodo(this.id, todo).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteTodo(todoSequence: number): Promise<Board> {
        return btodo.deleteTodo(this.id, todoSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    copyTodo(todo: BoardTodo, toBoardId: string, suppressUserActivity?: boolean): Promise<Board> {
        return btodo.copyTodo(this.id, todo, toBoardId, suppressUserActivity).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateTodo(todoSequence: number, todo: BoardTodo, noFeed?: boolean, generateReopenEvent?: boolean): Promise<Board> {
        return btodo.updateTodo(this.id, todoSequence, todo, noFeed, generateReopenEvent).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    setTodoAssignee(todoSeq:number, boardMemberSeq:number, isReassign?: boolean, noFeed?: boolean): Promise<Board> {
        return btodo.setAssignee(this.id, todoSeq, boardMemberSeq, isReassign, noFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    setTodoAssignee2(todoSeq:number, assignee: BoardActor, isReassign?: boolean, noFeed?: boolean): Promise<Board> {
        return btodo.setAssignee2(this.id, todoSeq, assignee, isReassign, noFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    addTodoAttachment(todoSequence: number, files: MxSPath[], suppressFeed?: boolean): Promise<Board> {
        return btodo.addAttachment(this.id, todoSequence, files, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    removeTodoAttachment(todoSequence: number, referenceSequence: number, suppressFeed?: boolean): Promise<Board> {
        return btodo.removeAttachment(this.id, todoSequence, referenceSequence, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    submitTransactionStep(transaction: BoardTransaction, suppressFeed?:boolean): Promise<Board> {
        return btransaction.submitTransactionStep(this.id, transaction, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board')
        })
    }

    createTransaction(transaction: BoardTransaction, suppressFeed?: boolean): Promise<Board> {
        return btransaction.createTransaction(this.id, transaction, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board')
        })
    }

    updateTransaction(transactionSeq: number, transaction: BoardTransaction, opt?: ITransactionUpdateOption): Promise<Board> {
        return btransaction.updateTransaction(this.id, transactionSeq, transaction, opt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board')
        })
    }

    deleteTransaction(transactionSeq: number | number[], suppressFeed?: boolean): Promise<Board> {
        return btransaction.deleteTransaction(this.id, transactionSeq, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board')
        })
    }

    reopenTransactionStep(transactionSeq: number, stepSeq: number|number[]): Promise<Board> {
        return btransaction.reopenTransactionStep(this.id, transactionSeq, stepSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board')
        })
    }

    createTransactionAttachmentFromResource(resource: IResourceInfo, toBoardId: string, toTransactionSeq: number, opt?: ICreateTransactionAttachmentParam): Promise<Board> {
        return btransaction.createTransactionAttachmentFromResource(this.id, resource, toBoardId, toTransactionSeq, opt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createTransactionAttachmentFromFile(files: MxSPath[], toBoardId: string, toTransactionSeq: number, opt?: ICreateTransactionAttachmentParam): Promise<Board> {
        return btransaction.createTransactionAttachmentFromFile(this.id, files, toBoardId, toTransactionSeq, opt).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createTransactionAttachmentFromRemoteUrl(transactionSeq: number, url: string, name: string, authToken: string, contentType?: string): Promise<Board> {
        return btransaction.createTransactionAttachmentFromRemoteUrl(this.id, transactionSeq, url, name, authToken, contentType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createTransactionAttachmentReply(transactionSequence: number, file: MxSPath, suppressFeed?: boolean): Promise<Board> {
        return btransaction.createTransactionAttachmentReply(this.id, transactionSequence, file, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    removeTransactionAttachment(transactionSeq: number, referenceSeq: number|number[], setLastModifiedTime?: boolean, suppressFeed?: boolean): Promise<Board> {
        return btransaction.removeAttachment(this.id, transactionSeq, referenceSeq, setLastModifiedTime, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    uploadTransactionResourceFromUrl(transactionSeq: number, url: string, name: string, client_uuid?: string): Promise<Board> {
        return btransaction.uploadTransactionResourceFromUrl(this.id, transactionSeq, url, name, client_uuid).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    copyTransactions(transactionSeqs: number[], toBoardId: string, keepCreator?: boolean, copyWithTotalUsedCount?: boolean, copyWithLastModifiedTime?: boolean, suppressUserActivity?: boolean, forceGenerateCreateFeed?: boolean, copyWithWorkflow?: boolean): Promise<Board> {
        return btransaction.copyTransactions(this.id, transactionSeqs, toBoardId, keepCreator, copyWithTotalUsedCount, copyWithLastModifiedTime, suppressUserActivity, forceGenerateCreateFeed, copyWithWorkflow).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    reopenTransaction(transactionSeq:number, generateReopenEvent?: boolean, status?: TransactionStatus): Promise<Board> {
        return btransaction.reopenTransaction(this.id, transactionSeq, generateReopenEvent, status).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    resetTransactionStatus(transactionSeq:number, steps?: TransactionStep[]): Promise<Board> {
        return btransaction.resetTransactionStatus(this.id, transactionSeq, steps).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateTransactionViewTime(transactionSeq: number, stepSeq: number): Promise<Board> {
        return btransaction.updateTransactionViewTime(this.id, transactionSeq, stepSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateTransactionReminder(transactionSeq: number, reminderTime: number): Promise<Board> {
        return btransaction.updateTransactionReminder(this.id, transactionSeq, reminderTime).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    readPins(startSequence?: number, beforeSize?: number, afterSize?: number): Promise<Board> {
        return this.wrapPendingPromise(bpin.readPinsByPagination(this.id, startSequence, beforeSize, afterSize)).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createPin(baseObject: MxBaseObject): Promise<Board> {
        return bpin.createPin(this.id, baseObject.spath).then((response: ClientResponse) => {
            return getByPath(response, 'object.board')
        });
    }

    deletePin(baseObject: MxBaseObject): Promise<Board> {
        return bpin.deletePin(this.id, baseObject.spath).then((response: ClientResponse) => {
            return getByPath(response, 'object.board')
        });
    }

    getPinBaseObject(pinSeq: number): MxBaseObject {
        return this._boardCache.getPinBaseObject(pinSeq);
    }

    getTag(key: string): string|number {
        return getTagByKey(key, this.board.tags);
    }

    getRawTag(key: string): BoardTag {
        let ret: BoardTag;
        this.board.tags && this.board.tags.forEach(tag => {
            if (!tag.is_deleted && tag.name === key) {
                ret = tag;
            }
        });
        return ret;
    }

    createOrUpdateTag(key: string, val: string | number, useKeyAsClientUuid?: boolean): Promise<void> {
        return btag.createOrUpdateTag(key, val, this.board.tags, btag.MxTagType.BOARD, this.id, useKeyAsClientUuid).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    createOrUpdateTags(tags: Object): Promise<void> {
        return btag.createOrUpdateTags(tags, this.board.tags, btag.MxTagType.BOARD, this.id).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    deleteTag(key: string): Promise<void> {
        return btag.deleteTag(key, this.board.tags, btag.MxTagType.BOARD, this.id).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    createOrUpdateWorkflowVariables(workflowSeq: number, variables: WorkflowVar[]): Promise<Board> {
        return bworkflow.createOrUpdateVariables(this.id, workflowSeq, variables).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteWorkflowVariable(workflowSeq: number, variableName: string): Promise<void> {
        let workflows: BoardWorkflow[] = this._boardCache.board.workflows;
        let allVars: WorkflowVar[] = (workflows && workflows.length > 0) ? workflows[0].variables : [];
        return bworkflow.deleteVariable(this.id, workflowSeq, variableName, allVars).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    updateWorkflowStep(workflow: BoardWorkflow): Promise<Board> {
        return bworkflow.updateWorkflowStep(this.id, workflow).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    restartWorkflow(workflowSeq: number): Promise<Board> {
        return bworkflow.restartWorkflow(this.id, workflowSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    completeWorkflow(workflowSeq: number): Promise<Board> {
        return bworkflow.completeWorkflow(this.id, workflowSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    cancelWorkflow(workflowSeq: number): Promise<Board> {
        return bworkflow.cancelWorkflow(this.id, workflowSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    reopenWorkflowStep(workflowSeq: number, stepSeq: number): Promise<Board> {
        return bworkflow.reopenWorkflowStep(this.id, workflowSeq, stepSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

  skipWorkflowStep(workflowSeq: number, stepSeq: number): Promise<Board> {
        return bworkflow.skipWorkflowStep(this.id, workflowSeq, stepSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createWorkflow(workflow: BoardWorkflow, suppressFeed?: boolean): Promise<Board> {
        return bworkflow.createWorkflow(this.id, workflow, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateWorkflow(workflow: BoardWorkflow, suppressFeed?: boolean): Promise<Board> {
        return bworkflow.updateWorkflow(this.id, workflow, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateWorkflowStatus(workflow: BoardWorkflow, suppressFeed?: boolean): Promise<Board> {
        return bworkflow.updateWorkflowStatus(this.id, workflow, suppressFeed).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteWorkflow(seq: number): Promise<Board> {
        return bworkflow.deleteWorkflow(this.id, seq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    copyWorkflow(workflow: BoardWorkflow, toBoardId?: string): Promise<Board> {
        return bworkflow.copyWorkflow(this.id, workflow, toBoardId).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        }); 
    }

  copyAsWorkflowTemplate(workflow: BoardWorkflow, createAsTemp?: boolean): Promise<CacheObject> {
    return bworkflow.copyAsWorkflowTemplate(this.id, workflow, createAsTemp).then((response: ClientResponse) => {
      return getByPath(response, 'object');
    });
  }

    copyAsMilestoneTemplate(workflow: BoardWorkflow, fromMilestone: number, toMilestone: number, createAsTemp?: boolean): Promise<CacheObject> {
        return bworkflow.copyAsWorkflowTemplate(this.id, workflow, createAsTemp, fromMilestone, toMilestone).then((response: ClientResponse) => {
            return getByPath(response, 'object');
        });
    }

    createWaitingUser(name?: string, email?: string): Promise<Board> {
        return bmeet.createWaitingUser(this.id, name, email).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateWaitingUser(users: ActionUserRoster[]): Promise<Board> {
        return bmeet.updateWaitingUser(this.id, users).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }to

    deleteWaitingUser(): Promise<Board> {
        return bmeet.deleteWaitingUser(this.id).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateNotificationLevel(level: NotificationLevel): Promise<User> {
        return bboard.updateNotificationLevel(this.id, level).then((response: ClientResponse) => {
            return getByPath(response, 'object.user');
        });
    }

    markFeedAsUnRead(feedSeq: number): Promise<Board> {
        return bboard.markFeedAsUnRead(this.id, feedSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createViewToken(accessType: BoardAccessType, actorAs?: User): Promise<Board> {
        return bboard.createViewToken(this.id, accessType, actorAs).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    createResourceViewToken(resSeq: number): Promise<Board> {
        return bboard.createResourceViewToken(this.id, resSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    transferActions(param: ITransferActionParam): Promise<Board> {
        return bboard.transferActions(this.id, param).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    resendInviteEmailSms(userId: string, isSms: boolean): Promise<Board> {
        return bboard.resendInviteEmailSms(this.id, userId, isSms).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    bulkResendInviteEmailSms(userIds: string[], isSms: boolean): Promise<Board> {
        return bboard.bulkResendInviteEmailSms(this.id, userIds, isSms).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    resendInviteEmailSmsForPrivacy(user: UserIdentity): Promise<void> {
        return brelation.createRelation(user, true, undefined, true, false, true).then(() => {});
    }

    resendTransactionReminder(transaction: BoardTransaction): Promise<void> {
        return btransaction.resendTransactionReminder(this.id, transaction).then(() => {});
    }

    resendSignatureReminder(signature: BoardSignature): Promise<void> {
        return bsignature.resendSignatureReminder(this.id, signature).then(() => {});
    }

    createInvitationViewToken(userId: string): Promise<Board> {
        return bboard.createInvitationViewToken(this.id, userId).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    renewWorkspaceId(boardViewToken: string): Promise<Board> {
        return bboard.renewBoardWorkspaceId(this.id, boardViewToken).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    renewPassword(): Promise<Board> {
        return bboard.renewBoardPassword(this.id).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateFeedReaction(feedSeq: number, reaction: string, suppressNotification?: boolean): Promise<Board> {
        return bboard.updateFeedReaction(this.id, feedSeq, reaction, suppressNotification).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    makeCreateWebdocUrl(name: string, folderSpath?: MxSPath): string {
        let folder =''
        if (folderSpath){
            let paths = folderSpath.split('.');
            paths = paths.map((path)=>{
                return parseSPath(path).attrVal
            })
            folder = '&destfolder='+paths.join(',')
        }

        return `${getContextPath()}/board/upload?type=web&id=${this.id}${folder}&name=${encodeURIComponent(name)}.html&newfile&newfilename=${encodeURIComponent(name)}`
    }

    makeUpdateWebdocUrl(name: string, fileSeq: number, pageSeq: number): string {
        return `${getContextPath()}/board/upload?type=vector&id=${this.id}&name=${encodeURIComponent(name)}.html&seq=${pageSeq}&destfile=${fileSeq}`
    }

    makeDownloadFolderUrl(folderSequence: number, fileName: string): string {
        return `${getContextPath()}/board/${this.id}/download?type=zip&folders=${folderSequence}&d=${fileName}`
    }

    makeDownloadFilesUrl(filesSequences: number[], fileName: string, type: "pdf" | "zip"): string {
        let name = type === 'pdf' ? 'pages': 'files';
        return `${getContextPath()}/board/${this.id}/download?type=${type}&${name}=${encodeURIComponent(fileName)}&files=${filesSequences.join(',')}`;
    }

    makeDownloadResourceUrl(resourceSequence: number, fileName: string): string {
        return `${getContextPath()}/board/${this.id}/${resourceSequence}?d=${encodeURIComponent(fileName)}`
    }

    makeDownloadZipResourceUrl(resources: number[], fileName: string): string {
        return `${getContextPath()}/board/${this.id}/downloadzip?resource=${resources.join(',')}&d=${encodeURIComponent(fileName)}`
    }

    makeUploadFileUrl(fileName: string, folderSpath?: MxSPath, resourceId?:string): string {
        let folder =''
        if (folderSpath){
            let paths = folderSpath.split('.');
            paths = paths.map((path)=>{
                return parseSPath(path).attrVal
            })
            folder = '&destfolder='+paths.join(',')
        }
        let newFile = 'newfile'
        if (resourceId) {
            newFile += `=${resourceId}`
        }
        let url = `${getContextPath()}/board/upload?${newFile}&id=${this.id}&name=${encodeURIComponent(fileName)}${folder}&type=original`
        if(this._option && this._option.viewToken)
            return `${url}&t=${this._option.viewToken}`;
        return url;
    }

    makeUploadToPageUrl(pageSequence: number, fileName: string): string {
        return `${getContextPath()}/board/upload?id=${this.id}&seq=${pageSequence}&name=${encodeURIComponent(fileName)}`;
    }

    makeUploadToSignatureUrl(signatureSequence: number, pageSequence: number, fileName: string): string {
        return `${getContextPath()}/board/${this.id}/signature/${signatureSequence}/${pageSequence}/${encodeURIComponent(fileName)}`;
    }

    makeUploadToSignatureSigneeUrl(signatureSequence: number, signeeSequence: number, fileName: string, style?: SignatureStyle, initials?: string): string {
        let url = `${getContextPath()}/board/${this.id}/signature/${signatureSequence}/${signeeSequence}?type=signature&name=${encodeURIComponent(fileName)}`;
        if (style !== undefined) {
            url += `&style=${style}`;
        }

        if (initials !== undefined) {
            url += `&initials=${initials}`;
        }

        return url;
    }

    makeUploadToTodoUrl(todoSequence: number, fileName: string): string {
        return `${getContextPath()}/board/${this.id}/todo/${todoSequence}/${encodeURIComponent(fileName)}?type=original`;
    }

    makeUploadToTransactionUrl(transactionSequence: number, fileName: string, isSupportFile?: string, customData?: string): string {
        let url = `${getContextPath()}/board/${this.id}/attach/transaction/${transactionSequence}/${encodeURIComponent(fileName)}?type=original`;
        if (isSupportFile) {
            url += '&reference_type=support'
        }
        if (customData) {
            url += `&custom_data=${encodeURIComponent(customData)}`
        }
        return url
    }

    makeUploadToTransactionResourceUrl(transactionSequence: number, name: string): string {
        return `${getContextPath()}/board/upload/?request_type=transaction&transaction_seq=${transactionSequence}&board_id=${this.id}&name=${encodeURIComponent(name)}`;
    }

    makeUploadBoardCoverUrl(fileName: string, type?:string): string {
        type = type || 'cover'
        return `${getContextPath()}/board/upload/?type=${type}&id=${this.id}&name=${encodeURIComponent(fileName)}`;
    }

    makeUploadBoardBannerUrl(fileName: string, isForMobile?: boolean): string {
        const t = isForMobile ? 'banner_mobile' : 'banner'
        return `${getContextPath()}/board/upload/?type=${t}&id=${this.id}&name=${encodeURIComponent(fileName)}`;
    }

    abortAllPendingRequests(): void {
        for (let p of this._pendingPrimiseIds) {
            try {
                Ajax.abortRequest(p);
            }catch(e) {
                // ignore
            }
        }
        this._pendingPrimiseIds = [];
    }

    private wrapPendingPromise(pro: Promise<any>): Promise<any> {
        this._pendingPrimiseIds.push(pro['id']);
        return pro;
    }

    createShareViewToken(memberOnly = false, code = ''): Promise<Board> {
        return bboard.createShareViewToken(this.id, memberOnly, code).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateShareViewToken(sequence: number, memberOnly = false, code = ''): Promise<Board> {
        return bboard.updateShareViewToken(this.id, sequence, memberOnly, code).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    joinBoardByViewToken(viewToken: string, user: User, verifyCode?: string, googleJWT?: string, appleJWT?: string, viewTokenCode?: string): Promise<User> {
        return bboard.joinBoardByViewToken(viewToken, user, verifyCode, googleJWT, appleJWT, viewTokenCode).then((response: ClientResponse) => {
            return getByPath(response, 'object.user');
        });
    }

    joinBoardByManagementUser(managementUser: UserIdentity): Promise<Board> {
        return bboard.joinBoardByManagementUser(this.id, managementUser).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    readViewTokens (): Promise<Board> {
        return bboard.readViewTokens(this.id).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateRequestingUser (sequence: number, approved: boolean, suppressFeed?: boolean, isInvalid?: boolean): Promise<Board> {
        return bboard.updateRequestingUser(this.id, sequence, approved, suppressFeed, isInvalid).then(response=>{
            return getByPath(response, 'object.board');
        })
    }
    inviteRequestingUserToOrg (sequence: number, isClient: boolean): Promise<Group> {
        return bboard.inviteRequestingUser(this.id, sequence, isClient).then(response=>{
            return getByPath(response, 'object.group');
        })
    }

    downloadBoardFolder (type: string, fileName: string, folder: number, boardFiles: Board, binderViewToke?:string): Promise<Blob> {
        return bboard.downloadBoardFolder(this.id, type, fileName, folder, boardFiles, binderViewToke)
    }

    getWorkflowDetail(_cover?: Board): Board {
        let workflowDetail: Board = {}
        let boardCover = _cover || this._boardCache.basicInfo()
        if (!boardCover.workflows || boardCover.workflows.length === 0) {
            return workflowDetail
        }

        workflowDetail = cloneObject(boardCover)
        workflowDetail.transactions = []
        workflowDetail.signatures = []
        workflowDetail.todos = []

        let workflow: BoardWorkflow = boardCover.workflows[0]
        let needReadOutputObjects = false

        for(let step of workflow.steps||[]) {
            let transactionSeq = step?.output?.board?.transactions?.[0]?.sequence
            let signatureSeq = step?.output?.board?.signatures?.[0]?.sequence
            let todoSeq = step?.output?.board?.todos?.[0]?.sequence

            if (transactionSeq) {
                let foundObjs = this._boardCache.cacheTransactions([transactionSeq], true)
                if (foundObjs?.length) {
                    workflowDetail.transactions.push(foundObjs[0])
                }else {
                    needReadOutputObjects = true
                }
            }

            if (signatureSeq) {
                let foundObjs = this._boardCache.cacheSignatures([signatureSeq], true)
                if (foundObjs?.length) {
                    workflowDetail.signatures.push(foundObjs[0])
                }else {
                    needReadOutputObjects = true
                }
            }

            if (todoSeq) {
                let foundObjs = this._boardCache.cacheTodos([todoSeq], true)
                if (foundObjs?.length) {
                    workflowDetail.todos.push(foundObjs[0])
                }else {
                    needReadOutputObjects = true
                }
            }
        }

        if (needReadOutputObjects) {
            workflowDetail['needReadOutputObjects'] = true
        }

        return workflowDetail
    }

    readWorkflow(workflowSeq: number): Promise<BoardWorkflow> {
        if (this.isSubscribed) {
            const cacheFlow = this.basicInfo.workflows?.find(wl => wl.sequence === workflowSeq)
            if (cacheFlow) {
                return Promise.resolve(cloneObject(cacheFlow))
            }
        }

        return bboard.readBoardByField(this.id, 'workflows', [workflowSeq]).then(response => {
            return getByPath(response, 'object.board.workflows.0');
        })
    }

    readWorkflowDetail(): Promise<Board> {
        return new Promise(async (resolve, reject) => {
            let boardCover = this._boardCache.basicInfo()
            if (boardCover.is_deleted === true) {
                resolve({id: this.id, is_deleted: true})
                return
            }

            if (!boardCover.users) {
                boardCover = await this.wrapPendingPromise(this.readCover())
            }

            if (!boardCover.workflows || boardCover.workflows.length === 0) {
                reject('no workflows found in board')
            }

            let workflowDetail = this.getWorkflowDetail(boardCover)
            if (workflowDetail['needReadOutputObjects']) {
                const board = await this.wrapPendingPromise(this.readFields(['/board/transactions', '/board/signatures', '/board/todos']))
                workflowDetail.transactions = board.transactions
                workflowDetail.signatures = board.signatures
                workflowDetail.todos = board.todos
            }

            return resolve(workflowDetail)
        })
    }

    subscribeWorkflowDetail(cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeWorkflowDetail((board) => {
            if (board.workflows || board.transactions || board.signatures || board.todos) {
                const workflowDetail = this.getWorkflowDetail()
                cb(workflowDetail)
            }
        });
    }

    subscribeWorkflows(cb: MxCallback<Board>): MxSubscription {
        this.subscribeBoard();
        return this._boardSubscriber.subscribeWorkflows((board) => {
            cb(board)
        });
    }

}
