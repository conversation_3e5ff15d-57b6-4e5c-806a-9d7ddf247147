import { CacheObject } from '../proto/generated/CacheObject';
import { ClientResponse } from '../proto/generated/ClientResponse';
import { GroupUserRole } from '../proto/generated/GroupUserRole';
import { GroupIntegration } from '../proto/generated/GroupIntegration';
import { GroupUser } from '../proto/generated/GroupUser';
import { Board } from '../proto/generated/Board';
import { UsageStatistics } from '../proto/generated/UsageStatistics';
import { UserActivityLog } from '../proto/generated/UserActivityLog';
import { Group } from './../proto/generated/Group';
import { RoutingConfig } from '../proto/generated/RoutingConfig';
import { SystemSamlService } from '../proto/generated/SystemSamlService';
import { RoutingChannel } from '../proto/generated/RoutingChannel';
import { UserGroup } from '../proto/generated/UserGroup';
import { GroupAccessType } from '../proto/generated/GroupAccessType';
import { UserRelation } from '../proto/generated/UserRelation';
import { GroupUserRoleType } from '../proto/generated/GroupUserRoleType';
import { BoardAccessType } from '../proto/generated/BoardAccessType';
import { GroupBoard } from '../proto/generated/GroupBoard';
import { GroupCapability } from '../proto/generated/GroupCapability';
import { OutOfOfficeStatus } from '../proto/generated/OutOfOfficeStatus';
import { BoardType } from '../proto/generated/BoardType';
import { GroupUserSetting } from '../proto/generated/GroupUserSetting';
import { GroupType } from '../proto/generated/GroupType';
import { User } from '../proto/generated/User';
import { UserCap } from '../proto/generated/UserCap';
import { BoardRoutingStatus } from '../proto/generated/BoardRoutingStatus';
import { Property } from '../proto/generated/Property';

import { GroupCache } from './../data/cache/groupCache';
import { MxGroupSubscriber } from './../data/subscribe/groupSubscriber';
import { ICreateTeamParam, MxGroup } from "../api/mxGroup";
import {
    UserIdentity,
    MxCallback,
    MxSubscription,
    MxGroupMemberFilter,
    MxUpdateEmailPhoneNumParam,
    MxRoutingChannelType,
    MxGroupManagementFilter,
    MxResetPasswordChannel,
    ConnectorSFDCConfig,
    ConnectorGlobalRelayConfig,
    ConnectorHubSpotConfig, ConnectorDynamicsConfig, FilevineConfig, SmarshConfig, WealthBoxConfig,
    MxReadGroupMemberOption
} from '../api/defines';
import { getAllTags, getTagByKey, filterDeleted } from '../data/cache/common';
import { currentOrgId, currentUser } from '../data/cache/cacheMgr';
import { getByPath, mxLogger } from '../util';
import { getContextPath } from './config';
import * as btag from '../biz/tag';
import * as bgroup from '../biz/group';
import * as baudit from '../biz/audit';
import * as brelation from '../biz/relation';
import * as bacdsr from '../biz/acdsr';
import * as bteam from '../biz/team';
import * as bstripe from '../biz/stripe';
import * as bconnector from '../biz/connector';
import * as bworkflow from '../biz/workflow';
import * as buser from '../biz/user';
import {ConnectorScheduleType} from "../biz/connector";
import moment from 'moment';


export class MxGroupImpl implements MxGroup {
    private _groupCache: GroupCache;
    private _groupSubscriber: MxGroupSubscriber;
    private _publicWorkflowTemplatesCount: number;
    private _isAnonymous?: boolean;

    constructor(groupCache: GroupCache, isAnonymous?: boolean) {
        this._groupCache = groupCache;
        this._groupSubscriber = new MxGroupSubscriber(this._groupCache);
        this._publicWorkflowTemplatesCount = 0;
        this._isAnonymous = isAnonymous

        this.mergeDefaultOrgConfig()
    }

    get id(): string {
        return this._groupCache.group.id;
    }

    get group(): Group {
        return this._groupCache.group;
    }

    get basicInfo(): Group {
        return this._groupCache.group;
    }

    get tags(): Object {
        return getAllTags(this.group.tags);
    }

    get roles(): GroupUserRole[] {
        return (this.group.roles || []).filter(filterDeleted);
    }

    get integrations(): GroupIntegration[] {
        return (this.group.integrations || []).filter(filterDeleted);
    }

    get routingConfig(): RoutingConfig {
        return this._groupCache.group.routing_config;
    }

    get routingRequests(): Group {
        return this._groupCache.routingRequestsGroup;
    }

    get publicWorkflowTemplatesCount(): number {
        return this._publicWorkflowTemplatesCount || 0
    }

    get isAnonymous(): boolean {
        return this._isAnonymous
    }

    subscribeBasicInfo(cb: MxCallback<Group>): MxSubscription {
        return this._groupSubscriber.subscribeBasicInfo(cb);
    }

    subscribeRoutingConfig(cb: MxCallback<RoutingConfig>): MxSubscription {
        return this._groupSubscriber.subscribeRoutingConfig(cb);
    }

    subscribeTeams(cb: MxCallback<UserGroup[]>): MxSubscription {
        return this._groupSubscriber.subscribeTeams(cb);
    }

    subscribeGroupBoards(cb: MxCallback<GroupBoard[]>): MxSubscription {
        return this._groupSubscriber.subscribeGroupBoards(cb);
    }

    updateGroup(info: Group): Promise<Group> {
        return bgroup.updateGroup(this.id, info).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    getMemberSequenceByUserId(userId: string): number {
        return this._groupCache.getMemberSequenceByUserId(userId);
    }

    readMember(user: UserIdentity, opt?: MxReadGroupMemberOption): Promise<Group> {
        return bgroup.readMember(this.id, user, opt).then((response: ClientResponse) => {
            let groupRes = getByPath(response, 'object.group');
            this._groupCache.updateGroupMemberCache(groupRes);
            return groupRes;
        });
    }

    readMembers(users: UserIdentity[], opt?: MxReadGroupMemberOption): Promise<Group> {
        return bgroup.readMembers(this.id, users, opt).then((response: ClientResponse) => {
            let groupRes = getByPath(response, 'object.group');
            this._groupCache.updateGroupMemberCache(groupRes);
            return groupRes;
        });
    }

    readMemberDetail(user: UserIdentity, includeRelatedRelation?: boolean, includeRelatedUserFlowTemplate?: boolean, includeRelatedUserContentLibrary?: boolean, includeRelatedWorkflowTemplateLibrary?: boolean): Promise<ClientResponse> {
        return bgroup.readMemberDetail(this.id, user, includeRelatedRelation, includeRelatedUserFlowTemplate, includeRelatedUserContentLibrary, includeRelatedWorkflowTemplateLibrary).then((response: ClientResponse) => {
            return response;
        });
    }

    readMemberMeetings(user: UserIdentity): Promise<CacheObject> {
        return bgroup.readMemberMeetings(this.id, user).then((response: ClientResponse) => {
            return getByPath(response, 'object');
        });
    }

    readMembersByPagination(startSequence: number, size: number): Promise<Group> {
        return bgroup.readMembersByPagination(this.id, startSequence, size).then((response: ClientResponse) => {
            let groupRes = getByPath(response, 'object.group');
            this._groupCache.updateGroupMemberCache(groupRes);
            return groupRes;
        });
    }

    searchMembers(startSequence: number, size: number, filter?: MxGroupMemberFilter): Promise<Group> {
        let oldP = bgroup.searchGroupMembers(this.id, startSequence, size, filter);
        let newP = new Promise((resolve, reject) => {
            oldP.then((response: ClientResponse) => {
                let groupRes = getByPath(response, 'object.group');
                this._groupCache.updateGroupMemberCache(groupRes);
                resolve(groupRes);
            }).catch(e => reject(e));
        });

        newP['id'] = oldP['id'];
        return newP;
    }

    searchUserBoardsByMember(userId: string, searchKey: string, startIndex: number, pageSize: number): Promise<User> {
        let oldP = bgroup.searchUserBoardsByMember(userId, searchKey, startIndex, pageSize);
        let newP = new Promise((resolve, reject) => {
            oldP.then((response: ClientResponse) => {
                resolve(getByPath(response, 'object.user'));
            }).catch(e => reject(e));
        });

        newP['id'] = oldP['id'];
        return newP;
    }

    searchSharedUserBoards(userId1: string, userId2: string): Promise<User> {
        let oldP = bgroup.searchSharedUserBoards(this.id, userId1, userId2);
        let newP = new Promise((resolve, reject) => {
            oldP.then((response: ClientResponse) => {
                resolve(getByPath(response, 'object.user'));
            }).catch(e => reject(e));
        });

        newP['id'] = oldP['id'];
        return newP;
    }

    inviteMember(user: GroupUser | GroupUser[], updateExisting?: boolean, suppressEmailSms?: boolean): Promise<Group> {
        return bgroup.inviteMember(this.id, user, updateExisting, suppressEmailSms).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    deleteMember(memberSequence: number): Promise<void> {
        return bgroup.deleteMember(this.id, memberSequence).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    deleteMemberFutureMeetings(userId: string): Promise<void> {
        return bgroup.deleteMemberFutureMeetings(this.id, userId).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    deleteMemberScheduleBoards(userId: string): Promise<void> {
        return bgroup.deleteMemberScheduleBoards(this.id, userId).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    activateMember(memberSequence: number): Promise<Group> {
        return bgroup.activateMember(this.id, memberSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    deactivateMember(memberSequence: number): Promise<void> {
        return bgroup.deactivateMember(this.id, memberSequence).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    removeMemberAvatar(memberSequence: number): Promise<void> {
        return bgroup.removeMemberAvatar(this.id, memberSequence).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    join(): Promise<Group> {
        throw new Error("Method not implemented.");
    }

    joinByInvitationToken(token: string): Promise<Group> {
        throw new Error("Method not implemented.");
    }

    leave(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    resetMemberPassword(memberSequence: number, channel?: MxResetPasswordChannel): Promise<void> {
        return bgroup.resetGroupUserPassword(this.id, memberSequence, channel).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    resendInviteEmail(param: UserIdentity): Promise<void> {
        return bgroup.resendInviteEmail(this.id, param).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    resendInviteSms(param: UserIdentity): Promise<void> {
        return bgroup.resendInviteSms(this.id, param).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    updateMemberProfile(memberSequence: number, user: GroupUser): Promise<Group> {
        return bgroup.updateGroupUser(this.id, memberSequence, user).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateMemberEmailPhoneNum(userId: string, param: MxUpdateEmailPhoneNumParam): Promise<Group> {
        return bgroup.updateMemberEmailPhoneNum(this.id, userId, param).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateMemberDistributionList(userId: string, roles: number[]): Promise<Group> {
        return bgroup.updateMemberDistributionList(this.id, userId, roles).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    createRole(role: GroupUserRole): Promise<Group> {
        return bgroup.createRole(this.id, role).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateRole(roleSequence: number, role: GroupUserRole): Promise<Group> {
        return bgroup.updateRole(this.id, roleSequence, role).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    deleteRole(roleSequence: number): Promise<Group> {
        return bgroup.deleteRole(this.id, roleSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateMemberRole(memberSequence: number, roleSequence: number): Promise<Group> {
        return bgroup.updateMemberRole(this.id, memberSequence, roleSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateMemberExtRoles(memberSequence: number, roles: number[]): Promise<Group> {
        return bgroup.updateMemberExtRoles(this.id, memberSequence, roles).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    batchUpdateMembersRole(users: UserIdentity[], roleSequence: number): Promise<Group> {
        return bgroup.batchUpdateMembersRole(this.id, users, roleSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    batchUpdateMembersRoles(rolesMap: Map<number, number[]>): Promise<Group> {
        return bgroup.batchUpdateMembersRoles(this.id, rolesMap).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateMemberOutOfOffice(userId: string, ooo: OutOfOfficeStatus): Promise<User> {
        return bgroup.updateMemberOutOfOffice(this.id, userId, ooo).then((response: ClientResponse) => {
            return getByPath(response, 'object.user');
        });
    }

    createRelation(rm: UserIdentity, customer: UserIdentity): Promise<Group> {
        return brelation.adminCreateRelation(rm, customer).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateRelation(rm: UserIdentity, relations: UserRelation[]): Promise<Group> {
        return brelation.adminUpdateRelation(rm, relations).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    deleteRelation(rm: UserIdentity, customer: UserIdentity): Promise<Group> {
        return brelation.adminDeleteRelation(rm, customer).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    transferRelation(customer: UserIdentity, oldRm: UserIdentity, newRm: UserIdentity, noRelationBoard = false, suppressEmailSms?: boolean): Promise<Group> {
        return brelation.adminTransferRelation(customer, oldRm, newRm, noRelationBoard, suppressEmailSms).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    createIntegration(integration: GroupIntegration): Promise<Group> {
        return bgroup.createIntegration(this.id, integration).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateIntegration(integrationSequence: number, integration: GroupIntegration): Promise<Group> {
        return bgroup.updateIntegration(this.id, integrationSequence, integration).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    deleteIntegration(integrationSequence: number): Promise<Group> {
        return bgroup.deleteIntegration(this.id, integrationSequence).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    verifyIntegration(integrationSequence: number): Promise<Group> {
      return bgroup.verifyIntegration(this.id, integrationSequence).then((response: ClientResponse) => {
          return getByPath(response, 'object.group');
      });
    }

    readSamlProviders(): Promise<SystemSamlService> {
        return bgroup.readSamlProviders(this.id).then((response: ClientResponse) => {
            return getByPath(response, 'object.saml_service');
        });
    }

    getTag(key: string): string|number {
        return getTagByKey(key, this.group.tags);
    }

    createOrUpdateTag(key: string, val: string | number): Promise<void> {
        return btag.createOrUpdateTag(key, val, this.group.tags, btag.MxTagType.GROUP, this.id).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    createOrUpdateTags(tags: Object): Promise<void> {
        return btag.createOrUpdateTags(tags, this.group.tags, btag.MxTagType.GROUP, this.id).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    deleteTag(key: string): Promise<void> {
        return btag.deleteTag(key, this.group.tags, btag.MxTagType.GROUP, this.id).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    createOrUpdateInvitationToken(): Promise<Group> {
        return bgroup.createOrUpdateInvitationToken().then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    refreshDefaultMeetPassword(): Promise<Group> {
        return bgroup.refreshDefaultMeetPassword(currentOrgId).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readUsage(timestamp: number): Promise<UsageStatistics> {
        return baudit.readOrgUsage(currentOrgId, timestamp).then((response: ClientResponse) => {
            return getByPath(response, 'object.usage');
        });
    }

    readCrmReport(timestamp: number): Promise<Group> {
        return baudit.readOrgCrmReport(currentOrgId, timestamp).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    searchOrgBoards(searchKey: string, fromTime: number,toTime: number, start: number, size: number, ownerIds?:string[], boardId?: string, boardType?:string[]): Promise<Group> {
        return baudit.searchOrgBoards(currentOrgId, searchKey, fromTime, toTime, ownerIds, start, size, boardId, boardType).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readUserActivites(users: UserIdentity[], fromTime: number, toTime: number, type?: string[]): Promise<UserActivityLog[]> {
        return baudit.readUserActivites(currentOrgId, users, fromTime, toTime, type).then((response: ClientResponse) => {
            return getByPath(response, 'user_activities', []);
        });
    }

    readAuditBoard(boardId: string): Promise<Board> {
        return baudit.readAuditObject(this.id, boardId).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    mergeDefaultOrgConfig() {
        const groupCaps = this._groupCache.group?.group_caps || {}
         const groupSettings = this._groupCache.group?.group_settings || {}

        if (groupCaps.enable_chat_workspace === undefined) {
            groupCaps.enable_chat_workspace = true
        }

        if (groupCaps.enable_share_link === undefined) {
            groupCaps.enable_share_link = true
        }

        if (groupSettings.enable_workspace_report_auditing === undefined) {
            groupSettings.enable_workspace_report_auditing = true
        }
    }

    onGroupUpdated(group: Group): void {
        // plan code updated
        if ((group.plan_code && group.plan_code != this._groupCache.group.plan_code)
            || (group.plan_code_local && group.plan_code_local != this._groupCache.group.plan_code_local)) {
            mxLogger.info("org plan_code updated")
            buser.readUserCap().then(response => {
                const cap: UserCap = getByPath(response, "object.user.cap")
                if (cap && currentUser) {
                    currentUser.onUserCapUpdated(cap)
                }
            })
        }

        this._groupCache.onObjectUpdate(group);
        this.mergeDefaultOrgConfig()

        this._groupSubscriber.onObjectUpdate(group);
    }

    onRoutingRequestsUpdated(group: Group): void {
        this._groupCache.onRoutingRequestsUpdate(group);
        this._groupSubscriber.onRoutingRequestsUpdate(group);
    }

    listRoutingRequests(fromTime: number, toTime: number, count: number): Promise<Group> {
        return bacdsr.listRoutingRequests(currentOrgId, fromTime, toTime, count).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    subscribeRoutingRequests(cb: MxCallback<Group>): MxSubscription {
        bacdsr.subscribeRoutingRequests();
        return this._groupSubscriber.subscribeRoutingRequests(cb);
    }

    updateRoutingConfig(config: RoutingConfig): Promise<Group> {
        return bacdsr.updateRoutingConfig(this.id, config).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readOfficeHour(): Promise<Board> {
        let board: Board = {
            routing_status: this.IsOfficeHourOpen() ? BoardRoutingStatus.ROUTING_STATUS_OPEN : BoardRoutingStatus.ROUTING_STATUS_OFFICE_CLOSE
        }
        return Promise.resolve(board)

        // return bacdsr.readOfficeHour(this.id).then((response: ClientResponse) => {
        //     return getByPath(response, 'object.board');
        // });
    }

    private IsOfficeHourOpen(): boolean {
        try{
            if (!this.routingConfig) return true

            const weekdays = this.routingConfig.weekdays || []
            const special_days = this.routingConfig.special_days || []

            if(weekdays.length === 0 && special_days.length === 0) {
                return true;
            }

            const timezone = this.basicInfo.timezone || 'America/Los_Angeles'
            const now = moment().tz(timezone).format('YYYYMMDD HHmm00 d')
            const [strDate, strTime, nDayofWeek] = now.split(' ')

            for (let i = 0; i < special_days.length; i++) {
                const sd = special_days[i]
                if (sd.date === strDate) {
                    if (sd.is_close) {
                        return false
                    }else {
                        if (strTime >= sd.start_time && strTime <= sd.end_time) {
                            return true
                        }else {
                            return false
                        }
                    }
                }
            }

            for (let i = 0; i < weekdays.length; i++) {
                const wd = weekdays[i]
                if (String(wd.day_of_week) === nDayofWeek) {
                    if (wd.is_close) {
                        return false
                    }else {
                        if (strTime >= wd.start_time && strTime <= wd.end_time) {
                            return true
                        }else {
                            return false
                        }
                    }
                }
            }
        }catch(e) {

        }
        return true
    }

    createRoutingChannel(type: MxRoutingChannelType, channel: RoutingChannel): Promise<Group> {
        return bacdsr.createRoutingChannel(this.id, type, channel).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateRoutingChannel(type: MxRoutingChannelType, channelSeq: number, channel: RoutingChannel): Promise<Group> {
        channel.sequence = channelSeq;
        return bacdsr.updateRoutingChannel(this.id, type, channel).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateRoutingChannels(type: MxRoutingChannelType, channels: RoutingChannel[]): Promise<Group> {
        return bacdsr.updateRoutingChannels(this.id, type, channels).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    deleteRoutingChannel(type: MxRoutingChannelType, channelSeq: number): Promise<Group> {
        return bacdsr.deleteRoutingChannel(this.id, type, channelSeq).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readTeam(teamId: string): Promise<Group> {
        return bteam.readTeam(teamId).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    createTeam(name: string, param?: ICreateTeamParam): Promise<Group> {
        return bteam.createTeam(name, param).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateTeam(teamId: string, name?: string, description?: string): Promise<Group> {
        return bteam.updateTeam(teamId, name, description).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    deleteTeam(teamId: string): Promise<Group> {
        return bteam.deleteTeam(teamId).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readTeamMembers(teamId: string, searchKey?: string, filter?: MxGroupMemberFilter): Promise<Group> {
        let finalFilter
        if (filter?.isInternal && filter?.isLocal) {
            finalFilter = {searchKey}
        } else {
            finalFilter = {isInternal: true, searchKey: searchKey, ...filter}
        }
        return bgroup.searchGroupMembers(teamId, 0, 10000, finalFilter).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readTeamMembersByPagination(teamId: string, startSequence: number, size: number, filter?: MxGroupMemberFilter): Promise<Group> {
        return bgroup.searchGroupMembers(teamId, startSequence, size, filter).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readTeamMemberDetail(teamId: string, user: UserIdentity): Promise<CacheObject> {
        return bgroup.readMemberDetail(teamId, user).then((response: ClientResponse) => {
            return getByPath(response, 'object');
        });
    }

    transferMemberBoards(fromUserId: string, boardIds: string[], toUserId: string): Promise<Group> {
        return bgroup.transferMemberBoards(this.id, fromUserId, boardIds, toUserId).then((response: ClientResponse) => {
            return getByPath(response, 'object');
        });
    }

    addTeamMembers(teamId: string, users: UserIdentity[], accessType?: GroupAccessType, suppressNotification?: boolean): Promise<Group> {
        return bteam.addTeamMembers(teamId, users, accessType, suppressNotification).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    addTeamManagers(teamId: string, users: UserIdentity[]): Promise<Group> {
        return bteam.addTeamManagers(teamId, users).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    removeTeamMember(teamId: string, user: UserIdentity): Promise<Group> {
        return bteam.removeTeamMember(teamId, user).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    removeTeamManager(teamId: string, user: UserIdentity): Promise<Group> {
        return bteam.removeTeamManager(teamId, user).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    assignTeamOwner(teamId: string, user: UserIdentity): Promise<Group> {
        return bteam.assignTeamOwner(teamId, user).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    updateTeamMemberAccessType(teamId: string, user: UserIdentity, accessType?: GroupAccessType): Promise<Group> {
        return bteam.updateTeamMemberAccessType(teamId, user, accessType).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readManagementClients(filter: MxGroupManagementFilter): Promise<ClientResponse> {
        return bteam.readManagementClients(this.id, filter);
    }

    readManagementInternalUsers(filter: MxGroupManagementFilter): Promise<ClientResponse> {
        return bteam.readManagementInternalUsers(this.id, filter);
    }

    readManagementClientDetail(filter: MxGroupManagementFilter, client: UserIdentity): Promise<ClientResponse> {
        return bteam.readManagementClientDetail(this.id, filter, client);
    }

    readManagementInternalUserDetail(filter: MxGroupManagementFilter, rm: UserIdentity): Promise<ClientResponse> {
        return bteam.readManagementInternalUserDetail(this.id, filter, rm);
    }

    readManagementTeamDetail(filter: MxGroupManagementFilter, teamId: string): Promise<ClientResponse> {
        return bteam.readManagementTeamDetail(this.id, filter, teamId);
    }

    readTeamMemberUserBoards(rm: UserIdentity): Promise<ClientResponse> {
        return bteam.readTeamMemberUserBoards(this.id, rm)
    }

    readManagementShareUserBoards(filter: MxGroupManagementFilter, client: UserIdentity, rm: UserIdentity): Promise<ClientResponse> {
        return bteam.readManagementShareUserBoards(this.id, filter, client, rm);
    }

    readManagementUserActivities(user: UserIdentity, peerUser: UserIdentity, from: number, to: number): Promise<ClientResponse> {
        return bteam.readManagementUserActivities(this.id, user, peerUser, from, to);
    }

    subscribeBilling (token:  string, user: User, group?: Group): Promise<any> {
        return bstripe.subscribeStripe(token, user, group);
    }

    getStripePublicKey():Promise<string> {
        return bstripe.getStripePublicKey().then((response: ClientResponse) => {
            return response.data;
        });
    }

    getStripeCustomer(): Promise<any> {
        return bstripe.getStripeCustomer()
    }

    getStripeInvoice(start?: string, size?: number): Promise<any> {
        return bstripe.getStripeInvoice(start, size);
    }

    getStripeUpcomingInvoice(newPlanCode: string): Promise<any[]> {
        return bstripe.getStripeUpcomingInvoice(newPlanCode)
    }

    getStripeCoupon(): Promise<any> {
        return bstripe.getStripeCoupon()
    }

    getStripePrice(group: Group): Promise<any> {
        return bstripe.getStripePrice(group)
    }

    createGroupBoard(board: Board, owner: UserIdentity): Promise<Board> {
        return bgroup.createGroupBoard(this.id, board, owner).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    deleteGroupBoard(boardId: string): Promise<void> {
        return bgroup.deleteGroupBoard(boardId).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    readGroupBoards(startSequence: number, size: number): Promise<Group> {
        return bgroup.readGroupBoards(this.id, startSequence, size).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readContentLibraryBoards(startSequence: number, size: number, filterBoardType?: BoardType[], parentBoardId?: string, orgId?: string, includeDefault?: boolean, isClientResource?: boolean): Promise<Group> {
        return bgroup.readContentLibraryBoards(orgId || this.id, startSequence, size, filterBoardType, parentBoardId, includeDefault, isClientResource).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readContentLibraryBoardCount(parentBoardId?: string, orgId?: string, isClientResource?: boolean): Promise<Group> {
        return bgroup.readContentLibraryBoardCount(orgId || this.id, parentBoardId, isClientResource).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    readContentLibraryBoardPredecessors(boardId: string): Promise<Group> {
        return bgroup.readContentLibraryBoardPredecessors(boardId).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        });
    }

    addGroupBoardMember(boardId: string, users: UserIdentity[], accessType?: BoardAccessType): Promise<Board> {
        return bgroup.addGroupBoardMember(this.id, boardId, users, accessType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    addGroupBoardMemberByRole(boardId: string, roles: GroupUserRoleType[], accessType?: BoardAccessType): Promise<Board> {
        return bgroup.addGroupBoardMemberByRole(this.id, boardId, roles, accessType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    updateGroupBoardMember(boardId: string, memberSeq: number, roles: GroupUserRoleType[], accessType?: BoardAccessType): Promise<Board> {
        return bgroup.updateGroupBoardMember(this.id, boardId, memberSeq, roles, accessType).then((response: ClientResponse) => {
            return getByPath(response, 'object.board');
        });
    }

    removeGroupBoardMember(boardId: string, memberSeq: number): Promise<void> {
        return bgroup.removeGroupBoardMember(this.id, boardId, memberSeq).then((response: ClientResponse) => {
            return Promise.resolve();
        });
    }

    readGroupCapability(): Promise<GroupCapability> {
        return bgroup.readGroupCapability(this.id).then((response: ClientResponse) => {
            return response.group_capability;
        })
    }

    readGroupTasks(timestamp: number): Promise<Group> {
        return bgroup.readGroupTasks(this.id, timestamp).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        })
    }

    readUserBusyTime(userIds: Array<string>, startTime: string, endTime: string, accessToken?: string): Promise<Group> {
        return bgroup.readUserBusyTime(this.id, userIds, startTime, endTime, accessToken).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        })
    }

    listWorkflowTemplates(boardId?: string): Promise<CacheObject> {
        return bworkflow.listWorkflowTemplates(this.id, boardId).then((response: ClientResponse) => {
            let object: CacheObject = getByPath(response, 'object');
            if(object.group && object.group.boards) {
                this._publicWorkflowTemplatesCount = object.group.boards.filter(b => !b.is_deleted).length
            }
            return object
        })
    }

    listPrebuiltWorkflowTemplates(): Promise<Group> {
        return bworkflow.listPrebuiltWorkflowTemplates(this.id).then((response: ClientResponse) => {
            return getByPath(response, 'object.group');
        })
    }

    readWorkflowTemplateFolderCount(): Promise<number> {
        return bworkflow.readWorkflowTemplateFolderCount(this.id).then((response: ClientResponse) => {
            return response?.object?.user?.boards_total || 0;
        })
    }

    readWorkflowTemplateCount(folderBoardId?: string): Promise<number> {
        return bworkflow.readWorkflowTemplateCount(this.id, folderBoardId).then((response: ClientResponse) => {
            return response?.object?.user?.boards_total || 0;
        })
    }

    makeDownloadResourceUrl(resourceSequence: number): string {
        return `${getContextPath()}/group/${this.id}/resource/${resourceSequence}`;
    }

    makeTCDownloadUrl(): string {
        if (!this.group.tac) {
            return '';
        } else {
            return `${getContextPath()}/group/${this.id}/resource/${this.group.tac}`;
        }
    }

    makeTCUploadUrl(name: string): string {
        return `${getContextPath()}/group/${this.id}/upload?name=${name}&type=tac`;
    }

    makeExportMembersUrl(userType: string): string {
        return `${getContextPath()}/group/${this.id}/exportmembers?filter=${userType}`;
    }

    makeClientEngagementReportUrl(fromTime: number, toTime: number): string {
        let offset = -1 * new Date().getTimezoneOffset() * 60000;
        return `${getContextPath()}/group/${this.id}/exportclientengagement?from=${fromTime}&to=${toTime}&offset=${offset}`;
    }

    makeInternalEngagementReportUrl(fromTime: number, toTime: number): string {
        let offset = -1 * new Date().getTimezoneOffset() * 60000;
        return `${getContextPath()}/group/${this.id}/exportinternaluserengagement?from=${fromTime}&to=${toTime}&offset=${offset}`;
    }

    makeClientCoverageReportUrl(fromTime: number, toTime: number): string {
        let offset = -1 * new Date().getTimezoneOffset() * 60000;
        return `${getContextPath()}/group/${this.id}/exportclientcoverage?from=${fromTime}&to=${toTime}&offset=${offset}`;
    }

    makeWeChatReportUrl(fromTime: number, toTime: number): string {
        return `${getContextPath()}/group/${this.id}/exportsocialengagement?from=${fromTime}&to=${toTime}`;
    }

    makeWhatsAppReportUrl(fromTime: number, toTime: number): string {
        return `${getContextPath()}/group/${this.id}/exportsocialengagement?type=whatsapp&from=${fromTime}&to=${toTime}`;
    }

    makeLineReportUrl(fromTime: number, toTime: number): string {
        return `${getContextPath()}/group/${this.id}/exportsocialengagement?type=line&from=${fromTime}&to=${toTime}`;
    }

    makeChatAuditPDFUrl(fromTime: number, toTime: number, boardId: string, creator: string='', text: string='', conversationType: string='', participants?: [number]): string {
        let offset = -1 * new Date().getTimezoneOffset() * 60000;
        let url = `${getContextPath()}/board/audit/${boardId}?from=${fromTime}&to=${toTime}&offset=${offset}&group_id=${this.id}&creator=${creator}&text=${text}&board_type=${conversationType}&type=pdf`;
        if (participants) {
            url += `&participants=${participants.join(',')}`
        }
        return url
    }

    makeUploadRoutingChannelPictureUrl(channelSeq: number, name: string, width?: number, height?: number): string {
        let url = `${getContextPath()}/group/${this.id}/upload?type=channel&channel=${channelSeq}&name=${encodeURIComponent(name)}`;
        if (width && height) {
            url += `&width=${width}&height=${height}`;
        }
        return url;
    }

    scheduleSFDCJob(config: ConnectorSFDCConfig): Promise<ClientResponse> {
        return bconnector.scheduleSFDCJob(config);
    }

    testSFDCConnection(config: ConnectorSFDCConfig): Promise<ClientResponse> {
        return bconnector.testSFDCConnection(config);
    }

    disConnectToWealthBox (config: WealthBoxConfig) {
        return bconnector.disConnectToWealthBox(config.auth_id)
    }

    scheduleGlobalRelayJob(config: ConnectorGlobalRelayConfig): Promise<ClientResponse> {
        return bconnector.scheduleGlobalRelayJob(config);
    }

    testGlobalRelayConnection(config: ConnectorGlobalRelayConfig): Promise<ClientResponse> {
        return bconnector.testGlobalRelayConnection(config);
    }

    scheduleHubSpotJob(config: ConnectorHubSpotConfig): Promise<ClientResponse> {
        return bconnector.scheduleHubSpotJob(config);
    }

    testHubSpotConnection(config: ConnectorHubSpotConfig): Promise<ClientResponse> {
        return bconnector.testHubSpotConnection(config);
    }

    scheduleDynamicsJob(config: ConnectorDynamicsConfig): Promise<ClientResponse> {
        return bconnector.scheduleDynamicsJob(config);
    }

    testDynamicsConnection(config: ConnectorDynamicsConfig): Promise<ClientResponse> {
        return bconnector.testDynamicsConnection(config);
    }

    testFilevineConnection(config: FilevineConfig): Promise<ClientResponse> {
        return bconnector.testFilevineConnection(config);
    }

    testSmarshConnection(config: SmarshConfig): Promise<ClientResponse> {
        return bconnector.testSmarshConnection(config);
    }

    testCommonConnection (type: string, config: Record<string, string>) {
        return bconnector.testCommonConnection(type, config)
    }

    scheduleCommonCRMJob(config: ConnectorScheduleType, type: string): Promise<ClientResponse> {
        return bconnector.scheduleCommonCRMJob(config, type)
    }

    getSmarshLog (start: number, end: number): Promise<any> {
        return bconnector.getSmarshLog(start, end)
    }

    updateBoardProperties(properties: Property[]): Promise<Group> {
      return bgroup.updateBoardProperties(this.id, properties)
        .then((response: ClientResponse) => {
          return getByPath(response, 'object.group');
        })
    }

    getInternalUserTeams(user: UserIdentity): Promise<UserGroup[]> {
      return bgroup.getInternalUserTeams(this.id, user)
        .then((response: ClientResponse) => {
          const groups = getByPath(response, 'object.group.members.0.user.groups') || []
          return groups.filter((group: UserGroup) => {
            return group.group.type === GroupType.GROUP_TYPE_TEAM
          })
        })
    }
}
