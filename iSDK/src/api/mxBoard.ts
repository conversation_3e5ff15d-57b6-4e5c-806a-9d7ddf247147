import { BoardPageGroup } from './../proto/generated/BoardPageGroup';
import { BoardSession } from './../proto/generated/BoardSession';
import { BoardTransaction } from './../proto/generated/BoardTransaction';
import { BoardSignature } from './../proto/generated/BoardSignature';
import { BoardComment } from './../proto/generated/BoardComment';
import { ObjectFeed } from './../proto/generated/ObjectFeed';
import { Board } from "../proto/generated/Board";
import { BoardTodo } from '../proto/generated/BoardTodo';
import { BoardSignee } from '../proto/generated/BoardSignee';
import { BoardPage } from '../proto/generated/BoardPage';
import { BoardAccessType } from '../proto/generated/BoardAccessType';
import { NotificationLevel } from '../proto/generated/NotificationLevel';
import { User } from '../proto/generated/User';
import {Group} from "../proto/generated/Group";
import {BoardTag} from '../proto/generated/BoardTag';
import {BoardEditorType} from '../proto/generated/BoardEditorType';
import {BoardPin} from '../proto/generated/BoardPin';
import {BoardUserRSVP} from '../proto/generated/BoardUserRSVP';
import {SignatureStyle} from '../proto/generated/SignatureStyle';
import { BoardSignatureStatus } from '../proto/generated/BoardSignatureStatus';
import {BoardWorkflow} from '../proto/generated/BoardWorkflow';
import {BoardReferenceLink} from './../proto/generated/BoardReferenceLink';
import {ActionUserRoster} from './../proto/generated/ActionUserRoster';
import {CacheObject} from "../proto/generated/CacheObject";
import {ClientResponse} from "../proto/generated/ClientResponse";
import {BoardUser} from "../proto/generated/BoardUser";
import {WorkflowVar} from "../proto/generated/WorkflowVar";
import {BoardType} from "../proto/generated/BoardType";
import { BoardDataReference } from '../proto/generated/BoardDataReference';
import { BoardFolderType } from '../proto/generated/BoardFolderType';

import {
  MxSPath,
  MxCallback,
  MxSubscription,
  MxBaseObject,
  UserIdentity,
  MxPageElementType,
  MxSignatureElement,
  MxBoardOption,
  MxInviteMemberOption,
  ITransactionUpdateOption,
  ISignatureUpdateOption,
  ITransferActionParam,
  ICreateTransactionAttachmentParam,
  IResourceInfo,
  TransactionStep,
  MxCreateCommentOption,
  MxCopyFileOption,
  TransactionStatus,
} from './defines';
import { BoardActor } from '../proto/generated/BoardActor';
import { BoardProperty } from '../proto/generated/BoardProperty';

export interface MxBoard {
    readonly id: string;
    readonly board: Board;
    readonly fullBoard: Board;
    readonly contentLibraryBoard: Board;
    readonly basicInfo: Board;
    readonly tags: Object;
    readonly reference_links: BoardReferenceLink[];
    readonly waiting_users: ActionUserRoster[];
    readonly sessions: BoardSession[];
    readonly isMocked: boolean;
    readonly isInstant: boolean;
    readonly isNoAction: boolean;
    readonly isSubscribed: boolean;
    readonly option: MxBoardOption;
    readonly viewModels: Map<string, Object>;

    // board basic info include:
    // repeated fields: users, tags
    // local fields: all
    subscribeBasicInfo(cb: MxCallback<Board>): MxSubscription;

    subscribeFullBoard(cb: MxCallback<Board>): MxSubscription;

    subscribeFeeds(cb: MxCallback<ObjectFeed[]>): MxSubscription;

    subscribeRawFeeds(cb: MxCallback<ObjectFeed[]>): MxSubscription;

    subscribeFolder(parentFolderPath: MxSPath, cb: MxCallback<Board>): MxSubscription;

    subscribeTodos(cb: MxCallback<Board>): MxSubscription;

    subscribeSessions(cb: MxCallback<BoardSession[]>): MxSubscription;

    subscribeSignatures(cb: MxCallback<BoardSignature[]>): MxSubscription;

    subscribeTransactions(cb: MxCallback<BoardTransaction[]>): MxSubscription;

    subscribeThread(baseObj: MxBaseObject, cb: MxCallback<Board>): MxSubscription;

    subscribeFile(filePath: MxSPath, cb: MxCallback<Board>): MxSubscription;

    subscribeMeetDetail(cb: MxCallback<Board>): MxSubscription;

    subscribePins(cb: MxCallback<BoardPin[]>): MxSubscription;

    subscribeTags(cb: MxCallback<BoardTag[]>): MxSubscription;

    subscribeRSVP(cb: MxCallback<BoardUserRSVP[]>): MxSubscription;

    subscribeWaitingUsers(cb: MxCallback<ActionUserRoster[]>): MxSubscription;

    subscribeWorkflowDetail(cb: MxCallback<Board>): MxSubscription;

    subscribeWorkflows(cb: MxCallback<Board>): MxSubscription;

    sync(): Promise<Board>; // for mock board, return real board

    // read API
    loadFullBoard(option?: MxBoardOption): Promise<Board>;

    loadContentLibraryBoard(): Promise<Board>;

    readWorkflowDetail(): Promise<Board>;

    readWorkflow(workflowSeq: number): Promise<BoardWorkflow>;

    readCover(): Promise<Board>;

    readFeeds(startSequence?: number, beforeSize?: number, afterSize?: number): Promise<Board>;

    readThread(baseObject: MxBaseObject): Promise<Board>;

    listAllFolders(parentFolder?: MxSPath): Promise<Board>;

    listFolder(parentFolder: MxSPath, noCache?: boolean, includeFirstSubFolderFile?: boolean): Promise<Board>;

    listActions(): Promise<Board>;

    listTodos(): Promise<Board>;

    listSignatures(): Promise<Board>;

    listTransactions(): Promise<Board>;

    listWorkflows(): Promise<Board>;

    readFields(fileds: string[]): Promise<Board>;

    readFileDetail(filePath: MxSPath): Promise<Board>;

    readFileByReferenceLink(refSeq: number): Promise<Board>;

    readPageDetail(pageSequence: number): Promise<Board>;

    getCacheSignature(signatureSequence: number): BoardSignature;

    readSignatureDetail(signatureSequence: number): Promise<Board>;

    readTransactionDetail(transactionSequence: number): Promise<Board>;

    readOnGoingSignatures(): Promise<Board>;

    readOnGoingTransactions(): Promise<Board>;

    readOnGoingDelegateFeeds(): Promise<Board>;

    readUserActivities(): Promise<Board>;

    readField(fieldName: string, seqs: number[]): Promise<Board>;

    // update
    updateBasicInfo(board: Board, suppressFeed?: boolean): Promise<Board>;

    // member
    inviteMember(users: UserIdentity[], option?: MxInviteMemberOption, teams?: Group[]): Promise<Board>;

    inviteGroup(group: Group, option?: MxInviteMemberOption): Promise<Board>;

    inviteGroups(groups: Group[], option?: MxInviteMemberOption): Promise<Board>;

    removeMember(seqs: number | number[], suppressFeed?: boolean): Promise<Board>;

    removeMemberAndTeam(userSeqs: number[], teams: BoardUser[], suppressFeed?: boolean): Promise<Board>;

    removeTeam(teamSeqs: number[], suppressFeed?: boolean): Promise<Board>;

    updateMemberAccessType(seqs: number | number[], type: BoardAccessType): Promise<Board>;

    setOwner(boardUserSequence: number): Promise<Board>;

    setBoardType(type: BoardType): Promise<Board>;

    updateTypeIndication(): Promise<Board>;

    updateBoardProperties(properties: BoardProperty[]): Promise<Board>;

    // base object
    getFeedBaseObject(feedSeq: number|ObjectFeed): MxBaseObject;

    getCommentBaseObject(commentSeq: number): MxBaseObject;

    getTodoBaseObject(todoSeq: number): MxBaseObject;

    getSignatureBaseObject(signatureSeq: number): MxBaseObject;

    getTransactionBaseObject(transactionSeq: number): MxBaseObject;

    getMeetBaseObject(sessionSeq: number): MxBaseObject;

    getFileBaseObject(filePath: MxSPath): MxBaseObject;

    getPageBaseObject(pageSeq: number): MxBaseObject;

    getPositionCommentBaseObject(pageSeq: number, commentSeq: number): MxBaseObject;

    getWorkflowBaseObject(workflowSeq: number): MxBaseObject;

    getWorkflowStepBaseObject(workflowSeq: number, stepSeq: number): MxBaseObject;

    // comment
    createComment(comment: BoardComment, opt?: MxCreateCommentOption): Promise<Board>;

    updateComment(commentSequence: number, comment: BoardComment, isUpdateUrlPreview?: boolean): Promise<Board>;

    deleteComment(commentSequence: number, noFeed?: boolean, isDeleteUrlPreview?: boolean): Promise<Board>;

    createThreadComment(obj: MxBaseObject, comment: BoardComment): Promise<Board>;

    updateThreadComment(obj: MxBaseObject, commentSequence: number, comment: BoardComment): Promise<Board>;

    deleteThreadComment(obj: MxBaseObject, commentSequence: number): Promise<Board>;

    createPositionComment(pageSequence: number, comment: BoardComment): Promise<Board>;

    updatePositionComment(pageSequence: number, commentSequence: number, comment: BoardComment): Promise<Board>;

    deletePositionComment(pageSequence: number,commentSequence: number): Promise<Board>;

    // page / file / folder
    createFolder(name: string, parentFolderPath?: MxSPath, folderType?: BoardFolderType): Promise<Board>;

    deleteFolder(folderPath: MxSPath): Promise<Board>;

    renameFolder(folderPath: MxSPath, name: string): Promise<Board>;

    createWhiteboard(name: string, width: number, height: number, foderPath?: MxSPath, fileUUID?:string): Promise<Board>;

    createWebLinkPage(url: string, fileUUID?: string, name?:string): Promise<Board>;

    copyResource(fromBoardId: string, resourceSeq: number, toBoardId: string, toFolderPath: MxSPath, newFileName?: string, suppressFeed?: boolean|null): Promise<Board>;

    copyFiles(fromBoardId: string, files: MxSPath[], toBoardId: string, toFolderPath: MxSPath, newFileNames?: string[], suppressFeed?: boolean|null, opt?: MxCopyFileOption): Promise<Board>;

    moveFiles(files: MxSPath[], toFolderPath: MxSPath, newFileNames?: string[]): Promise<Board>;

    uploadFromRemoteUrl(url: string, name: string, authToken: string, toFolderPath?: MxSPath, contentType?: string, id?:string, suppressFeed?: boolean): Promise<Board>;

    uploadResourceFromRemoteUrl(url: string, name: string, resType: string): Promise<Board>;

    getBoxAccessToken(payload: string): Promise<string>;

    renameFile(filePath: MxSPath, name: string): Promise<Board>;

    updateFile(filePath: MxSPath, file: BoardPageGroup): Promise<Board>;

    deleteFiles(files: MxSPath[], noFeed?: boolean): Promise<Board>;

    updatePage(pageSequence: number, param: BoardPage): Promise<Board>;

    lockPage(pageSequence: number): Promise<Board>;

    unlockPage(pageSequence: number): Promise<Board>;

    updatePageEditorType(pageSequence: number, editorType: BoardEditorType): Promise<Board>;

    createPageElement(pageSequence: number, content:string, elementUuid?: string, elementType?: MxPageElementType): Promise<Board>;

    updatePageElement(pageSequence: number, elementUuid: string, content: string, ddrs?: BoardDataReference[]): Promise<Board>;

    deletePageElement(pageSequence: number, elementUuid: string): Promise<Board>;

    getReferenceLinkBySequence(referenceLinkSeq: number): Board;

    readBoardResourceBySequence(seqs: number[]): Promise<Board>;

    // signature
    createSignature(filePath: MxSPath, newFileName?: string, isTemplate?: boolean): Promise<Board>;

    createBlankSignature(signature: BoardSignature): Promise<Board>;

    updateSignature(signatureSequence: number, param: BoardSignature, opt?: ISignatureUpdateOption): Promise<Board>;

    deleteSignature(signatureSequence: number | number[], noFeed?:boolean): Promise<Board>;

    addSignatureSignee(signatureSequence: number, signees: BoardActor | BoardActor[]): Promise<Board>;

    updateSignatureSignee(signatureSequence:number, signeeSequence: number, signee: BoardSignee, isUpdateOthers?: boolean): Promise<Board>;

    updateSignatureSignees(signatureSequence:number, signees: BoardSignee[], suppressFeed?: boolean): Promise<Board>;

    deleteSignatureSignee(signatureSequence: number, signeeSequence: number | number[]): Promise<Board>;

    assignElementToSignee(signatureSequence: number, signeeSequence: number, elements: number[]): Promise<Board>;

    startSignature(signatureSequence: number): Promise<Board>;

    submitSignature(signatureSequence: number, elements: MxSignatureElement[], keepStatusUnchanged?: boolean, jwt?: string): Promise<Board>;

    declineSignature(signatureSequence: number, message: string, jwt?: string): Promise<Board>;

    createSignatureElement(signatureSequence: number, pageSequence: number, content: string, elementType: MxPageElementType, client_uuid?: string): Promise<Board>;

    batchCreateSignatureElements(signatureSequence: number, pages: BoardPage[], suppressFeed?: boolean): Promise<Board>;

    updateSignatureElement(signatureSequence: number, pageSequence: number, client_uuid: string, content: string, ddrs?: BoardDataReference[], readOnly?: boolean): Promise<Board>;

    batchUpdateSignatureElements(signatureSequence: number, pages: BoardPage[], suppressFeed?: boolean): Promise<Board>;

    deleteSignatureElement(signatureSequence: number, pageSequence: number, client_uuid: string): Promise<Board>;

    deleteSignatureElements(signatureSequence: number, pages: BoardPage[]): Promise<Board>;

    uploadSignatureResourceToPage(signatureSequence: number, pageSequence: number, resourceUrl: string, resourceName:string): Promise<Board>;

    copySignature(fromBoardId: string, signatureSeq: number, toBoardId: string, keepCreator?: boolean, copyWithTotalUsedCount?: boolean, isTemplate?: boolean, status?: BoardSignatureStatus, signature?: BoardSignature, copyWithWorkflow?: boolean): Promise<Board>;

    reopenSignature(signatureSequence:number, generateReopenEvent?: boolean): Promise<Board>;

    resetSignatureStatus(signatureSequence:number): Promise<Board>;

    updateSignatureViewTime(signatureSequence:number): Promise<Board>;

    addSignatureAttachment(signatureSequence: number, file: MxSPath, isReply?: boolean, suppressFeed?: boolean): Promise<Board>;

    removeSignatureAttachment(signatureSequence: number, referenceSequence: number, suppressFeed?: boolean): Promise<Board>;

    replaceSignature(signatureSequence: number, fromBoardId: string, fromSignatureSequence: number, suppressFeed?: boolean): Promise<Board>;

    replaceSignaturePagesFromFile(signatureSequence: number, fromBoardId: string, fromFilePath: MxSPath, suppressFeed?: boolean): Promise<Board>;

    // todo
    createTodo(todo: BoardTodo): Promise<Board>;

    deleteTodo(todoSequence: number): Promise<Board>;

    copyTodo(todo: BoardTodo, toBoardId: string, suppressUserActivity?: boolean): Promise<Board>;

    updateTodo(todoSequence: number, todo: BoardTodo, noFeed?: boolean, generateReopenEvent?: boolean): Promise<Board>;

    setTodoAssignee(todoSeq:number, boardMemberSeq:number, isReassign?: boolean, noFeed?: boolean): Promise<Board>;

    setTodoAssignee2(todoSeq:number, assignee: BoardActor, isReassign?: boolean, noFeed?: boolean): Promise<Board>;

    addTodoAttachment(todoSequence: number, files: MxSPath[], suppressFeed?: boolean): Promise<Board>;

    removeTodoAttachment(todoSequence: number, referenceSequence: number, suppressFeed?: boolean): Promise<Board>;

    getTag(key: string): string|number;

    getRawTag(key: string): BoardTag;

    createOrUpdateTag(key: string, val: string|number, useKeyAsClientUuid?: boolean): Promise<void>;

    createOrUpdateTags(tags: Object): Promise<void>;

    deleteTag(key: string): Promise<void>;


    createOrUpdateWorkflowVariables(workflowSeq: number, variables: WorkflowVar[]): Promise<Board>;

    deleteWorkflowVariable(workflowSeq: number, variableName: string): Promise<void>;

    updateWorkflowStep(workflow: BoardWorkflow): Promise<Board>;

    restartWorkflow(workflowSeq: number): Promise<Board>;

    completeWorkflow(workflowSeq: number): Promise<Board>;

    cancelWorkflow(workflowSeq: number): Promise<Board>;

    reopenWorkflowStep(workflowSeq: number, stepSeq: number): Promise<Board>;

    skipWorkflowStep(workflowSeq: number, stepSeq: number): Promise<Board>;

    createWorkflow(workflow: BoardWorkflow, suppressFeed?: boolean): Promise<Board>;

    updateWorkflow(workflow: BoardWorkflow, suppressFeed?: boolean): Promise<Board>;

    updateWorkflowStatus(workflow: BoardWorkflow, suppressFeed?: boolean): Promise<Board>;

    deleteWorkflow(seq: number): Promise<Board>;

    copyWorkflow(workflow: BoardWorkflow, toBoardId?: string): Promise<Board>;

    copyAsWorkflowTemplate(workflow: BoardWorkflow, createAsTemp?: boolean): Promise<CacheObject>;

    copyAsMilestoneTemplate(workflow: BoardWorkflow, fromMilestone: number, toMilestone: number, createAsTemp?: boolean): Promise<CacheObject>;

    // transaction
    submitTransactionStep(transaction: BoardTransaction, suppressFeed?: boolean): Promise<Board>;

    createTransaction(transaction: BoardTransaction, suppressFeed?: boolean): Promise<Board>;

    updateTransaction(transactionSeq: number, transaction: BoardTransaction, opt?: ITransactionUpdateOption): Promise<Board>;

    deleteTransaction(transactionSeq: number | number[], suppressFeed?: boolean): Promise<Board>;

    reopenTransactionStep(transactionSeq: number, stepSeq: number|number[]): Promise<Board>;

    createTransactionAttachmentFromResource(resource: IResourceInfo, toBoardId: string, toTransactionSeq: number, opt?: ICreateTransactionAttachmentParam): Promise<Board>;

    createTransactionAttachmentFromFile(files: MxSPath[], toBoardId: string, toTransactionSeq: number, opt?: ICreateTransactionAttachmentParam): Promise<Board>;

    createTransactionAttachmentFromRemoteUrl(transactionSeq: number, url: string, name: string, authToken: string, contentType?: string): Promise<Board>;

    createTransactionAttachmentReply(transactionSequence: number, file: MxSPath, suppressFeed?: boolean): Promise<Board>;

    removeTransactionAttachment(transactionSequence: number, referenceSeq: number|number[], setLastModifiedTime?: boolean, suppressFeed?: boolean): Promise<Board>;

    uploadTransactionResourceFromUrl(transactionSeq: number, url: string, name: string, client_uuid?: string): Promise<Board>;

    copyTransactions(transactionSeqs: number[], toBoardId: string, keepCreator?: boolean, copyWithTotalUsedCount?: boolean, copyWithLastModifiedTime?: boolean, suppressUserActivity?: boolean, forceGenerateCreateFeed?: boolean, copyWithWorkflow?: boolean): Promise<Board>;

    reopenTransaction(transactionSeq:number, generateReopenEvent?: boolean, status?: TransactionStatus): Promise<Board>;

    resetTransactionStatus(transactionSeq:number, steps?: TransactionStep[]): Promise<Board>;

    updateTransactionViewTime(transactionSeq: number, stepSeq: number): Promise<Board>;

    updateTransactionReminder(transactionSeq: number, reminderTime: number): Promise<Board>;

    // pin
    readPins(startSequence?: number, beforeSize?: number, afterSize?: number): Promise<Board>;

    createPin(baseObject: MxBaseObject): Promise<Board>;

    deletePin(baseObject: MxBaseObject): Promise<Board>;

    getPinBaseObject(pinSeq: number): MxBaseObject;

    // waiting user
    createWaitingUser(name?: string, email?: string): Promise<Board>;

    updateWaitingUser(users: ActionUserRoster[]): Promise<Board>;

    deleteWaitingUser(): Promise<Board>;

    // others
    updateNotificationLevel(level: NotificationLevel): Promise<User>;

    markFeedAsUnRead(feedSeq: number): Promise<Board>;

    createViewToken(accessType: BoardAccessType, actorAs?: User): Promise<Board>;

    createResourceViewToken(resSeq: number): Promise<Board>;

    transferActions(param: ITransferActionParam): Promise<Board>;

    resendInviteEmailSms(userId: string, isSms: boolean): Promise<Board>;

    bulkResendInviteEmailSms(userIds: string[], isSms: boolean): Promise<Board>;

    resendInviteEmailSmsForPrivacy(user: UserIdentity): Promise<void>;

    resendTransactionReminder(transaction: BoardTransaction): Promise<void>;

    resendSignatureReminder(signature: BoardSignature): Promise<void>;

    createInvitationViewToken(userId: string): Promise<Board>;

    renewWorkspaceId(boardViewToken: string): Promise<Board>;

    renewPassword(): Promise<Board>;

    updateFeedReaction(feedSeq: number, reaction: string, suppressNotification?: boolean): Promise<Board>;

    // for content library file/signature/transaction
    increaseUsedCount(spath: MxSPath[]): Promise<Board>;

    // download  & upload url
    makeCreateWebdocUrl(name: string, folderSpath?: MxSPath): string;

    makeUpdateWebdocUrl(name: string, fileSeq: number, pageSeq: number): string;

    makeDownloadFolderUrl(folderSequence: number, name: string): string;

    makeDownloadFilesUrl(filesSequence: number[], name: string, type:'pdf'|'zip'): string;

    makeDownloadResourceUrl(resourceSequence: number, name: string): string;

    makeDownloadZipResourceUrl(resources: number[], name: string): string;

    makeUploadFileUrl(name: string, folderSpath?: MxSPath, resourceId?:string): string;

    makeUploadToPageUrl(pageSequence: number, name: string): string;

    makeUploadToSignatureUrl(signatureSequence: number, pageSequence:number, name: string): string;

    makeUploadToSignatureSigneeUrl(signatureSequence: number, signeeSequence: number, fileName: string, style?: SignatureStyle, initials?: string): string;

    makeUploadToTodoUrl(todoSequence: number, name: string): string;

    makeUploadToTransactionUrl(transactionSequence: number, name: string, isSupportFile?: string, customData?: string): string;

    makeUploadToTransactionResourceUrl(transactionSequence: number, name: string): string;

    makeUploadBoardCoverUrl(fileName: string, type?:string): string;

    makeUploadBoardBannerUrl(fileName: string, isForMobile?: boolean): string;

    abortAllPendingRequests(): void;

    // internal use
    setBoardOption(option: MxBoardOption): void;

    setIsNoAction(_isNoAction: boolean): void;

    markAsInstantBoard(): void;

    onBoardUpdated(board: Board): void;

    updateBoardCache(board : Board);

    createShareViewToken(memberOnly: boolean, code?: string): Promise<Board>;
    updateShareViewToken(sequence: number, memberOnly: boolean, code?: string): Promise<Board>;
    joinBoardByViewToken(viewToken: string, user: User, verifyCode?: string, googleJWT?: string, appleJWT?: string, viewTokenCode?: string): Promise<User>;
    joinBoardByManagementUser(managementUser: UserIdentity): Promise<Board>;

    readViewTokens(): Promise<Board>;
    updateRequestingUser (sequence: number, approved: boolean, suppressFeed?: boolean, isInvalid?: boolean): Promise<Board>;

    inviteRequestingUserToOrg (sequence: number, isClient: boolean): Promise<Group>;

    downloadBoardFolder (type: string, fileName: string, folder: number, boardFiles: Board, binderViewToke?:string): Promise<Blob>;
}
