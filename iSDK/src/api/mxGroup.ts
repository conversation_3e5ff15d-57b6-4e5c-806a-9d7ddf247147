import { ClientResponse } from './../proto/generated/ClientResponse';
import { CacheObject } from './../proto/generated/CacheObject';
import { SystemSamlService } from './../proto/generated/SystemSamlService';
import { UsageStatistics } from './../proto/generated/UsageStatistics';
import { GroupIntegration } from './../proto/generated/GroupIntegration';
import { GroupUser } from './../proto/generated/GroupUser';
import { GroupUserRole } from '../proto/generated/GroupUserRole';
import { Group } from '../proto/generated/Group';
import { Board } from '../proto/generated/Board';
import { RoutingConfig } from '../proto/generated/RoutingConfig';
import { UserActivityLog } from '../proto/generated/UserActivityLog';
import { RoutingChannel } from '../proto/generated/RoutingChannel';
import { UserGroup } from '../proto/generated/UserGroup';
import { GroupAccessType } from '../proto/generated/GroupAccessType';
import { UserRelation } from './../proto/generated/UserRelation';
import { GroupUserRoleType } from '../proto/generated/GroupUserRoleType';
import { BoardAccessType } from '../proto/generated/BoardAccessType';
import { GroupBoard } from '../proto/generated/GroupBoard';
import { GroupCapability } from '../proto/generated/GroupCapability';
import { OutOfOfficeStatus } from '../proto/generated/OutOfOfficeStatus';
import { BoardType } from '../proto/generated/BoardType';
import { GroupUserSetting } from '../proto/generated/GroupUserSetting';
import { Property } from '../proto/generated/Property';

import {
    UserIdentity,
    MxCallback,
    MxSubscription,
    MxGroupMemberFilter,
    MxUpdateEmailPhoneNumParam,
    MxRoutingChannelType,
    MxGroupManagementFilter,
    User,
    MxResetPasswordChannel,
    ConnectorSFDCConfig,
    ConnectorGlobalRelayConfig,
    ConnectorHubSpotConfig, ConnectorDynamicsConfig, FilevineConfig, SmarshConfig, GroupType, WealthBoxConfig,
    MxReadGroupMemberOption
} from './defines';
import {ConnectorScheduleType} from "../biz/connector";

export interface ICreateTeamParam {
    description?: string; 
    type?: GroupType; 
    managersSetting?: GroupUserSetting;
    useCreatorAsOwner?: boolean;
}

export interface MxGroup {
    readonly id: string;
    readonly group: Group;
    readonly basicInfo: Group;
    readonly tags: Object;
    readonly roles: GroupUserRole[];
    readonly integrations: GroupIntegration[];
    readonly routingConfig: RoutingConfig;
    readonly routingRequests: Group;
    readonly publicWorkflowTemplatesCount: number;
    readonly isAnonymous: boolean;

    // group basic info include:
    // repeated fields: tags
    // local fields: all
    subscribeBasicInfo(cb: MxCallback<Group>): MxSubscription;

    subscribeRoutingConfig(cb: MxCallback<RoutingConfig>): MxSubscription;

    subscribeTeams(cb: MxCallback<UserGroup[]>): MxSubscription;

    subscribeGroupBoards(cb: MxCallback<GroupBoard[]>): MxSubscription;

    updateGroup(info: Group): Promise<Group>;

    readMember(user: UserIdentity, opt?: MxReadGroupMemberOption): Promise<Group>;

    readMembers(users: UserIdentity[], opt?: MxReadGroupMemberOption): Promise<Group>;

    // for org admin to read single user, will load user and out user boards/relations
    readMemberDetail(user: UserIdentity, includeRelatedRelation?: boolean, includeRelatedUserFlowTemplate?: boolean, includeRelatedUserContentLibrary?: boolean, includeRelatedWorkflowTemplateLibrary?: boolean): Promise<ClientResponse>;

    // for org admin to read meeting boards of a user
    readMemberMeetings(user: UserIdentity): Promise<CacheObject>;

    // for admin to read org members with pagination, will load user object and merge user data in response
    readMembersByPagination(startSequence: number, size: number): Promise<Group>;

    transferMemberBoards(fromUserId: string, boardIds: string[], toUserId: string): Promise<Group>;

    searchMembers(startSequence: number, size: number, filter?: MxGroupMemberFilter): Promise<Group>;

    searchUserBoardsByMember(userId: string, searchKey: string, startIndex: number, pageSize: number): Promise<User>;

    searchSharedUserBoards(userId1: string, userId2: string): Promise<User>;

    getMemberSequenceByUserId(userId: string): number;

    inviteMember(user: GroupUser | GroupUser[], updateExisting?: boolean, suppressEmailSms?: boolean): Promise<Group>;

    deleteMember(memberSequence: number): Promise<void>;

    deleteMemberFutureMeetings(userId: string): Promise<void>;

    deleteMemberScheduleBoards(userId: string): Promise<void>;

    activateMember(memberSequence: number): Promise<Group>;

    deactivateMember(memberSequence: number): Promise<void>;

    removeMemberAvatar(memberSequence: number): Promise<void>;

    join(): Promise<Group>;

    joinByInvitationToken(token: string): Promise<Group>;

    leave(): Promise<void>;

    resetMemberPassword(memberSequence: number, channel?: MxResetPasswordChannel): Promise<void>;

    resendInviteEmail(param: UserIdentity): Promise<void>;

    resendInviteSms(param: UserIdentity): Promise<void>;

    updateMemberProfile(memberSequence: number, user: GroupUser): Promise<Group>;

    updateMemberEmailPhoneNum(userId: string, param: MxUpdateEmailPhoneNumParam): Promise<Group>;

    // for clear all DistributionList, need to set roles = [0]
    updateMemberDistributionList(userId: string, roles: number[]): Promise<Group>;

    createRole(role: GroupUserRole): Promise<Group>;

    updateRole(roleSequence: number, role: GroupUserRole): Promise<Group>;

    deleteRole(roleSequence: number): Promise<Group>;

    updateMemberRole(memberSequence: number, roleSequence: number): Promise<Group>;

    updateMemberExtRoles(memberSequence: number, roles: number[]): Promise<Group>;

    batchUpdateMembersRole(users: UserIdentity[], roleSequence: number): Promise<Group>;

    // rolesMap key: group member seq, val: role sequences
    batchUpdateMembersRoles(rolesMap: Map<number, number[]>): Promise<Group>;
      
    updateMemberOutOfOffice(userId: string, ooo: OutOfOfficeStatus): Promise<User>;

    createRelation(rm: UserIdentity, customer: UserIdentity): Promise<Group>;

    deleteRelation(rm: UserIdentity, customer: UserIdentity): Promise<Group>;

    updateRelation(rm: UserIdentity, relations: UserRelation[]): Promise<Group>;

    transferRelation(customer: UserIdentity, oldRm: UserIdentity, newRm: UserIdentity, noRelationBoard?:boolean, suppressEmailSms?: boolean): Promise<Group>;

    createIntegration(integration: GroupIntegration): Promise<Group>;

    updateIntegration(integrationSequence: number, integration: GroupIntegration): Promise<Group>;

    deleteIntegration(integrationSequence: number): Promise<Group>;

    verifyIntegration(integrationSeq: number): Promise<Group>;

    readSamlProviders(): Promise<SystemSamlService>;

    getTag(key: string): string|number;

    createOrUpdateTag(key: string, val: string|number): Promise<void>;

    createOrUpdateTags(tags: Object): Promise<void>;

    deleteTag(key: string): Promise<void>;

    createOrUpdateInvitationToken(): Promise<Group>;

    refreshDefaultMeetPassword(): Promise<Group>;

    updateBoardProperties(properties: Property[]): Promise<Group>;
    
    getInternalUserTeams(user: UserIdentity): Promise<UserGroup[]>;

    // org team API
    readTeam(teamId: string): Promise<Group>;

    createTeam(name: string, param?: ICreateTeamParam): Promise<Group>;

    updateTeam(teamId: string, name?: string, description?: string): Promise<Group>;

    deleteTeam(teamId: string): Promise<Group>;

    readTeamMembers(teamId: string, searchKey?: string, filter?: MxGroupMemberFilter): Promise<Group>;

    readTeamMembersByPagination(teamId: string, startSequence: number, size: number, filter?: MxGroupMemberFilter): Promise<Group>;

    readTeamMemberDetail(teamId: string, user: UserIdentity): Promise<CacheObject>;

    addTeamMembers(teamId: string, users: UserIdentity[], accessType?: GroupAccessType, suppressNotification?: boolean): Promise<Group>;

    addTeamManagers(teamId: string, users: UserIdentity[]): Promise<Group>;

    removeTeamMember(teamId: string, user: UserIdentity): Promise<Group>;

    removeTeamManager(teamId: string, user: UserIdentity): Promise<Group>;

    assignTeamOwner(teamId: string, user: UserIdentity): Promise<Group>;

    updateTeamMemberAccessType(teamId: string, user: UserIdentity, accessType?: GroupAccessType): Promise<Group>;

    // management API
    readManagementClients(filter: MxGroupManagementFilter): Promise<ClientResponse>;

    readManagementInternalUsers(filter: MxGroupManagementFilter): Promise<ClientResponse>;

    readManagementClientDetail(filter: MxGroupManagementFilter, client: UserIdentity): Promise<ClientResponse>;

    readManagementInternalUserDetail(filter: MxGroupManagementFilter, rm: UserIdentity): Promise<ClientResponse>;

    readManagementTeamDetail(filter: MxGroupManagementFilter, teamId: string): Promise<ClientResponse>;

    readTeamMemberUserBoards(rm: UserIdentity): Promise<ClientResponse>;
    readManagementShareUserBoards(filter: MxGroupManagementFilter, client: UserIdentity, rm: UserIdentity): Promise<ClientResponse>;

    readManagementUserActivities(user: UserIdentity, peerUser: UserIdentity, from: number, to: number): Promise<ClientResponse>;

    // audit
    readUsage(timestamp: number): Promise<UsageStatistics>;

    readCrmReport(timestamp: number): Promise<Group>;

    searchOrgBoards(searchKey: string, fromTime: number,toTime: number, start: number, size: number, ownerIds?:string[], boardId?:string, boardType?:string[]): Promise<Group>;

    readUserActivites(users: UserIdentity[], fromTime: number, toTime: number, type?:string[]): Promise<UserActivityLog[]>;

    readAuditBoard(boardId: string): Promise<Board>;

    // download  & upload url
    makeDownloadResourceUrl(resourceSequence: number): string;

    makeTCDownloadUrl(): string;

    makeTCUploadUrl(name: string): string;

    makeExportMembersUrl(userType: string): string;

    makeClientEngagementReportUrl(fromTime: number, toTime: number): string;

    makeInternalEngagementReportUrl(fromTime: number, toTime: number): string;

    makeClientCoverageReportUrl(fromTime: number, toTime: number): string;

    makeWeChatReportUrl(fromTime: number, toTime: number): string;

    makeWhatsAppReportUrl(fromTime: number, toTime: number): string;

    makeLineReportUrl(fromTime: number, toTime: number): string;

    makeChatAuditPDFUrl(fromTime: number, toTime: number, boardId: string, creator: string, text: string, conversationType?: string, participants?: [number]): string;

    makeUploadRoutingChannelPictureUrl(channelSeq: number, name: string, width?: number, height?: number): string;

    // ACD / SR
    updateRoutingConfig(config: RoutingConfig): Promise<Group>;

    readOfficeHour(): Promise<Board>;

    createRoutingChannel(type: MxRoutingChannelType, channel: RoutingChannel): Promise<Group>;

    updateRoutingChannel(type: MxRoutingChannelType, channelSeq: number, channel: RoutingChannel): Promise<Group>;

    updateRoutingChannels(type: MxRoutingChannelType, channels: RoutingChannel[]): Promise<Group>;

    deleteRoutingChannel(type: MxRoutingChannelType, channelSeq: number): Promise<Group>;

    listRoutingRequests(fromTime: number, toTime: number, count: number): Promise<Group>;

    subscribeRoutingRequests(cb: MxCallback<Group>): MxSubscription;

    // billing
    subscribeBilling(token:  string, user: User, group?: Group): Promise<any>;

    getStripePublicKey(): Promise<string>;

    getStripeCustomer(): Promise<any>;

    getStripeInvoice(start?: string, size?: number): Promise<any>;

    getStripeCoupon(): Promise<any>;

    getStripePrice(group: Group): Promise<any>;

    getStripeUpcomingInvoice (newPlanCode: string): Promise<any>;

    // group board
    listWorkflowTemplates(folderBoardId?: string): Promise<CacheObject>;

    listPrebuiltWorkflowTemplates(): Promise<Group>;

    readWorkflowTemplateFolderCount(): Promise<number>;
    readWorkflowTemplateCount(folderBoardId?: string): Promise<number>;

    createGroupBoard(board: Board, owner: UserIdentity): Promise<Board>;

    deleteGroupBoard(boardId: string): Promise<void>;

    readGroupBoards(startSequence: number, size: number): Promise<Group>;

    readContentLibraryBoards(startSequence: number, size: number, filterBoardType?: BoardType[], parentBoardId?: string, orgId?: string, includeDefault?: boolean, isClientResource?: boolean): Promise<Group>;

    readContentLibraryBoardCount(parentBoardId?: string, orgId?: string, isClientResource?: boolean): Promise<Group>;

    readContentLibraryBoardPredecessors(boardId: string): Promise<Group>;

    addGroupBoardMember(boardId: string, users: UserIdentity[], accessType?: BoardAccessType): Promise<Board>;

    addGroupBoardMemberByRole(boardId: string, roles: GroupUserRoleType[], accessType?: BoardAccessType): Promise<Board>;

    updateGroupBoardMember(boardId: string, memberSeq: number, roles: GroupUserRoleType[], accessType?: BoardAccessType): Promise<Board>;

    removeGroupBoardMember(boardId: string, memberSeq: number): Promise<void>;

    // connector
    testSFDCConnection(config: ConnectorSFDCConfig): Promise<ClientResponse>

    disConnectToWealthBox (config: WealthBoxConfig): Promise<ClientResponse>

    scheduleSFDCJob(config: ConnectorSFDCConfig): Promise<ClientResponse>

    testGlobalRelayConnection(config: ConnectorGlobalRelayConfig): Promise<ClientResponse>

    scheduleGlobalRelayJob(config: ConnectorGlobalRelayConfig): Promise<ClientResponse>

    testHubSpotConnection(config: ConnectorHubSpotConfig): Promise<ClientResponse>

    scheduleHubSpotJob(config: ConnectorHubSpotConfig): Promise<ClientResponse>

    scheduleCommonCRMJob(config: ConnectorScheduleType, type: string): Promise<ClientResponse>

    getSmarshLog(start: number, end: number): Promise<any>

    testCommonConnection (type: string, config: Record<string, string>): Promise<ClientResponse>

    testDynamicsConnection(config: ConnectorDynamicsConfig): Promise<ClientResponse>

    testFilevineConnection(config: FilevineConfig): Promise<ClientResponse>

    testSmarshConnection(config: SmarshConfig): Promise<ClientResponse>

    scheduleDynamicsJob(config: ConnectorDynamicsConfig): Promise<ClientResponse>

    // misc
    readGroupCapability(): Promise<GroupCapability>;

    readUserBusyTime(userIds: Array<string>, startTime: string, endTime: string, accessToken?: string): Promise<Group>;

    readGroupTasks(timestamp: number): Promise<Group>;

    // internal use
    onGroupUpdated(group: Group): void;

    onRoutingRequestsUpdated(group: Group): void;
}
