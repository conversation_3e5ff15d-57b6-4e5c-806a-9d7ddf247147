import { ClientParam } from './../proto/generated/ClientParam';
import { BoardTag } from './../proto/generated/BoardTag';
import { User } from './../proto/generated/User';
import { BoardSignee } from './../proto/generated/BoardSignee';
import { BoardPage } from './../proto/generated/BoardPage';
import { BoardSignature } from "../proto/generated/BoardSignature";
import { Board } from "../proto/generated/Board";
import { ClientRequestType } from "../proto/generated/ClientRequestType";
import { ClientResponse } from "../proto/generated/ClientResponse";
import { ClientRequestParameter } from '../proto/generated/ClientRequestParameter';
import { ClientRequest } from '../proto/generated/ClientRequest';
import { MxSPath, MxPageElementType, MxSignatureElement, BoardPageElement, ISignatureUpdateOption } from './../api/defines';
import { Ajax } from "../network/ajax";
import { boardRequestNode, boardRequestNodeWithOption, requestNode } from "../network/requestNode";
import { spathToObject, uuid, parseFileSequenceFromSPath } from "../util";
import { getBoardById, getInstantBoardById } from '../data/cache/cacheMgr';
import { MxBoard } from '../api/mxBoard';
import { BoardSignatureStatus } from '../proto/generated/BoardSignatureStatus';
import { BoardDataReference } from '../proto/generated/BoardDataReference';
import { Connection } from '../network/connection';
import { BoardActor } from '../proto/generated/BoardActor';

function getSignatureBoard(boardId: string, signatureSequence: number | number[]): Board {
    let board: Board = {
        id: boardId,
        signatures: []
    }

    if (Array.isArray(signatureSequence)) {
        signatureSequence.forEach(sig => {
            board.signatures.push({sequence: sig})
        })
    }else {
        board.signatures.push({sequence: signatureSequence})
    }

    return board;
}

function listSignatures(boardId: string): Promise<ClientResponse> {
    let board: Board = {id: boardId};
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_LIST_SIGNATURES, board));
}

function readOnGoingSignatures(boardId: string): Promise<ClientResponse> {
    let board: Board = {id: boardId};
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ_ONGOING_SIGNATURES, board));
}
  
function readSignature(boardId:string, sequence: number): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: sequence
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ_SIGNATURE, board));
}

function createSignature(boardId: string, filePath: MxSPath, newFileName?: string, isTemplate?: boolean): Promise<ClientResponse> {
    let board: Board = spathToObject(filePath, boardId);
    let params: ClientParam[] = [];
    if (newFileName) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_NEW_FILE_NAME_SEQUENCE,
            uint64_value: parseFileSequenceFromSPath(filePath),
            string_value: newFileName
          });
    }

    if (isTemplate) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_CREATE_SIGNATURE_AS_TEMPLATE,
          });
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_SIGNATURE, board, params));
}

function createBlankSignature(boardId: string, signature: BoardSignature): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [signature]
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_SIGNATURE, board));
}


function updateSignature(boardId: string, signatureSequence: number, signature: BoardSignature, opt?: ISignatureUpdateOption): Promise<ClientResponse> {
    let params: ClientParam[] = [];
    if(opt?.suppressFeed){
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
        })
    }

    if(opt?.forceUpdateFeed){
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_UPDATE_FEED
        })
    }

    if(opt?.forceReadyFeed){
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_READY_FEED
        })
    }

    if(opt?.forceReopenFeed){
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_REOPEN_FEED
        })

        signature.coc = 0
        signature.original = 0
        signature.original_with_coc = 0
    }

    let board: Board = {
        id: boardId,
        signatures: [{
            ...signature,
            sequence: signatureSequence,
        }]
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_SIGNATURE, board, params));
}

function deleteSignature(boardId:string, signatureSequence:number | number[], noFeed?:boolean): Promise<ClientResponse> {
    let params: ClientParam[] = [];
    if(noFeed){
        params =[{
            name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
        }]
    }
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_DELETE_SIGNATURE, getSignatureBoard(boardId, signatureSequence),params));
}

function startSignature(boardId:string, signatureSequence: number): Promise<ClientResponse> {
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_START_SIGNATURE, getSignatureBoard(boardId, signatureSequence)));
}
  
function submitSignature(boardId: string,signatureSequence: number, elements: MxSignatureElement[], keepStatusUnchanged?: boolean, jwt?: string): Promise<ClientResponse> {
    let params: ClientParam[] = [];
    if(keepStatusUnchanged){
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_SUBMIT_SIGNATURE_KEEP_STATUS_UNCHANGED
        })
    }

    if (jwt) {
        params.push({
            name:ClientRequestParameter.CLIENT_PARAM_JWT,
            string_value: jwt
        })
    }

    let pages: BoardPage[] = [];
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: pages,
        }]
    };

    elements.forEach((e) => {
        pages.push({
            sequence: e.pageSequence,
            contents: [{
                sequence: e.elementSequence,
                svg_tag: e.svgTag
            }]
        })
    });

    return Connection.getInstance().sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SUBMIT_SIGNATURE, board, params));
    // return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SUBMIT_SIGNATURE, board, params));
}

function declineSignature(boardId: string,signatureSequence: number, message: string, jwt?: string): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
        }]
    };

    let params: ClientParam[] = [{
        'name': ClientRequestParameter.BOARD_REQUEST_SIGNEE_MESSAGE,
        'string_value': message || ''
    }];

    if (jwt) {
        params.push({
            name:ClientRequestParameter.CLIENT_PARAM_JWT,
            string_value: jwt
        })
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SUBMIT_SIGNATURE, board, params));
}

function addSignatureSignee(boardId: string, signatureSequence: number, actors: BoardActor | BoardActor[]): Promise<ClientResponse> {
    let signees: BoardSignee[] = [];
    if (!Array.isArray(actors)) {
        actors = [actors];
    }

    actors.forEach((actor) => {
        signees.push({actor})
    });

    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            signees: signees,
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_ADD_SIGNATURE_SIGNEE, board));
}

function updateSignatureSignee(boardId: string, signatureSequence: number, signeeSequence: number, signee: BoardSignee, isUpdateOthers?: boolean): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            signees: [{
                ...signee,
                sequence: signeeSequence,
            }],
        }]
    };

    let reqType: ClientRequestType = isUpdateOthers === true ? ClientRequestType.BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE : ClientRequestType.BOARD_REQUEST_SIGNEE_UPDATE;
    return Ajax.sendRequest(boardRequestNode(reqType, board));
}

function updateSignatureSignees(boardId: string, signatureSequence: number, signees: BoardSignee[], suppressFeed?: boolean): Promise<ClientResponse> {
    let params: ClientParam[] = suppressFeed ? [{ name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED }] : [];

    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            signees: signees
        }]
    };

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE, board, params));
}

function deleteSignatureSignee(boardId: string, signatureSequence: number, signeeSequence: number | number[]): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            signees: [],
        }]
    };

    if (Array.isArray(signeeSequence)) {
        signeeSequence.forEach(seq => {
            board.signatures[0].signees.push({sequence: seq})
        })
    }else {
        board.signatures[0].signees.push({sequence: signeeSequence})
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_REMOVE_SIGNATURE_SIGNEE, board));
}

function createSignatureElement(boardId: string, signatureSequence: number, pageSequence: number, content: string, elementType: MxPageElementType, client_uuid?: string): Promise<ClientResponse> {
    let tags: BoardTag[];
    if (elementType) {
        tags = [];
        tags.push({
            name: 'TYPE',
            uint64_value:elementType
        })
    }

    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: [{
                sequence: pageSequence,
                contents: [{
                    client_uuid:client_uuid|| uuid(),
                    svg_tag:content,
                    tags: tags
                }]
            }],
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_SIGNATURE_PAGE_ELEMENT, board));
}

function batchCreateSignatureElements(boardId: string, signatureSequence: number, pages: BoardPage[], suppressFeed?: boolean): Promise<ClientResponse> {
    let params: ClientParam[] = suppressFeed ? [{ name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED }] : [];

    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: pages,
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_SIGNATURE_PAGE_ELEMENT, board, params));
}

function deleteSignatureElement(boardId: string, signatureSequence: number, pageSequence: number, client_uuid: string): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: [{
                sequence: pageSequence,
                contents: [{
                    client_uuid: client_uuid,
                    is_deleted: true
                }]
            }],
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_DELETE_SIGNATURE_PAGE_ELEMENT, board));
}

function deleteSignatureElements(boardId: string, signatureSequence: number, pages: BoardPage[]): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: pages,
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_DELETE_SIGNATURE_PAGE_ELEMENT, board));
}

function updateSignatureElement(boardId: string, signatureSequence: number, pageSequence: number, client_uuid: string, content: string, ddrs?: BoardDataReference[], readOnly?: boolean): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: [{
                sequence: pageSequence,
                contents: [{
                    client_uuid: client_uuid,
                    svg_tag: content,
                    readonly: readOnly
                }],
                ddrs
            }],
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT, board));
}


function batchUpdateSignatureElements(boardId: string, signatureSequence: number, pages: BoardPage[], suppressFeed?: boolean): Promise<ClientResponse> {
    let params: ClientParam[] = suppressFeed ? [{ name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED }] : [];

    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: pages
        }]
    };
    return Connection.getInstance().sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT, board, params));
    // return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT, board, params));
}

function assignElementToSignee(boardId: string, signatureSequence: number, signeeSequence: number, elements: number[]): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            signees: [{
                sequence: signeeSequence,
                elements: elements
            }]         
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE, board));
}

function uploadSignatureResourceToPage(boardId: string, signatureSequence: number, pageSequence: number, name: string, url: string): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            pages: [{
                sequence: pageSequence,
                resources: [{
                    name: name
                }]
            }]       
        }]
    };
    let params: ClientParam[] = [{
        'name': ClientRequestParameter.RESOURCE_UPLOAD_RESOURCE_URL,
        'string_value': url
    }];

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPLOAD_SIGNATURE_RESOURCE, board, params));
}

function copySignature(fromBoardId: string, sigSeq: number, toBoardId: string, keepCreator?: boolean, copyWithTotalUsedCount?: boolean, isTemplate?: boolean, status?: BoardSignatureStatus, signature: BoardSignature={}, copyWithWorkflow?: boolean): Promise<ClientResponse> {
    let toBoard: Board = {id: toBoardId};
    let fromBoard: Board = {
        id: fromBoardId,
        signatures:[{
            sequence: sigSeq,
            ...signature,
            status: status
        }]
    };

    if (isTemplate === true || isTemplate === false) {
        fromBoard.signatures[0].is_template = isTemplate;
    }

    let user: User = {
        boards: [{board: fromBoard}]
    }

    let params: ClientParam[] = [];
    if (keepCreator) {
        params.push({name: ClientRequestParameter.BOARD_REQUEST_COPY_SIGNATURE_KEEP_CREATOR})
    }

    if (copyWithTotalUsedCount) {
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_COPY_WITH_TOTAL_USED_COUNT,
        })
    }
    
    if (copyWithWorkflow) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_COPY_WITH_WORKFLOW,
        })
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE,
        })
    }

    // return Ajax.sendRequest(requestNode(ClientRequestType.BOARD_REQUEST_COPY_SIGNATURE, params, user, toBoard));
    let mxBoard: MxBoard = getBoardById(fromBoardId);
    if (!mxBoard) {
      // try to find instant board
      mxBoard = getInstantBoardById(fromBoardId);
    }

    let req: ClientRequest = boardRequestNode(ClientRequestType.BOARD_REQUEST_COPY_SIGNATURE, toBoard, params);
    if (mxBoard && mxBoard.option) {
        req = boardRequestNodeWithOption(ClientRequestType.BOARD_REQUEST_COPY_SIGNATURE, toBoard, params, mxBoard.option);
    }
    
    req.object.user = user;
    return Ajax.sendRequest(req);
}

function reopenSignature(boardId: string, signatureSequence:number, generateReopenEvent?: boolean): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence
        }]
    };

    let params: ClientParam[] = [];
    if (generateReopenEvent) {
        params.push({name: ClientRequestParameter.BOARD_REQUEST_REOPEN_EVENT})
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SIGNATURE_REOPEN, board, params));
}

function resetSignatureStatus(boardId: string, signatureSequence:number): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SIGNATURE_RESET_STATUS, board));
}

function updateSignatureViewTime(boardId: string, signatureSequence:number): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_VIEW_SIGNATURE, board));
}

function addSignatureAttachment(boardId: string, signatureSequence: number, file: MxSPath, isReply?: boolean, suppressFeed?: boolean): Promise<ClientResponse> {
    let refBoard: Board = spathToObject(file);
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            references: [{
                board: refBoard
            }]
        }]
    };

    let params: ClientParam[] = [];

    if (isReply) {
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_FILE_REPLY
          });
    }

    if (suppressFeed){
        params.push({
          name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
        });
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SIGNATURE_UPDATE_ATTACHMENT, board, params));
}

function removeSignatureAttachment(boardId: string, signatureSequence: number, referenceSequence: number, suppressFeed?: boolean): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
            references: [{
                sequence: referenceSequence,
                is_deleted: true
            }]
        }]
    };

    let params: ClientParam[] = [];
    if (suppressFeed){
        params.push({
          name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
        });
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SIGNATURE_DELETE_ATTACHMENT, board));
}


function replaceSignaturePagesFromFile(boardId: string, signatureSequence: number, fromBoardId: string, fromFilePath: MxSPath, suppressFeed?: boolean): Promise<ClientResponse> {
    let srcBoard: Board = spathToObject(fromFilePath, fromBoardId);
    let dstBoard: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
        }]
    };

    let user: User = {
        boards: [{board: srcBoard}]
    }

    let params: ClientParam[] = [];
    if (suppressFeed){
        params.push({
          name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
        });
    }

    return Ajax.sendRequest(requestNode(ClientRequestType.BOARD_REQUEST_SIGNATURE_REPLACE_PAGES_FROM_FILE, params, user, dstBoard));
}

function replaceSignature(boardId: string, signatureSequence: number, fromBoardId: string, fromSignatureSequence: number, suppressFeed?: boolean): Promise<ClientResponse> {
    let srcBoard: Board = {
        id: fromBoardId,
        signatures: [{
            sequence: fromSignatureSequence,
        }]
    };

    let dstBoard: Board = {
        id: boardId,
        signatures: [{
            sequence: signatureSequence,
        }]
    };

    let user: User = {
        boards: [{board: srcBoard}]
    }

    let params: ClientParam[] = [];
    if (suppressFeed){
        params.push({
          name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
        });
    }

    return Ajax.sendRequest(requestNode(ClientRequestType.BOARD_REQUEST_SIGNATURE_REPLACE, params, user, dstBoard));
}

function resendSignatureReminder(boardId:string, signature: BoardSignature): Promise<ClientResponse> {
    const board: Board = {
      id: boardId,
      signatures: [signature]
    }
  
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SIGNATURE_RESEND_REMINDER, board));
  }



export {
    listSignatures,
    readOnGoingSignatures,
    readSignature,
    createSignature,
    createBlankSignature,
    updateSignature,
    deleteSignature,
    startSignature,
    submitSignature,
    declineSignature,
    addSignatureSignee,
    updateSignatureSignee,
    updateSignatureSignees,
    deleteSignatureSignee,
    createSignatureElement,
    batchCreateSignatureElements,
    deleteSignatureElement,
    updateSignatureElement,
    deleteSignatureElements,
    batchUpdateSignatureElements,
    assignElementToSignee,
    uploadSignatureResourceToPage,
    copySignature,
    reopenSignature,
    resetSignatureStatus,
    updateSignatureViewTime,
    addSignatureAttachment,
    removeSignatureAttachment,
    replaceSignature,
    replaceSignaturePagesFromFile,
    resendSignatureReminder
 } 