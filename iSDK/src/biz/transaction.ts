import { BoardTransaction } from "../proto/generated/BoardTransaction";
import { BoardReference } from "../proto/generated/BoardReference";
import { Board } from "../proto/generated/Board";
import { ClientRequestType } from "../proto/generated/ClientRequestType";
import { ClientResponse } from "../proto/generated/ClientResponse";
import { ClientRequestParameter } from "../proto/generated/ClientRequestParameter";
import { User } from "../proto/generated/User";
import { ClientParam } from "../proto/generated/ClientParam";
import { ClientRequest } from "../proto/generated/ClientRequest";
import { Ajax } from "../network/ajax";
import { boardRequestNode, boardRequestNodeWithOption, requestNode } from "../network/requestNode";
import { ICreateTransactionAttachmentParam, IResourceInfo, ITransactionUpdateOption, MxSPath, TransactionStatus } from "../api/defines";
import { mergeObject, parseFileSequenceFromSPath, spathToObject } from "../util";
import { getBoardById, getInstantBoardById } from "../data/cache/cacheMgr";
import { MxBoard } from "../api/mxBoard";
import { TransactionStep } from "../proto/generated/TransactionStep";

function readOnGoingTransactions(boardId: string): Promise<ClientResponse> {
    let board: Board = { id: boardId };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ_ONGOING_SIGNATURES, board));
}

function listTransactions(boardId: string): Promise<ClientResponse> {
    let board: Board = { id: boardId, revision: 0 };
    let params = [{
        name: ClientRequestParameter.OUTPUT_FILTER_STRING,
        string_value: "/board/transactions"
    },{ // TODO 
        name: ClientRequestParameter.OUTPUT_FILTER_STRING,
        string_value: "/board/workflows"
    }];
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ, board, params));
}

function readTransaction(boardId: string, transactionSeq: number): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
        }]
    };

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_READ, board));
}

function submitTransactionStep(boardId: string, transaction: BoardTransaction, suppressFeed?: boolean): Promise<ClientResponse> {
    let params = []
    if (suppressFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
        })
    }
    let board: Board = {
        id: boardId,
        transactions: [transaction]
    };

    return sendTransactionRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_STEP_SUBMIT, board, params));
}

function sendTransactionRequest(req: ClientRequest): Promise<ClientResponse> {
    const largeBodyLimit = 50*1024
    let card: string = req?.object?.board?.transactions?.[0]?.card
    if (card && card.length > largeBodyLimit) {
        console.log('sendTransactionRequest with multipart')
        req.object.board.transactions[0].card = ''
        let blobs: Map<string, Blob> = new Map();
        let reqBlob = new Blob([JSON.stringify(req)], { type: 'application/json' });
        let cardBlob = new Blob([card], { type: 'application/json' });
        blobs.set('request', reqBlob);
        blobs.set('card', cardBlob);
        return Ajax.sendMultipartRequest(blobs)    
    }

    return Ajax.sendRequest(req);
}

function createTransaction(boardId: string, transaction: BoardTransaction, suppressFeed?: boolean): Promise<ClientResponse> {
    let params = [];
    if (suppressFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
        })
    }

    let board: Board = {
        id: boardId,
        transactions: [{
            ...transaction
        }]
    };
    
    return sendTransactionRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_CREATE, board, params));
}

function updateTransaction(boardId: string, transactionSeq: number, transaction: BoardTransaction, opt?: ITransactionUpdateOption): Promise<ClientResponse> {
    let params: ClientParam[] = [];
    if (opt?.suppressFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
        })
    }

    if (opt?.forceUpdateFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_UPDATE_FEED,
        })
    }

    if (opt?.forceReadyFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_READY_FEED,
        })
    }

    if (opt?.forceReopenFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_REOPEN_FEED,
        })
        transaction.original = 0
        transaction.original_masked = 0
        transaction.original_csv = 0
        transaction.original_csv_masked = 0
    }

    if (opt?.createCustomFolder) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_CREATE_ATTACHMENT_FOLDER,
        })
    }

    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
            ...transaction
        }]
    };

    return sendTransactionRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_UPDATE, board, params));
}

function deleteTransaction(boardId: string, transactionSeq: number | number[], suppressFeed?: boolean): Promise<ClientResponse> {
    let params = [];
    if (suppressFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
        })
    }

    let reqType: ClientRequestType = ClientRequestType.BOARD_REQUEST_TRANSACTION_DELETE;
    let transactions: BoardTransaction[] = [];
    if (Array.isArray(transactionSeq)) {
        reqType = ClientRequestType.BOARD_REQUEST_TRANSACTION_DELETE_BATCH;
        transactionSeq.forEach(seq => {
            transactions.push({sequence: seq})
        })
    }else {
        transactions.push({sequence: transactionSeq})
    }

    let board: Board = {
        id: boardId,
        transactions: transactions
    };

    return Ajax.sendRequest(boardRequestNode(reqType, board, params));
}


function reopenTransactionStep(boardId: string, transactionSeq: number, stepSeq: number|number[]): Promise<ClientResponse> {
    let params = [];
    if (!Array.isArray(stepSeq)) {
        stepSeq = [stepSeq]
    }

    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
            steps: stepSeq.map(seq => ({sequence: seq}))  
        }]
    };

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_STEP_REOPEN, board, params));
}

function removeAttachment(boardId: string, transactionSeq: number, referenceSeqs: number | number[], setLastModifiedTime?: boolean, suppressFeed?: boolean): Promise<ClientResponse> {
    let params = [];
    if (setLastModifiedTime) {
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_SET_LAST_MODIFIED_TIME
        })
    }

    if (suppressFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
        })
    }

    let references: BoardReference[] = []
    if (Array.isArray(referenceSeqs)) {
        references = referenceSeqs.map(seq => {
            return {
                sequence: seq,
                is_deleted: true
            }
        })
    }else if (typeof referenceSeqs === 'number') {
        references = [{
            sequence: referenceSeqs,
            is_deleted: true
        }]
    }

    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
            references
        }]
    };

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_DELETE_ATTACHMENT, board, params));
}


function createTransactionAttachmentFromResource(fromBoardId: string, resource: IResourceInfo, toBoardId: string, toTransactionSeq: number, opt?: ICreateTransactionAttachmentParam): Promise<ClientResponse> {
    let params: ClientParam[] = [];

    if (opt?.setLastModifiedTime) {
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_SET_LAST_MODIFIED_TIME
        })
    }

    if (opt?.customData) {
        params.push({
            name:ClientRequestParameter.CLIENT_PARAM_CUSTOM_DATA,
            string_value: opt?.customData
        })
    }

    if (opt?.isSupportFile) {
        params.push({
            name:ClientRequestParameter.CLIENT_PARAM_REFERENCE_TYPE,
            string_value: "support"
        })
    }

    let fromBoard: Board = {id: fromBoardId}
    if (resource?.signatureSeq) {
        fromBoard.signatures = [{
            sequence: resource?.signatureSeq,
            resources: [{
                sequence: resource?.resourceSeq
            }]
        }]
    }else {
        fromBoard.resources = [{
            sequence: resource?.resourceSeq
        }]
    }

    let user: User = {
        boards: [{
            board: fromBoard
        }]
    };

    let board: Board = {
        id: toBoardId,
        transactions: [{
            sequence: toTransactionSeq
        }]
    };

    return Ajax.sendRequest(requestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_RESOURCE, params, user, board));
}
    

function createTransactionAttachmentFromFile(fromBoardId: string, files: MxSPath[], toBoardId: string, toTransactionSeq: number, opt?: ICreateTransactionAttachmentParam): Promise<ClientResponse> {
    let params: ClientParam[] = [];
    if (opt?.setLastModifiedTime) {
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_SET_LAST_MODIFIED_TIME
        })
    }

    if (opt?.customData) {
        params.push({
            name:ClientRequestParameter.CLIENT_PARAM_CUSTOM_DATA,
            string_value: opt?.customData
        })
    }

    if (opt?.isSupportFile) {
        params.push({
            name:ClientRequestParameter.CLIENT_PARAM_REFERENCE_TYPE,
            string_value: "support"
        })
    } else if(opt?.isPDFForm) {
        params.push({
            name:ClientRequestParameter.CLIENT_PARAM_REFERENCE_TYPE,
            string_value: "pdf_form"
        })
    }


    if (opt?.suppressFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
        })
    }

    if (opt?.viewToken) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN,
            string_value: opt?.viewToken
        })
    }

    let fromBoard: Board = {id: fromBoardId};
    files && files.forEach(f => {
        let board: Board = spathToObject(f, fromBoardId);
        mergeObject(fromBoard, board);
    });

    let user: User = {
        boards: [{
            board: fromBoard
        }]
    };

    let board: Board = {
        id: toBoardId,
        transactions: [{
            sequence: toTransactionSeq
        }]
    };

    // keep file order
    let orderNum = 10000;
    for (let i = 0; i < files.length; i++) {
        params.push({
            name:ClientRequestParameter.BOARD_REQUEST_ORDER_NUMBER_SEQUENCE,
            uint64_value: parseFileSequenceFromSPath(files[i]),
            string_value: String(orderNum),
        });
        orderNum += 100;
    }

    return Ajax.sendRequest(requestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_FILE, params, user, board));
}

function createTransactionAttachmentFromRemoteUrl(boardId: string, transactionSeq: number, url: string, name: string, authToken: string, contentType?: string): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        transactions:[{
            sequence: transactionSeq
        }],
        resources: [{
            name: name
        }]
    };

    let params: ClientParam[] = [{
        name: ClientRequestParameter.RESOURCE_REQUEST_RESOURCE_TYPE,
        string_value: "original"
    },{
        name: ClientRequestParameter.RESOURCE_UPLOAD_RESOURCE_URL,
        string_value: url
    },{
        name: ClientRequestParameter.RESOURCE_UPLOAD_RESOURCE_AUTHORIZATION,
        string_value: authToken
    }];

    if (contentType) {
        params.push({
            name: ClientRequestParameter.RESOURCE_UPLOAD_RESOURCE_CONTENT_TYPE,
            string_value: contentType
        })
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_URL, board, params));
}

function createTransactionAttachmentReply(boardId: string, transactionSequence: number, file: MxSPath, suppressFeed?: boolean): Promise<ClientResponse> {
    let params: ClientParam[] = [{
        name: ClientRequestParameter.BOARD_REQUEST_FILE_REPLY
    }];

    if (suppressFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
        })
    }

    let refBoard: Board = spathToObject(file);


    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSequence,
            references: [{
                board: refBoard
            }]
        }]
    };

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_UPDATE_ATTACHMENT, board, params));
}



function uploadTransactionResourceFromUrl(boardId: string, transactionSeq: number, url: string, name: string, client_uuid?: string): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        transactions:[{
            sequence: transactionSeq,
            resources: [{
                name: name,
                client_uuid: client_uuid
            }]
        }]
    };

    let params: ClientParam[] = [{
        name: ClientRequestParameter.RESOURCE_UPLOAD_RESOURCE_URL,
        string_value: url
    }];

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE_URL, board, params));
}

function copyTransactions(fromBoardId: string, transactionSeqs: number[],toBoardId: string, keepCreator?: boolean, copyWithTotalUsedCount?: boolean, copyWithLastModifiedTime?: boolean, suppressUserActivity?: boolean, forceGenerateCreateFeed?: boolean, copyWithWorkflow?: boolean): Promise<ClientResponse> {
    let params = [];
    if (keepCreator) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_COPY_TRANSACTION_KEEP_CREATOR,
        })
    }

    if (copyWithTotalUsedCount) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_COPY_WITH_TOTAL_USED_COUNT,
        })
    }

    if (copyWithLastModifiedTime) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_COPY_WITH_LAST_MODIFIED_TIME,
        })
    }

    if (suppressUserActivity) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_USER_ACTIVITY,
        })
    }

    if (forceGenerateCreateFeed) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_CREATE_FEED,
        })
    }

    if (copyWithWorkflow) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_COPY_WITH_WORKFLOW,
        })
    }

    let fromBoard: Board = {
        id: fromBoardId,
        transactions: []
    };
    transactionSeqs.forEach(seq => {
        fromBoard.transactions.push({sequence: seq})
    })

    let user: User = {
        boards: [{
            board: fromBoard
        }]
    };

    let toBoard: Board = {
        id: toBoardId
    };

    // return Ajax.sendRequest(requestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_COPY, params, user, toBoard));
    let mxBoard: MxBoard = getBoardById(fromBoardId);
    if (!mxBoard) {
      mxBoard = getInstantBoardById(fromBoardId);
    }

    let req: ClientRequest = boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_COPY, toBoard, params);
    if (mxBoard && mxBoard.option) {
        req = boardRequestNodeWithOption(ClientRequestType.BOARD_REQUEST_TRANSACTION_COPY, toBoard, params, mxBoard.option);
    }

    req.object.user = user;
    return Ajax.sendRequest(req);
}

function reopenTransaction(boardId: string, transactionSeq:number, generateReopenEvent?: boolean, status?: TransactionStatus): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
            status
        }]
    };

    let params: ClientParam[] = [];

    if (generateReopenEvent) {
        params.push({
            name: ClientRequestParameter.BOARD_REQUEST_REOPEN_EVENT,
        })
    }

    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_REOPEN, board, params));
}

function resetTransactionStatus(boardId: string, transactionSeq:number, steps?: TransactionStep[]): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
            steps,
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_RESET_STATUS, board));
}

function updateTransactionViewTime(boardId: string, transactionSeq:number, stepSeq: number): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
            steps:[{
                sequence: stepSeq
            }]
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_VIEW, board));
}

function updateTransactionReminder(boardId: string, transactionSeq:number, reminderTime: number): Promise<ClientResponse> {
    let board: Board = {
        id: boardId,
        transactions: [{
            sequence: transactionSeq,
            reminders: [{
                reminder_time: reminderTime
            }]
        }]
    };
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_REMINDER_UPDATE, board));
}

function resendTransactionReminder(boardId:string, transaction: BoardTransaction): Promise<ClientResponse> {
    const board: Board = {
      id: boardId,
      transactions: [transaction]
    }
  
    return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TRANSACTION_RESEND_REMINDER, board));
  }

export {
    readOnGoingTransactions,
    listTransactions,
    readTransaction,
    submitTransactionStep,
    reopenTransactionStep,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    removeAttachment,
    copyTransactions,
    createTransactionAttachmentFromResource,
    createTransactionAttachmentFromFile,
    createTransactionAttachmentFromRemoteUrl,
    createTransactionAttachmentReply,
    uploadTransactionResourceFromUrl,
    reopenTransaction,
    resetTransactionStatus,
    updateTransactionViewTime,
    updateTransactionReminder,
    resendTransactionReminder
}
