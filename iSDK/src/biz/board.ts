import {<PERSON><PERSON><PERSON>, ITransferActionParam, MxBoardOption, MxInviteMemberOption, UserIdentity, UserType} from './../api/defines';
import {Connection} from './../network/connection';
import {Ajax} from "../network/ajax";
import {boardRequestNode, boardRequestNodeWithOption, requestNode, userRequestNode} from "../network/requestNode";
import {updateUserBoard} from './user';
import {Board} from "../proto/generated/Board";
import {BoardAccessType} from "../proto/generated/BoardAccessType";
import {ClientRequestType} from "../proto/generated/ClientRequestType";
import {ClientResponse} from "../proto/generated/ClientResponse";
import {ObjectFeed} from "../proto/generated/ObjectFeed";
import {NotificationLevel} from '../proto/generated/NotificationLevel';
import {BoardUser} from '../proto/generated/BoardUser';
import {ClientRequestParameter} from './../proto/generated/ClientRequestParameter';
import {ClientParam} from './../proto/generated/ClientParam';
import {Group} from "../proto/generated/Group";
import {User} from "../proto/generated/User";
import {BoardType} from "../proto/generated/BoardType";
import {RequestingUserStatus} from "../proto/generated/RequestingUserStatus";
import { BoardProperty } from '../proto/generated/BoardProperty';
import {currentOrg} from "../data/cache/cacheMgr";
import {MxErr} from '../core/error';
import uniqBy from 'lodash/uniqBy';

function createBoard(board: Board, group?: Group): Promise<ClientResponse> {
  let req = boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE, board)
  if (group) {
    req.object.group = group
  }
  return Ajax.sendRequest(req);
}

function deleteBoard(boardId: string): Promise<ClientResponse> {
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_DELETE, {id: boardId}));
}

function updateBoard(boardId: string, board: Board, suppressFeed?: boolean): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  if(suppressFeed){
    params =[{
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    }]
  }
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE, {id: boardId, ...board}, params));
}

function convertTempBoardToNormalBoard(boardId: string): Promise<ClientResponse> {
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SET_ISTEMP_OFF, {id: boardId}));
}

function joinBoard(boardId: string, noFeed?: boolean, viewToken?: string, invite_code?: string): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  if(noFeed){
    params =[{
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    }]
  }
  if (viewToken) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN,
      string_value: viewToken
    })
  }
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_JOIN, {id: boardId, invite_code}, params));
}

function leaveBoard(boardId: string): Promise<ClientResponse> {
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_LEAVE, {id: boardId}));
}

function subscribeBoard(boardId:string, revision:number): Promise<ClientResponse> {
  return Connection.getInstance().subscribeBoard(boardId, revision);
}

function unsubscribeBoard(boardId:string): Promise<ClientResponse> {
  return Connection.getInstance().unsubscribeBoard(boardId);
}

function readCover(boardId: string, option?: MxBoardOption): Promise<ClientResponse> {
  let params: ClientParam[] = [{
    name: ClientRequestParameter.BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE,
  }];

  if (option && option.isWorkflow) {
    const filters =['/board/transactions', '/board/signatures', '/board/todos', '/board/sessions']
    for(let f of filters) {
      params.push({
        name: ClientRequestParameter.OUTPUT_INCLUDE_STRING,
        string_value: f
      })
    }
 }

  return Ajax.sendRequest(boardRequestNodeWithOption(ClientRequestType.BOARD_REQUEST_READ_COVER, {id: boardId}, params, option));
}

function readContentLibraryBoard(boardId: string, option?: MxBoardOption): Promise<ClientResponse> {
  const filters = ['/board/local', '/board/users', '/board/transactions', '/board/signatures', '/board/todos', '/board/page_groups', '/board/resources', '/board/reference_links']
  return readBoard(boardId, filters)
}

function readFullBoard(boardId: string, option?: MxBoardOption): Promise<ClientResponse> {
  let board: Board = {id: boardId};
  let params: ClientParam[] = [{
    name: ClientRequestParameter.SUBSCRIBE_REQUEST_NOHANG,
  }, {
    name: ClientRequestParameter.BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE,
  }];

  return Ajax.sendRequest(boardRequestNodeWithOption(ClientRequestType.BOARD_REQUEST_SUBSCRIBE, board, params, option));
}

function readBoard(boardId: string, filters: string[], request_params?: ClientParam[]): Promise<ClientResponse> {
  let board: Board = {id: boardId};
  let params: ClientParam[] = [];
  if (filters && filters.length > 0) {
    filters.forEach(filter => {
      params.push({
        name: ClientRequestParameter.OUTPUT_FILTER_STRING,
        string_value: filter
      })
    })
  }

  if (request_params && request_params.length > 0) {
    request_params.forEach(request_param => {
      params.push(request_param)
    })
  }

  params.push({
    name: ClientRequestParameter.BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE,
  })
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ, board, params));
}

function readBoardByField(boardId: string, field: string, seqs: number[]): Promise<ClientResponse> {
  let board: Board = {id: boardId};
  board[field] = [];

  seqs && seqs.forEach(seq => {
    board[field].push({sequence: seq});
  })

  let params: ClientParam[] = [{
    name: ClientRequestParameter.BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE,
  }];

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ, board, params));
}

function inviteGroups(boardId: string, groups: Group[], option?: MxInviteMemberOption): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  option = option || {};

  if (option.addDirectly) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_INVITE_ADD_DIRECTLY
    })
  }
  if(option.noFeed){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    })
  }

  if(option.noNotification){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_PUSH_NOTIFICATION_OFF
    })
  }

  if(option.noEmail){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_EMAIL_OFF
    })
  }

  if(option.noSMS){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_SMS_OFF
    })
  }

  let boardUsers: BoardUser[] = groups?.map(g => {
    return {
      type: option.accessType,
      group: g
    }
  })
  
  let board: Board = {
    id: boardId,
    users: boardUsers,
  };

  let reqType: ClientRequestType = ClientRequestType.BOARD_REQUEST_INVITE;

  return Ajax.sendRequest(boardRequestNode(reqType, board, params));
}

function inviteUser(boardId: string, users: UserIdentity[], group?: Group, teams?: Group[], option?: MxInviteMemberOption): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  option = option || {};

  if (option.message) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_INVITE_MESSAGE,
      string_value: option.message
    })
  }
  if (option.addDirectly) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_INVITE_ADD_DIRECTLY
    })
  }
  if(option.noFeed){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    })
  }

  if(option.noNotification){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_PUSH_NOTIFICATION_OFF
    })
  }

  if(option.noEmail){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_EMAIL_OFF
    })
  }

  if(option.noSMS){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_SMS_OFF
    })
  }

  if (option.fromBoardId) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_INVITE_WITH_INFO_FROM,
      string_value: option.fromBoardId
    })
  }

  if (option.isForInstantOfflineMeet) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_SET_RSVP_ACCEPTED,
    })

    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_IS_INSTANT_MEET,
    })
  }

  let boardUsers: BoardUser[] = [];
  users && users.forEach(user => {
    let userKey = user.id || user.email || user.phone_number
    let inviteMsg = option.privateInviteMessages?.get(userKey) || undefined
    boardUsers.push({
      type: option.accessType,
      invite_msg: inviteMsg,
      user: user
    })
  });

  if (group) {
    let inviteMsg = option.privateInviteMessages?.get(group.id) || undefined
    boardUsers.push({
      type: option.accessType,
      invite_msg: inviteMsg,
      group: group
    })
  }

  let teamUsers: BoardUser[] = [];
  if (teams) {
    const uniqTeams = uniqBy(teams, 'id')
    uniqTeams.forEach(team => {
      let inviteMsg = option.privateInviteMessages?.get(team.id) || undefined
      teamUsers.push({
        type: option.accessType,
        invite_msg: inviteMsg,
        group: team
      })
    });
  }

  let board: Board = {
    id: boardId,
    users: boardUsers,
    teams: teamUsers
  };

  let reqType: ClientRequestType = ClientRequestType.BOARD_REQUEST_INVITE;
  if (option.isInviteOOOBackupUser) {
    reqType = ClientRequestType.BOARD_REQUEST_INVITE_OUT_OF_OFFICE_BACKUP_USER;
  }

  return Ajax.sendRequest(boardRequestNode(reqType, board, params));
}

function removeUser(boardId: string, seqs: number|number[], noFeed?: boolean): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  if(noFeed){
    params =[{
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    }]
  }

  if (!Array.isArray(seqs)) {
    seqs = [seqs];
  }

  let board: Board = {
    id: boardId,
    users: []
  };

  seqs.forEach(seq => {
    board.users.push({
      sequence: seq
    })
  })

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_EXPEL, board, params));
}

function removeTeam(boardId: string, teamSeqs: number[], suppressFeed?: boolean): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  if(suppressFeed){
    params =[{
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    }]
  }

  let boardUsers: BoardUser[] = [];
  teamSeqs.forEach(seq => {
    boardUsers.push({
      sequence: seq
    })
  });

  let board: Board = {
    id: boardId,
    teams: boardUsers
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_EXPEL, board, params));
}

function removeUserAndTeam(boardId: string, userSeqs: number[], teams: BoardUser[], noFeed?: boolean): Promise<ClientResponse> {

  let params: ClientParam[] = [];
  if(noFeed){
    params =[{
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    }]
  }

  let users: BoardUser[] = [];
  userSeqs && userSeqs.forEach(seq => {
    users.push({
      sequence: seq
    })
  });

  let board: Board = {
    id: boardId,
    users: users,
    teams: teams
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_EXPEL, board, params));
}
function setOwner(boardId: string, userSequence: number, noFeed?: boolean): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  if(noFeed){
    params =[{
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    }]
  }

  let board: Board = {
    id: boardId,
    users: [{
      sequence: userSequence
    }]
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SET_OWNER, board, params));
}

function updateUserAccessType(boardId: string, seqs: number | number[], type: BoardAccessType): Promise<ClientResponse> {
  if (!Array.isArray(seqs)) {
    seqs = [seqs];
  }

  let board: Board = {
    id: boardId,
    users: []
  };

  seqs && seqs.forEach(seq => {
    board.users.push({
      sequence: seq,
      type: type
    })
  })

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SET_ACCESS_TYPE, board));
}

function readFeedsByPagination(boardId: string, startSequence?: number, sizeBefore?: number, sizeAfter?: number): Promise<ClientResponse> {
  let params: ClientParam[] = [];

  if(sizeBefore){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_READ_SIZE_BEFORE,
      uint64_value: sizeBefore
    });
  }

  if(sizeAfter){
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_READ_SIZE_AFTER,
      uint64_value: sizeAfter
    });
  }

  let board: Board = {id: boardId};
  if(startSequence){
    board.feeds =[{
      sequence: startSequence
    }];
  }

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ_FLAT_FEEDS, board, params));
}

function readFlatFeeds(boardId: string, feedSeqs: number[]): Promise<ClientResponse> {
  if (!feedSeqs || feedSeqs.length === 0) {
    Promise.resolve([]);
  }

  let feeds: ObjectFeed[] = feedSeqs.map(e=> {
    return {sequence: e};
  });

  let board: Board = {
    id: boardId,
    feeds: feeds
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ_FLAT_FEEDS, board));
}

function updateTypeIndication(boardId: string): Promise<ClientResponse> {
  let board: Board = {id: boardId};
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_TYPE_INDICATION, board));
}

function readOnGoingDelegateFeeds(boardId:string): Promise<ClientResponse> {
  let board: Board = {id: boardId};
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ_ONGOING_DELEGATE_FEEDS, board));
}

function readUserActivities(boardId: string): Promise<ClientResponse> {
  let board: Board = {id: boardId, revision: 0};
  let params: ClientParam[] = [{
    name: ClientRequestParameter.OUTPUT_FILTER_STRING,
    string_value: "/board/user_activities"
  }];
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ, board, params));
}

function updateNotificationLevel(boardId:string, level:NotificationLevel): Promise<ClientResponse> {
  return updateUserBoard(boardId, {
    push_notification_level: level
  });
}

function markFeedAsUnRead(boardId:string, feedSeq: number): Promise<ClientResponse> {
  let board: Board = {
    id: boardId,
    feeds:[
      {
        sequence: feedSeq
      }
    ]
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SET_FEED_UNREAD, board));
}

function createViewToken(boardId:string, accessType: BoardAccessType, actorAs: User): Promise<ClientResponse> {
  let board: Board = {
    id: boardId,
    view_tokens:[
      {
        type: accessType,
        actor_file_as: actorAs
      }
    ]
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_VIEW_TOKEN, board));
}

function createResourceViewToken (boardId: string, resSeq: number): Promise<ClientResponse> {
  const board: Board = {
    id: boardId,
    view_tokens: [
      {
        resources: [
          {
            sequence: resSeq
          }
        ]
      }
    ]
  }
  
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_VIEW_TOKEN, board));
}

function createShareViewToken (boardId: string, memberOnly = false, code = ''): Promise<ClientResponse> {
  const board: Board = {
    id: boardId,
    view_tokens: [
      {
        is_share_token: true,
        member_only: memberOnly
      }
    ]
  }
  const params: ClientParam[] = [{name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED}];
  if (code) {
    //we always ask server side to generate password
    board.view_tokens[0].code = ''
    params.push({name: ClientRequestParameter.BOARD_REQUEST_RESET_CODE})
  }
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_VIEW_TOKEN, board, params));
}

function updateShareViewToken (boardId: string, sequence: number, memberOnly = false, code = ''): Promise<ClientResponse> {
  const board: Board = {
    id: boardId,
    view_tokens: [
      {
        is_share_token: true,
        sequence,
        code,
        member_only: memberOnly
      }
    ]
  }
  const params: ClientParam[] = []
  if (code) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_RESET_CODE
    })
  }
  params.push({
    name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
  })
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_VIEW_TOKEN, board, params));
}

function joinBoardByViewToken (viewToken: string, user: User, verifyCode?: string, googleJWT?: string, appleJWT?: string, viewTokenCode?: string) {
  const params: ClientParam[] = [{
    name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN,
    string_value: viewToken
  }];
  let requestType = ClientRequestType.BOARD_REQUEST_JOIN_BY_VIEW_TOKEN
  if (viewTokenCode) {
    params.push({
      name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN_CODE,
      string_value: viewTokenCode
    })
  }
  if (verifyCode) {
    params.push({
      name: user.email?ClientRequestParameter.USER_REQUEST_EMAIL_CODE:ClientRequestParameter.USER_REQUEST_SMS_CODE,
      string_value: verifyCode
    })
  } else if (appleJWT) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_APPLE_JWT,
      string_value: appleJWT
    })
    requestType = ClientRequestType.BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_APPLE_ID
  } else if (googleJWT) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_GOOGLE_JWT,
      string_value: googleJWT
    })
    requestType = ClientRequestType.BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_GOOGLE_ID
  }
  return Ajax.sendRequest(userRequestNode(requestType, user, params))
}

function joinBoardByManagementUser (boardId: string, user: User) {
  let requestType = ClientRequestType.BOARD_REQUEST_JOIN_BY_VIEW_TOKEN
  let group: Group = {
    members: [{
      user
    }]
  }

  return Ajax.sendRequest(requestNode(requestType, [], null, {id: boardId}, group))
}


function transferActions(boardId:string, param: ITransferActionParam): Promise<ClientResponse> {
  const { fromUserId, toUserId, fromTeamId, toTeamId, todos, esigns, transactions, workflow } = param
  let board: Board = {
    id: boardId,
    users: []
  }

  if (fromUserId) {
    board.users.push({
      user: {
        id: fromUserId
      }
    })
  }else if (fromTeamId) {
    board.users.push({
      group: {
        id: fromTeamId
      }
    })
  }else {
    // original action doesn't have assignee
    board.users.push({
      user: {}
    })
  }

  if (toUserId) {
    board.users.push({
      user: {
        id: toUserId
      }
    })
  }

  if (workflow) {
    if (!workflow.sequence || !workflow.steps || workflow.steps.length !== 1 || !workflow.steps[0].sequence) {
      return Promise.reject(MxErr.InvalidParam('invalid workflow param'));
    }
  }

  let board2: Board = {
    todos : todos || [],
    signatures : esigns || [],
    transactions : transactions || []
  }

  if (board2.todos.length > 0 || board2.signatures.length > 0 || board2.transactions.length > 0) {
    if (workflow) {
      board2 = {
        workflows: [{
          sequence: workflow.sequence,
          steps:[{
            sequence: workflow.steps[0].sequence,
            input: {
              board: board2
            }
          }]
      }]}
    }

    if (param.isTransferActionEditor) {
      if (board2.signatures?.length > 0) {
        board2.signatures[0].editor = {}
      }

    }

    let editor: BoardActor = null
    if (fromUserId) {
      board.users[0].user.boards = [{board: board2}]
      if (param.isTransferActionEditor) {
        editor = {user: {id: fromUserId}}
      }
    }else if (fromTeamId) {
      board.users[0].group.boards = [{board: board2}]
      if (param.isTransferActionEditor) {
        editor = {group: {id: fromTeamId}}
      }
    }else {
      board.users[0].user.boards = [{board: board2}]
    }

    if (editor) {
      if (board2.signatures?.length) {
        board2.signatures[0].editor = editor
      }else if (board2.transactions?.length) {
        board2.transactions[0].editor = editor
      }else if (board2.workflows?.[0]?.steps?.[0]) {
        board2.workflows[0].steps[0].editor = editor
        delete board2.workflows[0].steps[0].input
      }
    }
  }

  const params: ClientParam[] = []
  if(fromTeamId){
    params.push({
      name:ClientRequestParameter.BOARD_REQUEST_REASSIGN_FROM_GROUP_ID,
      string_value: fromTeamId
    })
  }

  if(toTeamId){
    params.push({
      name:ClientRequestParameter.BOARD_REQUEST_REASSIGN_TO_GROUP_ID,
      string_value: toTeamId
    })
  }

  if(param?.suppressFeed){
    params.push({
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    })
  }

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_ACTION_TRANSFER, board, params));
}

function resendInviteEmailSms(boardId:string, userId: string, isSms: boolean): Promise<ClientResponse> {
  let board: Board = {
    id: boardId,
    users: [{
      user: {
        id: userId
      }
    }]
  };

  let reqType: ClientRequestType = isSms ? ClientRequestType.BOARD_REQUEST_RESEND_INVITATION_SMS : ClientRequestType.BOARD_REQUEST_RESEND_INVITATION_EMAIL;

  return Ajax.sendRequest(boardRequestNode(reqType, board));
}

function bulkResendInviteEmailSms(boardId:string, userIds: string[], isSms: boolean): Promise<ClientResponse> {
  const users = userIds.map(id => {
    return {
      user: {
        id: id
      }
    }
  })

  let board: Board = {
    id: boardId,
    users
  };

  let reqType: ClientRequestType = isSms ? ClientRequestType.BOARD_REQUEST_RESEND_INVITATION_SMS : ClientRequestType.BOARD_REQUEST_RESEND_INVITATION_EMAIL;

  return Ajax.sendRequest(boardRequestNode(reqType, board));
}


function createInvitationViewToken(boardId:string, userId: string): Promise<ClientResponse> {
  let board: Board = {
    id: boardId,
    users: [{
      user: {
        id: userId
      }
    }]
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_CREATE_INVITATION_VIEW_TOKEN, board));
}

function setBoardType(boardId: string, type: BoardType): Promise<ClientResponse> {
  let board: Board = {
    id: boardId,
    type: type
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SET_BOARD_TYPE, board));
}

function setBoardActiveStatus(boardId: string, isActive: boolean): Promise<ClientResponse> {
  let board: Board = {
    id: boardId,
    is_inactive: !isActive
  };

  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_SET_ACTIVE, board));
}

function renewBoardWorkspaceId(boardId: string, boardViewToken: string): Promise<ClientResponse> {
  let origin = location.origin
  if (/localhost/.test(origin)) {
    origin = 'https://' + currentOrg.basicInfo.integrations[0].domain
  }
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_RENEW_WORKSPACE_ID, {id: boardId}, [{
    name: ClientRequestParameter.USER_REQUEST_HTML_URL,
    string_value: `${origin}/web/#applink?action=view&t=${boardViewToken}`
  }]));
}

function renewBoardPassword(boardId: string): Promise<ClientResponse> {
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_RESET_INVITE_CODE, {id: boardId}));
}

function readViewTokens(boardId: string): Promise<ClientResponse>  {
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_READ_VIEW_TOKENS, {id: boardId}));
}

function updateRequestingUser (boardId: string, sequence: number, approved: boolean, suppressFeed?: boolean, isInvalid = false): Promise<ClientResponse> {
  const user: BoardUser = {
    sequence
  }
  const params = []
  if (isInvalid) {
    user.requesting_user_status = RequestingUserStatus.REQUESTING_USER_STATUS_INVALID
  } else if (approved) {
    user.requesting_user_status = RequestingUserStatus.REQUESTING_USER_STATUS_APPROVED
  } else {
    user.requesting_user_status = RequestingUserStatus.REQUESTING_USER_STATUS_DENIED
  }
  if(suppressFeed){
    params.push({
      name:ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED
    })
  }
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_REQUESTING_USER, {
    id: boardId,
    requesting_users: [user]
  }, params));
}

function inviteRequestingUser (boardId: string, sequence: number, isClient: boolean): Promise<ClientResponse> {
  const user: BoardUser = {
    sequence,
    user: {
      type: isClient? UserType.USER_TYPE_LOCAL:UserType.USER_TYPE_NORMAL
    }
  }
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.GROUP_REQUEST_INVITE_BOARD_REQUESTING_USER, {
    id: boardId,
    requesting_users: [user]
  }));
}

function downloadBoardFolder (boardId: string, type: string, fileName: string, folder: number, board: Board, binderViewToke?:string): Promise<Blob> {
  const obj: Board = {
    id: boardId
  }
  if (folder) {
    obj.folders = [{
      sequence: folder,
      files: board.page_groups
    }]
  } else {
    if (board.page_groups) {
      obj.page_groups = board.page_groups
    } else if (board.pages) {
      obj.pages = board.pages
    }
  }
  return Ajax.sendRequest(boardRequestNode(type==='pdf'?ClientRequestType.BOARD_REQUEST_DOWNLOAD_BOARD:ClientRequestType.BOARD_REQUEST_DOWNLOAD_FOLDER, obj,[{
    name: ClientRequestParameter.RESOURCE_REQUEST_RESOURCE_TYPE,
    string_value: type
  },{
    name: ClientRequestParameter.RESOURCE_DOWNLOAD_RESOURCE_CONTENT_DISPOSITION,
    string_value: fileName
  }]), {
    responseType: 'blob'
  })
  /*return fetch('/board', {
    method: 'POST',
    headers,
    body: JSON.stringify(boardRequestNode(type==='pdf'?ClientRequestType.BOARD_REQUEST_DOWNLOAD_BOARD:ClientRequestType.BOARD_REQUEST_DOWNLOAD_FOLDER, obj,[{
      name: ClientRequestParameter.RESOURCE_REQUEST_RESOURCE_TYPE,
      string_value: type
    },{
      name: ClientRequestParameter.RESOURCE_DOWNLOAD_RESOURCE_CONTENT_DISPOSITION,
      string_value: fileName
    }]))
  }).then(resp => {
    if (resp.ok) {
      return resp.blob()
    }
    throw new Error('err')
  })*/
}


function updateBoardProperties(boardId: string, properties: BoardProperty[]): Promise<ClientResponse> {
  const board: Board = {
    id: boardId,
    properties: properties
  }

  return Ajax.sendRequest(
    boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_PROPERTY, board)
  )
}

function updateFeedReaction(boardId: string, feedSeq: number, reaction: string, suppressNotification?: boolean): Promise<ClientResponse> {
  let params: ClientParam[] = null
  const board: Board = {
    id: boardId,
    feeds: [{
      sequence: feedSeq,
      reactions: [{
        text: reaction
      }]
    }]
  }
  if (suppressNotification) {
    params = [{
      name: ClientRequestParameter.BOARD_REQUEST_PUSH_NOTIFICATION_OFF
    }]
  }
  return Ajax.sendRequest(boardRequestNode(ClientRequestType.BOARD_REQUEST_UPDATE_FEED_REACTION, board, params));
}

export {
  createBoard,
  deleteBoard,
  updateBoard,
  joinBoard,
  leaveBoard,
  subscribeBoard,
  unsubscribeBoard,
  readBoard,
  readBoardByField,
  readCover,
  readFullBoard,
  readContentLibraryBoard,
  inviteGroups,
  inviteUser,
  removeUser,
  removeTeam,
  removeUserAndTeam,
  setOwner,
  updateUserAccessType,
  readFeedsByPagination,
  readFlatFeeds,
  updateTypeIndication,
  readOnGoingDelegateFeeds,
  readUserActivities,
  updateNotificationLevel,
  convertTempBoardToNormalBoard,
  markFeedAsUnRead,
  createViewToken,
  createResourceViewToken,
  transferActions,
  resendInviteEmailSms,
  bulkResendInviteEmailSms,
  createInvitationViewToken,
  setBoardType,
  setBoardActiveStatus,
  renewBoardWorkspaceId,
  renewBoardPassword,
  createShareViewToken,
  updateShareViewToken,
  joinBoardByViewToken,
  joinBoardByManagementUser,
  readViewTokens,
  updateRequestingUser,
  inviteRequestingUser,
  downloadBoardFolder,
  updateBoardProperties,
  updateFeedReaction,
}
