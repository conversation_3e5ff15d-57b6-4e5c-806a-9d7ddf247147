<template>
  <div class="mx-dialIn-content">
    <div class="mx-dialIn-header">
      <mx-branding-logo
        ref="brandingLogoRef"
        :font-style="{ 'font-size': '18px'}"
        :type="'colorRect'"
        :logo-center="true"
        :image-style="{ height: '32px'}" />
    </div>
    <div class="mx-dialIn-middle">
      <div class="mx-dialIn-middle_title">
        {{ $t("local_dial_in_numbers") }}
      </div>
      <div class="mx-dialIn-middle_brief">
        {{ $t("select_phone_number") }}
      </div>
    </div>
    <div class="mx-dialIn-table">
      <Table :data="tableData" />
    </div>
    <div class="mx-dialIn-footer">
      <footer
        v-if="!hidePoweredBy && logoSrc"
        class="power-by-footer">
        <img :src="logoSrc">
      </footer>
    </div>
  </div>
</template>

<script>
import MxBrandingLogo from '../../../views/theme/helpers/logo';
import { poweredBy } from '../../../views/theme/src/images/logo';
import {ajaxPost} from '../../tinyProj/common/request';
import { mapMutations, mapGetters } from 'vuex';
import Table from './Table';

function transformOrgTags(tags) {
  let result = {
    API_Allow_Replace_Policy: false
  };
  tags.forEach(tag => {
    let tagValue;
    if (tag.string_value === '1' || tag.string_value === 'true') {
      tagValue = true;
    } else if (tag.string_value === '0' || tag.string_value === 'false') {
      tagValue = false;
    } else {
      tagValue = tag.string_value;
    }
    result[tag.name] = tagValue;
  });
  return result;
}

function getGroup(contextPath) {
  return new Promise(function(resolve, reject) {
    ajaxPost(contextPath + '/group', {type: 'GROUP_REQUEST_READ'}, function (response) {
      try {
        let tags = {},
            caps = {},
            isOnlineBillingOrg,
            groupName = '';
        tags = transformOrgTags(response.object.group.tags || []);
        caps = response.object.group.group_caps || {};
        groupName = tags.OrgConfig_BrandName || response.object.group.name;
        isOnlineBillingOrg =
            response.object.group.group_caps.is_online_billing;
        resolve({
          tags,
          caps,
          isOnlineBillingOrg,
          groupName,
          id: response.object.group.id,
          contextPath
        });
      } catch (e) {
        reject(e);
      }
    }, reject)
  });
}

function sortFn(type) {
  return (a, b) => {
    let nameA = (a[type] || '').toLowerCase();
    let nameB = (b[type] || '').toLowerCase();
    if (nameA > nameB) {
      return 1;
    } else if (nameA < nameB) {
      return -1;
    }
  };
}
function getMeetBoard(contextPath) {
  return new Promise((resolve, reject)=>{
    ajaxPost(contextPath + '/board', {type: 'GROUP_REQUEST_READ_TELEPHONY_DOMAIN'}, (result)=>{
      if (
          result &&
          result.object &&
          result.object.telephony_domain &&
          result.object.telephony_domain.numbers
      ) {
        //1.filter by is_default
        let defaultNumber = [];
        let numbers = result.object.telephony_domain.numbers || [];
        if (numbers.findIndex(num => num.is_default) !== -1) {
          defaultNumber = numbers.splice(
              numbers.findIndex(num => num.is_default),
              1
          );
        }
        //2.sort by Alphabetical order
        resolve(defaultNumber.concat(numbers.sort(sortFn('location'))))
      }
    }, ()=>{
      reject && reject()
    })
  })

}

export default {
  name: 'App',
  components: {
    MxBrandingLogo,
    Table
  },
  data() {
    return {
      isLoaded: false,
    };
  },
  mounted() {
    let pathname = location.pathname
    let contextPath = pathname.replace(/\/web\/dial-in\/?/, '')
    getGroup(contextPath).then(res => {
      this.setGroup(res);
      this.isLoaded = true;
      this.tag = res.tags;
      document.title = res.groupName;
    });
    getMeetBoard(contextPath).then(res => {
      this.setTelephonyConf(res);
    });
  },
  computed: {
    ...mapGetters('group', [
      'groupTags',
      'groupCaps',
      'groupTelephonyConfs'
    ]),
    logoSrc() {
      return (
        (this.groupTags && this.groupTags.White_Logo_Moxtra_Powerby) ||
        poweredBy
      );
    },
    hidePoweredBy() {
      return this.groupCaps && this.groupCaps.hide_moxtra_logo;
    },
    tosUrl() {
      return this.groupTags.API_CUS_TAC || 'https://moxo.com/terms';
    },
    cusPrivacyUrl() {
      return this.groupTags.API_Org_Policy_URL;
    },
    moxtraPolicy() {
      if (this.groupTags.API_Allow_Replace_Policy) {
        return '';
      } else {
        return 'https://moxo.com/privacy-policy';
      }
    },
    tableData() {
      return this.groupTelephonyConfs;
    }
  },
  methods: {
    ...mapMutations('group', ['setGroup', 'setTelephonyConf'])
  }
};
</script>
