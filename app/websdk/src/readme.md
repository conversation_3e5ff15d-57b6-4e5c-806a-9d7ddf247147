# MEP JSSDK 

### 1.init 
```javascript
mepsdk.setup({
    baseDomain:'http://baz.grouphour.com',
    deployDirectory:'mep',
    accessToken:"Wx4zMgAAAWm6D_9XAACowENVVDhYVXRYd203VTRjM1VuNmd2TmJ2MAAAAANUMThhMV9WcHpNN2p3b2xzTnFTWGlob01USTROR014WlRC"
});
```
### 2. show mep site
```javascript
 var mepwin = mepsdk.showMEPWindow('#box')
```
### 3. add event listener
```javascript
mepwin.onAddMember(function(binderId) {
  
})
mepwin.onNotification(function(msg) {
  
})
```