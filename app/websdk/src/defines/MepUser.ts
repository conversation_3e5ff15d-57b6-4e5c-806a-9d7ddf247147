export interface MepUser {
  id: string;
  uniqueId?: string;
  firstName: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  title?: string;
  avatar?: string;
  clientGroupId?: string;
  clientGroupName?: string;
  isMyself?: boolean;
}

export enum MepUserStatus {
  UNKNOWN = 0,
  ONLINE = 100,
  BUSY = 200,
  OFFLINE = 300,
  OUT_OF_OFFICE = 400

  // Online = 100,
	// Busy = 200,
	// Offline = 300,
	// OutOfOffice = 400,
	// CustomizeAvailable = 100,
	// CustomizeBusy = 200,
	// CustomizeInvisible = 300

}

export interface MepRelationUser extends MepUser {
  status: MepUserStatus;
  subscribeStatus: Function;
  statusTitle?: string;
  statusMessage?: string;
}