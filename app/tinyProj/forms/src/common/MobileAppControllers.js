import { BrowserUtils, ObjectUtils } from '@commonUtils'

function sendMessageToMobileApp (data) {
  let message = data
  if (!ObjectUtils.isString(data)) {
    message = JSON.stringify(data)
  }
  console.debug(message)
  if (BrowserUtils.isAndroid) {
    window.moxo.postMessage(message)
  } else if (BrowserUtils.isIOS) {
    window.webkit.messageHandlers.moxo.postMessage(message)
  } else {
    window.parent.postMessage(message)
  }
}

class MobileAppControllersImpl {
  submittingForm () {
    sendMessageToMobileApp('submittingForm')
  }

  closeFillForm () {
    sendMessageToMobileApp('closeWindow')
  }

  previewFile (fileSequence) {
    sendMessageToMobileApp({
      action: 'previewFile',
      data: {
        sequence: fileSequence
      }
    })
  }
  closeIntegrationForm(){
    sendMessageToMobileApp('close')
  }
  notifyApp(data){
    sendMessageToMobileApp(data)
  }
}

const MobileAppControllers = new MobileAppControllersImpl()
export { MobileAppControllers }
export default MobileAppControllers
