<template>
  <div style="height:100%;width: 100%;">
    <template v-if="ready">
      <template v-if="isIntegrationForm">
        <IntegrationFormView :fill-form-options="integrationOptions"
                             class="integration-form-view"
                             :action-type="IntegrationFormActionType.FillForm"
                             @events="handleEvents"
                             @success="handleSuccess"/>
      </template>
      <template v-else-if="isPDFForm">
        <PDFFormPreviewView
          v-if="isPreview"
          :runtime-options="runtimeOptions"
          :form-view-model="baseObject.formViewModel"
        />
        <FillFormView
          v-else
          :is-mobile="true"
          :runtime-options="runtimeOptions"
          :binder-id="formParams.boardId"
          :transaction-sequence="formParams.transactionSequence"
          @beforeSubmit="handleBeforeSubmit"
          @close="handleClose"
        />
      </template>
      <template v-else>
        <MobilePreviewForm
          v-if="isPreview"
          :base-object="baseObject"
          :board="board"
          :enable-d-d-r="enableDDR"
        />
        <MobileFillForm v-else :preset-object="presetObject" />
      </template>
    </template>
    <div v-if="error" class="empty-view">
      {{ error }}
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'pinia'
import { MxConsts, ObjectUtils } from '@commonUtils'
import { defineComponent } from '@vue/composition-api'
import { useUserStore } from './piniaStore'
import MobileFillForm from './views/MobileFillForm.vue'
import MobilePreviewForm from './views/MobilePreviewForm.vue'
import { useDDRSupportStore } from '@views/stores/ddrSupport'
import { FormScenes } from '@model/form/defines/shared'
import FillFormView from '@views/form/FillFormView.vue'
import { installFormComponents } from '@views/form/install'
import MobileAppControllers from './common/MobileAppControllers'
import { TransactionType } from 'isdk/src/proto/generated/TransactionType'
import PDFFormPreviewView from '@views/form/pdfForm/PDFFormPreviewView.vue'
import { setupPDFFormEnvironment } from './common/pdfFormHelper'
import IntegrationFormView from '@views/integrations/integrationCenter/views/IntegrationFormView.vue'
import { IntegrationFormActionType } from '@views/integrations/integrationCenter/common/transformIntegrationForm'

export default defineComponent({
  name: 'App',
  components: {
    IntegrationFormView,
    MobilePreviewForm,
    MobileFillForm,
    PDFFormPreviewView,
    FillFormView
  },

  data() {
    return {
      IntegrationFormActionType,
      error: '',
      presetObject: null,
      ready: false,
      isIntegrationForm: false,
      isPDFForm: false,
      isPreview: false,
      baseObject: null,
      board: null,
      formParams: {
        boardId: '',
        transactionSequence: 0,
        viewToken: ''
      },
      ddrContextId: null,
      enableDDR: false,
      integrationOptions: {},
      runtimeOptions: {
        viewToken: '',
        isPDFForm: false,
        enableDDR: false,
        scenes: ''
      }
    }
  },

  computed: {
    ...mapState(useUserStore, ['currentUser'])
  },
  mounted() {
    // debugger
    const params = new URLSearchParams(location.search)
    this.formParams = {
      boardId: params.get('boardId'),
      transactionSequence: parseInt(params.get('transactionSeq'), 10),
      viewToken: params.get('view_token'),
      requestType: params.get('requestType'),
      originBoardId: params.get('originBoardId')
    }
    const { viewToken } = this.formParams
    document.body.classList.add('use-scroll')
    this.verifyToken()
      .then(async () => {
        this.handleForm(false)
      })
      .catch((e) => {
        this.useSetAnonymousUser(viewToken)
          .then(async () => {
            this.handleForm(true)
          })
          .catch((err) => {
            this.error = this.$t('Something_went_wrong')
            this.removePageLoading()
          })
      })
  },
  beforeDestroy() {
    if (this.ddrContextId) {
      useDDRSupportStore().destroy(this.ddrContextId)
    }
  },
  methods: {
    ...mapActions(useUserStore, [
      'verifyToken',
      'readTransaction',
      'readWorkflowBoardViewModel',
      'useSetAnonymousUser'
    ]),
    removePageLoading() {
      document.getElementById('waiting-spinner')?.remove()
    },
    handleBeforeSubmit() {
      MobileAppControllers.submittingForm()
    },
    handleEvents(events){
      MobileAppControllers.notifyApp(JSON.stringify(events || '[]'))
    },
    handleSuccess(){
      MobileAppControllers.notifyApp('success')
    },
    handleClose() {
      MobileAppControllers.closeFillForm()
    },
    async handleForm(isAnonymous = false) {
      const {
        boardId,
        transactionSequence,
        viewToken,
        requestType,
        originBoardId
      } = this.formParams
      this.isPreview = requestType === 'VIEW_FORM'

      if (isAnonymous) {
        if (this.isPreview && originBoardId) {
          // TODO: in this case, boardId is tempBoardId, viewToken is tempBoardViewToken, originBoardId is realBoardId
          // TODO: we need to read realBoardId and realBoardViewToken
        }
      } else {
        if (this.isPreview && originBoardId) {
          const boardViewModel = await this.readWorkflowBoardViewModel(originBoardId)
          if (boardViewModel.workflow) {
            this.ddrContextId = { boardId: originBoardId }
            useDDRSupportStore().initStore(this.ddrContextId, boardViewModel)
            this.enableDDR = true
          }
        }
      }
      const params = new URLSearchParams(location.search)

      this.readTransaction(
        boardId,
        transactionSequence,
        isAnonymous ? viewToken : this.isPreview ? viewToken : ''
      ).then(({ board, baseObject }) => {
        const transaction = ObjectUtils.getByPath(board, 'transactions.0', '')
        this.board = board
        this.baseObject = baseObject
        const subType = transaction.sub_type || ''
        const type = transaction.type || ''
        if (subType.toLowerCase() === 'integration') {
          const transactionSeq = parseInt(params.get('transactionSeq'))
          const stepSeq = parseInt(params.get('stepSeq'))
          const buttonId = params.get('buttonId')
          const boardId = params.get('boardId')

          let inputs = {}
          const steps = transaction?.steps || []
          steps.forEach((step) => {
            if (step.sequence === stepSeq) {
              try {
                const actions = JSON.parse(step.actions || '{}')
                const action = actions.find((ac) => ac.id === buttonId)?.payload || '{}'
                inputs = JSON.parse(action)
              } catch (error) {
                console.error('parse json error:', error)
              }
            }
          })

          const customData = JSON.parse(transaction.custom_data || '{}')
          const integrationOptions = {
            customData: customData?.integration || {},
            board_id: boardId,
            transaction_sequence: transactionSeq,
            step_sequence: stepSeq,
            button_id: buttonId,
            input: inputs
          }
          this.integrationOptions = integrationOptions
          this.isIntegrationForm = true
        }
        if (type === TransactionType.TRANSACTION_TYPE_PDF_FORM) {
          installFormComponents()
          setupPDFFormEnvironment()

          this.isPDFForm = true
          document.body.classList.remove('use-scroll')
          document.body.classList.add('pdf-form')
          let scenes = this.isPreview ? FormScenes.PreviewTemplate : FormScenes.FillForm
          if (baseObject.formViewModel.isCompleted) {
            scenes = FormScenes.PreviewCompleted
          }
          this.runtimeOptions = {
            isPDFForm: true,
            enableDDR: this.enableDDR,
            scenes
          }
        }
        const isCompleted =
          transaction.status === MxConsts.TransactionStatus.TRANSACTION_STATUS_COMPLETED
        this.presetObject = {
          boardId: boardId,
          transaction,
          isCompleted,
          stepSequence: transaction.steps[0].sequence
        }

        const iAmAssignee = !!baseObject.steps.find((step) => {
          if (step.assignee.isMyTeam || step.assignee.isMySelf) {
            return true
          }
          return false
        })

        if ((!iAmAssignee && !this.isPreview) || isCompleted) {
          this.isPreview = true
        }

        this.ready = true
        this.removePageLoading()
      })
    }
  }
})
</script>
<style>
.empty-view {
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}


.no-scroll {
  overflow: hidden;
}
</style>
