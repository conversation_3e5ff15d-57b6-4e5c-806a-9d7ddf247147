{"$id": "Number", "$schema": "http://json-schema.org/draft-07/schema#", "title": "PhoneNumber", "description": "Moxtra Component Form Phone Number ", "type": "object", "properties": {"ui:widget": {"description": "Define UI components for rendering fields", "type": "string", "value": "Number", "readOnly": true}, "ui:editor": {"value": "FormFieldNumberOptions"}, "type": {"description": "The id of the component", "value": "Number", "type": "string", "readOnly": true}, "id": {"description": "The unique field id of the component", "type": "string", "default": "Number"}, "uniqueName": {"description": "The uniqueName of the component", "type": "string", "default": ""}, "label": {"description": "Number Value", "type": "string", "value": "Number"}, "value": {"type": "string", "default": ""}, "defaultValue": {"type": "string", "default": ""}, "fieldSpecific": {"type": "object", "properties": {"maxLength": {"type": "number"}, "minLength": {"type": "number"}, "condition": {"type": "object", "description": ""}, "isProtected": {"type": "boolean", "description": "Protected", "ui:widget": "checkbox"}, "precision": {"type": "number", "default": 0, "enum": [0, 1, 2, 3, 4]}, "hideLabel": {"type": "boolean", "description": "Hide Label", "ui:widget": "checkbox"}, "placeholder": {"description": "", "type": "string", "default": "123"}, "supporting": {"description": "The instruction of this Field", "type": "string"}, "required": {"type": "boolean", "description": "Is Required", "ui:widget": "checkbox"}, "readonly": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>", "ui:widget": "checkbox"}, "size": {"type": "string", "enum": ["full", "half", "one-third", "quarter"], "value": "full"}}, "value": {"precision": 0, "hideLabel": false, "placeholder": "123", "supporting": "", "required": false, "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}}, "allOf": [{"if": {"properties": {"fieldSpecific": {"type": "object", "properties": {"required": {"const": true}}}}}, "then": {"properties": {"value": {"$ref": "#/definitions/notEmptyString"}}, "required": ["value"], "errorMessage": "Required_field"}}], "definitions": {"notEmptyString": {"type": "string", "minLength": 1, "errorMessage": "Required_field"}}}