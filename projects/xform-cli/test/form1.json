{"type": "form", "isEmptyForm": false, "submitState": {"locale": "en-us"}, "metadata": {"conversation_name": "test 1013", "form_submitter": "rm2 xxx", "form_title": "test form", "org_name": "sheng test org", "submit_date": 1697159191066, "timezone": "Asia/Shanghai"}, "data": {"pages": [{"type": "Page", "id": "Page-c0d375ae", "fields": [{"type": "SingleLineText", "id": "SingleLineText-c3af4ed3", "uniqueName": "Type_a_question", "label": "Single Line", "value": "The form version associated with the plugin at creation time", "defaultValue": "", "fieldSpecific": {"isProtected": false, "hideLabel": false, "placeholder": "Enter your answer here", "supporting": "", "required": false, "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "MultiLineText", "id": "MultiLineText-6da370ff", "uniqueName": "Type_a_question_f186a8af", "label": "Multi line", "value": "Determines whether the form data is compatible with the current upgrade logic.\n  - If `supportVersion` is a string, the form's version must strictly equal it.\n  - If `supportVersion` is a function, returning `true` means the form is supported.", "defaultValue": "", "fieldSpecific": {"rowHeight": "4", "isProtected": false, "hideLabel": false, "placeholder": "Enter your answer here", "supporting": "", "required": false, "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Heading", "id": "Heading-fc41e8c3", "label": "Heading Image- left", "image": "/board/CBHmKHf5koPiKbvrkTDmsvFA/transaction/4/10", "fieldSpecific": {"labelAlignment": "left", "imageAlignment": "left", "supporting": "", "imageName": "Screenshot 2024-09-24 at 20.57.39.png", "imageUUID": "b20e6d28-0d8c-41c6-bdb3-17acdbd38c26", "imageWidth": 72, "enableImage": true, "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Heading", "id": "Heading-cda47232", "label": "Heading Image- left", "image": "/board/CBHmKHf5koPiKbvrkTDmsvFA/transaction/4/9", "fieldSpecific": {"labelAlignment": "center", "imageAlignment": "top", "supporting": "", "imageName": "Screenshot 2024-09-24 at 20.57.39.png", "imageUUID": "b20e6d28-0d8c-41c6-bdb3-17acdbd38c26", "imageWidth": 72, "enableImage": true, "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Heading", "id": "Heading-0a15aa20", "label": "Heading Image- left", "image": "/board/CBHmKHf5koPiKbvrkTDmsvFA/transaction/4/8", "fieldSpecific": {"labelAlignment": "right", "imageAlignment": "right", "supporting": "", "imageName": "Screenshot 2024-09-24 at 20.57.39.png", "imageUUID": "b20e6d28-0d8c-41c6-bdb3-17acdbd38c26", "imageWidth": 72, "enableImage": true, "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Paragraphs", "id": "Paragraphs-dbf6ba13", "label": "Paragraph", "text": "<p>this is a demo&nbsp;</p>\n<p><a href=\"https://baidu.com\" target=\"_blank\" rel=\"noopener\">Link</a></p>\n<ul>\n<li>List 1\n<ul>\n<li>List sub 1</li>\n</ul>\n</li>\n</ul>\n<ol>\n<li>Item\n<ol>\n<li>Item 1</li>\n</ol>\n</li>\n</ol>\n<p><strong>this is B</strong></p>\n<p><span style=\"text-decoration: underline;\">under line</span></p>", "fieldSpecific": {"hideLabel": false, "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "FileUpload", "id": "FileUpload-b5de580b", "uniqueName": "File_Upload", "label": "File Upload", "value": [{"refSeq": 9103, "SPath": "folders[sequence=32].files[sequence=9101]", "uuid": "f980c963-6449-4146-a34f-25f7ec900e4a", "thumbnail": "/board/CBDvZGhl64PgJuP9zuEtNZMF/9105/9107", "type": "PNG", "name": "Screenshot 21.11.08.png", "size": 245927, "resSeq": 9100, "url": "https://devta02.grouphour.com/ws?id=OcNhbZS"}, {"refSeq": 9103, "SPath": "folders[sequence=32].files[sequence=9101]", "uuid": "f980c963-6449-4146-a34f-25f7ec900e4a", "thumbnail": "/board/CBDvZGhl64PgJuP9zuEtNZMF/9105/9107", "type": "PNG", "name": "Screenshot 2025-04-17 at 21.11.08.png", "size": 245927, "resSeq": 9100, "url": "https://devta02.grouphour.com/ws?id=OcNhbZS"}], "defaultValue": "", "errors": [], "fieldSpecific": {"isProtected": false, "hideLabel": false, "placeholder": "", "supporting": "", "required": false, "maxFileSize": 5, "maxFileCount": 1, "fileAccept": "pdf, doc, docx, xls, xlsx, csv, txt, rtf, html, zip, mp3, wma, mpg, flv, avi, jpg, jpeg, png, gif", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Image", "id": "Heading-824f3834", "image": "/board/BzY2xi5IBIVDYmm341czZ7I/transaction/1555/172efc96-b803-49c2-b69f-7cb1a0e48864", "fieldSpecific": {"labelAlignment": "left", "imageAlignment": "center", "supporting": "", "imageWidth": 200, "imageName": "Screenshot 2024-12-13 at 15.36.51.png", "imageUUID": "172efc96-b803-49c2-b69f-7cb1a0e48864", "enableImage": true, "imageAlt": "", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "DropdownList", "id": "DropdownList-b021f21f", "uniqueName": "Type_a_question_636b8d74", "label": "Type a question", "value": "Type Option 1", "defaultValue": "", "fieldSpecific": {"options": ["Type Option 1", "Type Option 2"], "hideLabel": false, "placeholder": "Select an option", "supporting": "", "required": false, "size": "full", "isMoxoStep": false, "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "MultiSelection", "id": "MultiSelection-4ec80cc3", "uniqueName": "Type_a_question_1957a70c", "label": "Type a question", "defaultValue": [], "value": ["Type Option 1"], "displayValue": "Type Option 1", "fieldSpecific": {"layout": "vertical", "options": ["Type Option 1", "Type Option 2"], "hideLabel": false, "supporting": "", "required": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "SingleSelection", "id": "SingleSelection-3e27c044", "uniqueName": "Type_a_question_5ca16a3f", "label": "Type a question", "value": "Type Option 2", "defaultValue": "", "fieldSpecific": {"options": ["Type Option 1", "Type Option 2"], "layout": "vertical", "hideLabel": false, "placeholder": "", "supporting": "", "required": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Image", "id": "Heading-047b12bf", "image": "/board/CBHmKHf5koPiKbvrkTDmsvFA/transaction/4/11", "fieldSpecific": {"labelAlignment": "left", "imageAlignment": "center", "supporting": "", "imageWidth": 200, "imageName": "Screenshot 2024-10-25 at 10.28.59.png", "imageUUID": "6196f1f2-45bc-4c15-96e5-2f857ebae630", "enableImage": true, "imageAlt": "", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}], "fieldSpecific": {"condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Page", "id": "Page-6e2880c3", "fields": [{"type": "Signature", "label": "Signature", "id": "Signature-8d96a021", "uniqueName": "Signature", "value": "", "fieldSpecific": {"supporting": "", "hideLabel": false, "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Number", "id": "Number-9ef0dc23", "uniqueName": "Number", "label": "Number", "value": "", "defaultValue": "", "fieldSpecific": {"precision": 0, "hideLabel": false, "placeholder": "123", "supporting": "", "required": false, "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "id": "Currency-fb363d72", "uniqueName": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": {"amount": "21.00", "code": "HKD"}, "defaultValue": {"amount": "", "code": "HKD"}, "fieldSpecific": {"precision": 2, "hideLabel": false, "placeholder": "0.00", "required": false, "supporting": "", "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Date", "id": "Date-b3fb1f1c", "uniqueName": "Date_52d94b09", "label": "Date", "value": {"hour": 0, "minute": 0, "timeFormat": "AM", "timestamp": 1745387191379, "dayTimestamp": 1745337600000, "dateStr": "2025-04-23"}, "defaultValue": {"hour": 9, "minute": 21, "timeFormat": "AM", "timestamp": 0, "dayTimestamp": 0, "dateStr": ""}, "displayValue": "23/Apr/2025", "fieldSpecific": {"currentDate": false, "currentTime": false, "timeFormat": "A", "dateFormat": "DD/MMM/YYYY", "withTime": true, "withDate": true, "hideLabel": false, "supporting": "", "readonly": false, "required": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "Date", "id": "Date-e5d032a4", "uniqueName": "Date", "label": "Date", "value": {"hour": "11", "minute": "23", "timeFormat": "AM", "timestamp": 1745637813835, "dayTimestamp": 1745596800000, "dateStr": "2025-04-26"}, "defaultValue": {"hour": null, "minute": null, "timeFormat": "AM", "timestamp": 0, "dayTimestamp": 0, "dateStr": ""}, "displayValue": "26/Apr/2025", "fieldSpecific": {"currentDate": false, "currentTime": false, "timeFormat": "A", "dateFormat": "DD/MMM/YYYY", "withTime": true, "withDate": true, "hideLabel": false, "supporting": "", "readonly": false, "required": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "PhoneNumber", "id": "PhoneNumber-f68c7158", "uniqueName": "Phone_Number", "label": "Phone Number", "value": {"countryCode": "CN", "number": "+86", "phoneNumber": "13905609369"}, "defaultValue": {"countryCode": "HK", "number": "+852", "phoneNumber": null}, "displayValue": "+8613905609369", "fieldSpecific": {"isProtected": true, "defaultCountry": "US", "defaultFromProfile": false, "lockCountry": false, "hideLabel": false, "placeholder": "(*************", "supporting": "", "required": false, "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "EmailAddress-c9925ebd", "uniqueName": "Email_Address", "label": "Email Address", "value": "", "defaultValue": "<EMAIL>", "fieldSpecific": {"defaultFromProfile": false, "hideLabel": false, "placeholder": "<EMAIL>", "supporting": "", "required": false, "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}, "enableDefaultValue": true}}, {"type": "Address", "id": "Address-98ac3c22", "uniqueName": "Address_", "label": "Address ", "lineOneLabel": "Add test line 1", "value": {"addressLineOne": "Building D1, Innovation Park", "addressLineTwo": "800 Wangjiang Xi Road", "city": "he<PERSON>i", "state": "hebei", "zipcode": "311800", "countryCode": "US", "countryDisplayName": ""}, "defaultValue": {"addressLineOne": "", "addressLineTwo": "", "city": "", "state": "", "zipcode": "", "countryCode": "US"}, "fieldSpecific": {"lockCountry": false, "showAddressLineTwo": true, "showCity": true, "showState": true, "showZipcode": true, "showCountry": true, "hideLabel": false, "required": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "LineSeperator", "id": "LineSeperator-ea0dee46", "fieldSpecific": {"size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}, {"type": "UserName", "id": "UserName-523a2973", "uniqueName": "Name", "label": "Name", "value": {"firstName": "lin", "lastName": "han", "middleName": "-", "suffix": "ax"}, "defaultValue": {"firstName": "", "lastName": "", "middleName": "", "suffix": ""}, "fieldSpecific": {"showMiddleName": true, "showPrefix": false, "prefixOptions": ["Mr.", "Mrs.", "Ms."], "showSuffix": true, "defaultFromProfile": false, "hideLabel": false, "required": false, "readonly": false, "size": "full", "condition": {"enable": false, "visible": true, "rule": true, "rules": []}, "enableDefaultValue": false}}], "fieldSpecific": {"condition": {"enable": false, "visible": true, "rule": true, "rules": []}}}]}}